package org.cjc.les.device;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.List;

public class IcpAgilentDataManagerTest {

    @Test
    public void testParseStatusMsg_ValidJson() {
        String jsonStr = "[{\"Type\":\"SOLUTION_DETAILS\",\"Value\":\"INDEX: 0\",\"ID\":5,\"StrictMatch\":false}," +
                         "{\"Type\":\"SOLUTION_DETAILS\",\"Value\":\"LABEL: 空白\",\"ID\":5,\"StrictMatch\":false}," +
                         "{\"Type\":\"SOLUTION_DETAILS\",\"Value\":\"TYPE: BLANK\",\"ID\":5,\"StrictMatch\":false}]";

        List<IcpAgilentDataManager.StatusInfo> result = IcpAgilentDataManager.parseStatusMsg(jsonStr);

        assertNotNull(result);
        assertEquals(3, result.size());

        IcpAgilentDataManager.StatusInfo first = result.get(0);
        assertEquals("SOLUTION_DETAILS", first.getType());
        assertEquals("INDEX: 0", first.getValue());
        assertEquals("5", first.getId());

        IcpAgilentDataManager.StatusInfo second = result.get(1);
        assertEquals("SOLUTION_DETAILS", second.getType());
        assertEquals("LABEL: 空白", second.getValue());
        assertEquals("5", second.getId());

        IcpAgilentDataManager.StatusInfo third = result.get(2);
        assertEquals("SOLUTION_DETAILS", third.getType());
        assertEquals("TYPE: BLANK", third.getValue());
        assertEquals("5", third.getId());
    }

    @Test
    public void testParseStatusMsg_EmptyString() {
        List<IcpAgilentDataManager.StatusInfo> result = IcpAgilentDataManager.parseStatusMsg("");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseStatusMsg_NullString() {
        List<IcpAgilentDataManager.StatusInfo> result = IcpAgilentDataManager.parseStatusMsg(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseStatusMsg_InvalidJson() {
        String invalidJson = "invalid json string";
        List<IcpAgilentDataManager.StatusInfo> result = IcpAgilentDataManager.parseStatusMsg(invalidJson);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseStatusMsg_EmptyArray() {
        String emptyArray = "[]";
        List<IcpAgilentDataManager.StatusInfo> result = IcpAgilentDataManager.parseStatusMsg(emptyArray);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseStatusMsg_MissingFields() {
        String jsonStr = "[{\"Type\":\"SOLUTION_DETAILS\"}," +
                         "{\"Value\":\"LABEL: 空白\"}," +
                         "{\"ID\":5}]";

        List<IcpAgilentDataManager.StatusInfo> result = IcpAgilentDataManager.parseStatusMsg(jsonStr);

        assertNotNull(result);
        assertEquals(3, result.size());

        IcpAgilentDataManager.StatusInfo first = result.get(0);
        assertEquals("SOLUTION_DETAILS", first.getType());
        assertNull(first.getValue());
        assertNull(first.getId());

        IcpAgilentDataManager.StatusInfo second = result.get(1);
        assertNull(second.getType());
        assertEquals("LABEL: 空白", second.getValue());
        assertNull(second.getId());

        IcpAgilentDataManager.StatusInfo third = result.get(2);
        assertNull(third.getType());
        assertNull(third.getValue());
        assertEquals("5", third.getId());
    }

    @Test
    public void testParseSolutionList_ValidStatusInfoList() {
        List<IcpAgilentDataManager.StatusInfo> statusInfoList = new ArrayList<>();

        // 第一个溶液 (ID=5)
        IcpAgilentDataManager.StatusInfo info1 = new IcpAgilentDataManager.StatusInfo();
        info1.setType("SOLUTION_DETAILS");
        info1.setValue("INDEX: 0");
        info1.setId("5");
        statusInfoList.add(info1);

        IcpAgilentDataManager.StatusInfo info2 = new IcpAgilentDataManager.StatusInfo();
        info2.setType("SOLUTION_DETAILS");
        info2.setValue("LABEL: 空白");
        info2.setId("5");
        statusInfoList.add(info2);

        IcpAgilentDataManager.StatusInfo info3 = new IcpAgilentDataManager.StatusInfo();
        info3.setType("SOLUTION_DETAILS");
        info3.setValue("SEQUENCE_NAME: 空白");
        info3.setId("5");
        statusInfoList.add(info3);

        IcpAgilentDataManager.StatusInfo info4 = new IcpAgilentDataManager.StatusInfo();
        info4.setType("SOLUTION_DETAILS");
        info4.setValue("WEIGHT: 1.000000");
        info4.setId("5");
        statusInfoList.add(info4);

        IcpAgilentDataManager.StatusInfo info5 = new IcpAgilentDataManager.StatusInfo();
        info5.setType("SOLUTION_DETAILS");
        info5.setValue("VOLUME: 1.000000");
        info5.setId("5");
        statusInfoList.add(info5);

        IcpAgilentDataManager.StatusInfo info6 = new IcpAgilentDataManager.StatusInfo();
        info6.setType("SOLUTION_DETAILS");
        info6.setValue("DILUTION: 1.000000");
        info6.setId("5");
        statusInfoList.add(info6);

        IcpAgilentDataManager.StatusInfo info7 = new IcpAgilentDataManager.StatusInfo();
        info7.setType("SOLUTION_DETAILS");
        info7.setValue("IS_SELECTED_FOR_MEASUREMENT: FALSE");
        info7.setId("5");
        statusInfoList.add(info7);

        // 第二个溶液 (ID=6)
        IcpAgilentDataManager.StatusInfo info8 = new IcpAgilentDataManager.StatusInfo();
        info8.setType("SOLUTION_DETAILS");
        info8.setValue("INDEX: 1");
        info8.setId("6");
        statusInfoList.add(info8);

        IcpAgilentDataManager.StatusInfo info9 = new IcpAgilentDataManager.StatusInfo();
        info9.setType("SOLUTION_DETAILS");
        info9.setValue("LABEL: 标准 1");
        info9.setId("6");
        statusInfoList.add(info9);

        List<IcpSolutionItemArg> result = IcpAgilentDataManager.parseSolutionList(statusInfoList);

        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个溶液 (按索引排序后应该是index=0的)
        IcpSolutionItemArg solution1 = result.get(0);
        assertEquals(Long.valueOf(0), solution1.getIndex());
        assertEquals("空白", solution1.getLabel());
        assertEquals("空白", solution1.getSampleName());
        assertEquals("1.000000", solution1.getWeight());
        assertEquals("1.000000", solution1.getVolume());
        assertEquals("1.000000", solution1.getDilution());
        assertEquals("FALSE", solution1.getMeasurementCheck());

        // 验证第二个溶液
        IcpSolutionItemArg solution2 = result.get(1);
        assertEquals(Long.valueOf(1), solution2.getIndex());
        assertEquals("标准 1", solution2.getLabel());
    }

    @Test
    public void testParseSolutionList_EmptyList() {
        List<IcpSolutionItemArg> result = IcpAgilentDataManager.parseSolutionList(new ArrayList<>());
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @Test
    public void testParseSolutionResultList_ValidStatusInfoList() {
        List<IcpAgilentDataManager.StatusInfo> statusInfoList = new ArrayList<>();

        // 第一个测量结果 (ID=439, 空白样品的P元素测量)
        IcpAgilentDataManager.StatusInfo info1 = new IcpAgilentDataManager.StatusInfo();
        info1.setType("MEASUREMENT_CALCULATION");
        info1.setValue("LABEL: 空白");
        info1.setId("439");
        statusInfoList.add(info1);

        IcpAgilentDataManager.StatusInfo info2 = new IcpAgilentDataManager.StatusInfo();
        info2.setType("MEASUREMENT_CALCULATION");
        info2.setValue("COLUMN_HEADER: P (213.618 nm)");
        info2.setId("439");
        statusInfoList.add(info2);

        IcpAgilentDataManager.StatusInfo info3 = new IcpAgilentDataManager.StatusInfo();
        info3.setType("MEASUREMENT_CALCULATION");
        info3.setValue("MEASUREMENT_INTENSITY: 0.90");
        info3.setId("439");
        statusInfoList.add(info3);

        IcpAgilentDataManager.StatusInfo info4 = new IcpAgilentDataManager.StatusInfo();
        info4.setType("MEASUREMENT_CALCULATION");
        info4.setValue("RSD_INTENSITY: 0.00");
        info4.setId("439");
        statusInfoList.add(info4);

        IcpAgilentDataManager.StatusInfo info5 = new IcpAgilentDataManager.StatusInfo();
        info5.setType("MEASUREMENT_CALCULATION");
        info5.setValue("FLAGS_INTENSITY: !");
        info5.setId("439");
        statusInfoList.add(info5);

        IcpAgilentDataManager.StatusInfo info6 = new IcpAgilentDataManager.StatusInfo();
        info6.setType("MEASUREMENT_CALCULATION");
        info6.setValue("MEASUREMENT_CONCENTRATION: 0.00");
        info6.setId("439");
        statusInfoList.add(info6);

        IcpAgilentDataManager.StatusInfo info7 = new IcpAgilentDataManager.StatusInfo();
        info7.setType("MEASUREMENT_CALCULATION");
        info7.setValue("RSD_CONCENTRATION: 不适用");
        info7.setId("439");
        statusInfoList.add(info7);

        IcpAgilentDataManager.StatusInfo info8 = new IcpAgilentDataManager.StatusInfo();
        info8.setType("MEASUREMENT_CALCULATION");
        info8.setValue("FLAGS_CONCENTRATION: !");
        info8.setId("439");
        statusInfoList.add(info8);

        IcpAgilentDataManager.StatusInfo info9 = new IcpAgilentDataManager.StatusInfo();
        info9.setType("MEASUREMENT_CALCULATION");
        info9.setValue("UNITS: ppm");
        info9.setId("439");
        statusInfoList.add(info9);

        // 第二个测量结果 (ID=442, 标准1样品的P元素测量)
        IcpAgilentDataManager.StatusInfo info10 = new IcpAgilentDataManager.StatusInfo();
        info10.setType("MEASUREMENT_CALCULATION");
        info10.setValue("LABEL: 标准 1");
        info10.setId("442");
        statusInfoList.add(info10);

        IcpAgilentDataManager.StatusInfo info11 = new IcpAgilentDataManager.StatusInfo();
        info11.setType("MEASUREMENT_CALCULATION");
        info11.setValue("COLUMN_HEADER: P (213.618 nm)");
        info11.setId("442");
        statusInfoList.add(info11);

        IcpAgilentDataManager.StatusInfo info12 = new IcpAgilentDataManager.StatusInfo();
        info12.setType("MEASUREMENT_CALCULATION");
        info12.setValue("MEASUREMENT_INTENSITY: 899.83");
        info12.setId("442");
        statusInfoList.add(info12);

        IcpAgilentDataManager.StatusInfo info13 = new IcpAgilentDataManager.StatusInfo();
        info13.setType("MEASUREMENT_CALCULATION");
        info13.setValue("MEASUREMENT_CONCENTRATION: 1.00");
        info13.setId("442");
        statusInfoList.add(info13);

        IcpAgilentDataManager.StatusInfo info14 = new IcpAgilentDataManager.StatusInfo();
        info14.setType("MEASUREMENT_CALCULATION");
        info14.setValue("UNITS: ppm");
        info14.setId("442");
        statusInfoList.add(info14);

        List<IcpSolutionResultItem> result = IcpAgilentDataManager.parseSolutionResultList(statusInfoList);

        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个测量结果 (按标签排序后应该是"标准 1")
        IcpSolutionResultItem result1 = result.get(0);
        assertEquals("标准 1", result1.getLabel());
        assertEquals("P (213.618 nm)", result1.getColumnHeader());
        assertEquals("899.83", result1.getMeasurementIntensity());
        assertEquals("1.00", result1.getMeasurementConcentration());
        assertEquals("ppm", result1.getUnits());

        // 验证第二个测量结果
        IcpSolutionResultItem result2 = result.get(1);
        assertEquals("空白", result2.getLabel());
        assertEquals("P (213.618 nm)", result2.getColumnHeader());
        assertEquals("0.90", result2.getMeasurementIntensity());
        assertEquals("0.00", result2.getRsdIntensity());
        assertEquals("!", result2.getFlagsIntensity());
        assertEquals("0.00", result2.getMeasurementConcentration());
        assertEquals("不适用", result2.getRsdConcentration());
        assertEquals("!", result2.getFlagsConcentration());
        assertEquals("ppm", result2.getUnits());
    }

    @Test
    public void testParseSolutionResultList_EmptyList() {
        List<IcpSolutionResultItem> result = IcpAgilentDataManager.parseSolutionResultList(new ArrayList<>());
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseSolutionResultList_NonMeasurementType() {
        List<IcpAgilentDataManager.StatusInfo> statusInfoList = new ArrayList<>();

        // 添加非MEASUREMENT_CALCULATION类型的StatusInfo
        IcpAgilentDataManager.StatusInfo info1 = new IcpAgilentDataManager.StatusInfo();
        info1.setType("SOLUTION_DETAILS");
        info1.setValue("LABEL: 空白");
        info1.setId("5");
        statusInfoList.add(info1);

        List<IcpSolutionResultItem> result = IcpAgilentDataManager.parseSolutionResultList(statusInfoList);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

}
