import org.cjc.les.device.IcpAgilentDataManager;
import org.cjc.les.device.IcpSolutionResultItem;
import java.util.List;

public class TestParseSolutionResultList {
    public static void main(String[] args) {
        // 测试完整的解析流程：从JSON字符串到StatusInfo列表，再到IcpSolutionResultItem列表
        String jsonStr = "[" +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"LABEL: 空白\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"COLUMN_HEADER: P (213.618 nm)\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_INTENSITY: 0.90\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_INTENSITY: 0.00\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_INTENSITY: !\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_CONCENTRATION: 0.00\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_CONCENTRATION: 不适用\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_CONCENTRATION: !\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"UNITS: ppm\",\"ID\":439,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"LABEL: 标准 1\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"COLUMN_HEADER: P (213.618 nm)\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_INTENSITY: 899.83\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_INTENSITY: 0.00\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_INTENSITY: !\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_CONCENTRATION: 1.00\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_CONCENTRATION: 不适用\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_CONCENTRATION: !\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"UNITS: ppm\",\"ID\":442,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"LABEL: 标准 1\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"COLUMN_HEADER: N (174.213 nm)\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_INTENSITY: 900.23\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_INTENSITY: 0.00\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_INTENSITY: !\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_CONCENTRATION: 1.00\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_CONCENTRATION: 不适用\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_CONCENTRATION: !\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"UNITS: ppm\",\"ID\":443,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"LABEL: 样品 1\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"COLUMN_HEADER: P (213.618 nm)\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_INTENSITY: 3383.15\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_INTENSITY: 26.24\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_INTENSITY: !\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"MEASUREMENT_CONCENTRATION: 11748701.94\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"RSD_CONCENTRATION: 26.24\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"FLAGS_CONCENTRATION: !\",\"ID\":454,\"StrictMatch\":false}," +
            "{\"Type\":\"MEASUREMENT_CALCULATION\",\"Value\":\"UNITS: ppm\",\"ID\":454,\"StrictMatch\":false}" +
            "]";

        System.out.println("=== 测试测量结果解析流程 ===");
        
        // 第一步：解析JSON字符串为StatusInfo列表
        System.out.println("第一步：解析JSON字符串为StatusInfo列表");
        List<IcpAgilentDataManager.StatusInfo> statusInfoList = IcpAgilentDataManager.parseStatusMsg(jsonStr);
        System.out.println("解析得到 " + statusInfoList.size() + " 个StatusInfo对象");
        
        // 第二步：将StatusInfo列表解析为IcpSolutionResultItem列表
        System.out.println("\n第二步：将StatusInfo列表解析为IcpSolutionResultItem列表");
        List<IcpSolutionResultItem> resultList = IcpAgilentDataManager.parseSolutionResultList(statusInfoList);
        System.out.println("解析得到 " + resultList.size() + " 个测量结果对象");
        
        // 显示解析结果
        System.out.println("\n=== 解析结果 ===");
        for (int i = 0; i < resultList.size(); i++) {
            IcpSolutionResultItem result = resultList.get(i);
            System.out.println("测量结果 " + (i + 1) + ":");
            System.out.println("  标签: " + result.getLabel());
            System.out.println("  列标题: " + result.getColumnHeader());
            System.out.println("  测量强度: " + result.getMeasurementIntensity());
            System.out.println("  RSD强度: " + result.getRsdIntensity());
            System.out.println("  强度标志: " + result.getFlagsIntensity());
            System.out.println("  测量浓度: " + result.getMeasurementConcentration());
            System.out.println("  RSD浓度: " + result.getRsdConcentration());
            System.out.println("  浓度标志: " + result.getFlagsConcentration());
            System.out.println("  单位: " + result.getUnits());
            System.out.println();
        }
        
        // 测试边界情况
        System.out.println("=== 测试边界情况 ===");
        
        // 测试空列表
        System.out.println("测试空StatusInfo列表:");
        List<IcpSolutionResultItem> emptyResult = IcpAgilentDataManager.parseSolutionResultList(java.util.Collections.emptyList());
        System.out.println("结果数量: " + emptyResult.size());

        // 测试非MEASUREMENT_CALCULATION类型
        System.out.println("测试非MEASUREMENT_CALCULATION类型:");
        List<IcpAgilentDataManager.StatusInfo> nonMeasurementList = new java.util.ArrayList<>();
        IcpAgilentDataManager.StatusInfo nonMeasurementInfo = new IcpAgilentDataManager.StatusInfo();
        nonMeasurementInfo.setType("SOLUTION_DETAILS");
        nonMeasurementInfo.setValue("LABEL: 测试");
        nonMeasurementInfo.setId("1");
        nonMeasurementList.add(nonMeasurementInfo);
        List<IcpSolutionResultItem> nonMeasurementResult = IcpAgilentDataManager.parseSolutionResultList(nonMeasurementList);
        System.out.println("结果数量: " + nonMeasurementResult.size());
        
        System.out.println("\n所有测试完成！");
    }
}
