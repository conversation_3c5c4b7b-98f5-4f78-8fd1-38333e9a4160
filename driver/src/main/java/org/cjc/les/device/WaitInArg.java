/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.Data;
import org.cjc.les.annotation.Parameter;

@Data
public class WaitInArg {
    /**
     * IO编号
     */
    @Parameter("IO编号")
    private int inNumber;
    /**
     * 期望值
     */
    @Parameter("期望值")
    private boolean expectValue;
    /**
     * 超时时间
     */
    @Parameter("超时时间")
    private int timeout;
}
