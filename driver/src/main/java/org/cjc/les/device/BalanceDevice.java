/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;


import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Device;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.annotation.Variable;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.driver.socket.CommandLineTcpSocket;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 分析天平设备，通过TCP驱动实现与IC的通讯与交互
 */
@Log4j2
@Device(name = "分析天平设备", type = DeviceTypeEnum.PERIPHERAL, description = "提供精确称量")
public class BalanceDevice {

    private final static double WEIGHING_OFFSET = 0.1;

    /**
     * TCP命令行驱动
     */
    private CommandLineTcpSocket driver = new CommandLineTcpSocket();

    @Command(name = "初始化连接", type = CommandTypeEnum.INIT)
    public boolean init(@Parameter("IP配置") IpConfigArg config) {
        try {
            driver.connect(config.getIpAddress(), config.getPort());
        } catch (IOException e) {
            log.error("Initialize BalanceDevice error, with config:{}, exception:{}", config, e.getMessage(), e);
            return false;
        }

        return true;
    }


    @Command("初始化")
    public void active(){

    }

    @Command("异步称量检测")
    public void asyncWeighing(@Parameter("称量参数")WeighingArg arg) throws IOException {
        TaskExecutorContext.setVar("WEIGHING_ARG", arg);
        AtomicInteger errorRetries = new AtomicInteger(1000);
        driver.asyncSendCommand("SIR", (lineStr) -> {

            parseWeighingResult(lineStr, arg);
            // 当出现出错次数超过3次时，终止称量任务，并通知其他辅助设备停止倒样
            if (!arg.isDone() && arg.getErrorCode().equals(ErrorCodeEnum.ERROR) && (errorRetries.decrementAndGet()) == 0 ){
                arg.setDone(true);
            }
            return arg.isDone();
        });
    }

    @Command("稳定称量结果查询")
    @Variable(name = "BALANCE_WEIGHING_RESULT", type = "Array", tip = "分析天平称量结果列表")
    public void stableWeight() throws IOException {

        driver.sendCommand("S", (lineStr) -> {
            if (lineStr.startsWith("S S")) {
                log.info("S S response OK");
                putBalanceValue(lineStr.substring("S S".length()));
            } else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for S response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    private void putBalanceValue(String valueStr) {
        if (StringUtils.isEmpty(valueStr)) {
            log.error("Illegal argument valueStr={}", valueStr);
            return;
        }

        String[] vArr = valueStr.trim().split(" ");

        TaskExecutorContext.addVar("BALANCE_WEIGHING_RESULT", new BigDecimal(vArr[0].trim()));

    }

    @Command("清零")
    public void zero() throws IOException {
        driver.sendCommand("Z", (lineStr) -> {
            if (lineStr.startsWith("Z A")) {
                log.info("Z A response OK");
            } else if (lineStr.startsWith("Z")) {
                log.error("Z response FAILED.{}", lineStr);
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for HA07 response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    @Command(name = "结束", type = CommandTypeEnum.FINALIZE)
    public void finalize() {
        try {
            driver.close();
            driver = new CommandLineTcpSocket();
        } catch (IOException e) {
            log.error("Finalize MagICDevice error, with config:{}, exception:{}", driver.getInetAddress(), e.getMessage(), e);
        }
    }

    private void parseWeighingResult(String lineStr, WeighingArg arg) {
        arg.setErrorCode(ErrorCodeEnum.SUCCESS);
        arg.setErrorMsg("");
        try {
            double rs = 0.0;
            if (lineStr.startsWith("S S")) {
                rs = Double.parseDouble(StringUtils.trim(lineStr.substring(3, lineStr.length() - 1)));
                arg.setActualValue(rs);
                log.info("Stable value: " + rs);
            } else if (lineStr.startsWith("S D")) {
                rs = Double.parseDouble(StringUtils.trim(lineStr.substring(3, lineStr.length() - 1)));
                arg.setActualValue(rs);
                log.info("Unstable value: " + rs);
            } else {
                log.error("Not expected response: {}", lineStr);
                arg.setErrorCode(ErrorCodeEnum.ERROR);
                arg.setErrorMsg("Not expected response");
            }
        }catch(NumberFormatException ex0){
            log.error("Number format error, lineStr={}", lineStr);
            arg.setErrorCode(ErrorCodeEnum.ERROR);
            arg.setErrorMsg("Number format error.");
        }
        // 未达到设定的数值精度时，重复监听最新数据
        if ((arg.getExpectValue() - arg.getActualValue()) - WEIGHING_OFFSET > arg.getPrecise()) {
            arg.setDone(false);
            return;
        }
        arg.setDone(true);
    }
}
