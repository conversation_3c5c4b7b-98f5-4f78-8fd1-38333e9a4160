package org.cjc.les.device;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

@Data
public class IcpSolutionResultItem {
    // LABEL
    private String label;
    // COLUMN_HEADER
    private String columnHeader;
    // MEASUREMENT_INTENSITY
    private String measurementIntensity;
    // RSD_INTENSITY
    private String rsdIntensity;
    // FLAGS_INTENSITY
    private String flagsIntensity;
    // MEASUREMENT_CONCENTRATION
    private String measurementConcentration;
    // RSD_CONCENTRATION
    private String rsdConcentration;
    // FLAGS_CONCENTRATION
    private String flagsConcentration;
    // UNITS
    private String units;

    public void copy(IcpSolutionResultItem source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
