/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Device;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.driver.socket.CommandLineTcpSocket;

import java.io.IOException;

@Log4j2
@Device(name = "多通阀控制设备", type = DeviceTypeEnum.PERIPHERAL, description = "与IC/ICP进样器配合加注液体")
public class MpvDevice {

    /**
     * TCP命令行驱动
     */
    private CommandLineTcpSocket driver = new CommandLineTcpSocket();

    @Command(name = "初始化连接", type = CommandTypeEnum.INIT)
    public boolean init(@Parameter("IP配置") IpConfigArg config) {
        try {
            driver.connect(config.getIpAddress(), config.getPort());
        } catch (IOException e) {
            log.error("Initialize MpvDevice error, with config:{}, exception:{}", config, e.getMessage(), e);
            return false;
        }

        return true;
    }

    @Command(name = "回位")
    public void home() throws IOException {
        String cmdStr = "HOME0";
        driver.sendCommand(cmdStr, (lineStr) -> {
            if (lineStr.startsWith("Done")) {
                log.info(cmdStr + " response OK");
            } else {
                // 没有收到该指令的返回结果，继续等待
                log.warn(cmdStr + " response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    @Command(name = "设置阀位")
    public void valvePos(@Parameter("阀位编号") Long value) throws IOException {
        String cmdStr = "VALVEPOS=" + value;
        driver.sendCommand(cmdStr, (lineStr) -> {
            if (lineStr.startsWith("Done")) {
                log.info(cmdStr + " response OK");
            } else {
                // 没有收到该指令的返回结果，继续等待
                log.warn(cmdStr + " response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    @Command(name = "结束", type = CommandTypeEnum.FINALIZE)
    public void finalize() {
        try {
            driver.close();
        } catch (IOException e) {
            log.error("Finalize MpvDevice error, with config:{}, exception:{}", driver.getInetAddress(), e.getMessage(), e);
        }
    }

}
