/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;


import lombok.extern.log4j.Log4j2;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Device;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.driver.socket.CommandLineTcpSocket;
import org.cjc.les.exception.TaskRunningException;

import java.io.IOException;

/**
 * 分析天平设备，通过TCP驱动实现与IC的通讯与交互
 */
@Log4j2
@Device(name = "液体加注辅助设备", type = DeviceTypeEnum.PERIPHERAL, description = "与分析天平配合加注液体")
public class LduDevice {

    /**
     * TCP命令行驱动
     */
    private CommandLineTcpSocket driver = new CommandLineTcpSocket();

    @Command(name = "初始化连接", type = CommandTypeEnum.INIT)
    public boolean init(@Parameter("IP配置") IpConfigArg config) {
        try {
            driver.connect(config.getIpAddress(), config.getPort());
        } catch (IOException e) {
            log.error("Initialize LduDevice error, with config:{}, exception:{}", config, e.getMessage(), e);
            return false;
        }

        return true;
    }

    @Command(name="校准液体注入速度")
    public void cal(@Parameter("校准值")Long value) throws IOException {
        String cmdStr = "CAL="+value;
        driver.sendCommand(cmdStr, (lineStr) -> {
            if (lineStr.startsWith("CA: Calibration adjusted to "+value)) {
                log.info(cmdStr+" response OK: {}", lineStr);
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.warn(cmdStr+ " response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    @Command(name="选择阀门")
    public void doseValve(@Parameter(name="阀门编号", inputComponent = "position-value-map")Long value) throws IOException {
        String cmdStr = "DOSEVALVE="+value;
        driver.sendCommand(cmdStr, (lineStr) -> {
            if (lineStr.startsWith("CA: Dosing valve defined to "+value)) {
                log.info(cmdStr+" response OK: {}", lineStr);
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.warn(cmdStr+ " response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    @Command(name = "选择容量")
    public void volumeDose(@Parameter("容量(ml)") Long value) throws IOException {
        String cmdStr = "VOLUMEDOSE=" + value;
        driver.sendCommand(cmdStr, (lineStr) -> {
            if (lineStr.startsWith("Dosing ")) {
                log.info(cmdStr + " response OK: {}", lineStr);
            } else {
                // 没有收到该指令的返回结果，继续等待
                log.warn(cmdStr + " response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }


    @Command(name="设置最大时长")
    public void maxTime(@Parameter("最大时长")Long value) throws IOException {
        String cmdStr = "MAXTIME="+value;
        driver.sendCommand(cmdStr, (lineStr) -> {
            if (lineStr.startsWith("CA: Max dosing time adjusted to "+value)) {
                log.info(cmdStr+" response OK");
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.warn(cmdStr+ " response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    @Command(name="计量控制")
    public void dose(@Parameter("预期计量")Long value) {
        WeighingArg arg = (WeighingArg) TaskExecutorContext.getVar("WEIGHING_ARG");
        double lastValue = arg.getActualValue();
        while(!Thread.interrupted()){
            log.info("arg: {}", arg);
            if (arg.isDone()){

                break;
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new TaskRunningException("InterruptedException occurred");
            }

        }
    }

    @Command(name = "结束", type = CommandTypeEnum.FINALIZE)
    public void finalize() {
        try {
            driver.close();
            driver = new CommandLineTcpSocket();
        } catch (IOException e) {
            log.error("Finalize LduDevice error, with config:{}, exception:{}", driver.getInetAddress(), e.getMessage(), e);
        }
    }


}
