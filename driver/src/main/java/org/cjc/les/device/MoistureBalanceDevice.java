/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.helper.ResultHelper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.driver.moisture.MoistureBalanceComDriver;
import org.cjc.les.driver.serialport.SerialPortConfig;

@org.cjc.les.annotation.Device("水分检测仪HC103")
public class MoistureBalanceDevice extends Device {

    private MoistureBalanceComDriver driver = new MoistureBalanceComDriver();

    public boolean doInit(@Parameter("配置参数") SerialPortConfig config) {
        return driver.init(config);
    }

    @Command("初始化")
    public void active(){
        driver.home();
        driver.active();
    }

    @Command(value = "选择方法", description = "HIGH:高精度测量")
    public void selectMethod(@Parameter("方法名") String methodName){
        driver.setDryMethodName(methodName);
    }

    @Command("异步称量检测")
    public void asyncWeighing(@Parameter("称量参数")WeighingArg arg) {
        TaskExecutorContext.setVar("WEIGHING_ARG", arg);
        driver.asyncWeighing(arg);
    }

    @Command("稳定称量结果查询")
    public void stableWeight(){
        driver.stableWeight();
    }

    @Command("检测当前状态")
    public void status(){
        driver.status();
    }

    @Command(value = "获取WET结果", description = "单位:g")
    public void getWetResult(){
        String rs = driver.getWetResult();
        if (StringUtils.isEmpty(rs)){
            // 返回空时，结果异常，返回0值
            rs = "0.00";
        }
        MoistureResult moiRs = (MoistureResult) TaskExecutorContext.getVar("MOISTURE_WEIGHING_RESULT");
        if (moiRs==null){
            moiRs = new MoistureResult();
            TaskExecutorContext.setVar("MOISTURE_WEIGHING_RESULT",moiRs);
        }

        moiRs.setWetWeightAsGram(Double.parseDouble(rs));
        ResultHelper.saveResultFeature("MOISTURE_WET_RS","原始重量(g)", rs);
    }

    @Command(value = "获取DRY结果", description = "单位: g 或者 %MC")
    public void getDryResult(@Parameter("单位") String unit){
        String rs = driver.getDryResult(unit);
        if (StringUtils.isEmpty(rs)){
            // 返回空时，结果异常，返回0值
            rs = "0.00";
        }
        MoistureResult moiRs = (MoistureResult) TaskExecutorContext.getVar("MOISTURE_WEIGHING_RESULT");
        if (moiRs==null){
            moiRs = new MoistureResult();
            TaskExecutorContext.setVar("MOISTURE_WEIGHING_RESULT",moiRs);
        }
        if (StringUtils.equals("g",unit)){
            moiRs.setDryWeightAsGram(Double.parseDouble(rs));
            ResultHelper.saveResultFeature("MOISTURE_DRY_RS","干燥后重量(g)", rs);
        }else if (StringUtils.equals("%MC",unit)){
            moiRs.setDrgWeightAsMc(Double.parseDouble(rs));
            ResultHelper.saveResultFeature("MOISTURE_DRY_MC_RS","水分含量(%MC)", rs);
        }

    }

    @Command("返回首页")
    public void home(){
        driver.home();
    }

    @Command("清零")
    public void zero(){
        driver.zero();
    }

    @Command(value = "结束连接",type = CommandTypeEnum.FINALIZE)
    @Override
    public void finalize() {
        driver.finalize();
        driver = new MoistureBalanceComDriver();
    }
}
