/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.core.domain.Device;
import org.cjc.les.driver.motion.RobotDriver;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.vo.MotionInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

@Log4j2
@org.cjc.les.annotation.Device(name="Trio控制器", type= DeviceTypeEnum.ROBOT)
public class TrioMotionDevice extends Device {

    /**
     * 调试用，当设置为true时，不执行Robot指令,防止误动作
     */
    private boolean skipMoveCommand = false;

    private RobotDriver driver = new RobotDriver();

    private Timer timer = new Timer();
    /**
     * 保存当前轴位置，最多支持24
     */
    private volatile double[] axisPositions = new double[24];

    int count = 0;

    synchronized public boolean doInit(@Parameter("控制器配置") MotionConfigArg motionConfigArg ) {
        String config = motionConfigArg.getConnect();
        if (StringUtils.isEmpty(config)) {
            log.error("Argument config is null.");
            return false;
        }
        boolean ret = driver.init(config);
        if (!ret){
            return false;
        }
       // initScheduler();
       //  driver.run("ONLINE");
        return true;
    }

    private void initScheduler() {

        timer.scheduleAtFixedRate(new TimerTask() {

            @Override
            public void run() {
                updateCurPos();
            }
        }, 0, 1000);

    }

    synchronized public boolean checkConnection(){
        double fout = driver.getVr(1000);
        if (doubleEqual(fout, -1.00)){
            driver.finalize();
            return false;
        }
        return true;
    }

    private void updateCurPos(){
        double fout = driver.getVr(1000);
        double xpos = driver.getVr(100);
        double ypos = driver.getVr(101);
        double zpos = driver.getVr(102);
        TrioMotionDevice.this.axisPositions[0] = xpos;
        TrioMotionDevice.this.axisPositions[1] = ypos;
        TrioMotionDevice.this.axisPositions[2] = zpos;

      //  if (count % 5 == 0) {
      //      log.info("ROBOT POS: xpos:{},ypos:{},zpos:{},fout:{},", xpos, ypos, zpos,fout);
     //   }
      //  count ++;
    }

    private void waitIdle(){
        // 实现思路：3次连续都为0，则认为信号可靠
        List<Integer> idleList =new ArrayList<>(3);
        try {
            while (!Thread.interrupted()) {
                Thread.sleep(100);
                double idle = getVr(202);
                if (equalPosition(idle, 0)){
                    idleList.add(0);
                    if (idleList.size()>=3){
                        break;
                    }
                }else{
                    idleList.clear();
                }

            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new TaskRunningException("InterruptedException occurred");
        }
    }
    private void waitIdle(long firstMilliSec){
        // 实现思路：3次连续都为0，则认为信号可靠
        List<Integer> idleList =new ArrayList<>(3);
        try {
            Thread.sleep(firstMilliSec);
            while (!Thread.interrupted()) {
                Thread.sleep(100);
                double idle = getVr(202);
                if (equalPosition(idle, 0)){
                    idleList.add(0);
                    if (idleList.size()>=3){
                        break;
                    }
                }else{
                    idleList.clear();
                }

            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new TaskRunningException("InterruptedException occurred");
        }
    }

    private void logPos(String motionName, Object arg){
        double xpos = getVr(100);
        double ypos = getVr(101);
        double zpos = getVr(102);
        double status = getVr(202);
        log.info("ROBOT timestamp[{}][{},{},{}]: status:{}, motionName:{} begin, cmd:{}",System.currentTimeMillis(),xpos, ypos,zpos, status, motionName, arg);
    }

    synchronized public MotionInfo getCurrentMotionInfo() {
        double fout = driver.getVr(1000);
        double xpos = driver.getVr(100);
        double ypos = driver.getVr(101);
        double zpos = driver.getVr(102);
        MotionInfo info = new MotionInfo();
        info.setXpos(xpos);
        info.setYpos(ypos);
        info.setZpos(zpos);

        info.setDoorStatus(checkDoorStatus());

        if (doubleEqual(fout, 0.00)){
            info.setStatus(MotionInfo.StatusEnum.IDLE);
        }else{
            info.setStatus(MotionInfo.StatusEnum.ERROR);
        }

        String lout = String.valueOf((long)fout);
        info.setErrorCode(lout);
        switch(lout){
            case "0":
                break;
            default:
                info.setErrorMsg("轴状态错误");
        }

        return info;
    }

    /**
     * 执行指令
     * @param cmd BASIC程序指令
     */
    @Command(name="执行BASIC", description = "执行控制器的BASIC脚本")
    synchronized  public void execute(@Parameter("BASIC指令") String  cmd){
        waitIdle();
        if (skipMoveCommand) return;
        logPos("execute",cmd);
        driver.execute(cmd);
        waitIdle();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new TaskRunningException("InterruptedException occurred");
        }

    }

    @Command(value = "相对位置移动指令", description = "同步动作，会等待移动到指定位置才会返回该命令")
    synchronized  public void move(@Parameter("轴距离") MoveArg arg){
            waitIdle();
        if (skipMoveCommand) return;
       //     double lastPos = this.axisPositions[arg.getAxis()];
        logPos("move",arg);
            driver.execute("MOVE(" + arg.getPos() + ") AXIS(" + arg.getAxis() + ")");
           /*
            try {
                while (true) {
                    if (equalPosition(lastPos+arg.getPos(), this.axisPositions[arg.getAxis()])){
                        break;
                    }
                    Thread.sleep(200);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        updateCurPos();
            */
        waitIdle();
    }

    @Command(value = "绝对位置移动指令", description = "同步动作，会等待移动到指定位置才会返回该命令")
    synchronized  public void moveAbs(@Parameter(name = "目标点位", inputComponent = "position-input")PickPlaceArg arg){
        waitIdle();
        if (skipMoveCommand) return;
        //     double lastPos = this.axisPositions[arg.getAxis()];

        driver.execute("BA("+arg.getBa()+")");
        driver.execute("ACC("+arg.getAcc()+")");
        driver.execute("BA("+arg.getBa()+")");
        driver.execute("SPEED="+arg.getSpeed());
        driver.execute("BA("+arg.getBa()+")");

        logPos("moveAbs",arg);
        driver.execute("MOVEABS(" + arg.getXpos() + "," + arg.getYpos() + ","+arg.getZpos()+")");
           /*
            try {
                while (true) {
                    if (equalPosition(lastPos+arg.getPos(), this.axisPositions[arg.getAxis()])){
                        break;
                    }
                    Thread.sleep(200);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        updateCurPos();
            */
        waitIdle();
    }
    /**
     * 执行指令
     * @param programName BASIC程序指令
     */
    @Command(name="运行BASIC程序", description = "执行控制器的BASIC脚本")
    synchronized  public void run(@Parameter("BASIC程序名") String  programName){
        waitIdle();
        if (skipMoveCommand) return;
        logPos("run",programName);
        driver.run(programName);
        waitIdle();
    }

    @Command("抓取动作")
    synchronized  public void pick(@Parameter(name = "源点位", inputComponent = "position-select") PickPlaceArg arg){
        waitIdle();
        if (skipMoveCommand) return;
      //  driver.execute("BA(0)");

    //    driver.execute("SPEED=10");
     //   driver.execute("MOVE(50) AXIS(0)");
     //   double xStatus =  driver.getAxisParameter(0, "AXISSTATUS");
      //  log.info("xStatus={}", xStatus);
     //   driver.execute("MOVE(50) AXIS(1)");
      //  double yStatus =  driver.getAxisParameter(1, "AXISSTATUS");
      //  log.info("yStatus={}", yStatus);

        driver.execute("BA("+arg.getBa()+")");
        driver.execute("ACC("+arg.getAcc()+")");
        driver.execute("BA("+arg.getBa()+")");
        driver.execute("SPEED="+arg.getSpeed());
        driver.execute("VR(203)="+arg.getSpeedFactor());
        driver.execute("VR(307)="+arg.getYfree());
        driver.execute("VR(308)="+arg.getZfree());

        driver.execute("VR(301)="+arg.getXpos());
        driver.execute("VR(302)="+arg.getYpos());
        driver.execute("VR(303)="+arg.getZpos());

        driver.execute("VR(310)="+arg.getVr310());

        logPos("pick", arg);
        driver.execute("RUN \"PICK\", 6");

        waitIdle(500);
    }

    @Command("放置动作")
    synchronized  public void place(@Parameter(name = "目标点位", inputComponent = "position-select") PickPlaceArg arg){
        waitIdle();
        if (skipMoveCommand) return;
        driver.execute("BA("+arg.getBa()+")");
        driver.execute("ACC("+arg.getAcc()+")");
        driver.execute("BA("+arg.getBa()+")");
        driver.execute("SPEED="+arg.getSpeed());
        driver.execute("VR(203)="+arg.getSpeedFactor());
        driver.execute("VR(307)="+arg.getYfree());
        driver.execute("VR(308)="+arg.getZfree());

        driver.execute("VR(304)="+arg.getXpos());
        driver.execute("VR(305)="+arg.getYpos());
        driver.execute("VR(306)="+arg.getZpos());

        driver.execute("VR(310)="+arg.getVr310());

        logPos("place", arg);
        driver.execute("RUN \"PLACE\", 7");

        waitIdle(500);
    }

    /**
     * 轴位置是否相等判断，非精确相等
     * @param pos1
     * @param pos2
     * @return
     */
    private boolean equalPosition(double pos1, double pos2) {
        if (Math.abs(pos1 - pos2)<= 0.001){
            return true;
        }else{
            return false;
        }
    }

    @Override
    synchronized public void doFinalize() {
        timer.cancel();
        driver.run("OFFLINE");
        driver.finalize();
        log.info("TrioMotionDevice was finalized.");
    }

    synchronized private double getVr(int vrNo) {
        double ret = driver.getVr(vrNo);
        //log.debug("Get VR({})={}", vrNo, ret);
        return ret;
    }

    /**
     * 检查门状态：
     * @return -1,连接异常， DOOR_BIT/LOCK_BIT: 11 DoorOpened and UnLocked, 10 DoorOpened and locked: Should NOT BE in this statues, 01 DoorClosed and Unlocked, 00 DoorClosed and locked
     */
    synchronized public int checkDoorStatus(){
        double doorStatus = driver.getVr(1002); // 0: DoorClosed, 1: DoorOpened
        int doorBit = 0;
        if (doubleEqual(doorStatus, 1.0)){
            doorBit = 2;
        }
        int lockStatus = driver.getOut(0); // 1: Unlocked, 0:Locked

        return doorBit | lockStatus;
    }

    /**
     * 关门动作
     * @param bOpen
     * @return 锁状态
     */
    synchronized public int openDoor(Boolean bOpen){
        int doorStatus = checkDoorStatus();
        if (bOpen && doorStatus == 0) {
            driver.run("OFFLINE");
        }else if (!bOpen && doorStatus == 1){
            driver.run("ONLINE");
        }
        return checkDoorStatus();
    }

    private boolean doubleEqual(double d1, double d2) {
        if (d1-d2 >=-0.00001 && d1-d2 <= 0.00001){
            return true;
        }else{
            return false;
        }
    }
}
