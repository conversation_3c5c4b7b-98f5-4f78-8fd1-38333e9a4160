/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Device;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.helper.ResultHelper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.driver.socket.CommandLineTcpSocket;
import org.cjc.les.exception.TaskRunningException;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MagIC控制设备，通过TCP驱动实现与IC的通讯与交互
 */
@Log4j2
@Device(name = "MagIC控制设备", type = DeviceTypeEnum.PERIPHERAL, description = "MagIC Net 3.3软件接入控制设备")
public class MagICDevice {

    /**
     * TCP命令行驱动
     */
    private CommandLineTcpSocket driver = new CommandLineTcpSocket();

    private IpConfigArg ipConfigArg = new IpConfigArg();

    @Data
    public class ICSampleData {
        private String method; // IC执行方法
        private String ident; // 样品编号
        private String sampleType = "Sample";
        private int position = 1;
        private int injections = 1;
        private long volume = 20L; //单位 ul
        private BigDecimal dilution = new BigDecimal(1); // 稀释倍数
        private int sampleAmount = 1;
        private String info1="1";
        private String batchName="1";

        public String toCmdLine(){
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(method).append(";")
                    .append(ident).append(";")
                    .append(sampleType).append(";")
                    .append(position).append(";")
                    .append(injections).append(";")
                    .append(volume).append(";")
                    .append(dilution).append(";")
                    .append(sampleAmount).append(";")
                    .append(info1).append(";")
                    .append(batchName);
            return stringBuffer.toString();
        }
    }


    @Command(name = "初始化", type = CommandTypeEnum.INIT)
    public boolean init(@Parameter("IP配置") IpConfigArg config) {
        try {
            ipConfigArg = config;
            driver.connect(config.getIpAddress(), config.getPort());
        } catch (IOException e) {
            log.error("Initialize MagICDevice error, with config:{}, exception:{}", config, e.getMessage(), e);
            return false;
        }

        return true;
    }

    @Command(name = "执行方法", description = "执行并等待方法返回结果")
    public void executeMethod(@Parameter("IC方法参数") ICMethodArg methodArg) throws IOException {
        String methodName = methodArg.getMethodName();
        String sampleName = "test";
        if (TaskExecutorContext.getTask() != null) {
            Sample sample = TaskExecutorContext.getTask().getSample();
            if (sample != null) {
                sampleName = sample.getName();
            }
            TaskMethod mth = TaskExecutorContext.getTaskMethod();
            if (mth!=null) {
                sampleName += "-" + mth.getId();
            }
        }
        // BigDecimal dilution = calculateDilution();
        BigDecimal dilution = new BigDecimal(methodArg.getDilution());
        ICSampleData icSampleData = new ICSampleData();
        icSampleData.setMethod(methodName);
        icSampleData.setIdent(sampleName);
        icSampleData.setVolume(1);
        icSampleData.setDilution(dilution);
        StringBuffer cmdBuffer = new StringBuffer();
         cmdBuffer.append("EXECUTE_METHOD ").append( icSampleData.toCmdLine() );
        if (this.isEnableMock()) {
            cmdBuffer.append(" ENABLE_MOCK");
        }
        final String cmd = cmdBuffer.toString();
        driver.sendCommand(cmd, (respLine) -> {
            // READY
            // RUNNING
            // FINISHED
            log.info(cmd + " response: " + respLine);
            if (respLine.startsWith("EXECUTE_METHOD DONE")) {
                // 保存计算结果
               // saveResults(respLine.substring("EXECUTE_METHOD DONE:".length()));
            }else{
                log.error("executeMethod response error: " + respLine);
            }

            return true;// true,响应完成, false,继续等待下一个响应行
        });
    }

    private BigDecimal calculateDilution() {
        BigDecimal dilution = new BigDecimal(1);
        List<BalanceResult> rsList = (List<BalanceResult>)TaskExecutorContext.getContext().getVariableMap().get("BALANCE_WEIGHING_RESULT");
        if (CollectionUtils.isEmpty(rsList)){
            log.error("Could not found balance result");
            return dilution;
        }
        TaskCommand currentCommand = TaskExecutorContext.getTaskCommand();
        if (currentCommand == null){
            log.warn("No task executing environment.");
            return dilution;
        }
        List<BalanceResult> curMethodRsList = rsList.stream().filter(rs->{
            return currentCommand.getTaskMethodId().equals( rs.getTaskMethodId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curMethodRsList)){
            log.error("Could not found current method's balance result");
            return dilution;
        }
        // 水溶检测方法的称量，固体样品重量 + 纯水重量
        BigDecimal sampleWeight = curMethodRsList.get(0).getValue();
        BigDecimal waterWeight = curMethodRsList.get(curMethodRsList.size()-1).getValue();
        if (sampleWeight.longValue() == 0L){
            throw new TaskRunningException("Water weight is zero.");
        }

        BigDecimal factor = new BigDecimal("0.01095");
        sampleWeight = sampleWeight.subtract(factor);
        waterWeight = waterWeight.subtract(factor);

        // 计算公式: (固体样品重量 + 纯水重量)/固体样品重量/100
        dilution =  waterWeight.add(sampleWeight).divide(sampleWeight,5, RoundingMode.HALF_UP).divide(new BigDecimal(100),5, RoundingMode.HALF_UP);
        log.info("Calculated Dilution result: {}, sample weight: {}, water weight: {}", dilution, sampleWeight, waterWeight);

        return dilution;
    }

    @Command(name = "获取结果", description = "获取IC返回结果")
    public boolean getResult(@Parameter("方法名") String methodName) throws IOException {
        String sampleName = "test";
        if (TaskExecutorContext.getTask() != null) {
            Sample sample = TaskExecutorContext.getTask().getSample();
            if (sample != null) {
                sampleName = sample.getName();
            }
            TaskMethod mth = TaskExecutorContext.getTaskMethod();
            if (mth!=null) {
                sampleName += "-" + mth.getId();
            }
        }
        String cmd = "GET_RESULT " + methodName + ";" + sampleName;
        final String _sampleName = sampleName;
        final StringBuffer out = new StringBuffer();
        driver.sendCommand(cmd, (respLine) -> {
            // READY
            // RUNNING
            // FINISHED
            log.info(cmd + " response: " + respLine);
            if (respLine.startsWith("GET_RESULT DONE:")) {
                // 保存计算结果
                saveResults(respLine.substring("GET_RESULT DONE:".length()), methodName, _sampleName);
            }
            else if (respLine.startsWith("GET_RESULT UNFINISHED:")) {
                return false;
            }
            else{
                log.error("GET_RESULT response error: " + respLine);
                out.append("ERROR");
            }

            return true;// true,响应完成, false,继续等待下一个响应行
        });
        if (out.length()>0){
            return false;
        }else{
            return true;
        }
    }

    private void saveResults(String resultStr, String methodName, String sampleName) {
        if (StringUtils.isEmpty(resultStr)){
            log.error("illegal argument resultStr="+resultStr);
            return;
        }
        int idxSampleRs = resultStr.indexOf(methodName+";"+sampleName);
        if (idxSampleRs==-1){
            log.error("Could not found result: {}", methodName+";"+sampleName );
            return;
        }
        String[] rsArr = resultStr.substring(idxSampleRs).split(";");
        if (rsArr.length<11){
            log.error("Illegal format result="+resultStr);
            return;
        }

        if (TaskExecutorContext.getTask() != null) {
            ResultHelper.saveResultFeature("IC_N-NH4-RS", "N-NH4", rsArr[3]);
            ResultHelper.saveResultFeature("IC_K2O-RS", "K2O", rsArr[4]);
            ResultHelper.saveResultFeature("IC_MgO-RS", "MgO", rsArr[5]);
            ResultHelper.saveResultFeature("IC_CaO-RS", "CaO", rsArr[6]);
            ResultHelper.saveResultFeature("IC_Cl-RS", "Cl", rsArr[7]);
            ResultHelper.saveResultFeature("IC_N-NO3-RS", "N-NO3", rsArr[8]);
            ResultHelper.saveResultFeature("IC_SO4-RS", "SO4", rsArr[9]);
            ResultHelper.saveResultFeature("IC_P2O5-RS", "P2O5", rsArr[10]);
        }
    }

    @Command(name = "等待IC准备完成", description = "IC忙该指令会阻塞该动作继续执行")
    public void waitICReady() throws IOException {
        String cmd = "WAIT_IC_READY";
        CommandLineTcpSocket driverPri = new CommandLineTcpSocket();
        driverPri.connect(ipConfigArg.getIpAddress(), ipConfigArg.getPort());
        driverPri.sendCommand(cmd, (respLine) -> {
            // READY
            // RUNNING
            // FINISHED
            log.info(cmd + " response: " + respLine);
            if (respLine.startsWith("WAIT_IC_READY DONE")) {
                return true;
            } else {
                return false;
            }

        });
        driverPri.close();
    }

    @Command(name = "等待IC状态", description = "获取预期的IC状态")
    public boolean waitICStatus(@Parameter("IC状态参数") ICStatusArg statusArg) throws IOException {
        String sampleName = "test";
        if (TaskExecutorContext.getTask() != null) {
            Sample sample = TaskExecutorContext.getTask().getSample();
            if (sample != null) {
                sampleName = sample.getName();
            }
            TaskMethod mth = TaskExecutorContext.getTaskMethod();
            if (mth!=null) {
                sampleName += "-" + mth.getId();
            }
        }
        String cmd = "WAIT_IC_STATUS " + statusArg.getMethodName() + ";" + sampleName + " "+ statusArg.getStatus();
        final String _sampleName = sampleName;
        final StringBuffer out = new StringBuffer();
        driver.sendCommand(cmd, (respLine) -> {
            // READY
            // RUNNING
            // FINISHED
            log.info(cmd + " response: " + respLine);
            if (respLine.startsWith("WAIT_IC_STATUS DONE")) {
                // 保存计算结果
                //saveResults(respLine.substring("GET_RESULT DONE:".length()), statusArg.getMethodName(), _sampleName);
            }
            else if (respLine.startsWith("WAIT_IC_STATUS UNFINISHED")) {
                return false;
            }
            else{
                log.error("GET_RESULT response error: " + respLine);
                out.append("ERROR");
            }

            return true;// true,响应完成, false,继续等待下一个响应行
        });
        if (out.length()>0){
            return false;
        }else{
            return true;
        }
    }

    @Command(name = "结束", type = CommandTypeEnum.FINALIZE)
    public void finalize() {
        try {
            driver.close();
            driver = new CommandLineTcpSocket();
        } catch (IOException e) {
            log.error("Finalize MagICDevice error, with config:{}, exception:{}", driver.getInetAddress(), e.getMessage(), e);
        }
    }
    private boolean isEnableMock() {
        if (TaskExecutorContext.getRealDeviceInstance() != null) {
            return TaskExecutorContext.getRealDeviceInstance().isEnableMock();
        }
        return false;
    }
}
