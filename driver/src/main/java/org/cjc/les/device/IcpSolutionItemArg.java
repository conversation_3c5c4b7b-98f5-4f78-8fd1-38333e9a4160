package org.cjc.les.device;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.map.MapUtil;
import lombok.Data;
import org.cjc.les.annotation.Parameter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class IcpSolutionItemArg {
    @Parameter("索引")
    private Long index;
    @Parameter("标签")
    private String label;
    @Parameter("新标签")
    private String newLabel;
    @Parameter("样品名")
    private String sampleName;
    @Parameter("重量")
    private String weight;
    @Parameter("容量")
    private String volume;
    @Parameter("稀释倍数")
    private String dilution;
    @Parameter("选中为待测项")
    private String measurementCheck;

    /**
     * 溶液类型 BLANK, STANDARD, SAMPLE
     */
    private String type;

    /**
     * 是否新增
     */
    private Boolean addNew;

    private Long sampleCount;

    private Long executedByTaskId;
    private Long executedByTaskMethodId;
    private Long executedByTaskCommandId;

    /**
     * 检测结果
     */
    private List<IcpSolutionResultItem> results;

    public Map<String, Object> toMap() {
        return MapUtil.builder(new HashMap<String, Object>())
                .put("index", index)
                .put("label", label)
                .put("newLabel", newLabel)
                .put("sampleName", sampleName)
                .put("weight", weight)
                .put("volume", volume)
                .put("dilution", dilution)
                .put("measurementCheck", measurementCheck)
                .put("type", type)
                .put("addNew", addNew)
                .put("sampleCount", sampleCount)
                .build();
    }

    public void copy(IcpSolutionItemArg source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
