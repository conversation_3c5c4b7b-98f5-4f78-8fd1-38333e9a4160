package org.cjc.les.device;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.service.dto.ResultDto;
import org.cjc.les.core.service.dto.ResultFeatureDto;
import org.cjc.les.core.service.dto.ResultItemDto;
import org.cjc.les.exception.TaskRunningException;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class IcpAgilentDataManager {

    @Data
    static class CacheKey {
        private Long methodId;
        private String worksheetPath;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CacheKey cacheKey = (CacheKey) o;
            return methodId.equals(cacheKey.methodId) && worksheetPath.equals(cacheKey.getWorksheetPath());
        }

        @Override
        public int hashCode() {
            return methodId.hashCode() + worksheetPath.hashCode();
        }
    }

    /**
     * ICP_OES设备定义状态格式
     */
    @Data
    static public class StatusInfo {
        private String type;
        private String value;
        private String id;
    }

    /**
     * 定义设备状态信息, 消息格式:
     * [{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"ONLINE","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"PLASMA_STATUS: Off","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"PLASMA_CONTROL_STATE: CanIgnite","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"PUMP_SPEED: 0","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"SNOUT_BOOST: ON","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"POLY_BOOST: OFF","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"READY","ID":3045}]
     */
    @Data
    static public class DeviceStatusInfo {
        private String plasmaState;
        private String bumpState;
    }

    /**
     * 缓存检测方法对应的Worksheet的测试溶液列表及测试结果
     */
    private Map<CacheKey, List<IcpSolutionItemArg>> cacheMap = new HashMap<>();

    private CacheKey currentKey;

    private DeviceStatusInfo deviceStatusInfo;

    public CacheKey buildCacheKey(Long methodId, String worksheetPath) {
        CacheKey key = new CacheKey();
        key.setMethodId(methodId);
        key.setWorksheetPath(worksheetPath);
        return key;
    }

    /**
     * 设置当前选择方法并初始化对应的列表
     * @param currentKey
     */
    public void setCurrentKey(CacheKey currentKey) {
        this.currentKey = currentKey;
        if (!cacheMap.containsKey(currentKey)) {
            cacheMap.put(currentKey, new ArrayList<>());
        }
    }

    public void setCurrentKey(Long methodId, String worksheetPath) {
        CacheKey key = buildCacheKey(methodId, worksheetPath);
        setCurrentKey(key);
    }

    public List<IcpSolutionItemArg> getCurrentSolutionItems() {
        return cacheMap.get(getCurrentKey());
    }

    public Optional<ResultItemDto> getLastResultByTaskCommandId(Long taskCommandId) {
        // 后序遍历getCurrentSolutionItems,并找出taskCommandId一致的记录
        if (CollectionUtils.isEmpty(getCurrentSolutionItems())){
            return Optional.empty();
        }
        List<IcpSolutionItemArg> list = getCurrentSolutionItems();
        for (int i=list.size()-1; i>=0; i--) {
            IcpSolutionItemArg item = list.get(i);
            if (item.getExecutedByTaskCommandId().equals(taskCommandId)){
                return Optional.of(buildResultItem(item));
            }
        }
        return Optional.empty();
    }

    private ResultItemDto buildResultItem(IcpSolutionItemArg item) {
        ResultItemDto resultItem = new ResultItemDto();
        resultItem.setName(item.getLabel());
        resultItem.setDescription(item.getType());
        item.getResults().forEach(rs->{
            ResultFeatureDto feature = new ResultFeatureDto();
            feature.setName(rs.getColumnHeader());
            feature.setValue(rs.getMeasurementConcentration());
            feature.setUnit(rs.getUnits());
            feature.setRawValue(JSON.toJSONString(rs));
            feature.setRsd(rs.getRsdConcentration());
            resultItem.getResultFeatures().add(feature);
        });
        return resultItem;
    }

    public IcpSolutionItemArg createNewSolutionItem() {
        List<IcpSolutionItemArg> list = getCurrentSolutionItems();
        IcpSolutionItemArg item = new IcpSolutionItemArg();
        if (CollectionUtils.isEmpty(list)) {
            item.setIndex(0L);
            list.add(item);
        }else{
            item.copy(list.get(list.size() - 1));
            item.setIndex(list.get(list.size() - 1).getIndex() + 1);
            list.add(item);
        }
        item.setType("SAMPLE");
        item.setAddNew(true);
        long count = list.stream().filter(i-> StringUtils.equalsIgnoreCase(i.getType(),"SAMPLE")).count();
        item.setSampleCount(count);
        return item;
    }

    /**
     * 重新加载溶液列表
     * @param msgStr
     */
    public void reloadSolutions(String msgStr) {
        if (currentKey == null){
            throw  new TaskRunningException("Please call setCurrentKey first.");
        }
        reloadSolutions(currentKey, msgStr);
    }
    public void reloadSolutions(CacheKey cacheKey, String msgStr) {
        List<IcpSolutionItemArg> solutionItemArgs = parseSolutionList(msgStr);
        cacheMap.get(cacheKey).clear();
        cacheMap.get(cacheKey).addAll(solutionItemArgs);
    }

    public void reloadSolutionResults(String msgStr) {
        if (currentKey == null){
            throw  new TaskRunningException("Please call setCurrentKey first.");
        }
        reloadSolutionResults(currentKey, msgStr);
    }
    public void reloadSolutionResults(CacheKey cacheKey, String msgStr) {
        List<IcpSolutionResultItem> solutionResultItems = parseSolutionResultList(msgStr);

        for (IcpSolutionResultItem rsItem : solutionResultItems) {
            IcpSolutionItemArg cachedSolution = cacheMap.get(cacheKey).stream()
                    .filter(s -> s.getLabel().equals(rsItem.getLabel()))
                    .findFirst()
                    .orElse(null);
            if (cachedSolution != null) {
                List<IcpSolutionResultItem> rsList = cachedSolution.getResults();
                if (rsList == null){
                    rsList = new ArrayList<>();
                    cachedSolution.setResults(rsList);
                }
                Optional<IcpSolutionResultItem> existedRsItemOpt = rsList.stream().filter(rs->{
                    return StringUtils.equals(rs.getLabel(), rsItem.getLabel())
                            && StringUtils.equals(rs.getColumnHeader(), rsItem.getColumnHeader());
                }).findFirst();
                if (existedRsItemOpt.isPresent()){
                    existedRsItemOpt.get().copy(rsItem);
                }else if (StringUtils.equalsAny(rsItem.getLabel(), cachedSolution.getLabel())){
                    rsList.add(rsItem);
                }
            }
        }
    }

    /**
     * 解析消息，转换为List<StatusInfo>列表
     * @param msgStr 形如 [{"Type":"SOLUTION_DETAILS","Value":"INDEX: 0","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"LABEL: 空白","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"SEQUENCE_NAME: 空白","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"RACK_TUBE: S1:1","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"TUBE: 1","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"TYPE: BLANK","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"IS_SELECTED_FOR_MEASUREMENT: FALSE","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"WEIGHT: 1.000000","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"VOLUME: 1.000000","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DILUTION: 1.000000","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"LIMS_ID","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"CUSTOMER","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"REFERENCE","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"SITE","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DATE","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DESCRIPTION","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"BLOCK_ID: c6dbb34a-93f4-4aae-a0c5-f12dbd11f600","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"QC_ID: 00000000-0000-0000-0000-000000000000","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"QC_RATE_ID: 00000000-0000-0000-0000-000000000000","ID":5,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"INDEX: 1","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"LABEL: 标准 1","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"SEQUENCE_NAME: 标准 1","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"RACK_TUBE: S1:2","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"TUBE: 2","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"TYPE: STANDARD","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"IS_SELECTED_FOR_MEASUREMENT: FALSE","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"WEIGHT: 1.000000","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"VOLUME: 1.000000","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DILUTION: 1.000000","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"LIMS_ID","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"CUSTOMER","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"REFERENCE","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"SITE","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DATE","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DESCRIPTION","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"BLOCK_ID: c6dbb34a-93f4-4aae-a0c5-f12dbd11f600","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"QC_ID: 00000000-0000-0000-0000-000000000000","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"QC_RATE_ID: 00000000-0000-0000-0000-000000000000","ID":6,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"INDEX: 2","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"LABEL: 标准 2","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"SEQUENCE_NAME: 标准 2","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"RACK_TUBE: S1:3","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"TUBE: 3","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"TYPE: STANDARD","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"IS_SELECTED_FOR_MEASUREMENT: FALSE","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"WEIGHT: 1.000000","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"VOLUME: 1.000000","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DILUTION: 1.000000","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"LIMS_ID","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"CUSTOMER","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"REFERENCE","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"SITE","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DATE","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"DESCRIPTION","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"BLOCK_ID: c6dbb34a-93f4-4aae-a0c5-f12dbd11f600","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"QC_ID: 00000000-0000-0000-0000-000000000000","ID":7,"StrictMatch":false},{"Type":"SOLUTION_DETAILS","Value":"QC_RATE_ID: 00000000-0000-0000-0000-000000000000","ID":7,"StrictMatch":false}]
     * @return List<StatusInfo>列表
     */
    public static List<StatusInfo> parseStatusMsg(String msgStr) {
        List<StatusInfo> statusInfoList = new ArrayList<>();

        if (msgStr == null || msgStr.trim().isEmpty()) {
            return statusInfoList;
        }

        try {
            // 解析JSON数组
            List<Map<String, Object>> jsonList = JSON.parseObject(msgStr, List.class);

            for (Map<String, Object> jsonItem : jsonList) {
                StatusInfo statusInfo = new StatusInfo();

                // 映射字段，注意JSON中的字段名是大写的
                if (jsonItem.containsKey("Type")) {
                    statusInfo.setType(String.valueOf(jsonItem.get("Type")));
                }
                if (jsonItem.containsKey("Value")) {
                    statusInfo.setValue(String.valueOf(jsonItem.get("Value")));
                }
                if (jsonItem.containsKey("ID")) {
                    statusInfo.setId(String.valueOf(jsonItem.get("ID")));
                }

                statusInfoList.add(statusInfo);
            }
        } catch (Exception e) {
            // 如果解析失败，返回空列表
            statusInfoList.clear();
        }

        return statusInfoList;
    }

    public static List<IcpSolutionItemArg> parseSolutionList(String msgStr) {
        List<StatusInfo> statusInfoList = parseStatusMsg(msgStr);
        return parseSolutionList(statusInfoList);
    }

    public static List<IcpSolutionItemArg> parseSolutionList(List<StatusInfo> statusInfoList) {
        List<IcpSolutionItemArg> solutionList = new ArrayList<>();

        if (statusInfoList == null || statusInfoList.isEmpty()) {
            return solutionList;
        }

        // 按ID分组，每个ID对应一个溶液的所有属性
        Map<String, List<StatusInfo>> groupedByID = new HashMap<>();

        for (StatusInfo statusInfo : statusInfoList) {
            if (statusInfo.getId() != null) {
                groupedByID.computeIfAbsent(statusInfo.getId(), k -> new ArrayList<>()).add(statusInfo);
            }
        }

        // 为每个ID组创建一个IcpSolutionItemArg对象
        for (Map.Entry<String, List<StatusInfo>> entry : groupedByID.entrySet()) {
            IcpSolutionItemArg solution = new IcpSolutionItemArg();

            for (StatusInfo statusInfo : entry.getValue()) {
                String value = statusInfo.getValue();
                if (value != null) {
                    parseAndSetSolutionProperty(solution, value);
                }
            }
            if (solution.getIndex() != null) {
                solutionList.add(solution);
            }
        }

        // 按索引排序
        solutionList.sort((s1, s2) -> {
            if (s1.getIndex() == null && s2.getIndex() == null) return 0;
            if (s1.getIndex() == null) return 1;
            if (s2.getIndex() == null) return -1;
            return s1.getIndex().compareTo(s2.getIndex());
        });

        return solutionList;
    }

    /**
     * 解析单个属性值并设置到溶液对象中
     * @param solution 溶液对象
     * @param value 属性值字符串，格式如 "INDEX: 0", "LABEL: 空白" 等
     */
    private static void parseAndSetSolutionProperty(IcpSolutionItemArg solution, String value) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }

        String trimmedValue = value.trim();

        if (trimmedValue.startsWith("INDEX:")) {
            String indexStr = trimmedValue.substring(6).trim();
            try {
                solution.setIndex(Long.parseLong(indexStr));
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        } else if (trimmedValue.startsWith("LABEL:")) {
            solution.setLabel(trimmedValue.substring(6).trim());
        } else if (trimmedValue.startsWith("SEQUENCE_NAME:")) {
            solution.setSampleName(trimmedValue.substring(14).trim());
        } else if (trimmedValue.startsWith("WEIGHT:")) {
            solution.setWeight(trimmedValue.substring(7).trim());
        } else if (trimmedValue.startsWith("VOLUME:")) {
            solution.setVolume(trimmedValue.substring(7).trim());
        } else if (trimmedValue.startsWith("DILUTION:")) {
            solution.setDilution(trimmedValue.substring(9).trim());
        } else if (trimmedValue.startsWith("IS_SELECTED_FOR_MEASUREMENT:")) {
            solution.setMeasurementCheck(trimmedValue.substring(28).trim());
        } else if (trimmedValue.startsWith("TYPE:")) {
            solution.setType(trimmedValue.substring(5).trim());
        }
        // 其他属性如 TYPE, RACK_TUBE, TUBE, LIMS_ID, CUSTOMER, REFERENCE, SITE, DATE, DESCRIPTION, BLOCK_ID, QC_ID, QC_RATE_ID 等
        // 根据需要可以继续添加解析逻辑
    }

    public static List<IcpSolutionResultItem> parseSolutionResultList(String msgStr) {
        List<StatusInfo> statusInfoList = parseStatusMsg(msgStr);
        return parseSolutionResultList(statusInfoList);
    }
    /**
     * 消息格式形如：[{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 空白","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 0.90","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 0.00","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":439,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 空白","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: N (174.213 nm)","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 0.90","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 0.00","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":440,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 空白","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: K (766.491 nm)","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 0.90","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 0.00","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":441,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 1","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 899.83","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 1.00","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":442,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 1","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: N (174.213 nm)","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 900.23","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 1.00","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":443,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 1","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: K (766.491 nm)","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 900.01","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 1.00","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":444,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 2","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 1799.66","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 2.00","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":445,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 2","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: N (174.213 nm)","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 1800.45","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 2.00","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":446,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 2","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: K (766.491 nm)","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 1800.01","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 2.00","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":447,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 3","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 3599.32","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 4.00","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":448,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 3","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: N (174.213 nm)","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 3600.91","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 4.00","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":449,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 3","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: K (766.491 nm)","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 3600.02","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 4.00","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":450,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 4","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 8998.29","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 10.00","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":451,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 4","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: N (174.213 nm)","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 9002.27","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 10.00","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":452,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 标准 4","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: K (766.491 nm)","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 9000.05","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 10.00","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 不适用","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":453,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 样品 1","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 3383.15","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 26.24","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 11748701.94","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 26.24","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":454,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 样品 1","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: N (174.213 nm)","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 1830.18","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: > 100.00","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 6353011.48","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: > 100.00","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":455,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 样品 1","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: K (766.491 nm)","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 2260.43","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: > 100.00","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY: !","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 7847547.52","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: > 100.00","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION: !","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":456,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"LABEL: 样品 2","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"COLUMN_HEADER: P (213.618 nm)","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_INTENSITY: 7558.57","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_INTENSITY: 0.00","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_INTENSITY","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"MEASUREMENT_CONCENTRATION: 26250215.53","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"RSD_CONCENTRATION: 0.00","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"FLAGS_CONCENTRATION","ID":457,"StrictMatch":false},{"Type":"MEASUREMENT_CALCULATION","Value":"UNITS: ppm","ID":457,"StrictMatch":false}]
     * @param statusInfoList 解析消息后的列表
     * @return IcpSolutionResultItem对象封装列表
     */
    public static List<IcpSolutionResultItem> parseSolutionResultList(List<StatusInfo> statusInfoList) {
        List<IcpSolutionResultItem> resultList = new ArrayList<>();

        if (statusInfoList == null || statusInfoList.isEmpty()) {
            return resultList;
        }

        // 过滤出MEASUREMENT_CALCULATION类型的StatusInfo
        List<StatusInfo> measurementInfoList = statusInfoList.stream()
                .filter(info -> "MEASUREMENT_CALCULATION".equals(info.getType()))
                .collect(Collectors.toList());

        // 按ID分组，每个ID对应一个测量结果的所有属性
        Map<String, List<StatusInfo>> groupedByID = new HashMap<>();

        for (StatusInfo statusInfo : measurementInfoList) {
            if (statusInfo.getId() != null) {
                groupedByID.computeIfAbsent(statusInfo.getId(), k -> new ArrayList<>()).add(statusInfo);
            }
        }

        // 为每个ID组创建一个IcpSolutionResultItem对象
        for (Map.Entry<String, List<StatusInfo>> entry : groupedByID.entrySet()) {
            IcpSolutionResultItem resultItem = new IcpSolutionResultItem();

            for (StatusInfo statusInfo : entry.getValue()) {
                String value = statusInfo.getValue();
                if (value != null) {
                    parseAndSetResultProperty(resultItem, value);
                }
            }
            if (resultItem.getLabel() != null) {
                resultList.add(resultItem);
            }
        }

        // 按标签和列标题排序
        resultList.sort((r1, r2) -> {
            int labelCompare = compareStrings(r1.getLabel(), r2.getLabel());
            if (labelCompare != 0) {
                return labelCompare;
            }
            return compareStrings(r1.getColumnHeader(), r2.getColumnHeader());
        });

        return resultList;
    }

    /**
     * 解析单个测量结果属性值并设置到结果对象中
     * @param resultItem 测量结果对象
     * @param value 属性值字符串，格式如 "LABEL: 空白", "COLUMN_HEADER: P (213.618 nm)" 等
     */
    private static void parseAndSetResultProperty(IcpSolutionResultItem resultItem, String value) {
        if (value == null || value.trim().isEmpty()) {
            return;
        }

        String trimmedValue = value.trim();

        if (trimmedValue.startsWith("LABEL:")) {
            resultItem.setLabel(trimmedValue.substring(6).trim());
        } else if (trimmedValue.startsWith("COLUMN_HEADER:")) {
            resultItem.setColumnHeader(trimmedValue.substring(14).trim());
        } else if (trimmedValue.startsWith("MEASUREMENT_INTENSITY:")) {
            resultItem.setMeasurementIntensity(trimmedValue.substring(22).trim());
        } else if (trimmedValue.startsWith("RSD_INTENSITY:")) {
            resultItem.setRsdIntensity(trimmedValue.substring(14).trim());
        } else if (trimmedValue.startsWith("FLAGS_INTENSITY:")) {
            resultItem.setFlagsIntensity(trimmedValue.substring(16).trim());
        } else if (trimmedValue.startsWith("MEASUREMENT_CONCENTRATION:")) {
            resultItem.setMeasurementConcentration(trimmedValue.substring(26).trim());
        } else if (trimmedValue.startsWith("RSD_CONCENTRATION:")) {
            resultItem.setRsdConcentration(trimmedValue.substring(18).trim());
        } else if (trimmedValue.startsWith("FLAGS_CONCENTRATION:")) {
            resultItem.setFlagsConcentration(trimmedValue.substring(20).trim());
        } else if (trimmedValue.startsWith("UNITS:")) {
            resultItem.setUnits(trimmedValue.substring(6).trim());
        }
    }

    /**
     * 比较两个字符串，处理null值
     */
    private static int compareStrings(String s1, String s2) {
        if (s1 == null && s2 == null) return 0;
        if (s1 == null) return 1;
        if (s2 == null) return -1;
        return s1.compareTo(s2);
    }

    /**
     * 解析设备状态消息
     * @param msgStr 形如:[{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"ONLINE","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"PLASMA_STATUS: Off","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"PLASMA_CONTROL_STATE: CanIgnite","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"PUMP_SPEED: 0","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"SNOUT_BOOST: ON","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"POLY_BOOST: OFF","ID":3045},{"Type":"INSTRUMENT_STATE","StrictMatch":false,"Value":"READY","ID":3045}]
     * @return
     */
    public DeviceStatusInfo parseDeviceStatus(String msgStr) {
        List<StatusInfo> statusInfoList = parseStatusMsg(msgStr);
        DeviceStatusInfo deviceStatusInfo = new DeviceStatusInfo();
        for (StatusInfo statusInfo : statusInfoList) {
            if (statusInfo.getValue() != null) {
                String value = statusInfo.getValue().trim();
                if (value.startsWith("PLASMA_STATUS:")) {
                    deviceStatusInfo.setPlasmaState(value.substring(14).trim());
                } else if (value.startsWith("PUMP_SPEED:")) {
                    deviceStatusInfo.setBumpState(value.substring(11).trim());
                }
            }
        }
        return this.deviceStatusInfo = deviceStatusInfo;
    }

}
