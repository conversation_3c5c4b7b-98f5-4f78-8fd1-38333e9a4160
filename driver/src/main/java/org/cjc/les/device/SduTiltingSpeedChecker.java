/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.concurrent.atomic.AtomicInteger;

public class SduTiltingSpeedChecker {

    private final static int MAX_RESERVED_TIME = 5000; // ms

    private final static int MAX_RESERVED_LENGTH = 1000;

    private final static int SPEED_ADJUST_DURATION = 1000;


    protected static class SampleValue {
        private double value;
        private  long timestamp;

        SampleValue(double val) {
            this.timestamp = System.currentTimeMillis();
            this.value = val;
        }
    }

    /**
     * 保留5秒之内的数据，超过5秒自动丢弃
     * */
    private LinkedList<SampleValue> valueList = new LinkedList<>();

    private long lastSpeedAdjustTimestamp = System.currentTimeMillis();

    public boolean canAdjustSpeed() {
        return System.currentTimeMillis() - SPEED_ADJUST_DURATION >= lastSpeedAdjustTimestamp;
    }

    public void refreshLasSpeedAdjustTimestamp() {
        lastSpeedAdjustTimestamp = System.currentTimeMillis();
    }

    public void add(Double value) {
        valueList.addLast(new SampleValue(value));

        if (valueList.size() >= MAX_RESERVED_LENGTH) {
            valueList.removeFirst();
        }
        long curTm = System.currentTimeMillis();
        valueList.removeIf(val ->{
           return  curTm - val.timestamp > MAX_RESERVED_TIME;
        });

    }
    public double getAverageValue(){
        double total = 0.0;
        for (SampleValue val : valueList){
            total += val.value;
        }
        if (doubleEqual(total, 0.0)){
            return 0.0;
        }
        return total / (valueList.size()==0?1:valueList.size());
    }

    public double getAverageSpeed() {
        if (valueList.size() == 0) {
            return 0.0;
        }
        SampleValue firstSv = valueList.getFirst();
        SampleValue lastSv = valueList.getLast();
        if (lastSv.timestamp - firstSv.timestamp == 0) {
            return 0.0;
        }

        double averSpeed = (lastSv.value - firstSv.value) / (lastSv.timestamp / 1000 - firstSv.timestamp / 1000);// value/sec

        return averSpeed;
    }

    private static boolean doubleEqual(double d1, double d2) {
        if (d1 >= d2-0.001 && d1<=d2+0.001 ){
            return true;
        }
        return false;
    }


    public static void main(String[] args) throws InterruptedException {

        BigDecimal decimal = new BigDecimal(1);
        decimal.add(BigDecimal.valueOf(1));



        System.out.println("decimal = " + decimal.intValue());

        LinkedList<SampleValue> valueList = new LinkedList<>();
        valueList.addLast(new SampleValue(1.0));
        valueList.addLast(new SampleValue(1.0));
        valueList.addLast(new SampleValue(1.0));
        valueList.addLast(new SampleValue(1.0));
        valueList.removeIf(val ->{
            return doubleEqual(val.value, 1.0);
        });

        SduTiltingSpeedChecker checker = new SduTiltingSpeedChecker();
        checker.add(1.00);
        Thread.sleep(1000);
        checker.add(1.00);
        Thread.sleep(1000);
        checker.add(1.00);
        Thread.sleep(1000);
        checker.add(1.00);
        Thread.sleep(1000);
        checker.add(1.00);
        Thread.sleep(1000);
        checker.add(2.00);
        Thread.sleep(1000);
        checker.add(2.00);
        Thread.sleep(1000);
        Thread.sleep(10000);
        checker.add(2.00);


        System.out.println("average speed: " + checker.getAverageSpeed());

    }

}
