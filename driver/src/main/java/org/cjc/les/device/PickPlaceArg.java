/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.Data;
import org.cjc.les.annotation.Parameter;

/**
 * 抓取或者放置指令参数
 */
@Data
public class PickPlaceArg {
    /**
     * 基轴编号,AXIS(0)
     */
    private int ba = 0;
    /**
     * 加速度
     */
    private double acc = 760;
    /**
     * 移动速度,mmm/s
     */
    private double speed = 570;
    /**
     * VR(203)
     */
    private double speedFactor = 38;

    /**
     * Y轴自由移动点位
     */
    private double yfree = 390.0;
    /**
     * Z轴自由移动点位
     */
    private double zfree = 5.0;

    /**
     * X轴位置
     */
    @Parameter(name = "X轴位置")
    private double xpos;
    /**
     * Y轴位置
     */
    @Parameter(name = "Y轴位置")
    private double ypos;
    /**
     * Z轴位置
     */
    @Parameter(name = "Z轴位置")
    private double zpos;

    /**
     * 暂不使用，已废弃
     */
    private double vr310 = 0;
}
