/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.Data;
import org.cjc.les.annotation.Parameter;

@Data
public class IpConfigArg {
    @Parameter("IP地址")
    private String ipAddress;
    @Parameter("端口号")
    private int port;

    @Override
    public String toString() {
        return "IpConfigArg{" +
                "ipAddress='" + ipAddress + '\'' +
                ", port=" + port +
                '}';
    }
}
