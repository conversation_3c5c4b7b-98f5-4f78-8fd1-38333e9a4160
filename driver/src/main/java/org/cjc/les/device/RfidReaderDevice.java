/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.annotation.Variable;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.util.CommandUtil;
import org.cjc.les.driver.rfid.RfidReaderDriver;
import org.cjc.les.driver.socket.CommandLineTcpSocket;
import org.cjc.les.exception.TaskRunningException;

import java.io.IOException;
import java.util.function.Function;

@Log4j2
@org.cjc.les.annotation.Device("RFID扫描器")
public class RfidReaderDevice extends Device {

    private StringBuffer rfTagBuffer = new StringBuffer();

    private RfidReaderDriver driver = new RfidReaderDriver();

    private IpConfigArg config;

    public boolean doInit(@Parameter("IP配置") IpConfigArg config) {

        try {
            this.config = config;
            driver.init(config.getIpAddress(), config.getPort());
            driver.clear();
        } catch (IOException e) {
            log.error("Initialize RfidReaderDevice error, with config:{}, exception:{}",config,e.getMessage());
            return false;
        }

        return true;
    }


    @Command(name = "RF扫描器连接结束", type = CommandTypeEnum.FINALIZE)
    public void finalize() {
        try {
            driver.finalize();
        } catch (IOException e) {
            log.error("Finalize RfidReaderDevice error, with config:{}, exception:{}", this.config, e.getMessage(), e);
        }
    }


    /**
     * 接收RFID字符串
     * @param callbackFun 回调函数
     *
     */
    public void receive(Function<String, Boolean> callbackFun){
        try {
            driver.receive(callbackFun);
        } catch (IOException e) {
            log.error("receive error, config:{}, exception: {}", this.config, e.getMessage());
        }
    }


    @Variable(name = "RF_TAG", type = "String", tip = "RF扫描标签")
    @Command(value = "同步接收RFTAG",description = "该命令调用后会等待RFTAG消息到来时，会存入rfTagBuffer")
    public void syncReceive() {
        RfidReaderDevice.this.receive(rfTag -> {
            rfTagBuffer.delete(0,RfidReaderDevice.this.rfTagBuffer.length());
            rfTagBuffer.append(rfTag);
            TaskExecutorContext.setVar("RF_TAG", rfTag);
            return true;
        });
        try {
            driver.clear();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 异步接收RFTAG，并存入rfTagBuffer
     */
    @Variable(name = "CONFIRM_RF_TAG", type = "String", tip = "RF确认扫描标签")
    @Command(value = "异步接收RFTAG",description = "该命令调用后立即返回, 当有RFTAG消息到来时，会存入rfTagBuffer")
    public void asyncReceive() {
        TaskExecutorContext context = TaskExecutorContext.getContext();
        CommandUtil.submitToCommandListenerPool(new Runnable() {
            @Override
            public void run() {
                RfidReaderDevice.this.receive(rfTag -> {
                    RfidReaderDevice.this.rfTagBuffer.delete(0,RfidReaderDevice.this.rfTagBuffer.length());
                    RfidReaderDevice.this.rfTagBuffer.append(rfTag);
                    TaskExecutorContext.setVar("CONFIRM_RF_TAG", rfTag);
                    return true;
                });
                try {
                    driver.clear();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Command(value = "检查接收RFTAG",description = "用于分支判断是否继续下一步")
    public boolean checkReceivedTag() {
        while (!Thread.interrupted()) {
            log.info("checkReceivedTag: {}", rfTagBuffer);
            if (rfTagBuffer.length() == 0) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    throw new TaskRunningException("InterruptedException occurred");
                }
                continue;
            } else {
                return true;
            }
        }
        return false;
    }

}
