/*
 *  Copyright 2024-2025 Wuhan Annis Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import com.alibaba.fastjson2.TypeReference;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Device;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.driver.sdu.SduDriver;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.HttpClientUtil;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * RESTFUL通讯设备实现类
 */
@Log4j2
@Device(name = "RestFul协议通讯设备", type = DeviceTypeEnum.PERIPHERAL, description = "RestFul协议通讯通用设备,该设备的实例可以据此实现详细接口")
public class RestFulDevice {

    private String urlConfig;

    @Command(name = "初始化配置", type = CommandTypeEnum.INIT)
    public boolean init(@Parameter("URL") String urlConfig) {

        this.urlConfig = urlConfig;
        return true;
    }

    @Command(value="结束", type= CommandTypeEnum.FINALIZE)
    public void finalize() {
        urlConfig = null;
    }

    @Command(name = "POST方法提交请求", description = "POST方法提交请求")
    public CommandReturn post(@Parameter(name = "请求参数", inputComponent = "restful-req-map") RestFulReqArg arg) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);
        log.info("post inner: arg={}", arg);
        try {
            String url = getUrl(arg);
            Map httpRet = HttpClientUtil.httpPost(url, arg.getParamObj(), new TypeReference<Map>() {
            });
            ret.setResult(httpRet);
        }catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
        }
        return ret;
    }

    @Command(name = "GET方法提交请求", description = "GET方法提交请求")
    public CommandReturn get(@Parameter(name = "请求参数", inputComponent = "restful-req-map") RestFulReqArg arg) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);
        Map outMap = new HashMap();
        try {
            String url = getUrl(arg);
            outMap = HttpClientUtil.httpGet(url, arg.getParamObj(), new TypeReference<Map>() {
            });
            ret.setResult(outMap);
        } catch (Exception e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
        }

        return ret;
    }

    private String getUrl(RestFulReqArg arg) {
        String url = arg.getUrl();
        if (!(url.startsWith("http://") || url.startsWith("https://"))) {
            url = this.urlConfig + url;
        }
        return url;
    }

}
