/*
 *  Copyright 2024-2025 <PERSON>han Annis Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.*;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.helper.VariableHelper;
import org.cjc.les.core.service.dto.ResultItemDto;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.util.TaskCommandExecutorUtil;
import org.cjc.les.driver.socket.CommandLineTcpSocket;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.FileUtil;
import org.cjc.utils.HttpClientUtil;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;

/**
 * Agilent ICP-OES设备
 */
@Log4j2
@Device(name = "Agilent ICP-OES设备", type = DeviceTypeEnum.PERIPHERAL, description = "Agilent ICP-OES设备")
public class IcpAgilentDevice {

    @Data
    static class CommandArg {
        private String Command;
        private Map<String, Object> Parameters;
    }

    @Data
    static class CommandResponse {
        private boolean Success;
        private String Message;
        private Map<String, String> Data;
    }

    @Data
    static class StartProcedureArg {

        @Parameter("Worksheet路径")
        private String worksheetPath;

        @Parameter("复测")
        private boolean reDo;
    }

    private IcpAgilentDataManager dataManager;


    /**
     * TCP命令行驱动
     */
    private CommandLineTcpSocket driver = new CommandLineTcpSocket();

    @Command(name = "初始化连接", type = CommandTypeEnum.INIT)
    synchronized public boolean init(@Parameter("IP配置") IpConfigArg config) {
        try {
            if (!driver.isConnected()) {
                driver.connect(config.getIpAddress(), config.getPort());
            }

            try {
                CommandReturn ret = new CommandReturn();
                ret.setStatus(RunStatusEnum.SUCCESS);

                driver.sendCommand(buildCommand("init"), (respLine) -> {
                    //
                    CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                    ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                    ret.setErrorMsg(resp.getMessage());
                    ret.setResult(resp.getData());
                    return true;
                });
                if (RunStatusEnum.SUCCESS.equals(ret.getStatus())){
                    dataManager = new IcpAgilentDataManager();
                    return true;
                }else{
                    return false;
                }
            } catch (Throwable e) {
                log.error("Initialize IcpAgilentDevice error, with config:{}, exception:{}", config, e.getMessage(), e);
                finalize();
                return false;
            }
        } catch (IOException e) {
            log.error("Initialize IcpAgilentDevice error, with config:{}, exception:{}", config, e.getMessage(), e);
            finalize();
            return false;
        }

    }

    synchronized public boolean checkInitialized() {
        return driver.isConnected();
    }

    @Command(value="结束", type= CommandTypeEnum.FINALIZE)
    synchronized public void finalize() {
        try {
            driver.close();
            driver = new CommandLineTcpSocket();
        } catch (IOException e) {
            log.error("Finalize IcpAgilentDevice error, with config:{}, exception:{}", driver.getInetAddress(), e.getMessage(), e);
        }
    }

    @Command(name = "检测设备状态", type = CommandTypeEnum.STATUS, description = "检测ICP设备状态,CONNECTED, data: {plasma:ON, bump:ON}")
    synchronized public CommandReturn getStatus() {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        if (!driver.isConnected()){
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("初始化连接失败");
            return ret;
        }

        try {
            driver.sendCommand(buildCommand("status"), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                if (resp.isSuccess()) {
                    IcpAgilentDataManager.DeviceStatusInfo deviceStatusInfo = dataManager.parseDeviceStatus(resp.getData().get("status"));
                    ret.setResult(deviceStatusInfo);
                }
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }

    @Command(name = "等离子点火控制", type = CommandTypeEnum.NORMAL, description = "等离子点火控制,ON/OFF")
    synchronized public CommandReturn plasma(@Parameter(name="控制选项",candidateValues = {"ON","OFF"})String state) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            driver.sendCommand(buildCommand("plasma", state), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }

    @Command(name = "泵速控制", type = CommandTypeEnum.NORMAL, description = "泵速控制,OFF,0 - 80")
    synchronized public CommandReturn pump(@Parameter(name="泵速选项",candidateValues = {"OFF","20","80"})String state) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            driver.sendCommand(buildCommand("pump", state), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }
    @Command(name = "加载Worksheet", type = CommandTypeEnum.NORMAL, description = "加载Worksheet: 完整路径")
    synchronized public CommandReturn loadWorksheet(@Parameter(name="完整路径")String path) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("path", path);
            driver.sendCommand(buildCommand("worksheet_load", paramMap), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }

    // workload_new_from_template

    @Command(name = "从模板创建Worksheet", type = CommandTypeEnum.NORMAL, description = "从模板创建Worksheet: 完整路径")
    synchronized public CommandReturn newWorksheetFromTemplate(@Parameter(name="完整路径")String path) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            String fileName = FileUtil.getName(path);
            fileName = fileName.substring(0, fileName.indexOf("."));
            String fileEx = FileUtil.getExtensionName(path);
            // 把当前时间格式化为"yyyyMMddHHmmss"格式
            DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String formattedDate = dateFormat.format(new Date());
            String saveAs = fileName + "_"+ formattedDate + "." + fileEx;
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("path", path);
            paramMap.put("fileName", saveAs);

            driver.sendCommand(buildCommand("workload_new_from_template", paramMap), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }

    @Command(name = "修改溶液项", type = CommandTypeEnum.NORMAL, description = "修改溶液项: 按照溶液在列表中的索引顺序(从0开始)选择")
    synchronized public CommandReturn modifySolution(@Parameter(name="参数")IcpSolutionItemArg item) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            Map<String, Object> paramMap = item.toMap();
            driver.sendCommand(buildCommand("modify_solution", paramMap), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }


    @Command(name = "查询所有溶液详情", type = CommandTypeEnum.NORMAL, description = "查询所有溶液详情: 显示当前Worksheet中的溶液列表详情及执行结果")
    synchronized public CommandReturn queryAllSolutions() {
        return queryAllSolutions(null);
    }

    private CommandReturn queryAllSolutions(Function<String, Boolean> respHandler) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            driver.sendCommand(buildCommand("query_all_solutions"), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                if (respHandler!=null && ret.getStatus().equals(RunStatusEnum.SUCCESS)){
                    return respHandler.apply(respLine);
                }
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }

    @Command(name = "查询所有溶液检测结果详情", type = CommandTypeEnum.NORMAL, description = "查询所有溶液检测结果详情: 显示当前Worksheet中的溶液列表详情及执行结果")
    synchronized public CommandReturn queryAllSolutionResults() {
        return queryAllSolutionResults(null);
    }

    private CommandReturn queryAllSolutionResults(Function<String, Boolean> respHandler) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            driver.sendCommand(buildCommand("query_all_results"), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                if (respHandler!=null && ret.getStatus().equals(RunStatusEnum.SUCCESS)){
                    return respHandler.apply(respLine);
                }
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }


    @Command(name = "开启检测", type = CommandTypeEnum.NORMAL, description = "开启检测,START/PAUSE/CONTINUE/STOP")
    synchronized public CommandReturn start(@Parameter(name="开启选项",candidateValues = {"START","PAUSE", "CONTINUE","STOP"})String state) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);

        try {
            driver.sendCommand(buildCommand("start", state), (respLine) -> {
                //
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                ret.setStatus(!resp.isSuccess() ? RunStatusEnum.FAILED : RunStatusEnum.SUCCESS);
                ret.setErrorCode(resp.isSuccess() ? ErrorCodeEnum.SUCCESS:ErrorCodeEnum.ERROR);
                ret.setErrorMsg(resp.getMessage());
                ret.setResult(resp.getData());
                if (resp.isSuccess()) {
                    if (resp.isSuccess()) {
                        dataManager.reloadSolutionResults(resp.getData().get("allReceivedStatus"));
                    }
                }
                return true;
            });
        } catch (Throwable e) {
            log.error("error: " + e.getMessage(), e);
            ret.setStatus(RunStatusEnum.FAILED);
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            ret.setErrorMsg("连接失败");
            finalize();
        }
        return ret;
    }

    @Variables({
            @Variable( name = "ICP_ENTRY_VALVE_CHANNEL", type = "String", tip = "ICP进液管路选取索引", scope = "SYS"),
            @Variable( name = "ICP_SAMPLE_ITEM", type = "Object", tip = "ICP测样结果", scope = "SYS"),
            @Variable( name = "ICP_SAMPLE_ITEM_REDO", type = "Object", tip = "ICP复测样结果", scope = "SYS")})
    @Command(name = "流程检测", type = CommandTypeEnum.NORMAL, description = "流程检测,用于流程编排中，封装完整的检测步骤")
    synchronized public CommandReturn startProcedure(@Parameter(name="参数")StartProcedureArg arg) {
        CommandReturn ret = new CommandReturn();
        ret.setStatus(RunStatusEnum.SUCCESS);
        Long methodId = 0L;
        Long taskCommandId = 0L;
        Long taskMethodId = 0L;
        Long taskId = 0L;
        String sampleName = "TestSample";
        if ( TaskExecutorContext.getTaskMethod() != null) {
            methodId = TaskExecutorContext.getTaskMethod().getMethodId();
            taskMethodId = TaskExecutorContext.getTaskMethod().getId();

        }
        if (TaskExecutorContext.getTaskCommand() != null) {
            taskCommandId = TaskExecutorContext.getTaskCommand().getId();
            sampleName = TaskExecutorContext.getTask().getSample().getName()+"_"+taskCommandId;
            taskId = TaskExecutorContext.getTask().getId();
        }

        // 初始化加载工作表
        String worksheetPath = arg.getWorksheetPath();
        IcpAgilentDataManager.CacheKey cacheKey = dataManager.buildCacheKey(methodId, worksheetPath);
        if (!cacheKey.equals(dataManager.getCurrentKey())){
            dataManager.setCurrentKey(cacheKey);
            CommandReturn retNewWorksheet = newWorksheetFromTemplate(worksheetPath);
            if (RunStatusEnum.FAILED.equals(retNewWorksheet.getStatus())){
                return retNewWorksheet;
            }
            queryAllSolutions((respLine) -> {
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                if (resp.isSuccess()) {
                    dataManager.reloadSolutions(resp.getData().get("status"));
                }
                return true;
            });
            queryAllSolutionResults((respLine) -> {
                CommandResponse resp = JSON.parseObject(respLine, CommandResponse.class);
                if (resp.isSuccess()) {
                    dataManager.reloadSolutionResults(resp.getData().get("status"));
                }
                return true;
            });
        }

        // 现有溶液是否可被执行当前样品
        boolean isSampleSelect = false;
        for ( IcpSolutionItemArg item : dataManager.getCurrentSolutionItems() ){
            if (item.getExecutedByTaskCommandId() == null){
                // 修改溶液选中测量
                item.setMeasurementCheck("True");
                if (StringUtils.equalsAny("SAMPLE",item.getType())){
                    item.setSampleName(sampleName);
                    item.setLabel(sampleName);
                    isSampleSelect = true;
                }
                modifySolution(item);

                // 确保等离子开启
                makesurePlasmaOn(1000);

                // 选择进样阀
                TaskExecutorContext.setVar("ICP_ENTRY_VALVE_CHANNEL", item.getIndex());
                // 开启对应溶液的进样阀
                TaskCommandExecutorUtil.executeByOptionCode("START_ICP_ENTRY");

                // 开启检测并等待完成
                CommandReturn retStart = start("START");
                if ( RunStatusEnum.SUCCESS.equals( retStart.getStatus()) ){
                    item.setExecutedByTaskCommandId(taskCommandId);
                    item.setExecutedByTaskMethodId(taskMethodId);
                    item.setExecutedByTaskId(taskId);
                }else{
                    return retStart;
                }

                TaskCommandExecutorUtil.executeByOptionCode("STOP_ICP_ENTRY");

            }
        };

        if (!isSampleSelect){
            IcpSolutionItemArg item = dataManager.createNewSolutionItem();
            // 修改溶液选中测量
            item.setMeasurementCheck("True");
            if (StringUtils.equalsAny("SAMPLE",item.getType())){
                item.setSampleName(sampleName);
                item.setLabel(sampleName);
                isSampleSelect = true;
            }
            CommandReturn retModifySolution = modifySolution(item);
            if (!RunStatusEnum.SUCCESS.equals( retModifySolution.getStatus())){
                return retModifySolution;
            }
            // 确保等离子开启
            makesurePlasmaOn(1000);

            // 选择进样阀
            TaskExecutorContext.setVar("ICP_ENTRY_VALVE_CHANNEL", item.getIndex());
            // 开启对应溶液的进样阀
            TaskCommandExecutorUtil.executeByOptionCode("START_ICP_ENTRY");

            // 开启检测并等待完成
            CommandReturn retStart = start("START");
            if ( RunStatusEnum.SUCCESS.equals( retStart.getStatus()) ){
                item.setExecutedByTaskCommandId(taskCommandId);
                item.setExecutedByTaskMethodId(taskMethodId);
                item.setExecutedByTaskId(taskId);
            }else{
                return retStart;
            }

            TaskCommandExecutorUtil.executeByOptionCode("STOP_ICP_ENTRY");
        }

        String varName = arg.isReDo()?"ICP_SAMPLE_ITEM_REDO":"ICP_SAMPLE_ITEM";
        Optional<ResultItemDto> rsItemOpt = dataManager.getLastResultByTaskCommandId(taskCommandId);
        rsItemOpt.ifPresent(resultItemDto -> VariableHelper.addVar(varName, resultItemDto));
        return ret;
    }

    private void makesurePlasmaOn(long timeout){
        if (!StringUtils.equalsIgnoreCase("Lit",dataManager.getDeviceStatusInfo().getPlasmaState())){
            plasma("ON");
            getStatus();
            try {
                Thread.sleep(timeout);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private String buildCommand(String cmdName) {
        return buildCommand(cmdName, null);
    }
    private String buildCommand(String cmdName, Object parameter) {
        CommandArg arg = new CommandArg();
        arg.setCommand(cmdName);
        if (parameter!=null){
            if (parameter instanceof Map){
                arg.setParameters((Map)parameter);
            }else {
                Map<String, Object> parmMap = new HashMap<>();
                parmMap.put("data", parameter);
                arg.setParameters(parmMap);
            }
        }
        return JSON.toJSONString(arg);
    }



}
