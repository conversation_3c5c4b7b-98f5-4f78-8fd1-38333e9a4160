/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.driver.sdu.SduDriver;
import org.cjc.les.exception.TaskRunningException;

import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

@org.cjc.les.annotation.Device("固体称量倒样装置SDU")
@Log4j2
public class SduDevice extends Device {
    private SduDriver driver = new SduDriver();

    private final static double WEIGHING_OFFSET = 0.1;

    public boolean doInit(@Parameter("IP配置") IpConfigArg config) {

        try {
            driver.init(config.getIpAddress(), config.getPort());
        } catch (IOException e) {
            log.error("Initialize SduDevice error, with config:{}, exception:{}",config,e.getMessage(), e);
            return false;
        }

        return true;
    }

    @Command("移动相对位置")
    public void move(@Parameter("相对位置") Integer dis ){
        try {
            driver.move(dis);
        } catch (IOException e) {
            log.error("move error, exception:{}",e.getMessage(), e);
        }
    }

    /**
     * 旋转开启
     */
    @Command("旋转开启")
    public void turnOn(){
        try {
            driver.turnOn();
        } catch (IOException e) {
            log.error("turnOn error, exception:{}",e.getMessage(), e);
        }
    }
    /**
     * 旋转停止
     */
    @Command("旋转停止")
    public void turnOff(){
        try {
            driver.turnOff();
        } catch (IOException e) {
            log.error("turnOff error, exception:{}",e.getMessage(), e);
        }
    }

    /**
     * 倾斜开启
     */
    @Command("倾斜开启")
    public void tiltEnable(){
        try {
            driver.enable();
        } catch (IOException e) {
            log.error("enable error, exception:{}",e.getMessage(), e);
        }
    }
    /**
     * HOME
     */
    @Command("返回首页")
    public void home(){
        try {
            driver.home();
        } catch (IOException e) {
            log.error("home error, exception:{}",e.getMessage(), e);
        }
    }

    private class LimitArray {
        private int maxLength = 5;
        private LinkedList<Double> valueList = new LinkedList<>();

        public void add(Double value) {
            if (valueList.size()==0){
                valueList.addLast(value);
                return;
            }
            if (valueList.size() >= maxLength) {
                valueList.removeFirst();
            }
            double vLast = valueList.getLast();
            if (value.doubleValue()> (vLast+0.00001)){
                valueList.clear();
            }
            valueList.addLast(value);
        }

        public double getAverage() {

            if (valueList.size() == 0) {
                return 0;
            }

            double sum = 0;
            for (double num : valueList) {
                sum += num;
            }

            return sum / valueList.size();
        }

        /**
         * 检查是否所有都相同
         * @return
         */
        public boolean checkAllInSame(){
            if (valueList.size()<maxLength){
                return false;
            }
            Iterator<Double> itr = valueList.iterator();
            boolean allEq = true;
            double first = itr.next();
            while(itr.hasNext()){
                double the = itr.next();
                if (!doubleEqual(first, the)){
                    allEq = false;
                    break;
                }
            }

            if (allEq){
                valueList.clear();
            }
            return allEq;
        }

    }

    private boolean doubleEqual(double d1, double d2) {
        if (d1 >= d2-0.001 && d1<=d2+0.001 ){
            return true;
        }
        return false;
    }
    /**
     * 倾倒开始
     */
    @Command(value = "倾倒开始", description = "倾倒开始，直到计量数字达到预设")
    public void tilting() {
        WeighingArg arg = (WeighingArg) TaskExecutorContext.getVar("WEIGHING_ARG");
        //double lastValue = arg.getActualValue();
        double lastActualValue = arg.getActualValue();

        double firstSpeedLimit = 0.2; // g/sec
        double lastSpeedLimit = 0.1; // g/sec
        double speedProgressRatio = 0.7;

        //double maxSpeed = 0.100;// UNIT: g/sec
        //double limitSpeedRange = 2.0; // 限速重量从最后多少克开始

        long sleepTime = 250; // millisecond
        double curSpeed = 0.0;

      //  LimitArray lastValues = new LimitArray();
        SduTiltingSpeedChecker speedChecker = new SduTiltingSpeedChecker();
        while (!Thread.interrupted()) {
            arg = (WeighingArg) TaskExecutorContext.getVar("WEIGHING_ARG");
            log.info("arg: {}", arg);
            if (arg.isDone()) {
                try {
                    // 回正一点儿，防止样品继续洒落
                    driver.turnOff();
                    driver.move(-35);
                } catch (IOException e) {
                    log.error("driver.move error, exception: {}", e.getMessage(), e);
                }
                break;
            }

            speedChecker.add(arg.getActualValue());

            double curSpeedProgressRatio = arg.getActualValue() / arg.getExpectValue();

            if (curSpeedProgressRatio <= speedProgressRatio &&  speedChecker.canAdjustSpeed() ){
                 if (speedChecker.getAverageSpeed()<firstSpeedLimit){
                     try {

                         int dis = (int) (20 * (1 - (arg.getActualValue()) / (arg.getExpectValue() - WEIGHING_OFFSET)));
                         if (dis < 10) {
                             dis = 10; // 限定最小值5，否则进度太慢
                         }
                         driver.move(dis);
                         speedChecker.refreshLasSpeedAdjustTimestamp();

                     } catch (IOException e) {
                         log.error("driver.move error, exception: {}", e.getMessage(), e);
                     }
                 }
                if (speedChecker.getAverageSpeed()>firstSpeedLimit+0.05){
                    try {

                        driver.move(-35);
                        speedChecker.refreshLasSpeedAdjustTimestamp();
                    } catch (IOException e) {
                        log.error("driver.move error, exception: {}", e.getMessage(), e);
                    }
                }
            } else if (curSpeedProgressRatio > speedProgressRatio &&  speedChecker.canAdjustSpeed() ){
                if (speedChecker.getAverageSpeed()<lastSpeedLimit){
                    try {

                        int dis = (int) (20 * (1 - (arg.getActualValue()) / (arg.getExpectValue() - WEIGHING_OFFSET)));
                        if (dis < 10) {
                            dis = 10; // 限定最小值5，否则进度太慢
                        }
                        driver.move(dis);
                        speedChecker.refreshLasSpeedAdjustTimestamp();
                    } catch (IOException e) {
                        log.error("driver.move error, exception: {}", e.getMessage(), e);
                    }
                }
                if (speedChecker.getAverageSpeed()>lastSpeedLimit+0.05){
                    try {
                        driver.move(-35);
                        speedChecker.refreshLasSpeedAdjustTimestamp();
                    } catch (IOException e) {
                        log.error("driver.move error, exception: {}", e.getMessage(), e);
                    }
                }
            }


            // 等待下一次查询
            try {
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
                throw new TaskRunningException("InterruptedException occurred");
            }

        }
    }
    /*
    public void tilting() {
        WeighingArg arg = (WeighingArg) TaskExecutorContext.getContext().getVariableMap().get("WEIGHING_ARG");
        //double lastValue = arg.getActualValue();
        double lastActualValue = arg.getActualValue();
        double maxSpeed = 0.100;// UNIT: g/sec
        double limitSpeedRange = 2.0; // 限速重量从最后多少克开始
        long sleepTime = 250;// millisecond
        double curSpeed = 0.0;
        LimitArray lastValues = new LimitArray();
        while (!Thread.interrupted()) {
            arg = (WeighingArg) TaskExecutorContext.getContext().getVariableMap().get("WEIGHING_ARG");
            log.info("arg: {}", arg);
            if (arg.isDone()) {
                try {
                    // 回正一点儿，防止样品继续洒落
                    driver.turnOff();
                    driver.move(-35);
                } catch (IOException e) {
                    log.error("driver.move error, exception: {}", e.getMessage(), e);
                }
                break;
            }

            lastValues.add(arg.getActualValue());
           // lastActualValue = lastValues.getAverage();

            double deta = Math.abs(arg.getActualValue() - lastActualValue);
            // 最后1克计量，控制速度不超过maxSpeed
            if (doubleEqual(deta, 0.00f)){
                curSpeed = 0.0;
            }else {
                curSpeed = deta / ((float)sleepTime / 1000);
            }
            if ((arg.getExpectValue() - arg.getActualValue() < limitSpeedRange)
                    && (curSpeed > maxSpeed)) {
                try {
                    driver.move(-35);
                } catch (IOException e) {
                    log.error("driver.move error, exception: {}", e.getMessage(), e);
                }
                lastActualValue = arg.getActualValue();
                log.info("Speed limit: curSpeed={}, maxSpeed={}， limitSpeedRange={}", curSpeed, maxSpeed, limitSpeedRange);

                // 等待下一次查询
                try {
                    Thread.sleep(sleepTime);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    throw new TaskRunningException("InterruptedException occurred");
                }

                continue;
            }

            if (Math.abs(deta) < arg.getPrecise()) {
                // 两次计量结果无变化
                try {
                    // 出现多次无变化值才继续加速，否则保持位置不变
                    if (arg.getActualValue()<=0.001 || ((arg.getActualValue()>0.001) && lastValues.checkAllInSame())) {
                        int dis = (int) (20 * (1 - (arg.getActualValue()) / (arg.getExpectValue() - WEIGHING_OFFSET)));
                        if (dis < 10) {
                            dis = 10; // 限定最小值5，否则进度太慢
                        }
                        driver.move(dis);
                    }
                } catch (IOException e) {
                    log.error("driver.move error, exception: {}", e.getMessage(), e);
                }
            } else if (Math.abs(deta) >= arg.getPrecise()+WEIGHING_OFFSET) {
                // 两次计量结果变化超过允许精度
                try {
                    driver.move(-35);
                } catch (IOException e) {
                    log.error("driver.move error, exception: {}", e.getMessage(), e);
                }
            } else {
                // 测量结果有变化，保持当前位置不变
            }
           lastActualValue = arg.getActualValue();

            // 等待下一次查询
            try {
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
                throw new TaskRunningException("InterruptedException occurred");
            }

        }
    }
     */

    /**
     * 倾倒指定位置
     * @param value
     */
    @Command(value="倾倒指定位置")
    public void tiltAbsolute(@Parameter("位置值") Integer value){
        try {
            driver.absMove(value);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    @Command(value="结束", type= CommandTypeEnum.FINALIZE)
    public void finalize() {
        try {
            driver.finalize();
            driver = new SduDriver();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
