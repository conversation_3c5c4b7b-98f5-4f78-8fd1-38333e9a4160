/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.vo.CommandReturn;
import org.cjc.les.driver.plc.PlcDriver;

@Log4j2
@org.cjc.les.annotation.Device(value = "Jetter PLC 控制器",type = DeviceTypeEnum.CONTROL)
public class JetterPlcDevice extends Device {

    private PlcDriver driver = new PlcDriver();

    public boolean doInit(@Parameter("连接配置") String config) {
        if (StringUtils.isEmpty(config)) {
            log.error("Argument config is null");
            return false;
        }
        return driver.init(config);
    }

    public boolean checkConnection(){
        boolean bConn = driver.getConnectionStatus();
        if (!bConn){
            driver.finalize();
            return false;
        }
        return true;
    }

    @Command(value = "设置IO",description = "设置类型")
    public CommandReturn<Boolean> setBool(@Parameter(name = "IO设置参数", inputComponent = "position-io-map")IoSettingArg arg){
        return setBool(arg.getIBoolType(), arg.getBoolNumber(), arg.isBoolValue(),true);
    }

    @Command(value = "设置OUTPUT",description = "设置OUT端口，无需等待复位标识")
    public CommandReturn<Boolean> setOutput(@Parameter(name = "IO设置参数", inputComponent = "position-io-map")IoSettingArg arg){
        return setBool(1, arg.getBoolNumber(), arg.isBoolValue(), false);
    }

    /**
     * 设置布尔值
     * @param iBoolType 设置类型
     *                  BT_OUT   1  (output)
     *                  BT_FLAG   2  (flag)
     * @param boolNumber 类型编号
     * @param boolValue true/false
     * @param checkResetFlag 是否等待复位
     * @return 设置成功为true,否则为false
     */
    public CommandReturn<Boolean> setBool(int iBoolType, int boolNumber, boolean boolValue, boolean checkResetFlag)
    {
        // 模拟测试固定使用端口100000101
        boolNumber = this.isEnableMock()?100000101:boolNumber;
        int ret = driver.setBool(iBoolType, boolNumber, boolValue);
        CommandReturn<Boolean> retObj = new CommandReturn<>();
        retObj.setResult(ret==1?true:false);
        if (ret!=1){
            retObj.setErrorCode(ErrorCodeEnum.ERROR);
            retObj.setErrorMsg("ErrorCode;"+ret);
        }else{
            // 等待复位
            if (boolValue && checkResetFlag) {

                // 模拟仿真模式下，模拟延迟2s.
                if (this.isEnableMock()) {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                while (true) {
                    int retBack = driver.getBool(iBoolType, boolNumber, 0);
                    if (retBack == 0) {
                        //复位成功
                        log.info("Reset boolNumber: {} setValue={}, retBack={}", boolNumber, boolValue, retBack);
                        break;
                    } else {
                        // 继续等待
                        log.debug("Waiting for reset boolNumber: {} setValue={}, retBack={}", boolNumber, boolValue, retBack);
                    }
                }
            }
        }
        return retObj;
    }

    /**
     * 获取布尔值
     * @param iBoolType 获取类型
     *      BT_IN   0  (input)
     *      BT_OUT   1  (output)
     *      BT_FLAG   2  (flag)
     *      BT_REGBIT  3  (register bit)
     * @param boolNumber 类型编号
     * @param registerBit 当类型为register3时有效，取值位数
     * @return 返回实际值true/false
     */
    public CommandReturn<Boolean> getBool(int iBoolType,  int boolNumber, int registerBit) {
        // 模拟测试固定使用端口100000101
        boolNumber = this.isEnableMock()?100000101:boolNumber;
        int ret =  driver.getBool(iBoolType, boolNumber, registerBit);
        CommandReturn<Boolean> retObj = new CommandReturn<>();
        retObj.setResult(ret==1?true:false);
        if (ret<0){
            retObj.setErrorCode(ErrorCodeEnum.ERROR);
            retObj.setErrorMsg("ErrorCode;"+ret);
        }
        return retObj;
    }

    /**
     *
     * @param arg
     * @return
     */
    @Command("等待IN信号")
    public boolean waitIn(WaitInArg arg) {
        return waitIn(arg.getInNumber(),arg.isExpectValue(),arg.getTimeout());
    }

    /**
     * 等待IO信号为期望的值
     * @param inNumber IN编号
     * @param expectValue true高电平, false低电平
     * @param timeout 超时时间， 单位Sec.
     * @return
     */
    public boolean waitIn(int inNumber, boolean expectValue, int timeout) {
        int iExpectValue = expectValue?1:0;
        while(!Thread.interrupted()){

            // 模拟测试固定使用端口100000101
            inNumber = this.isEnableMock()?100000101:inNumber;

            int ret = driver.getBool(PlcDriver.BT_IN, inNumber, 0);
            if ( ret == iExpectValue){
                return true;
            }else if (ret < 0){
                log.error("IN getBool({}) error, ret={}", inNumber, ret );
            }

            try {
                Thread.sleep(1000);

            } catch (InterruptedException e) {
                log.error("InterruptedException:{}", e.getMessage());
                throw new TaskRunningException("InterruptedException occurred");

            }
        }
        return false;
    }

    @Override
    public void doFinalize() {
        // PLC开机一直链接，自动重连，不需要释放资源
    //    driver.finalize();
    }

    @Override
    public boolean isEnableMock() {
        if (TaskExecutorContext.getRealDeviceInstance() != null) {
            return TaskExecutorContext.getRealDeviceInstance().isEnableMock();
        }else{
            return super.isEnableMock();
        }
    }
}
