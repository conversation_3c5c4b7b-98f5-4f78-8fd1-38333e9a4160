/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.Data;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.ErrorCodeEnum;

@Data
public class WeighingArg {
    /**
     * 期望重量值
     */
    @Parameter(name="期望重量值")
    private double expectValue;
    /**
     * 实际重量值
     */
    private double actualValue;
    /**
     * 重量单位
     */
    @Parameter(name="重量单位")
    private String unit = "g";
    /**
     * 误差精度
     */
    @Parameter(name="误差精度")
    private double precise = 0.001;

    /**
     * 称量是否完成
     */
    private boolean done = false;

    /**
     * 称量完成后的状态是否正确,状态码
     */
    private ErrorCodeEnum errorCode;

    /**
     * 错误详情
     */
    private String errorMsg;
}
