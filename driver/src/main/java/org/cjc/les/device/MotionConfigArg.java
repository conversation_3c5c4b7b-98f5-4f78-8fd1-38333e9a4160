/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.device;

import lombok.Data;
import org.cjc.les.annotation.Parameter;

/**
 * 运动控制器初始化配置
 */
@Data
public class MotionConfigArg {
    /**
     * 控制器连接串
     */
    @Parameter("控制器连接")
    private String connect;
    /**
     * 基轴编号,AXIS(0)
     */
    @Parameter(name="基轴编号", defaultValue = "0")
    private int ba = 0;
    /**
     * 加速度
     */
    @Parameter(name="加速度", defaultValue = "760")
    private double acc = 760;
    /**
     * 移动速度,mmm/s
     */
    @Parameter(name="移动速度", defaultValue = "570")
    private double speed = 570;

    /**
     * 最大移动速度,mm/s, 可以配合速度控制因子改变当前速度
     */
    @Parameter(name="最大移动速度", defaultValue = "1500")
    private double maxSpeed = 1500;
    /**
     * VR(203)
     */
    @Parameter(name="速度控制因子", defaultValue = "38")
    private double speedFactor = 38;

}
