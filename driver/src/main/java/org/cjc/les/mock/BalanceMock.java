/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mock;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.driver.serialport.SerialPort;
import org.cjc.les.exception.TaskRunningException;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

@Log4j2
public class BalanceMock {

    private SerialPort sp = new SerialPort();

    private static ExecutorService executorService = Executors.newFixedThreadPool(4);

    private Socket sock;

    private List<BigDecimal> ssValueList = new ArrayList<>();

    public static void main(String[] args) throws IOException {
        System.out.println("Begin...");
     //   System.out.println("pwd="+System.getProperty("user.dir"));
      //  System.out.println("java.library.path="+System.getProperty("java.library.path"));
        int port = 4707;
        if (args!=null && args.length>0){
            String portStr = args[0];
            String[] portArr = portStr.split("=");
            port = Integer.parseInt(portArr[1]);

        }

/*

        while(true) {
            System.out.println("Listening...");
            mock.listen();
        }
*/

        ServerSocket serverSocket = new ServerSocket(port);

        while (true) {
            try {
                final Socket sock = serverSocket.accept();
                BalanceMock mock = new BalanceMock();
                mock.sock = sock;
                mock.input(sock);
            }catch(Exception e){
                e.printStackTrace();
            }
        }


        // System.out.println("End.");
    }

    private void input(Socket sock) {

        executorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    InputStreamReader isr = new InputStreamReader(sock.getInputStream());
                    BufferedReader br = new BufferedReader(isr);
                    String cmdLine = null;

                    do {
                        cmdLine = br.readLine();
                        System.out.println("Received cmdLine: " + cmdLine);
                        if (cmdLine == null){
                            break;
                        }
                        handleLine(cmdLine);
                        // String out = "Done " + cmdLine + " Command\r\n";
                        //  sock.getOutputStream().write(out.getBytes());
                        //  System.out.println("Response: " + out);
                    }
                    while (true);
                } catch (Exception e) {
                    e.printStackTrace();

                } finally {
                    try {
                        sock.close();
                    } catch (IOException ioException) {
                        ioException.printStackTrace();
                    }
                }
            }
        });


    }

    private void handleLine(String lineStr) {
        log.info("Receive: "+lineStr);

        if (StringUtils.equals(lineStr,"@")){
            abort();
        }else{
            int sep = lineStr.indexOf(' ')==-1?lineStr.length():lineStr.indexOf(' ');
            String cmd = lineStr.substring(0, sep);
            try {
                Method mth = BalanceMock.class.getDeclaredMethod(cmd, String.class);
                mth.setAccessible(true);
                mth.invoke(this, lineStr);
            } catch (NoSuchMethodException e) {
                log.error("No such method: "+ e.getMessage());
                sendCmd("No found such cmd:"+cmd);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
        }

    }

//
//    private void listen(){
//        sp.open("COM2",9600, (char)0,(char)8,(char)1);
//
//        streamResponse((lineStr)->{
//            log.info("Receive: "+lineStr);
//
//            if (StringUtils.equals(lineStr,"@")){
//                abort();
//            }else{
//                int sep = lineStr.indexOf(' ')==-1?lineStr.length():lineStr.indexOf(' ');
//                String cmd = lineStr.substring(0, sep);
//                try {
//                    Method mth = BalanceMock.class.getDeclaredMethod(cmd, String.class);
//                    mth.setAccessible(true);
//                    mth.invoke(this, lineStr);
//                } catch (NoSuchMethodException e) {
//                    log.error("No such method: "+ e.getMessage());
//                    sendCmd("No found such cmd:"+cmd);
//                } catch (IllegalAccessException e) {
//                    e.printStackTrace();
//                } catch (InvocationTargetException e) {
//                    e.printStackTrace();
//                }
//            }
//
//
//            return false;
//        });
//
//        sp.close();
//    }

    private void abort(){
        ssValueList.clear();
        sendCmd("I4 A \"C0001\"");
    }

    private void C(String cmdLine){
        ssValueList.clear();
        sendCmd("C B");
        sendCmd("C A");
    }

    private void HA07(String cmdLine){
        sendCmd("HA07 A");
    }

    private void HA09(String cmdLine){
        sendCmd("HA09 A");
    }

    private void HA65(String cmdLine){
        sendCmd("HA65 A");
    }

    private void Z(String cmdLine){
        sendCmd("Z A");
    }

    private void SIR(String cmdLine){
        for (int i=0; i< 10; i++) {
            sendCmd("S S 0.000 g");
            sleep(1000);
        }
        for (int i=0; i< 5; i++) {
            sendCmd("S D "+(i+1)+".000 g");
            sleep(1000);
        }

        for (int i=0; i< 5; i++) {
            sendCmd("S S 4.950 g");
            sleep(1000);
        }
    }

    private void S(String cmdLine){
        BigDecimal v = new BigDecimal("5.0");
        if (ssValueList.size()==0){
            v = new BigDecimal("4.950");
            ssValueList.add(v);
        }else if (ssValueList.size()==1){
            v = new BigDecimal("492.239");
            ssValueList.add(v);
        }

        sendCmd("S S "+v+" g");
    }

    private void HA26(String cmdLine){
        sendCmd("HA26 A 2 3 4.950 3.066 35.61 497");
    }

    private void HA27(String cmdLine){
        String[] cmdArr = cmdLine.split(" ");
        String unit = cmdArr[1];
        if (StringUtils.equals("1", unit)) {
            sendCmd("HA27 A 3.066 g");
        }else if (StringUtils.equals("0", unit) || StringUtils.equals("3", unit)) {
            sendCmd("HA27 A 35.61 %MC");
        }
    }



    private void sleep(long millsec){
        try {
            Thread.sleep(millsec);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送指令，自动添加行结束符
     * @param cmd
     */
    private void sendCmd(String cmd) throws TaskRunningException {
        String out = cmd+"\r\n";
        try {
            this.sock.getOutputStream().write(out.getBytes());
        } catch (IOException e) {
            e.printStackTrace();
            throw new TaskRunningException(e.getMessage());
        }
        // sp.send(cmd+"\r\n");
    }
    /**
     * Function<String, Boolean>
     *     当Bealean返回为true时结束循环，否则继续等待
     * @param fun
     */
    private void streamResponse(Function<String, Boolean> fun){
        StringBuffer strBuf = new StringBuffer();
        while(true) {
            String rcvStr = sp.receive();

            strBuf.append(rcvStr);

            if (!streamResponseInner(strBuf, fun)){
                break;
            }
            /*
            int sepIdx = strBuf.indexOf("\r\n");
            if (sepIdx > 0) {
                String lineStr = strBuf.substring(0, sepIdx);
                strBuf.delete(0, sepIdx + 2);
                log.info("lineStr="+lineStr);
                if (fun.apply(lineStr)){

                    break;
                }

            }
             */
        }

    }

    private boolean streamResponseInner(StringBuffer strBuf, Function<String, Boolean> fun) {
        int sepIdx = -1;
        boolean bCountinue = false;
        do {
            sepIdx = strBuf.indexOf("\r\n");
            if (sepIdx >= 0) {
                String lineStr = strBuf.substring(0, sepIdx);
                strBuf.delete(0, sepIdx + 2);
                sepIdx = strBuf.indexOf("\r\n");
                log.info("lineStr=" + lineStr);
                if (StringUtils.isEmpty(lineStr)){
                    continue;
                }
                if (fun.apply(lineStr)) {
                    strBuf.delete(0,strBuf.length());
                    bCountinue = false;
                    break;
                }else{
                    bCountinue = true;
                }

            }
        }while(sepIdx>=0);

        return bCountinue;
    }

}
