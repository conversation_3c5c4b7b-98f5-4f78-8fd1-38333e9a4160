/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.serialport;

/**
 * 串口测试
 */
public class SerialPortTest {
    public static void main(String[] args) {
        System.out.println("Begin...");
        System.out.println("pwd="+System.getProperty("user.dir"));
        System.out.println("java.library.path="+System.getProperty("java.library.path"));

        SerialPort sp = new SerialPort();
        sp.open("COM1",9600, (char)0,(char)8,(char)1 );
        sp.send("Hi");
        sp.close();
    }

}
