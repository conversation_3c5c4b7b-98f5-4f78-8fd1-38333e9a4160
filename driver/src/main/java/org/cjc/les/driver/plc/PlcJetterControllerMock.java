/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.plc;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.net.*;

/**
 * 模拟Jetter PLC控制器
 */
@Log4j2
public class PlcJetterControllerMock {
    public static void main(String[] args) throws IOException {
      //  ServerSocket serverSocket = new ServerSocket(4000);
      //  Socket sock = serverSocket.accept();

        byte[] buf2 = new byte[21];
        String hexStr = "4a57495000010002000025880000000000b3000020";
        //buf2 = hexStr.getBytes();
        for (int i=0; i< buf2.length; i++){
            String tmp = hexStr.substring(i*2,i*2+2);
            buf2[i] = (byte)Integer.parseInt(tmp,16);
        }

        do {
            DatagramSocket ds = new DatagramSocket(50010);

            byte[] buf = new byte[1024];
            DatagramPacket dp = new DatagramPacket(buf, buf.length);

            ds.receive(dp);
            byte[] rcvData = dp.getData();
            int rcvLen = dp.getLength();

            log.info("Received: {}, rcvLen={}", byteArrayToHexString(rcvData), rcvLen);

            DatagramSocket ds2 = new DatagramSocket();

            // "JWIP\x00\x01\x00\x02\x00\x00%\x88\x00\x00\x00\x00\x00\xb3\x00\x00 "
            // 4a57495000010002000025880000000000b3000020
            // 4a5749500001000200002588000000000088000021

            // 4a57495000010000000005e800000fa100000000550b
            // 4a574950000100000000313400000fa100000000550b

/*
            byte[] buf2 = new byte[21];
            String hexStr = "4a57495000010002000025880000000000b3000020";
            for (int i=0;i< hexStr.length(); i++) {
                buf2[i] = (byte)Integer.parseInt(hexStr.substring(i,i+1),16);
            }
*/
            byte[] backData = new byte[rcvLen];
            for (int i=0; i<backData.length; i++){
                if (i<14) {
                    backData[i] = rcvData[i];
                }
            }
     //       backData[backData.length-2] = (byte) 0x55;
     //       backData[backData.length-1] = (byte) 0x0b;
            backData[backData.length-2] = (byte) 0x00;
            backData[backData.length-1] = (byte) 0x21;


            InetAddress addr = InetAddress.getByAddress(new byte[]{127,0,0,1});
            DatagramPacket dp2 = new DatagramPacket(backData, backData.length, addr,50012);

            ds2.send(dp2);

            System.out.println("Sock 4001 sent OK.");
            ds.close();
            ds2.close();
        }while(true);

    }

    public static String byteArrayToHexString(byte[] byteArray) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : byteArray) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
}
