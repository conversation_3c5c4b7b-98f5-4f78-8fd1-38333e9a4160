/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.socket;

import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.util.CommandUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.util.function.Function;

/**
 * 命令行形式的TCP_SOCKET, 阻塞方式执行
 */
@Log4j2
public class CommandLineTcpSocket extends Socket {

    private BufferedReader br;
    /**
     * 连接服务端
     * @param ipAddr IP或者主机名
     * @param port 端口号
     * @throws IOException
     */
    public void connect(String ipAddr, int port) throws IOException {
        SocketAddress addr = new InetSocketAddress(ipAddr, port);
        super.connect(addr);
        InputStreamReader isr = new InputStreamReader(this.getInputStream());
        br = new BufferedReader(isr);
    }

    /**
     * 发送命令并等待返回
     * @param cmdStr 发送命令行, 无需增加/r/n结尾字符
     * @param callbackFun 命令执行结果回调函数
     * @throws IOException
     */
    public void sendCommand(String cmdStr, Function<String, Boolean> callbackFun) throws IOException {
        sendCmd(cmdStr);
        streamResponse(callbackFun);
    }

    /**
     * 发送命令并直接返回，异步回调callback
     * @param cmdStr 发送命令行, 无需增加/r/n结尾字符
     * @param callbackFun 命令执行结果回调函数
     * @throws IOException
     */
    public void asyncSendCommand(String cmdStr, Function<String, Boolean> callbackFun) throws IOException {
        sendCmd(cmdStr);
        CommandUtil.submitToCommandListenerPool(new Runnable() {

            @Override
            public void run() {
                try {
                    streamResponse(callbackFun);
                } catch (IOException e) {
                    log.error("asyncSendCommand streamResponse error, "+e.getMessage());
                }
            }
        });
    }
    /**
     * 接收命令行
     * @param callbackFun 接收到命令后的回调函数
     * @throws IOException
     */
    public void receiveCommand(Function<String, Boolean> callbackFun) throws IOException {
        streamResponse(callbackFun);
    }


    /**
     * 发送指令，自动添加行结束符
     * @param cmd
     */
    private void sendCmd(String cmd) throws IOException {
        String inStr = cmd+"\r\n";
        this.getOutputStream().write(inStr.getBytes());
        log.info("Sent cmdLine: "+ cmd);
    }

    /**
     * Function<String, Boolean>
     *     当Bealean返回为true时结束循环，否则继续等待
     * @param fun
     */
    private void streamResponse(Function<String, Boolean> fun) throws IOException {

        while(true) {
            String rcvStr = br.readLine();
            if (rcvStr == null) {
                throw new IOException("Socket was closed unexceptedly.");
            }
            String lineStr = rcvStr;
            log.info("Received cmdLine: " + lineStr);
            if (StringUtils.isEmpty(lineStr)){
                continue;
            }
            if (fun.apply(lineStr)) {
                break;
            }
        }
    }

    @Override
    public synchronized void close() throws IOException {
        if (!super.isClosed()) {
            super.close();
        }
        if (br != null){
            br.close();
        }
    }
}
