/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.serialport;

/*
   串口通讯接口
 */
public class SerialPort {
    static {
        System.loadLibrary("SerialPort");
    }

    /**
     * 保存CPP实现的实例指针，便于管理内部对象的生命周期
     */
    private long cppHandlerPtr;

    /**
     * 打开串口
     * @param portName 串口名
     * @param baudRate 波特率
     * @param parity 奇偶校验
     * @param dataBit 数据位长
     * @param stopBit 停止位
     * @return 是否开启成功 true/false
     */
    public native boolean open(String portName, int baudRate, char parity, char dataBit, char stopBit);

    /**
     * 关闭串口
     */
    public native void close();

    /**
     * 发送命令字符串
     * @param cmd 命令名
     * @return 是否发送成功 true/false
     */
    public native boolean send(String cmd);

    /**
     * 接受命令返回字符串
     * @return 返回字符串
     */
    public native String receive();
}
