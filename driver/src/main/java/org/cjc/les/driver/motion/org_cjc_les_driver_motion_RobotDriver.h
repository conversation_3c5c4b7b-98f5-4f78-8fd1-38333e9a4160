/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_cjc_les_driver_motion_RobotDriver */

#ifndef _Included_org_cjc_les_driver_motion_RobotDriver
#define _Included_org_cjc_les_driver_motion_RobotDriver
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    init
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_motion_RobotDriver_init
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    execute
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_motion_RobotDriver_execute
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    run
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_motion_RobotDriver_run
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_motion_RobotDriver_finalize
  (JNIEnv *, jobject);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    getAxisVar
 * Signature: (Ljava/lang/String;I)D
 */
JNIEXPORT jdouble JNICALL Java_org_cjc_les_driver_motion_RobotDriver_getAxisVar
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    getVr
 * Signature: (I)D
 */
JNIEXPORT jdouble JNICALL Java_org_cjc_les_driver_motion_RobotDriver_getVr
  (JNIEnv *, jobject, jint);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    getVersionInfo
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_cjc_les_driver_motion_RobotDriver_getVersionInfo
  (JNIEnv *, jobject);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    move
 * Signature: (ID)V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_motion_RobotDriver_move
  (JNIEnv *, jobject, jint, jdouble);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    getAxisParameter
 * Signature: (ILjava/lang/String;)D
 */
JNIEXPORT jdouble JNICALL Java_org_cjc_les_driver_motion_RobotDriver_getAxisParameter
  (JNIEnv *, jobject, jint, jstring);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    getIn
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_motion_RobotDriver_getIn
  (JNIEnv *, jobject, jint);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    setOut
 * Signature: (IZ)Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_motion_RobotDriver_setOut
  (JNIEnv *, jobject, jint, jboolean);

/*
 * Class:     org_cjc_les_driver_motion_RobotDriver
 * Method:    getOut
 * Signature: (I)Z
 */
JNIEXPORT jint JNICALL Java_org_cjc_les_driver_motion_RobotDriver_getOut
  (JNIEnv *, jobject, jint);

#ifdef __cplusplus
}
#endif
#endif
