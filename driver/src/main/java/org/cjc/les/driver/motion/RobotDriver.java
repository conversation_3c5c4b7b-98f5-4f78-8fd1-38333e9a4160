/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.motion;

/*
    机器人驱动接口，具体实现需要对应的硬件控制器通过C++ SDK完成
 */
public class RobotDriver {
    static {
        System.loadLibrary("RobotDriverTrioPC");
    }

    /**
     * 保存CPP实现的实例指针，便于管理内部对象的生命周期
     */
    private long cppHandlerPtr = 0L;

    /**
     * 初始化驱动，连接控制器
     * @param hostIp 控制器IP
     */
    public native boolean init(String hostIp);

    /**
     * 执行控制器运动指令
     * @param cmd 运动指令
     */
    public native void execute(String cmd);

    /**
     * 运行程序
     * @param program 程序名
     */
    public native void run(String program);

    /**
     * 终止驱动，关闭控制器连接，回收资源
     */
    public native void finalize();

    /**
     * 获取轴参数
     * @param varName 参数名
     * @param axis 轴序号
     * @return 参数值
     */
    public native double getAxisVar(String varName, int axis);

    /**
     * 获取VR变量值
     * @param vrNo VR编号
     * @return VR变量值
     */
    public native double getVr(int vrNo);

    /**
     * 当前驱动及硬件相关的版本信息
     * @return
     */
    public native String getVersionInfo();

    /**
     * 移动单轴位置
     * @param axis 轴编号
     * @param distance 移动距离，反向为负数
     */
    public native void move(int axis, double distance);

    /**
     * 获取轴参数值
     * @param axis 轴编号
     * @param paramName 参数名称
     * @return 参数值
     */
    public native double getAxisParameter(int axis, String paramName);

    /**
     * 输入端口设置
     * @param in 端口号
     * @return
     */
    public native boolean getIn(int in);

    /**
     * 输出端口设置
     * @param out 端口号
     * @param bValue 布尔值
     * @return
     */
    public native boolean setOut(int out, boolean bValue);

    /**
     * 获取输出端口状态
     * @param out 端口号
     * @return -1:连接异常， 0:false, 1:true
     */
    public native int getOut(int out);
}
