/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.moisture;

import org.cjc.les.driver.serialport.SerialPortConfig;

import java.io.IOException;

public class MoistureBalanceComDriverTest {

    public static void main(String[] args) throws IOException {
        System.out.println("Begin...");
        System.out.println("pwd="+System.getProperty("user.dir"));
        System.out.println("java.library.path="+System.getProperty("java.library.path"));

      //  testVirtual();
        testProd();

        System.out.println("End.");
    }

    private static void testVirtual(){

        MoistureBalanceComDriver driver = new MoistureBalanceComDriver();
        driver.init();

        driver.active();
        //   driver.status();

        driver.finalize();

    }

    private static void testProd() {

        SerialPortConfig spConfig = new SerialPortConfig();
        spConfig.setPortName("COM3");
        spConfig.setBaudRate(9600);
        spConfig.setParity((char)0);
        spConfig.setDataBit((char)8);
        spConfig.setStopBit((char)1);
        MoistureBalanceComDriver driver = new MoistureBalanceComDriver();
        driver.init(spConfig);

       // driver.active();
        driver.status();

        driver.finalize();

        System.out.println("End.");

    }
}
