/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.plc;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.vo.CommandReturn;
import org.cjc.les.device.JetterPlcDevice;

import java.io.IOException;

@Log4j2
public class PlcDriverTest {
    public static void main(String[] args) throws InterruptedException, IOException {
        System.out.println("Begin...");
        System.out.println("pwd="+System.getProperty("user.dir"));
        System.out.println("java.library.path="+System.getProperty("java.library.path"));

      //  Socket sock = new Socket();
      //  SocketAddress rmAddr = new InetSocketAddress("127.0.0.1",4000);
      //  sock.connect(rmAddr);
      //  testJetter340();
      //  testVirtual();
        testDevice();
        System.out.println("End...");
    }

    private static void testDevice(){
        /**
         * JetSym虚拟设备测试
         */
        JetterPlcDevice plc = new JetterPlcDevice();
        plc.setId(2L);
        plc.setConfig("JETIP:*************:4005:4000");
        plc.setMockConfig("JETIP:127.0.0.1:50012:50010");
        plc.setEnableMock(false);
        plc.setJavaClassName("org.cjc.les.device.JetterPlcDevice");
        plc.setConfigJavaClassName("java.lang.String");

        boolean plcRet = plc.init();
        if (!plcRet){
            log.error("Initialize PLC error, Please check the configuration.");
            return;
        }

        // 打开Moisture 190 // 关闭Moisture 200
        CommandReturn<Boolean> ret = plc.setBool(PlcDriver.BT_FLAG,200,true, true);

        boolean bIn = ret.getResult();
        log.info("Return {}", ret);
        // 关闭Moisture
   //     boolean bIn =  plc.setBool(PlcDriver.BT_FLAG,200,true);

       // boolean bIn =  plc.getBool(PlcDriver.BT_FLAG,200,0);
        System.out.println("PlcDriver.BT_IN 101=" + bIn);

        plc.finalize();
    }

    private static void testVirtual() throws InterruptedException{


        PlcDriver driver = new PlcDriver();
        driver.init("JETIP:127.0.0.1:50012:50010");

        /*
        boolean status = driver.getConnectionStatus();
        System.out.println("getConnectionStatus=" + status);

         */
        int bIn =  driver.getBool(PlcDriver.BT_IN,200007107,0);
        System.out.println("PlcDriver.BT_IN 101=" + bIn);

 //       boolean bOut =  driver.getBool(PlcDriver.BT_OUT,200007107,0);
 //       System.out.println("PlcDriver.BT_OUT 102=" + bOut);
 //       boolean bFlag =  driver.getBool(PlcDriver.BT_FLAG,200007107,0);
 //       System.out.println("PlcDriver.BT_FLAG 103=" + bFlag);
 //       boolean bReg =  driver.getBool(PlcDriver.BT_REGBIT,200007107,1);
 //       System.out.println("PlcDriver.BT_REGBIT 104=" + bReg);

 //       driver.setBool(PlcDriver.BT_OUT,200007107,true);
 //       System.out.println("SetBool PlcDriver.BT_OUT 110=true");

        //driver.execute("MOVE(10)");
        //driver.run("TUNEX");
        Thread.currentThread().sleep(1000);
        driver.finalize();

    }
    private static void testJetter340() throws InterruptedException {

        System.out.println("Jetter340 testing...");

        /**
         * JetSym虚拟设备测试
         */
        PlcDriver driver = new PlcDriver();
        driver.init("JETIP:*************:4001:4000");

        boolean status = driver.getConnectionStatus();
        System.out.println("getConnectionStatus=" + status);
/*
        boolean bIn =  driver.getBool(PlcDriver.BT_IN,1000000101,0);
        System.out.println("PlcDriver.BT_IN 101=" + bIn);
        boolean bOut =  driver.getBool(PlcDriver.BT_OUT,1000000102,0);
        System.out.println("PlcDriver.BT_OUT 102=" + bOut);

        boolean bFlag =  driver.getBool(PlcDriver.BT_FLAG,1000000103,0);
        System.out.println("PlcDriver.BT_FLAG 103=" + bFlag);

        boolean bReg =  driver.getBool(PlcDriver.BT_REGBIT,1000000104,1);
        System.out.println("PlcDriver.BT_REGBIT 104=" + bReg);

        driver.setBool(PlcDriver.BT_OUT,1000000110,true);
        System.out.println("SetBool PlcDriver.BT_OUT 110=true");
        boolean oRet = driver.getBool(PlcDriver.BT_OUT,1000000110,0);
        System.out.println("GetBool PlcDriver.BT_OUT 110=" + oRet);
*/

        driver.setBool(PlcDriver.BT_FLAG,190, true);


        Thread.currentThread().sleep(5000);

        driver.setBool(PlcDriver.BT_FLAG,200, true);

        driver.finalize();
        System.out.println("End...");
    }

}
