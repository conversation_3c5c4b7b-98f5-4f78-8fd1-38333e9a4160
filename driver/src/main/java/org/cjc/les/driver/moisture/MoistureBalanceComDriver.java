/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.moisture;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.core.util.CommandUtil;
import org.cjc.les.device.WeighingArg;
import org.cjc.les.driver.serialport.SerialPort;
import org.cjc.les.driver.serialport.SerialPortConfig;
import org.cjc.les.exception.TaskRunningException;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * 水分检测仪器串口驱动
 */
@Log4j2
public class MoistureBalanceComDriver {

    private SerialPort sp = new SerialPort();

    private final static double WEIGHING_OFFSET = 0.1;

    /**
     * 驱动初始化，连接串口
     */
    public boolean init() {
        return sp.open("COM1", 9600, (char) 0, (char) 8, (char) 1);
    }

    /**
     * 指定端口的初始化
     * @param config 串口配置
     */
    public boolean init(SerialPortConfig config) {
        return sp.open(config.getPortName(), config.getBaudRate(), (char)config.getParity(), (char)config.getDataBit(), (char)config.getStopBit());
    }

    /**
     * 从standy状态转到激活态
     */
    public void active() {
        // 执行@指令，并等待完成
        sendCmd("@");
        streamResponse("@",(lineStr) -> {
            if (lineStr.startsWith("I4 A")) {
                log.info("@ response OK");

            } else {
                log.error("@ response failed.");
                return false;
            }
            return true;
        });

        // 执行撤销指令C, 并等待完成
        sendCmd("C");
        streamResponse("C",(lineStr) -> {
            if (lineStr.startsWith("C A")) {
                log.info("C A response OK");
                return true;
            } else if (lineStr.startsWith("C B")) {
                log.info("C B response OK");
                return false;
            }else if  (lineStr.startsWith("C E")) {
                log.info("C E response Failed");
                return true;
            }else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for Ha09 response, but found: {}", lineStr);
                return false;
            }
        });
    }

    /**
     * 查看激活状态
     */
    public void status() {
        sendCmd("HA07 1");
        streamResponse("HA07",(lineStr) -> {
            if (lineStr.startsWith("HA07 A")) {
                log.info("HA07 A response OK");
            }else if  (lineStr.startsWith("HA07 E")) {
                log.info("HA07 E response Failed");
                return true;
            }
            else if (lineStr.startsWith("HA07")){
                // 返回错误结果，暂不处理
                log.info("HA07 A response {}, continue.", lineStr);
                return false;
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for HA07 response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    /**
     * 返回主菜单
     */
    public void home() {
        sendCmd("HA09");
        streamResponse("HA09",(lineStr) -> {
            if (lineStr.startsWith("HA09 A")) {
                log.info("HA09 A response OK");
            }else if (lineStr.startsWith("HA09")){
                // 返回错误结果，暂不处理
                log.info("HA09 A response Failed");
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for Ha09 response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    public void setDryMethodName(String name) {
        sendCmd("HA65 \"" + name + "\"");
        streamResponse("HA65",(lineStr) -> {
            if (lineStr.startsWith("HA65 A")) {
                log.info("HA65 A response OK");
            } else if (lineStr.startsWith("HA65")){
                // 返回错误结果，暂不处理
                log.info("HA65 A response Failed");
            }
            else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for HA65 response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    /**
     * 清零， 在执行称重之前进行该动作
     */
    public void zero() {
        final AtomicBoolean respStatus = new AtomicBoolean(true);
        int max_tries = 3; // 最大尝试次数
        do {
            sendCmd("Z");
            respStatus.set(true);
            streamResponse("Z", (lineStr) -> {
                if (lineStr.startsWith("Z A")) {
                    log.info("Z A response OK");
                    respStatus.set(true);
                } else if (lineStr.startsWith("Z")) {
                    log.error("Z response FAILED.{}", lineStr);
                    respStatus.set(false);
                } else {
                    // 没有收到该指令的返回结果，继续等待
                    log.info("Waiting for HA07 response, but found: {}", lineStr);
                    return false;
                }
                return true;
            });
            if ((--max_tries) == 0){
                break;
            }
            if (!respStatus.get()) {
                // 失败等待3秒后重试
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    throw new TaskRunningException("InterruptedException occurred");
                }
            }
        }while(!respStatus.get() && !Thread.interrupted());
    }

    /**
     * 同步称量
     * @param arg 称量参数
     */
    public void weighing(WeighingArg arg) {
        sendCmd("SIR");
        AtomicInteger errorRetries = new AtomicInteger(3);
        streamResponse("SIR",(lineStr) -> {

            parseWeighingResult(lineStr, arg);
            // 当出现出错次数超过3次时，终止称量任务，并通知其他辅助设备停止倒样
            if (!arg.isDone() && arg.getErrorCode().equals(ErrorCodeEnum.ERROR) && (errorRetries.decrementAndGet()) == 0 ){
                arg.setDone(true);
            }
            return arg.isDone();
        });
    }


    /**
     * 异步称量
     * @param arg 称量参数
     */
    public void asyncWeighing(WeighingArg arg) {
        sendCmd("SIR");
        CommandUtil.submitToCommandListenerPool(new Runnable() {

            @Override
            public void run() {
                AtomicInteger errorRetries = new AtomicInteger(1000);
                streamResponse("SIR", (lineStr) -> {

                    parseWeighingResult(lineStr, arg);
                    // 当出现出错次数超过3次时，终止称量任务，并通知其他辅助设备停止倒样
                    if (!arg.isDone() && arg.getErrorCode().equals(ErrorCodeEnum.ERROR) && (errorRetries.decrementAndGet()) == 0 ){
                        arg.setDone(true);
                    }
                    return arg.isDone();
                });
            }
        });

    }

    private void parseWeighingResult(String lineStr, WeighingArg arg) {
        arg.setErrorCode(ErrorCodeEnum.SUCCESS);
        arg.setErrorMsg("");
        try {
            double rs = 0.0;
            if (lineStr.startsWith("S S")) {
                rs = Double.parseDouble(StringUtils.trim(lineStr.substring(3, lineStr.length() - 1)));
                arg.setActualValue(rs);
                log.info("Stable value: " + rs);
            } else if (lineStr.startsWith("S D")) {
                rs = Double.parseDouble(StringUtils.trim(lineStr.substring(3, lineStr.length() - 1)));
                arg.setActualValue(rs);
                log.info("Unstable value: " + rs);
            } else {
                log.error("Not expected response: {}", lineStr);
                arg.setErrorCode(ErrorCodeEnum.ERROR);
                arg.setErrorMsg("Not expected response");
            }
        }catch(NumberFormatException ex0){
            log.error("Number format error, lineStr={}", lineStr);
            arg.setErrorCode(ErrorCodeEnum.ERROR);
            arg.setErrorMsg("Number format error.");
        }
        // 未达到设定的数值精度时，重复监听最新数据
        if ((arg.getExpectValue() - arg.getActualValue()) - WEIGHING_OFFSET > arg.getPrecise()) {
            arg.setDone(false);
            return;
        }
        arg.setDone(true);
    }

    /**
     * 稳定称量结果查询
     */
    public void stableWeight(){
        sendCmd("S");
        streamResponse("S", (lineStr) -> {
            if (lineStr.startsWith("S S")) {
                log.info("S S response OK");
            } else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for S response, but found: {}", lineStr);
                return false;
            }
            return true;
        });
    }

    public String getWetResult(){
        sendCmd("HA26 0");
        StringBuffer resultBuf = new StringBuffer();
        streamResponse("HA26", (lineStr) -> {
            if (lineStr.startsWith("HA26 A")) {
                log.info("HA26 A response OK");
                String[] lineArr = lineStr.split(" ");
                resultBuf.append(lineArr[4]);
            } else if (lineStr.startsWith("HA26"))  {
                log.error("HA26 response FAILED.");
            }else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for HA26 response, but found: {}", lineStr);
                return false;
            }
            return true;
        });

        return resultBuf.toString();
    }

    public String getDryResult(String unit ){
        int iUnit = 0;
        if (StringUtils.equals("g", unit)){
            iUnit = 1;
        }else if(StringUtils.equals("%MC", unit)){
            iUnit = 3;
        }
        sendCmd("HA27 "+iUnit);
        StringBuffer resultBuf = new StringBuffer();
        streamResponse("HA27", (lineStr) -> {
            if (lineStr.contains("HA27 A")) {
                log.info("HA27 response OK");
                String[] lineArr = lineStr.split(" ");
                resultBuf.append(lineArr[2]);
            } else if (lineStr.startsWith("HA27"))  {
                log.error("HA27 response FAILED.");
            }else{
                // 没有收到该指令的返回结果，继续等待
                log.info("Waiting for HA27 response, but found: {}", lineStr);
                return false;
            }
            return true;
        });

        return resultBuf.toString();
    }

    /**
     * 关闭串口
     */
    public void finalize() {
        sp.close();
    }

    /**
     * 发送指令，自动添加行结束符
     *
     * @param cmd
     */
    private void sendCmd(String cmd) {
       boolean ret = sp.send(cmd + "\r\n");
        log.info("Sent: {}, ret={}", cmd, ret);
        if (!ret) {
            waitingAlways(cmd);
        }
    }

    private void waitingAlways(String cmd){
        while(!Thread.interrupted()){
            try {
                log.error("Could not send command: {} as connection error, please check and try it again manually.", cmd);
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
                throw new TaskRunningException("InterruptedException occurred");
            }
        }
    }

    /**
     * Function<String, Boolean>
     * 当Bealean返回为true时结束循环，否则继续等待
     *
     * @param cmd 指令名
     * @param fun 回调方法
     */
    private void streamResponse(String cmd, Function<String, Boolean> fun) {
        StringBuffer strBuf = new StringBuffer();
        while (true) {
            String rcvStr = sp.receive();

            strBuf.append(rcvStr);

            if (!streamResponseInner(cmd, strBuf, fun)) {
                break;
            }

        }

    }

    private boolean streamResponseInner(String cmd, StringBuffer strBuf, Function<String, Boolean> fun) {
        int sepIdx = -1;
        boolean bCountinue = true;
        do {
            sepIdx = strBuf.indexOf("\r\n");
            if (sepIdx >= 0) {
                String lineStr = strBuf.substring(0, sepIdx);
                strBuf.delete(0, sepIdx + 2);
                sepIdx = strBuf.indexOf("\r\n");
                log.info("{} Received: {}" ,cmd, lineStr);
                if (StringUtils.isEmpty(lineStr)) {
                    continue;
                }
                if (fun.apply(lineStr)) {
                    strBuf.delete(0, strBuf.length());
                    bCountinue = false;
                    break;
                } else {
                    bCountinue = true;
                }

            }
        } while (sepIdx >= 0);

        return bCountinue;
    }

}
