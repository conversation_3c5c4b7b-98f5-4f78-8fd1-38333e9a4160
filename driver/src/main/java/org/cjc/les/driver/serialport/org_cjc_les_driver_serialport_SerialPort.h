/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_cjc_les_driver_serialport_SerialPort */

#ifndef _Included_org_cjc_les_driver_serialport_SerialPort
#define _Included_org_cjc_les_driver_serialport_SerialPort
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_cjc_les_driver_serialport_SerialPort
 * Method:    open
 * Signature: (Ljava/lang/String;ICCC)Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_serialport_SerialPort_open
  (JNIEnv *, jobject, jstring, jint, jchar, jchar, jchar);

/*
 * Class:     org_cjc_les_driver_serialport_SerialPort
 * Method:    close
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_serialport_SerialPort_close
  (JNIEnv *, jobject);

/*
 * Class:     org_cjc_les_driver_serialport_SerialPort
 * Method:    send
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_serialport_SerialPort_send
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_les_driver_serialport_SerialPort
 * Method:    receive
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_cjc_les_driver_serialport_SerialPort_receive
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
