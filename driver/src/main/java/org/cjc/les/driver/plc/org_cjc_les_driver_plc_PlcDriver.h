/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_cjc_les_driver_plc_PlcDriver */

#ifndef _Included_org_cjc_les_driver_plc_PlcDriver
#define _Included_org_cjc_les_driver_plc_PlcDriver
#ifdef __cplusplus
extern "C" {
#endif
#undef org_cjc_les_driver_plc_PlcDriver_BT_IN
#define org_cjc_les_driver_plc_PlcDriver_BT_IN 0L
#undef org_cjc_les_driver_plc_PlcDriver_BT_OUT
#define org_cjc_les_driver_plc_PlcDriver_BT_OUT 1L
#undef org_cjc_les_driver_plc_PlcDriver_BT_FLAG
#define org_cjc_les_driver_plc_PlcDriver_BT_FLAG 2L
#undef org_cjc_les_driver_plc_PlcDriver_BT_REGBIT
#define org_cjc_les_driver_plc_PlcDriver_BT_REGBIT 3L
/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    init
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_plc_PlcDriver_init
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    execute
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_plc_PlcDriver_execute
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    getConnectionStatus
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_org_cjc_les_driver_plc_PlcDriver_getConnectionStatus
  (JNIEnv *, jobject);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    getRegister
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_org_cjc_les_driver_plc_PlcDriver_getRegister
  (JNIEnv *, jobject, jint);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    setRegister
 * Signature: (II)V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_plc_PlcDriver_setRegister
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    getBool
 * Signature: (III)I
 */
JNIEXPORT jint JNICALL Java_org_cjc_les_driver_plc_PlcDriver_getBool
  (JNIEnv *, jobject, jint, jint, jint);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    setBool
 * Signature: (IIZ)I
 */
JNIEXPORT jint JNICALL Java_org_cjc_les_driver_plc_PlcDriver_setBool
  (JNIEnv *, jobject, jint, jint, jboolean);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    getString
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_cjc_les_driver_plc_PlcDriver_getString
  (JNIEnv *, jobject, jint);

/*
 * Class:     org_cjc_les_driver_plc_PlcDriver
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_org_cjc_les_driver_plc_PlcDriver_finalize
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
