/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.serialport;

import lombok.Data;
import org.cjc.les.annotation.Parameter;

/**
 * 串口配置类
 */
@Data
public class SerialPortConfig {
    @Parameter("端口名")
    private String portName;
    @Parameter("波特率")
    private int baudRate;
    /**
     *
     */
    @Parameter("奇偶校验")
    private int parity;
    @Parameter("数据位")
    private int dataBit;
    @Parameter("停止位")
    private int stopBit;
}
