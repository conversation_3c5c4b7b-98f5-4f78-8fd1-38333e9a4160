/*
 *  Copyright 2024-2024 <PERSON><PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.rfid;

import lombok.extern.log4j.Log4j2;

import java.io.IOException;

/**
 * RFID Reader驱动测试
 */
@Log4j2
public class RfidReaderDriverTest {
    public static void main(String[] args) throws InterruptedException, IOException {
        System.out.println("Begin...");

        // testVirtual();
        testProd1();

        System.out.println("End...");
    }

    private static void testVirtual() throws IOException {
        RfidReaderDriver driver = new RfidReaderDriver();
        driver.init("127.0.0.1",50065);
        driver.clear();

        driver.receive((cmdLine)->{
            System.out.println("Received RFID OK: " + cmdLine);
            return true;
        });
        driver.finalize();

    }


    private static void testProd1() throws IOException {
        RfidReaderDriver driver = new RfidReaderDriver();
        driver.init("192.168.0.112",47);
        driver.clear();
        log.info("Waiting for RFID ...");
        driver.receive((cmdLine)->{
            System.out.println("Received RFID OK: " + cmdLine);
            return true;
        });
        driver.finalize();

    }

}
