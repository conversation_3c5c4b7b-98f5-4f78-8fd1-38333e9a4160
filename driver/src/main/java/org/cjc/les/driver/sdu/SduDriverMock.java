/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.sdu;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SduDriverMock {

    private static ExecutorService executorService = Executors.newFixedThreadPool(4);

    public static void main(String[] args) throws InterruptedException, IOException {
        System.out.println("Begin...");

        boolean bAutoResponse = false;
        int port = 4701;
        if (args!=null && args.length>0){
            String portStr = args[0];
            String[] portArr = portStr.split("=");
            port = Integer.parseInt(portArr[1]);

            if (args.length>1){
                String autoResponse = args[1];
                bAutoResponse = true;
            }
        }


        ServerSocket serverSocket = new ServerSocket(port);

        while (true) {
            try {
                final Socket sock = serverSocket.accept();
                input(sock);
            }catch(Exception e){
                e.printStackTrace();
            }
        }

    }

    private static void input(Socket sock)  {

        executorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    InputStreamReader isr = new InputStreamReader(sock.getInputStream());
                    BufferedReader br = new BufferedReader(isr);
                    String cmdLine = null;

                    do {
                        cmdLine = br.readLine();
                        System.out.println("Received cmdLine: " + cmdLine);

                        String out = "Done " + cmdLine + " Command\r\n";
                        sock.getOutputStream().write(out.getBytes());
                        System.out.println("Response: " + out);
                    }
                    while (true);
                }catch (Exception e){
                    e.printStackTrace();
                    if (!sock.isClosed()){
                        try {
                            sock.close();
                        } catch (IOException ioException) {
                            ioException.printStackTrace();
                        }
                    }
                }
            }
        });


    }

}
