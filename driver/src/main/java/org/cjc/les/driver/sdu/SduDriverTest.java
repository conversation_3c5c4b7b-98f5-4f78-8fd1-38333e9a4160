/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.sdu;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.driver.rfid.RfidReaderDriver;
import org.cjc.utils.StringUtils;

import java.io.IOException;
import java.util.Scanner;

/**
 * 倒样装置测试
 */
@Log4j2
public class SduDriverTest {
    public static void main(String[] args) throws InterruptedException, IOException {
        log.info("Begin...");

      //   testVirtual();
        testProd1();

        log.info("End...");
    }

    private static void testVirtual() throws IOException, InterruptedException {
        SduDriver driver = new SduDriver();
        driver.init("127.0.0.1",4701);
        driver.home();
        driver.turnOn();
        driver.enable();
        // driver.absMove(5680);
        //  driver.turnOn();
        for (int i=0; i<100; i++) {
            driver.move(18);
            Thread.sleep(100);
        }
        // driver.move(-2000);
        driver.zero();
        driver.turnOff();
        driver.home();

        driver.finalize();

    }


    private static void testProd1() throws IOException, InterruptedException {
        SduDriver driver = new SduDriver();
        // 192.168.0.117 分析天平SDU
        // 192.168.0.119 水分检测仪SDU
        driver.init("192.168.0.119",47);

        Scanner scanner = new Scanner(System.in);
        for (;;){
            System.out.println("请输入指令:");
            String lineStr = scanner.nextLine();
            if (StringUtils.equalsAnyIgnoreCase(lineStr,"exit")){
                break;
            }
            if (org.apache.commons.lang3.StringUtils.isEmpty(lineStr)){
                continue;
            }
            driver.sendCommand(lineStr.toUpperCase());
        }
        /*
        driver.turnOff();
      //  driver.home();
      //  driver.turnOn();
      //  driver.enable();
       // driver.absMove(5680);
      //  driver.turnOn();
       for (int i=0; i<100; i++) {
            driver.move(-18);
            Thread.sleep(100);
       }
       // driver.move(-2000);
    //    driver.zero();
     //   driver.turnOff();
        driver.home();
*/

        scanner.close();
        driver.finalize();

    }
}
