/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.motion;

import lombok.extern.log4j.Log4j2;

/**
 * 测试机械臂驱动
 */
@Log4j2
public class RobotDriverTest {
    public static void main(String[] args) throws InterruptedException {
        System.out.println("Begin...");
        System.out.println("pwd="+System.getProperty("user.dir"));
        System.out.println("java.library.path="+System.getProperty("java.library.path"));

         testVirtual();
       // testProd();

        System.out.println("End...");
    }

    private static void testVirtual() {
        RobotDriver driver = new RobotDriver();
        driver.init("127.0.0.1");

     //   boolean in7doorLocks = driver.getIn(7);
    //    log.info("in7doorLocks="+in7doorLocks);

        driver.getAxisVar("test",0);
/*
        boolean in7doorLocks = driver.getIn(7);
log.info("in7doorLocks="+in7doorLocks);

      //  driver.setOut(0,true);
        driver.setOut(1,true);
        driver.setOut(222,true);

        driver.run("ONLINE");

        boolean out0 = driver.getOut(0);
        log.info("out0:" + out0);


            driver.execute("MOVE(10.00) AXIS(1)");
        //  driver.run("TUNEX");
        String ver = driver.getVersionInfo();
        log.info("Version:" + ver);

        driver.move(0, 10.0);
        log.info("move（10.0） AXIS（0）");

        Double axisParaValue = driver.getAxisParameter(0,"DPOS");
        log.info("[0]AxisParam DPOS="+axisParaValue);

        Double axisStatus = driver.getAxisParameter(0,"AXISSTATUS");
        log.info("[0]AXISSTATUS="+axisStatus);

        Double fwdIn = driver.getAxisParameter(0,"FWD_IN");
        log.info("[0]FWD_IN="+fwdIn);

        Double revIn = driver.getAxisParameter(0,"REV_IN");
        log.info("[0]REV_IN="+revIn);

*/
        //  Thread.currentThread().sleep(1000);
        driver.finalize();
    }

    private static void testProd() throws InterruptedException {
        RobotDriver driver = new RobotDriver();
        driver.init("192.168.0.250");


        driver.getAxisVar("test",0);


        //    driver.execute("MOVE(10)");
       //// for (int i=0; i<10; i++) {
      //     driver.execute("MOVE(10.00) AXIS(0)");
      //      Thread.sleep(5000);
        //}
        //  driver.run("TUNEX");
        //  Double axisParaValue = driver.getAxisVar("DPOS",0);
        //  Thread.currentThread().sleep(1000);
   //     String ver = driver.getVersionInfo();
    //    log.info("Version:" + ver);

      //  driver.move(0, 10.0);
     //   log.info("move（10.0） AXIS（0）");

        /*
        driver.run("ONLINE");
        boolean out0 = driver.getOut(0);
        log.info("out0:" + out0);

        boolean in7doorLocks = driver.getIn(7);
        log.info("in7doorLocks="+in7doorLocks);

        driver.execute("MOVE(10.00) AXIS(0)");

        Double axisParaValue = driver.getAxisParameter(0,"DPOS");
        log.info("[0]AxisParam DPOS="+axisParaValue);

        Double axisStatus = driver.getAxisParameter(0,"AXISSTATUS");
        log.info("[0]AXISSTATUS="+axisStatus);

        Double fwdIn = driver.getAxisParameter(0,"FWD_IN");
        log.info("[0]FWD_IN="+fwdIn);

        Double revIn = driver.getAxisParameter(0,"REV_IN");
        log.info("[0]REV_IN="+revIn);

        Thread.sleep(5000);

        driver.run("OFFLINE");
         out0 = driver.getOut(0);
        log.info("OFFLINE out0:" + out0);

         in7doorLocks = driver.getIn(7);
        log.info("OFFLINE  in7doorLocks="+in7doorLocks);
*/
        driver.finalize();
    }
}
