/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.sdu;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.driver.socket.CommandLineTcpSocket;

import java.io.*;

/**
 * 固体进样装置驱动
 * Moisture SDU IP: *************:47
 */
@Log4j2
public class SduDriver {

    CommandLineTcpSocket sock = new CommandLineTcpSocket();

    /**
     * 初始化驱动，TCP连接建立
     *
     * @param ipAddr IP地址
     * @param port   端口号
     */
    public void init(String ipAddr, int port) throws IOException {
        sock.connect(ipAddr, port);
    }

    /**
     * 旋转开启
     */
    public void turnOn() throws IOException {
        sock.sendCommand("TURNON", (lineStr) -> {
            return true;
        });
    }

    /**
     * 旋转关闭
     */
    public void turnOff() throws IOException {
        sock.sendCommand("TURNOFF", (lineStr) -> {
            return true;
        });
    }

    /**
     * 使能
     */
    public void enable() throws IOException {
        sock.sendCommand("ENABLE", (lineStr) -> {
            return true;
        });
    }

    /**
     * 回位原点
     */
    public void home() throws IOException {
        sock.sendCommand("HOME", (lineStr) -> {
            return true;
        });
    }

    /**
     * 设置原点偏移量
     *
     * @param value 偏移量
     */
    public void homeOffset(int value) throws IOException {
        sock.sendCommand("HOMEOFFSET=" + value, (lineStr) -> {
            return true;
        });
    }

    /**
     * 倾斜移动指令
     *
     * @param value 移动量
     * @throws IOException
     */
    public void move(int value) throws IOException {
        sock.sendCommand("MOVE=" + value, (lineStr) -> {
            return true;
        });
    }

    /**
     * 绝对移动指令
     *
     * @param value 绝对移动量
     * @throws IOException
     */
    public void absMove(int value) throws IOException {
        sock.sendCommand("ABSMOVE=" + value, (lineStr) -> {
            return true;
        });
    }

    /**
     * 置零
     *
     * @throws IOException
     */
    public void zero() throws IOException {
        sock.sendCommand("ZERO", (lineStr) -> {
            return true;
        });
    }

    /**
     * 发送通用指令
     * @param cmdLine
     * @throws IOException
     */
    public void sendCommand(String cmdLine) throws IOException{
        sock.sendCommand(cmdLine, (lineStr) -> {
            return true;
        });
    }

    /**
     * 结束，断开TCP连接
     */
    public void finalize() throws IOException {
        if (!sock.isClosed()) {
            sock.close();
        }
    }


}
