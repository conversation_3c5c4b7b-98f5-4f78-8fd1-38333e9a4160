/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.rfid;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.device.JetterPlcDevice;
import org.cjc.les.driver.socket.CommandLineTcpSocket;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;
import java.util.Stack;
import java.util.concurrent.*;

@Log4j2
public class RfidReaderDriverMock {

    private static ExecutorService executorService = Executors.newFixedThreadPool(4);

    private static  ExecutorService executor = Executors.newSingleThreadExecutor();

    private static JetterPlcDevice plc = new JetterPlcDevice();

    private static    Scanner scanner = new Scanner(System.in);

    private static Stack<String> inputStack = new Stack<>();

    public static void main(String[] args) throws InterruptedException, IOException {
        System.out.println("Begin...");

        boolean bAutoResponse = false;
        int port = 4700;
        if (args != null && args.length > 0) {
            String portStr = args[0];
            String[] portArr = portStr.split("=");
            port = Integer.parseInt(portArr[1]);

            if (args.length > 1) {
                String autoResponse = args[1];
                bAutoResponse = true;
                plc.setMockConfig("JETIP:127.0.0.1:50014:50010");
            }else{
                plc.setMockConfig("JETIP:127.0.0.1:50013:50010");
            }
        }

        /*
        plc.setId(2L);
        plc.setConfig("JETIP:*************:4001:4000");

        plc.setEnableMock(true);
        plc.setJavaClassName("org.cjc.les.device.JetterPlcDevice");
        plc.setConfigJavaClassName("java.lang.String");
        plc = (JetterPlcDevice)plc.createActualInstance();

        boolean plcRet = plc.init();
        if (!plcRet){
            log.error("Initialize PLC error, Please check the configuration.");
            return;
        }
         */

        ServerSocket serverSocket = new ServerSocket(port);

        startScannerInputThread();

        while (true) {
            try {
                Socket sock = serverSocket.accept();
                    input(sock, bAutoResponse);
            } catch (Exception e) {
                e.printStackTrace();

            }
        }

    }

    private static void startScannerInputThread() {
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                while(true){
                    try {
                        String lineStr = scanner.nextLine(); // 读取用户输入的一行文本
                        inputStack.push(lineStr);
                    }catch(Exception e){
                        e.printStackTrace();
                    }
                }
            }
        });
        thread.start();
    }

    private static void input(Socket sock, boolean bAutoResponse) throws IOException {

        executorService.submit(new Runnable() {

            private String cmdLine = null;

            @Override
            public void run() {

                try {
                    InputStreamReader isr = new InputStreamReader(sock.getInputStream());
                    BufferedReader br = new BufferedReader(isr);


                        do {
                            cmdLine = br.readLine();
                            System.out.println("Received cmdLine: " + cmdLine);
                            if (cmdLine == null){
                                break;
                            }
                            if (StringUtils.equals("CLEAR", cmdLine)) {
                                String out = "CLEAR\r\n";
                                sock.getOutputStream().write(out.getBytes());
                            }

                            String out = null;
                            if (bAutoResponse) {
                                out = "test" + "\r\n";
                                sock.getOutputStream().write(out.getBytes());
                            } else {


                                    Thread respThread = new Thread(new Runnable() {
                                        @Override
                                        public void run() {
                                            System.out.println("Please input RFID: ");
                                            while(true){
                                                if (cmdLine == null ){
                                                    break;
                                                }
                                                if (inputStack.isEmpty()){
                                                    try {
                                                        Thread.sleep(1000);
                                                    } catch (InterruptedException e) {
                                                        e.printStackTrace();
                                                    }
                                                    continue;
                                                }

                                                String popStr = inputStack.pop();
                                                popStr = popStr + "\r\n";
                                                try {
                                                    sock.getOutputStream().write(popStr.getBytes());
                                                    System.out.println("Response: " + popStr);
                                                    break;
                                                } catch (IOException e) {
                                                    e.printStackTrace();
                                                    break;
                                                }
                                            }
                                            System.out.println("input RFID end.");
                                        }
                                    });
                                    respThread.start();

/*
                                boolean bContinue = false;

                                do {

                                    Future<String> future = executor.submit(new Callable<String>() {
                                        @Override
                                        public String call() throws Exception {
                                            System.out.println("请输入RFID响应：");
                                            try {

                                                String lineStr = scanner.nextLine(); // 读取用户输入的一行文本
                                              //  scanner.close();
                                                return lineStr;

                                            }catch(Exception e){
                                                e.printStackTrace();
                                                return "";
                                            }
                                        }
                                    });
                                    try {
                                        executor.awaitTermination(10, TimeUnit.SECONDS);
                                        String input = future.get();
                                       // future.cancel(true);
                                        if (sock.isConnected()) {
                                            bContinue = true;
                                        }else{
                                            bContinue = false;
                                        }
                                        if (StringUtils.isEmpty(input) && sock.isConnected()){
                                            bContinue = true;
                                            continue;
                                        }
                                        out = input + "\r\n";
                                        sock.getOutputStream().write(out.getBytes());
                                        System.out.println("Response: " + out);
                                    //    invokePlcWaitIn(200007107);
                                        bContinue= false;
                                    }
                                    catch (InterruptedException interruptedException) {
                                        interruptedException.printStackTrace();
                                        Thread.currentThread().interrupt();
                                    }

                                }while(bContinue);
*/
                            }
                            System.out.println("Response: " + out);
                        }
                        while (true);

                } catch (Exception e) {
                    e.printStackTrace();
                    cmdLine = null;
                }
            }
        });

    }


    private static void invokePlcWaitIn(int inputNumber) {
        inputNumber=190;
       // boolean ret = plc.setBool(2,inputNumber, true);
        //log.info("setBool({}), ret={}", inputNumber, ret);

       // boolean backRet = plc.getBool(2, inputNumber, 0);
       // log.info("getBool({}), ret={}", inputNumber, backRet);
        /*
        boolean ret = plc.setBool(0,inputNumber, true);
        log.info("setBool({}), ret={}", inputNumber, ret);

        boolean backRet = plc.getBool(0, inputNumber, 0);
        log.info("getBool({}), ret={}", inputNumber, backRet);
         */
    }

}
