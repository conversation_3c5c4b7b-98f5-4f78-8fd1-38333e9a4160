/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.rfid;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.driver.socket.CommandLineTcpSocket;

import java.io.IOException;
import java.util.function.Function;


/**
 * RFID Reader驱动
 * 固体放样RF IP: *************:47
 * 固体进样确认 RF IP: *************:47
 */
@Log4j2
public class RfidReaderDriver {

    private CommandLineTcpSocket sock = new CommandLineTcpSocket();

    /**
     * 初始化驱动，TCP连接建立
     *
     * @param ipAddr IP地址
     * @param port   端口号
     */
    public void init(String ipAddr, int port) throws IOException {
        try {
            finalize();
            sock.connect(ipAddr, port);
        }catch(IOException ex){
            log.error("IOException ex:{}, retry it.", ex.getMessage(), ex);
        }catch(Exception ex){
            log.error("Unknown exception ex:{}", ex.getMessage(), ex);
        }

    }

    /**
     * 清除指令
     */
    public void clear() throws IOException {
        sock.sendCommand("CLEAR", (lineStr) -> {
            return true;
        });
    }

    /**
     * 接收RFID字符串
     * @param callbackFun 回调函数
     * @throws IOException
     */
    public void receive(Function<String, Boolean> callbackFun) throws IOException {
        sock.receiveCommand(callbackFun);
    }

    /**
     * 结束，断开TCP连接
     */
    public void finalize() throws IOException {
        if (!sock.isClosed()) {
            sock.close();
        }
        sock = new CommandLineTcpSocket();
    }

}
