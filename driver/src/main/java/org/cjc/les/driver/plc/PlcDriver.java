/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.driver.plc;

/*
    PLC驱动接口，具体实现需要对应的硬件控制器通过C++ SDK完成
 */
public class PlcDriver {
    static {
        System.loadLibrary("PlcDriverJetter");
    }

    public final static int BT_IN = 0;
    public final static int BT_OUT = 1;
    public final static int BT_FLAG = 2;
    public final static int BT_REGBIT = 3;


    /**
     * 初始化驱动，连接控制器
     * @param config 控制器配置
     */
    public native boolean init(String config);

    /**
     * 执行PLC启停指令
     * @param cmd 启停指令
     *
    JET32_CMD_START_RAM   'N' ((re-)start program flow)
    JET32_CMD_STOP_AUTOMATIC  'P' (stop program flow)
    JET32_CMD_CONTINUE   'O' (continue program flow)
     */
    public native void execute(String cmd);

    /**
     * 获取连接状态
     * @return 连接状态描述,
     *  true:CONNECTED, false:DISCONNECTED
     */
    public native boolean getConnectionStatus();

    /**
     * 获取寄存器值
     * @param registerNumber 寄存器编号
     * @return 寄存器值
     */
    public native int getRegister(int registerNumber);

    /**
     * 设置寄存器值
     * @param registerNumber 寄存器编号
     * @param value 寄存器值
     */
    public native void setRegister(int registerNumber, int value);

    /**
     * 获取布尔值，
     * @param iBoolType 获取类型
     *      BT_IN   0  (input)
     *      BT_OUT   1  (output)
     *      BT_FLAG   2  (flag)
     *      BT_REGBIT  3  (register bit)
     * @param boolNumber 类型编号
     * @param registerBit 当类型为register3时有效，取值位数
     * @return 返回值 1:true/0:false, -负数异常码
     */
    public native int getBool(int iBoolType,  int boolNumber, int registerBit);

    /**
     * 设置布尔值
     * @param iBoolType 设置类型
     *                  BT_OUT   1  (output)
     *                  BT_FLAG   2  (flag)
     * @param boolNumber 类型编号
     * @param boolValue true/false
     * @return 设置成功为1:true,0:false, -负数异常码
     */
    public native int setBool(int iBoolType,  int boolNumber, boolean boolValue);

    /**
     * 获取寄存器字符串
     * (获取完整字符串)
     * @param registerNumber 寄存器编号
     * @return 获取完整字符串
     */
    public native String getString(int registerNumber);

    /**
     * 终止驱动，关闭控制器连接，回收资源
     */
    public native void finalize();
}
