/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.common.uniapp;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * @param <T> 业务内容详情
 *            提供给给前端APP统一格式
 * <AUTHOR>
 * @date 2023-3-8
 */
@Getter
@Setter
@RequiredArgsConstructor
public class UniappResponseEntity<T> {
    private long status = 200;
    private String message;
    private T data;

    public UniappResponseEntity(T data) {
        this.data = data;
    }

    public UniappResponseEntity(long status, String message) {
        this.status = status;
        this.message = message;
    }
}
