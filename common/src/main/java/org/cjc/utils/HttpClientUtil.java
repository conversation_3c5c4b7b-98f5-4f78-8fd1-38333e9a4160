/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.cjc.exception.HttpClientException;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

@Slf4j
public class HttpClientUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    static private CloseableHttpClient httpClient;
    static {
        httpClient = HttpClientBuilder.create().build();
    }

    static public <T, R> R httpPost(String url, T param, TypeReference<R> resultType) {
        HttpPost post = new HttpPost(url);
        String bodyStr = null;
        if (param instanceof String ) {
            bodyStr = (String)param;
        }else{
            bodyStr = JSON.toJSONString(param);
        }
        StringEntity entity = new StringEntity(bodyStr, "UTF-8");
        post.setEntity(entity);
        post.setHeader("Content-Type", "application/json;charset=utf8");
        return doRequest(post, param, resultType);

    }

    static public <T, R> R httpGet(String url, T param, TypeReference<R> resultType) {
        String queryParams = convertToQueryParams(param);
        URI uri = null;
        try {
            uri = new URI(url + (queryParams.isEmpty() ? "" : "?" + queryParams));
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        HttpGet get = new HttpGet(uri);
        return doRequest(get, param, resultType);
    }

    static public <T, R> R doRequest(HttpUriRequest request, T param, TypeReference<R> resultType) {

        CloseableHttpResponse response = null;

        try {
            response = httpClient.execute(request);
            HttpEntity responseEntity = response.getEntity();
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(responseEntity);
                return JSON.parseObject(result, resultType.getType());
            } else {
                throw new HttpClientException(response.getStatusLine().getStatusCode(),
                        "Response failed status:" + response.getStatusLine().getStatusCode()
                                + EntityUtils.toString(responseEntity));
            }
        } catch (IOException e) {
            log.error("Response exception, e.errMsg=" + e.getMessage(), e);
            throw new HttpClientException("Response exception, e.errMsg=" + e.getMessage(), e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("response close exception,,e.msg" + e.getMessage(), e);
                }
            }
        }

    }

    private static <T> String convertToQueryParams(T param) {
        if (param == null || StringUtils.isEmpty(param.toString())) {
            return "";
        }

        if (param instanceof String) {
            JSONObject jsonObject = JSON.parseObject((String) param);
            return jsonObject.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .map(entry -> {
                        try {
                            return URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.name()) + "=" +
                                    URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8.name());
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        return null;
                    })
                    .collect(Collectors.joining("&"));
        }

        return "";
    }

    public static void closeClient() {
        try {
            httpClient.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("Close client error:" + e.getMessage(), e);
        }
    }

}
