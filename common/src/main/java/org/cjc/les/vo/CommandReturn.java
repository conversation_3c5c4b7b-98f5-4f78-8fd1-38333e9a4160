/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;

/**
 * 命令返回对象定义，是@Command注解方法的标准返回对象，
 * 用户再构建需要返回自定义结果时，可以使用它
 * @param <T> 用户自定义结果
 */
@Data
public class CommandReturn<T> {

    /**
     * 自定义结果
     */
    private T result;

    /**
     * 运行状态, 系统内部控制任务运行时
     */
    private RunStatusEnum status = RunStatusEnum.READY;

    /**
     * 返回错误码, 用户端处理返回值
     */
    private ErrorCodeEnum errorCode = ErrorCodeEnum.SUCCESS;

    /**
     * 返回错误详情
     */
    private String errorMsg;

    private FailedThenEnum failedThen = FailedThenEnum.PAUSE;


    public void copy(Object source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

}
