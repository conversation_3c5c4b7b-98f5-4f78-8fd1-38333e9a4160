/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.vo;

import lombok.Data;
import org.cjc.les.constants.ErrorLevelEnum;

@Data
public class CommonResponseEntity<T> {

    /**
     * 自定义结果数据
     */
    private T data;

    /**
     * 错误级别
     */
    private ErrorLevelEnum errorLevel = ErrorLevelEnum.SUCCESS;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 返回错误详情
     */
    private String errorMsg;
}
