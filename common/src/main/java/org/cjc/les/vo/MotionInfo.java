/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.vo;

import lombok.Data;

/**
 * 机器人运动信息
 */
@Data
public class MotionInfo {

    public enum StatusEnum {
        IDLE,
        ERROR,
        NO_CONNECT
    }

    private StatusEnum status;
    private String errorCode;
    private String errorMsg;

    private double speed;

    private double xpos;
    private double ypos;
    private double zpos;


    /**
     * 门锁状态： -1,连接异常，
     *         3: 门打开且锁已打开
     *         2: 门打开但已上锁 (异常状态)
     *         1: 门关闭但锁已打开
     *         0: 门关闭且已上锁
     *         DOOR_BIT/LOCK_BIT: 11 DoorOpened and UnLocked, 10 DoorOpened and locked: Should NOT BE in this statues, 01 DoorClosed and Unlocked, 00 DoorClosed and locked
     */
    private int doorStatus;

}
