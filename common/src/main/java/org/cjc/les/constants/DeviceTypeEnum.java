/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.constants;

/**
 * 设备类型,
 * SYS: 系统软件内部虚拟的设备,通常用来执行系统等待，数据存储，调度协调等指令;
 * ROBOT: 机器人设备，用来搬运物料,
 * CONTROL: 控制设备如PLC等, 用来控制外设的启停,通常不具有点位属性;
 * PERIPHERAL:外设如各种检测仪器, 台架等，一般具有点位属性,是控制设备操作的对象
 * STORAGE: 存储设备，用来存储物料，如烧杯架，样品架等.
 * FRAME: 框架设备，用于外框控制，如加解锁等
 */
public enum DeviceTypeEnum {
    SYS,
    ROBOT,
    CONTROL,
    PERIPHERAL,
    STORAGE,
    FRAME
}
