/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.constants;

/**
 * 预处理任务状态枚举类
 */
public enum TaskPrepareStatusEnum {
    DRAFT, // 草稿状态，初始创建
    WAIT_TAG_INPUT, // 等待用户扫描TAG标签(临时状态，不入库)
    READY, // 已完成样本与流程绑定，并录入了TAG标签，准备推送进样
    WAIT_CONVEYOR_INPUT, // 等待样品进入传送带(临时状态，不入库)
    PUSHED, // 已推送进入任务执行队列
    SUCCESS, // 处理完成
    ERROR, // 错误
    CANCELLED // 已撤销，用户手工执行撤销操作
}
