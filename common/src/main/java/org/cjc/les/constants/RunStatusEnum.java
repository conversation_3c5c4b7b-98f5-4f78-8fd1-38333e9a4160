/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.constants;

public enum RunStatusEnum {
    DRAFT, // 草稿状态，在提交进样时的状态，
    READY, // 初始状态，准备运行
    IN_SCHEDULE_QUE, // 在调度队列中, 当需要异步执行时，先推送到执行队列，并设置该状态
    IN_RUNNING_QUE, // 在运行队列中等待
    RUNNING, // 执行中，该任务下任意指令运行状态为RUNNING,则任务状态为RUNNING
    SUCCESS_WITH_FORKED, // 带有FORKED状态的成功标识，表示其下有异步子步骤，动作未完成，其他都是成功状态
    SUCCESS, // 成功结束
    FAILED,  // 失败结束
    STOPPED, // 已停止,
             // 该任务未完成(not in SUCCESS/FAILED/CANCELLED),但是不在内存任务队列中，则设置为已停止
    PAUSED, // 已暂停，需要人工干预解锁，当用户手工干预(如打开门锁)，当前正在任务队列中的未完成态任务，设置为暂停状态
    SKIPPED, // 已跳过，无需执行，当不满足预设条件并选择SKIP时，或者调试中选择跳过
    FORKED, // 异步分支开启状态，保留
    SUSPEND, // 挂起，调度器发现不满足运行条件，暂时挂起，等待条件满足后，自动恢复为IN_SCHEDULE_QUE
    CANCELLED, // 已撤销，用户手工执行撤销操作, 或者不满足任务进样条件，系统自动撤销
    BREAK // 断点状态，用于任务调试
}
