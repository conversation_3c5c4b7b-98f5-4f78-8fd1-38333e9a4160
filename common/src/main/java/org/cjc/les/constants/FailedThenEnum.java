/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.constants;

/**
 * 命令失败后的处理方式
 */
public enum FailedThenEnum {
    PAUSE, // 暂停，等待人工选择处理方式，, 默认, 未知异常时，让人工确认执行方式，防止误操作导致撞击.
    CONTINUE, //继续下一个命令,
    RETRY, //重试,
    THROW, // 往上层调用方抛出异常，交给调用节点处理异常，若无处理异常方法，直接结束进程任务
    RETURN_ACTION, //返回到ACTION,
    RETURN_STEP, //返回到步骤,
    RETURN_METHOD, //返回到方法,
    RETURN_PROCEDURE // 返回到进程
}
