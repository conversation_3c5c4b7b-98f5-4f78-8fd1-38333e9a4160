/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.exception;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import lombok.Setter;
import org.cjc.exception.ApplicationException;

@Getter
@Setter
public class ConfigApplicationException extends ApplicationException {

    private Object data;

    public ConfigApplicationException(String errorCode) {
        super(errorCode);
    }

    public ConfigApplicationException(String errorCode, String message) {
        super(errorCode, message);
    }

    @Override
    public String getMessage() {
        String msg = super.getMessage();
        if (data != null) {
            msg = msg + ", data: " + JSON.toJSONString(data);
        }
        return msg;
    }
}
