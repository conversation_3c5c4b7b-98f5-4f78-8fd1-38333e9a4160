/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Variable {
    /**
     * 变量名，命名规则：大写英文字母+"_"
     * @return
     */
    String name() default "";

    /**
     * 变量类型, Number, String, Object
     * @return
     */
    String type() default "";

    /**
     * USER: 用户范围，用户界面可修改值
     * SYS： 系统范围，用户界面不可修改
     * @return
     */
    String scope() default "SYS";

    /**
     * 变量提示信息
     * @return
     */
    String tip() default "";

}
