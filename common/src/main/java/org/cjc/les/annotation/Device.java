/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.annotation;

import org.cjc.les.constants.DeviceTypeEnum;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 设备注解，该注解用来识别包含驱动的实现类，通常该设备实现类至少包含一个以上@Command注解的命令方法
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Device {
    /**
     * 设备名称,在用户界面展示， zh_CN=***,en_CS=***
     * 无指定格式，直接显示未原值
     * @return
     */
    @AliasFor("value")
    String name() default "";
    @AliasFor("name")
    String value() default "";

    /**
     * 设备描述
     * @return
     */
    String description() default "";

    /**
     * 设备类型，默认为外设
     * @return
     */
    DeviceTypeEnum type() default DeviceTypeEnum.PERIPHERAL;

}
