/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.annotation;


import org.cjc.les.constants.CommandTypeEnum;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 命令注解，用来标识该设备具有的命令方法，该方法必须是被@Device注解类的方法;
 * 通常该设备应该包含命令类型为CommandTypeEnum.INIT和CommandTypeEnum.FINALIZE的注解方法（特别是具有网路驱动的设备）;
 * 该命令注解的方法参数,应该通过@Parameter注解，增加对参数名称定义及描述;
 * 该命令注解的方法返回类型，应该为void,boolean类型;
 * 该命令注解的方法异常，应该被抛出（特别是未知异常或者异常影响整个业务流程的完整性）
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Command {
    /**
     * 命令名称,在用户界面展示， zh_CN=***,en_CS=***
     *      * 无指定格式，直接显示未原值
     * @return
     */
    @AliasFor("value")
    String name() default "";
    @AliasFor("name")
    String value() default "";

    /**
     * 命令类型
     * @return
     */
    CommandTypeEnum type() default CommandTypeEnum.NORMAL;

    /**
     * 命令描述
     * @return
     */
    String description() default "";

    /**
     * 过期时长，默认-1永不过期，0表示立即返回，其他整数表示最多等待多长时间
     * @return
     */
    int expireTime() default -1;

    /**
     * 命令执行失败后，执行的动作, 默认CONTINUE:继续下一个命令, RETRY:重试, RETURN_ACTION:返回到ACTION, RETURN_METHOD: 返回到方法, RETURN_PROCEDURE:返回到进程
     * @return
     */
    String failedThen() default "CONTINUE";

    /**
     * 失败重试次数，当failedThen==RETRY时有效
     * @return
     */
    int retries() default 0;
}
