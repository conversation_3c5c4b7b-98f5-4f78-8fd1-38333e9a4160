/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Function {
    /**
     * 函数名，命名规则：大写英文字母+"_"
     * @return
     */
    String name() default "";

    /**
     * 函数提示信息
     * @return
     */
    String tip() default "";

    /**
     * 韩式使用说明
     * @return
     */
    String usage() default "";

    /**
     * 使用举例
     * @return
     */
    String example() default "";
}
