/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.annotation;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 命令或者设备配置参数的注解
 */
@Target({ElementType.TYPE,ElementType.PARAMETER,ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public  @interface  Parameter {
    /**
     * 参数名称
     * @return
     */
    @AliasFor("value")
    String name() default "";
    @AliasFor("name")
    String value() default "";

    /**
     * 参数描述
     * @return
     */
    String description() default "";

    /**
     * 参数默认值
     * @return
     */
    String defaultValue() default "";

    /**
     * 待选值列表
     * @return
     */
    String[] candidateValues() default {};

    /**
     * 参数输入页面控件，默认input文本框, 现有实现可选控件为input, select, position-select, position-input, lock-select
     * @return
     */
    String inputComponent() default "input";

    /**
     * 输入值校验规则
     * @return
     */
    String validateRules() default "";

}
