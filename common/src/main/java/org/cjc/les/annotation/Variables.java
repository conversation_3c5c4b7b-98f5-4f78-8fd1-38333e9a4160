/*
 *  Copyright 2025-2025 <PERSON>han Annis Robot Co., Ltd. All rights reserved.
 */
package org.cjc.les.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Variables {
    Variable[] value() default {};
}
