/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.repository.impl;

import org.cjc.repository.EnhancedJpaRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import java.util.List;

@NoRepositoryBean
public class EnhancedJpaRepositoryImpl<T, ID> extends SimpleJpaRepository<T, ID> implements EnhancedJpaRepository<T> {

    private  final EntityManager em;

    public EnhancedJpaRepositoryImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.em = entityManager;
    }

    public EnhancedJpaRepositoryImpl(Class<T> domainClass, EntityManager em) {
        super(domainClass, em);
        this.em = em;
    }

    @Override
    public List<T> findAllTop(Specification<T> spec, Sort sort, Integer limit) {
        TypedQuery<T> query = this.getQuery(spec, sort);
        query.setMaxResults(limit);
        return query.getResultList();
    }
}