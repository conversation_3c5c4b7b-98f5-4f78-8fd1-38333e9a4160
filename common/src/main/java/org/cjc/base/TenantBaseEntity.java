/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.base;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;

/**
 * 带有tenant_id字段的通用实体基类
 * <AUTHOR>
 * @Date 2021年6月3日 14:57
 */
@Getter
@Setter
@MappedSuperclass
public class TenantBaseEntity extends BaseEntity{

    @Column(name = "tenant_id")
    @Schema(description = "租户ID", hidden = true)
    private Long tenantId=0L;

}
