/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.base;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;

@Data
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@MappedSuperclass
public class LesConfigBaseEntity extends LesBaseEntity{

    /**
     * 软删除标识
     */
    private String deleteFlag = "N";

    @PrePersist
    @PreUpdate
    private void ensureDeleteFlag() {
        if (this.deleteFlag == null) {
            this.deleteFlag = "N";
        }
    }
}
