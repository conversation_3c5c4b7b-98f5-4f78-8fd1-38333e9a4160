/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.exception;

public abstract class ApplicationException extends Exception {
    private String errorCode;
    public ApplicationException(String errorCode) {
        super();
        this.errorCode = errorCode;
    }
    public ApplicationException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
