#!/bin/bash

echo "========================================"
echo "ProGuard混淆构建脚本"
echo "========================================"
echo

show_menu() {
    echo "请选择混淆方案:"
    echo "1. 方案一: 统一在主模块混淆 (推荐)"
    echo "2. 方案二: 分模块混淆后统一打包"
    echo "3. 清理所有构建产物"
    echo "4. 退出"
    echo
}

plan1() {
    echo
    echo "========================================"
    echo "执行方案一: 统一在主模块混淆"
    echo "========================================"
    echo
    echo "1. 清理项目..."
    mvn clean

    echo
    echo "2. 编译所有模块..."
    mvn compile

    echo
    echo "3. 打包所有模块..."
    mvn package -DskipTests

    echo
    echo "4. 在system模块执行ProGuard混淆..."
    cd system
    mvn install -DskipTests
    cd ..

    echo
    echo "========================================"
    echo "方案一构建完成！"
    echo "混淆后的可执行jar位于: system/target/les-admin-obfuscated.jar"
    echo "========================================"
}

plan2() {
    echo
    echo "========================================"
    echo "执行方案二: 分模块混淆后统一打包"
    echo "========================================"
    echo
    echo "1. 清理项目..."
    mvn clean

    echo
    echo "2. 编译所有模块..."
    mvn compile

    echo
    echo "3. 分别混淆各个子模块..."
    echo "混淆common模块..."
    cd common
    mvn package -DskipTests
    cd ..

    echo "混淆logging模块..."
    cd logging
    mvn package -DskipTests
    cd ..

    echo "混淆tools模块..."
    cd tools
    mvn package -DskipTests
    cd ..

    echo "混淆system-api模块..."
    cd system-api
    mvn package -DskipTests
    cd ..

    echo "混淆les-core模块..."
    cd les-core
    mvn package -DskipTests
    cd ..

    echo "混淆driver模块..."
    cd driver
    mvn package -DskipTests
    cd ..

    echo "混淆generator模块..."
    cd generator
    mvn package -DskipTests
    cd ..

    echo "混淆les-mgt模块..."
    cd les-mgt
    mvn package -DskipTests
    cd ..

    echo
    echo "4. 在system模块统一打包..."
    cd system
    mvn install -DskipTests
    cd ..

    echo
    echo "========================================"
    echo "方案二构建完成！"
    echo "混淆后的可执行jar位于: system/target/les-admin-obfuscated.jar"
    echo "各子模块混淆jar位于各自的target目录"
    echo "========================================"
}

clean_all() {
    echo
    echo "========================================"
    echo "清理所有构建产物..."
    echo "========================================"
    mvn clean
    echo "清理完成！"
}

while true; do
    show_menu
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1)
            plan1
            ;;
        2)
            plan2
            ;;
        3)
            clean_all
            ;;
        4)
            echo "退出构建脚本"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
    
    echo
    read -p "按回车键继续..."
    echo
done
