<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="主键ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="点位ID" prop="positionId">
            <el-input v-model="form.positionId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="关联ROBOT设备实例ID" prop="robotDevInstanceId">
            <el-input v-model="form.robotDevInstanceId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="机器人定义的位置编号，用于机器人点位唯一识别">
            <el-input v-model="form.robotPosCode" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="xpos">
            <el-input v-model="form.xpos" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="ypos">
            <el-input v-model="form.ypos" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="zpos">
            <el-input v-model="form.zpos" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="保留">
            <el-input v-model="form.status" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="upos">
            <el-input v-model="form.upos" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="vpos">
            <el-input v-model="form.vpos" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="wpos">
            <el-input v-model="form.wpos" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否已被删除,Y/N">
            <el-input v-model="form.deleteFlag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updateBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="主键ID" />
        <el-table-column prop="positionId" label="点位ID" />
        <el-table-column prop="robotDevInstanceId" label="关联ROBOT设备实例ID" />
        <el-table-column prop="robotPosCode" label="机器人定义的位置编号，用于机器人点位唯一识别" />
        <el-table-column prop="xpos" label="xpos" />
        <el-table-column prop="ypos" label="ypos" />
        <el-table-column prop="zpos" label="zpos" />
        <el-table-column prop="status" label="保留" />
        <el-table-column prop="upos" label="upos" />
        <el-table-column prop="vpos" label="vpos" />
        <el-table-column prop="wpos" label="wpos" />
        <el-table-column prop="deleteFlag" label="是否已被删除,Y/N" />
        <el-table-column prop="createBy" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateBy" label="更新人" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','positionRobot:edit','positionRobot:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudPositionRobot from '@/api/positionRobot'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, positionId: null, robotDevInstanceId: null, robotPosCode: null, xpos: null, ypos: null, zpos: null, status: null, upos: null, vpos: null, wpos: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
export default {
  name: 'PositionRobot',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '机器人点位配置', url: 'api/positionRobot', idField: 'id', sort: 'id,desc', crudMethod: { ...crudPositionRobot }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'positionRobot:add'],
        edit: ['admin', 'positionRobot:edit'],
        del: ['admin', 'positionRobot:del']
      },
      rules: {
        positionId: [
          { required: true, message: '点位ID不能为空', trigger: 'blur' }
        ],
        robotDevInstanceId: [
          { required: true, message: '关联ROBOT设备实例ID不能为空', trigger: 'blur' }
        ]
      }    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
