@echo off
echo ========================================
echo 验证ProGuard混淆效果
echo ========================================
echo.

set ORIGINAL_JAR=system\target\system-1.0.jar
set OBFUSCATED_JAR=system\target\les-admin-obfuscated.jar

if not exist "%ORIGINAL_JAR%" (
    echo 错误: 原始jar文件不存在: %ORIGINAL_JAR%
    echo 请先运行构建脚本
    pause
    exit /b 1
)

if not exist "%OBFUSCATED_JAR%" (
    echo 错误: 混淆后jar文件不存在: %OBFUSCATED_JAR%
    echo 请先运行混淆构建脚本
    pause
    exit /b 1
)

echo 1. 检查文件大小...
for %%A in ("%ORIGINAL_JAR%") do set ORIGINAL_SIZE=%%~zA
for %%A in ("%OBFUSCATED_JAR%") do set OBFUSCATED_SIZE=%%~zA

echo 原始jar大小: %ORIGINAL_SIZE% 字节
echo 混淆jar大小: %OBFUSCATED_SIZE% 字节
echo.

echo 2. 检查jar文件内容...
echo 原始jar中的类文件数量:
jar tf "%ORIGINAL_JAR%" | find /c ".class"

echo 混淆jar中的类文件数量:
jar tf "%OBFUSCATED_JAR%" | find /c ".class"
echo.

echo 3. 检查主类是否存在...
jar tf "%OBFUSCATED_JAR%" | findstr "org/cjc/AppRun.class"
if %errorlevel% equ 0 (
    echo ✓ 主类AppRun存在
) else (
    echo ✗ 主类AppRun不存在
)
echo.

echo 4. 检查Spring Boot Loader...
jar tf "%OBFUSCATED_JAR%" | findstr "org/springframework/boot/loader"
if %errorlevel% equ 0 (
    echo ✓ Spring Boot Loader存在
) else (
    echo ✗ Spring Boot Loader不存在
)
echo.

echo 5. 检查MANIFEST.MF...
jar xf "%OBFUSCATED_JAR%" META-INF/MANIFEST.MF
if exist "META-INF\MANIFEST.MF" (
    echo MANIFEST.MF内容:
    type "META-INF\MANIFEST.MF"
    del /q "META-INF\MANIFEST.MF"
    rmdir "META-INF"
) else (
    echo ✗ MANIFEST.MF不存在
)
echo.

echo 6. 检查混淆映射文件...
if exist "obfuscate\print_mapping.txt" (
    echo ✓ 混淆映射文件存在
    echo 映射文件大小:
    for %%A in ("obfuscate\print_mapping.txt") do echo %%~zA 字节
) else (
    echo ✗ 混淆映射文件不存在
)
echo.

echo 7. 检查混淆种子文件...
if exist "obfuscate\print_seeds.txt" (
    echo ✓ 混淆种子文件存在
    echo 种子文件大小:
    for %%A in ("obfuscate\print_seeds.txt") do echo %%~zA 字节
) else (
    echo ✗ 混淆种子文件不存在
)
echo.

echo ========================================
echo 验证完成！
echo 如果要测试应用是否能正常运行，请执行:
echo java -jar %OBFUSCATED_JAR%
echo ========================================
pause
