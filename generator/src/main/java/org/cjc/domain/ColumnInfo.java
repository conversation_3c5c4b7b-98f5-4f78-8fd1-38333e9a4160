/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.cjc.utils.GenUtil;
import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 列的数据信息
 * <AUTHOR>
 * @date 2019-01-02
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "code_column_config")
public class ColumnInfo implements Serializable {

    @Id
    @Column(name = "column_id")
    @Schema(description = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "数据库字段名称")
    private String columnName;

    @Schema(description = "数据库字段类型")
    private String columnType;

    @Schema(description = "数据库字段键类型")
    private String keyType;

    @Schema(description = "字段额外的参数")
    private String extra;

    @Schema(description = "数据库字段描述")
    private String remark;

    @Schema(description = "是否必填")
    private Boolean notNull;

    @Schema(description = "是否在列表显示")
    private Boolean listShow;

    @Schema(description = "是否表单显示")
    private Boolean formShow;

    @Schema(description = "表单类型")
    private String formType;

    @Schema(description = "查询 1:模糊 2：精确")
    private String queryType;

    @Schema(description = "字典名称")
    private String dictName;

    @Schema(description = "日期注解")
    private String dateAnnotation;

    public ColumnInfo(String tableName, String columnName, Boolean notNull, String columnType, String remark, String keyType, String extra) {
        this.tableName = tableName;
        this.columnName = columnName;
        this.columnType = columnType;
        this.keyType = keyType;
        this.extra = extra;
        this.notNull = notNull;
        if(GenUtil.PK.equalsIgnoreCase(keyType) && GenUtil.EXTRA.equalsIgnoreCase(extra)){
            this.notNull = false;
        }
        this.remark = remark;
        this.listShow = true;
        this.formShow = true;
    }
}
