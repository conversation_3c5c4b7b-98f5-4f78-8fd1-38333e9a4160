/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 代码生成配置
 * <AUTHOR>
 * @date 2019-01-03
 */
@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "code_gen_config")
public class GenConfig implements Serializable {

    public GenConfig(String tableName) {
        this.tableName = tableName;
    }

    @Id
    @Column(name = "config_id")
    @Schema(description = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "接口名称")
    private String apiAlias;

    @NotBlank
    @Schema(description = "包路径")
    private String pack;

    @NotBlank
    @Schema(description = "模块名")
    private String moduleName;

    @NotBlank
    @Schema(description = "前端文件路径")
    private String path;

    @Schema(description = "前端文件路径")
    private String apiPath;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "表前缀")
    private String prefix;

    @Schema(description = "是否覆盖")
    private Boolean cover = false;
}
