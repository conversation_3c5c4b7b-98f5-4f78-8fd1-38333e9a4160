/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service;

import org.cjc.modules.system.domain.SysUserAddress;
import org.cjc.modules.system.service.dto.SysUserAddressDto;
import org.cjc.modules.system.service.dto.SysUserAddressQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-12-16
**/
public interface SysUserAddressService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SysUserAddressQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SysUserAddressDto>
    */
    List<SysUserAddressDto> queryAll(SysUserAddressQueryCriteria criteria);

    /**
     * 通过用户ID查找收货地址
     * @param userId 用户ID
     * @return List<SysUserAddressDto>
     */
    List<SysUserAddressDto> findByUserId(Long userId);

    /**
     * 根据ID查询
     * @param addressId ID
     * @return SysUserAddressDto
     */
    SysUserAddressDto findById(Integer addressId);

    /**
    * 创建
    * @param resources /
    * @return SysUserAddressDto
    */
    SysUserAddressDto create(SysUserAddress resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(SysUserAddress resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Integer[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SysUserAddressDto> all, HttpServletResponse response) throws IOException;
}