/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-16
**/
@Data
public class SysRegionDto implements Serializable {

    /** 区划信息ID */
    private Integer id;

    /** 区划名称 */
    private String name;

    /** 父级ID */
    private Integer pid;

    /** 区划编码 */
    private String code;

    /** 层级(1省级 2市级 3区/县级) */
    private Integer level;

    private List<SysRegionDto> children;
}