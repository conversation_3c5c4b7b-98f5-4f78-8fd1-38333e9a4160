/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.BaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-19
**/
@Entity
@Data
@Table(name="sys_user_grade")
public class UserGrade extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "grade_id")
    @Schema(description = "ID")
    private Long gradeId;

    @Column(name = "tenant_id")
    @Schema(description = "租户ID")
    private Long tenantId;

    @Column(name = "name",unique = true)
    @Schema(description = "等级名称")
    private String name;

    @Column(name = "weight")
    @Schema(description = "等级权重(1-9999)")
    private Integer weight;

    @Column(name = "upgrade")
    @Schema(description = "升级条件")
    private String upgrade;

    @Column(name = "equity")
    @Schema(description = "等级权益(折扣率0-100)")
    private String equity;

    @Column(name = "enabled")
    @Schema(description = "状态：1启用、0禁用")
    private Long enabled;

    public void copy(UserGrade source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}