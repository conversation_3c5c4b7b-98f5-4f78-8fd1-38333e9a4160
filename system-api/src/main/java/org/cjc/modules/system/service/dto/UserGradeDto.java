/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-19
**/
@Data
public class UserGradeDto implements Serializable {

    /** ID */
    private Long gradeId;

    /** 租户ID */
    private Long tenantId;

    /** 等级名称 */
    private String name;

    /** 等级权重(1-9999) */
    private Integer weight;

    /** 升级条件 */
    private String upgrade;

    /** 等级权益(折扣率0-100) */
    private String equity;

    /** 状态：1启用、0禁用 */
    private Long enabled;

    /** 创建日期 */
    private Timestamp createTime;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 更新者 */
    private String updateBy;
}