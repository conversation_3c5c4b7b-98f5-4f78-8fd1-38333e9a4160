package org.cjc.modules.system.utils;

import org.cjc.modules.security.service.dto.OnlineUserDto;

import jakarta.servlet.http.HttpServletRequest;

public class RequestHolder {

    static private ThreadLocal<HttpServletRequest> httpServletRequest = new ThreadLocal<>();
    static private ThreadLocal<OnlineUserDto> onlineUser = new ThreadLocal<>();

    static public void initRequest(HttpServletRequest request) {
        httpServletRequest.set(request);
    }

    static public void initOnlineUser(OnlineUserDto user) {
        onlineUser.set(user);
    }

    static public HttpServletRequest getCurrentRequest() {
        return httpServletRequest.get();
    }

    static public OnlineUserDto getOnlineUser() {
        return onlineUser.get();
    }
}
