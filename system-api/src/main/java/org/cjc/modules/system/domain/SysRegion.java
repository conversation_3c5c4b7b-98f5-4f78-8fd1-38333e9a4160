/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-16
**/
@Entity
@Data
@Table(name="sys_region")
public class SysRegion implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Schema(description = "区划信息ID")
    private Integer id;

    @Column(name = "name",nullable = false)
    @NotBlank
    @Schema(description = "区划名称")
    private String name;

    @Column(name = "pid",nullable = false)
    @NotNull
    @Schema(description = "父级ID")
    private Integer pid;

    @Column(name = "code",nullable = false)
    @NotBlank
    @Schema(description = "区划编码")
    private String code;

    @Column(name = "level",nullable = false)
    @NotNull
    @Schema(description = "层级(1省级 2市级 3区/县级)")
    private Integer level;

    public void copy(SysRegion source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}