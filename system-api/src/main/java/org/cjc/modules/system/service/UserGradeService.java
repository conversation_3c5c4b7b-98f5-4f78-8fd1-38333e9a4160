/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service;

import org.cjc.modules.system.domain.UserGrade;
import org.cjc.modules.system.service.dto.UserGradeDto;
import org.cjc.modules.system.service.dto.UserGradeQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-03-19
**/
public interface UserGradeService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(UserGradeQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<UserGradeDto>
    */
    List<UserGradeDto> queryAll(UserGradeQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param gradeId ID
     * @return UserGradeDto
     */
    UserGradeDto findById(Long gradeId);

    /**
    * 创建
    * @param resources /
    * @return UserGradeDto
    */
    UserGradeDto create(UserGrade resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(UserGrade resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<UserGradeDto> all, HttpServletResponse response) throws IOException;
}