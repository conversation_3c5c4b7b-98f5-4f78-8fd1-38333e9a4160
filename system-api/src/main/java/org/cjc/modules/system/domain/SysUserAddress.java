/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-16
**/
@Entity
@Data
@Table(name="sys_user_address")
public class SysUserAddress implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "address_id")
    @Schema(description = "主键ID")
    private Integer addressId;

    @Column(name = "name",nullable = false)
    @NotBlank
    @Schema(description = "收货人姓名")
    private String name;

    @Column(name = "phone",nullable = false)
    @NotBlank
    @Schema(description = "联系电话")
    private String phone;

    @Column(name = "province_id")
    @Schema(description = "省份ID")
    private Integer provinceId;

    @Column(name = "city_id")
    @Schema(description = "城市ID")
    private Integer cityId;

    @Column(name = "region_id")
    @Schema(description = "区/县ID")
    private Integer regionId;

    @Column(name = "detail",nullable = false)
    @NotBlank
    @Schema(description = "详细地址")
    private String detail;

    @Column(name = "user_id",nullable = false)
    @NotNull
    @Schema(description = "用户ID")
    private Integer userId;

    @Column(name = "is_delete",nullable = false)
    @NotNull
    @Schema(description = "是否删除")
    private Integer isDelete;

    @Column(name = "tenant_id",nullable = false)
    @NotNull
    @Schema(description = "租户ID")
    private Integer tenantId;

    @Column(name = "create_time",nullable = false)
    @NotNull
    @Schema(description = "创建时间")
    private Integer createTime;

    @Column(name = "update_time",nullable = false)
    @NotNull
    @Schema(description = "更新时间")
    private Integer updateTime;

    public void copy(SysUserAddress source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}