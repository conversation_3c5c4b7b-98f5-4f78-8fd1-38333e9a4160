/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.cjc.modules.system.service.dto;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import org.cjc.base.BaseDTO;
import org.cjc.modules.security.service.dto.OnlineUserDto;
import org.cjc.utils.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-11-23
 */
@Getter
@Setter
public class UserRegisterDto extends BaseDTO implements Serializable {

    private Long id;

    private Set<RoleSmallDto> roles;

    private Set<JobSmallDto> jobs;

    private DeptSmallDto dept;

    private OnlineUserDto onlineUser;

    private Long deptId;

    private String username;

    private String nickName;

    private String email;

    private String phone;

    private String gender;

    private String avatarName;

    private String avatarPath;

    private String password;

    private Boolean enabled;

    @JSONField(serialize = false)
    private Boolean isAdmin = false;

    private Date pwdResetTime;

    private String lastLoginInfo;

    public void setLastLoginInfo(String info) {
        this.lastLoginInfo = info;
        if (StringUtils.isEmpty(info)) {
            return;
        }
        this.onlineUser = JSON.parseObject(info, OnlineUserDto.class);
    }
    public String getLastLoginInfo(){
        return lastLoginInfo;
    }
}
