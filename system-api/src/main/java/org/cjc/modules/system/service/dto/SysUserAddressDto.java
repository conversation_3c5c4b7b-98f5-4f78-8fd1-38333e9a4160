/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-12-16
**/
@Data
public class SysUserAddressDto implements Serializable {

    /** 主键ID */
    private Integer addressId;

    /** 收货人姓名 */
    private String name;

    /** 联系电话 */
    private String phone;

    /** 省份ID */
    private Integer provinceId;

    /** 城市ID */
    private Integer cityId;

    /** 区/县ID */
    private Integer regionId;

    /** 详细地址 */
    private String detail;

    /** 用户ID */
    private Integer userId;

    /** 是否删除 */
    private Integer isDelete;

    /** 租户ID */
    private Integer tenantId;

    /** 创建时间 */
    private Integer createTime;

    /** 更新时间 */
    private Integer updateTime;
}