<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.cjc</groupId>
        <artifactId>les-admin</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tools</artifactId>
    <name>工具模块</name>

    <properties>
        <mail.version>1.4.7</mail.version>
        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <alipay.version>4.9.153.ALL</alipay.version>
    </properties>

    <dependencies>
        <!-- 同时需要common模块和logging模块只需要引入logging模块即可 -->
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>logging</artifactId>
            <version>1.0</version>
        </dependency>

        <!--邮件依赖-->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>${mail.version}</version>
        </dependency>

        <!--七牛云存储-->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>

        <!--支付宝依赖-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>${alipay.version}</version>
        </dependency>
    </dependencies>

</project>