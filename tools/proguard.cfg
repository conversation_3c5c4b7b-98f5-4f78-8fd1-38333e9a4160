# 保留所有类不混淆，适用于工具模块
-keep class * {
    *;
}

# 保留Spring相关类
-keep class org.springframework.** { *; }
-keep class org.cjc.** { *; }

# 保留工具类
-keep class org.cjc.utils.** { *; }

# 保留邮件相关类
-keep class javax.mail.** { *; }

# 保留七牛云相关类
-keep class com.qiniu.** { *; }

# 保留支付宝相关类
-keep class com.alipay.** { *; }

# 保留实体类和DTO
-keep class **.*Entity { *; }
-keep class **.*DTO { *; }
-keep class **.*Dto { *; }
-keep class **.*VO { *; }
-keep class **.*Vo { *; }

# 保留Service和Controller
-keep class **.*Service { *; }
-keep class **.*ServiceImpl { *; }
-keep class **.*Controller { *; }

# 保留配置类
-keep @org.springframework.context.annotation.Configuration class * { *; }
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * { *; }

# 保留注解
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# 不混淆枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留序列化相关
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
