/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_cjc_utils_RobotDriver */

#ifndef _Included_org_cjc_utils_RobotDriver
#define _Included_org_cjc_utils_RobotDriver
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_cjc_utils_RobotDriver
 * Method:    init
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_cjc_utils_RobotDriver_init
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_utils_RobotDriver
 * Method:    execute
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_cjc_utils_RobotDriver_execute
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_utils_RobotDriver
 * Method:    run
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_cjc_utils_RobotDriver_run
  (JNIEnv *, jobject, jstring);

/*
 * Class:     org_cjc_utils_RobotDriver
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_org_cjc_utils_RobotDriver_finalize
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
