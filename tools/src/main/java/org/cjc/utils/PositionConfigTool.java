package org.cjc.utils;

public class PositionConfigTool {

    public static void main(String[] args) {

        double deta = 0.001;
        long sleepTime = 250;
        double base = (float)sleepTime / 1000;
        double curSpeed = deta / ((float)sleepTime / 1000);
        System.out.println("curSpeed="+curSpeed);

        System.out.println("Water Soluble Positions:");
        calculate( new double[]{965.2, 678.2, 409.8}, new double[]{966.21, 205.29, 409.79}, 5);

        System.out.println("Titration Soluble Positions:");
        calculate( new double[]{1070.38, 1070.38, 409.75}, new double[]{1071.4, 252.24, 409.79}, 4);

        System.out.println("Total Soluble Positions:");
        calculate( new double[]{1174.44, 678.22, 409.8}, new double[]{1175.84, 205.4, 409.89}, 5);


        System.out.println("Solid Sample 1 Positions:");
        calculate( new double[]{1301.86, 784.57, 429.11}, new double[]{1303.79, 226.1, 430.28}, 7);

        System.out.println("Solid Sample 2 Positions:");
        calculate( new double[]{1397.275, 784.930, 429.710}, new double[]{1398.7, 226.25, 429.97}, 7);

        System.out.println("Solid Sample 3 Positions:");
        calculate( new double[]{1492.69, 785.29, 430.31}, new double[]{1493.68, 226.52, 429.96}, 7);



        System.out.println("Solid Sample Reck 1 Positions:");
        calculate( new double[]{1301.86, 784.57, 429.11}, new double[]{1303.29, 306.08, 429.28}, 6);



        System.out.println("Solid Sample Reck 1.1 Positions:");
        calculate( new double[]{1301.86, 784.57, 429.11}, new double[]{1492.69, 785.29, 430.31}, 2);

    }

    private static void calculate(double[] startPos,  double[] endPos, int count ) {

        double xStep = (endPos[0] - startPos[0])/count;
        double yStep = (endPos[1] - startPos[1])/count;
        double zStep = (endPos[2] - startPos[2])/count;

        for (int i=1; i<count; i++){
            double x = startPos[0] + ( xStep *i);
            double y = startPos[1] + ( yStep *i);
            double z = startPos[2] + ( zStep *i);
            System.out.println(x+", " + y+ ", " + z);
        }
    }


}
