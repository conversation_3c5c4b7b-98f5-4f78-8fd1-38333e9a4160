/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.cjc.config;

/**
 * 空的占位符类 - 避免ClassNotFoundException
 * Spring Boot 3.x 使用默认的文件上传配置
 * @date 2018-12-28
 * <AUTHOR>
 */
public class MultipartConfig {
    // 空类，仅作为占位符使用
    // 实际的文件上传配置通过 application.yml 中的 spring.servlet.multipart 配置
}
