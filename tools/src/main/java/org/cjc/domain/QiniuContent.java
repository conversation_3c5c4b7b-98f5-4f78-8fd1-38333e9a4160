/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.cjc.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.annotations.UpdateTimestamp;
import jakarta.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 上传成功后，存储结果
 * <AUTHOR>
 * @date 2018-12-31
 */
@Data
@Entity
@Table(name = "tool_qiniu_content")
public class QiniuContent implements Serializable {

    @Id
    @Column(name = "content_id")
    @Schema(description = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    @Schema(description = "文件名")
    private String key;

    @Schema(description = "空间名")
    private String bucket;

    @Schema(description = "大小")
    private String size;

    @Schema(description = "文件地址")
    private String url;

    @Schema(description = "文件类型")
    private String suffix;

    @Schema(description = "空间类型：公开/私有")
    private String type = "公开";

    @UpdateTimestamp
    @Schema(description = "创建或更新时间")
    @Column(name = "update_time")
    private Timestamp updateTime;
}
