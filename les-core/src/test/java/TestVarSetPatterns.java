import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TestVarSetPatterns {
    
    // 复制实际的正则表达式
    private final static Pattern VAR_SET_PATTERNS = Pattern.compile("(?:SET_VAR|ADD_VAR)\\s*\\(\\s*([A-Za-z_][A-Za-z0-9_]*)\\s*,\\s*([^)]+)\\s*\\)\\s*;?");
    
    public static void main(String[] args) {
        // 测试用例
        String[] testCases = {
            "SET_VAR(AAA,\"ee\");",
            "ADD_VAR(BBB,\"V\");", 
            "SET_VAR(CCC,99);",
            "SET_VAR(TEST_VAR, \"hello world\");",
            "ADD_VAR(my_var, 123.45);",
            "SET_VAR( SPACED_VAR , \"value with spaces\" );",
            "SET_VAR(NO_SEMICOLON, \"test\")",
            "SET_VAR(COLON_VALUE, \":prefixed_value\");",
            "ADD_VAR(_underscore_start, \"valid\");",
            "SET_VAR(VAR123, true);",
            // 包含多个变量设置的字符串
            "SET_VAR(VAR1, \"value1\"); ADD_VAR(VAR2, 42); SET_VAR(VAR3, \"value3\");",
            // 无效的格式（应该不匹配）
            "INVALID_VAR(test, \"value\");",
            "SET_VAR(123invalid, \"value\");", // 变量名不能以数字开头
            "SET_VAR(test);", // 缺少值
        };
        
        System.out.println("=== 测试 VAR_SET_PATTERNS 正则表达式 ===\n");
        
        for (int i = 0; i < testCases.length; i++) {
            String testCase = testCases[i];
            System.out.println("测试用例 " + (i + 1) + ": " + testCase);
            
            Matcher matcher = VAR_SET_PATTERNS.matcher(testCase);
            boolean found = false;
            
            while (matcher.find()) {
                found = true;
                String propertyName = matcher.group(1); // 提取属性名
                String defaultValue = matcher.group(2); // 提取默认值
                
                // 模拟实际代码中的逻辑
                if (defaultValue != null && defaultValue.startsWith(":")) {
                    defaultValue = defaultValue.substring(1);
                }
                
                System.out.println("  匹配成功:");
                System.out.println("    属性名: " + propertyName);
                System.out.println("    默认值: " + defaultValue);
                System.out.println("    完整匹配: " + matcher.group(0));
            }
            
            if (!found) {
                System.out.println("  未匹配");
            }
            
            System.out.println();
        }
        
        // 测试复杂的多行字符串
        System.out.println("=== 测试复杂的多行字符串 ===");
        String complexInput = "";
        /*
            """
            SET_VAR(DATABASE_URL, "********************************");
            ADD_VAR(MAX_CONNECTIONS, 100);
            SET_VAR(TIMEOUT, 30000);
            
            // 一些其他代码
            if (condition) {
                SET_VAR(DEBUG_MODE, true);
                ADD_VAR(LOG_LEVEL, "INFO");
            }
            
            SET_VAR(API_KEY, ":secret_key_123");
            """;
        */

        System.out.println("输入字符串:");
        System.out.println(complexInput);
        System.out.println("\n匹配结果:");
        
        Matcher complexMatcher = VAR_SET_PATTERNS.matcher(complexInput);
        int matchCount = 0;
        
        while (complexMatcher.find()) {
            matchCount++;
            String propertyName = complexMatcher.group(1);
            String defaultValue = complexMatcher.group(2);
            
            if (defaultValue != null && defaultValue.startsWith(":")) {
                defaultValue = defaultValue.substring(1);
            }
            
            System.out.println("匹配 " + matchCount + ":");
            System.out.println("  属性名: " + propertyName);
            System.out.println("  默认值: " + defaultValue);
        }
        
        System.out.println("\n总共匹配到 " + matchCount + " 个变量设置");
    }
}
