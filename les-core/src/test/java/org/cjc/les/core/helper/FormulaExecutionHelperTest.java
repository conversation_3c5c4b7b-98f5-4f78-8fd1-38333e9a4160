package org.cjc.les.core.helper;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.jexl3.*;
import org.cjc.TestApplication;
import org.cjc.les.core.config.TestConfig;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ContextConfiguration;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest(
        classes = TestApplication.class,
        properties = {"spring.profiles.active=test"}
)
@Log4j2
public class FormulaExecutionHelperTest {

    @Autowired
    private ApplicationContext context;

    private JexlEngine jexlEngine;


    @Test
    public void testExpression() {
        System.out.println("Hello");
        String express = "GET_NUM(ICP_RAW_VALUE) * (GET_NUM(BAL_CONSTANT_VOLUME_WEIGHT,0) / GET_NUM(BAL_SAMPLE_WEIGHT)) * (GET_NUM(BAL_CONSTANT_VOLUME_WEIGHT,1) / GET_NUM(BAL_2ND_SAMPLE_WEIGHT))/(GET_NUM(BAL_SAMPLE_WEIGHT)*1000*1000) * 100";

        Task task = new Task();
        task.setId(1L);
        TaskMethod method = new TaskMethod();
        method.setId(1L);
        task.getMethodList().add(method);
        TaskStep step = new TaskStep();
        step.setId(1L);
        method.getSteps().add(step);
        TaskAction action = new TaskAction();
        action.setId(1L);
        step.getActions().add(action);
        TaskCommand cmd = new TaskCommand();
        cmd.setId(1L);
        cmd.setTaskId(task.getId());
        cmd.setTaskMethodId(method.getId());
        cmd.setTaskStepId(step.getId());
        cmd.setTaskAction(action);
        action.getCommands().add(cmd);

        TaskExecutorContext context = TaskExecutorContext.getContext();
        TaskExecutorContext.setTask(task);
        TaskExecutorContext.copy(context, cmd);
        TaskExecutorContext.addVar("ICP_RAW_VALUE", "2.21");
        TaskExecutorContext.addVar("BAL_SAMPLE_WEIGHT",0.992);
        TaskExecutorContext.addVar("BAL_2ND_SAMPLE_WEIGHT", 9.98);
        TaskExecutorContext.addVar("BAL_CONSTANT_VOLUME_WEIGHT",248.3);
        TaskExecutorContext.addVar("BAL_CONSTANT_VOLUME_WEIGHT",248.5);

        ResultFeature resultFeature = new ResultFeature();
        resultFeature.setValue("0");
        Object ret = FormulaExecutionHelper.getInstance().executeExpression(express);

        BigDecimal dd = (BigDecimal)ret;
        BigDecimal newScaleDD = dd.setScale(2, RoundingMode.HALF_UP);
        resultFeature.setValue(newScaleDD.toPlainString());
        log.error("ret={}, rs.getValue={}, newScaleDD={}", ret, resultFeature.getValue(), newScaleDD);
    }

    @Test
    public void testIcpSaveRS() {

        Task task = new Task();
        task.setId(1L);
        TaskMethod method = new TaskMethod();
        method.setId(1L);
        task.getMethodList().add(method);
        TaskStep step = new TaskStep();
        step.setId(1L);
        method.getSteps().add(step);
        TaskAction action = new TaskAction();
        action.setId(1L);
        step.getActions().add(action);
        TaskCommand cmd = new TaskCommand();
        cmd.setId(1L);
        cmd.setTaskId(task.getId());
        cmd.setTaskMethodId(method.getId());
        cmd.setTaskStepId(step.getId());
        cmd.setTaskAction(action);
        action.getCommands().add(cmd);

        TaskExecutorContext context = TaskExecutorContext.getContext();
        TaskExecutorContext.setTask(task);
        TaskExecutorContext.copy(context, cmd);
        TaskExecutorContext.addVar("ICP_RAW_VALUE", "2.21");
        TaskExecutorContext.addVar("BAL_SAMPLE_WEIGHT",0.992);
        TaskExecutorContext.addVar("BAL_2ND_SAMPLE_WEIGHT", 9.98);
        TaskExecutorContext.addVar("BAL_CONSTANT_VOLUME_WEIGHT",248.3);
        TaskExecutorContext.addVar("BAL_CONSTANT_VOLUME_WEIGHT",248.5);


        Object ret = FormulaExecutionHelper.getInstance().execute("ICP检测公式计算及保存");
        log.error("ret={}", ret);
    }

    @Test
    public void printAllBeans() {
        String[] beans = context.getBeanDefinitionNames();
        for (String beanName : beans) {
            Object bean = context.getBean(beanName);
            System.out.printf("Bean: %-50s  Type: %s%n", beanName, bean.getClass().getName());
        }
    }
    public FormulaExecutionHelperTest() {

        Map<String, Object> functions = new HashMap<>();
        //functions.put(null, funcImpl);
        jexlEngine = new JexlBuilder()
                .namespaces(functions) // 注册函数
                .create();
    }
    @Test
    public void testDecimal(){
        BigDecimal value = new BigDecimal("123.12345678");
        BigDecimal rounded = value.setScale(2, RoundingMode.HALF_UP);

        String str = rounded.toPlainString();
        System.out.println(str); // 输出：123.00
    }

}
