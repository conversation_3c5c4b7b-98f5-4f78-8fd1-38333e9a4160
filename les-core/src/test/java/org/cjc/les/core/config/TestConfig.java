package org.cjc.les.core.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
/**
@Configuration
@ComponentScan(basePackages = "org.cjc") // 替换为你的包路径
@EnableJpaRepositories(basePackages = "org.cjc.les.core.repository")
@EntityScan(basePackages = "org.cjc.les.core.domain")
@PropertySource(value = "classpath:application-test.yml", factory = YamlPropertySourceFactory.class)
*/
public class TestConfig {
    // 可以添加测试专用 @Bean 也可以自动扫描组件
}