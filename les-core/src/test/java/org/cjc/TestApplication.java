package org.cjc;

import org.cjc.repository.impl.EnhancedJpaRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication

@ComponentScan(basePackages = {"org.cjc"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = org.cjc.les.core.init.Bootstrap.class)
        }
        )
@EnableJpaRepositories(repositoryBaseClass = EnhancedJpaRepositoryImpl.class)
@EnableTransactionManagement
@EnableJpaAuditing(auditorAwareRef = "auditorAware")
public class TestApplication {
        public static void main(String[] args) {
                SpringApplication springApplication = new SpringApplication(TestApplication.class);
                // 监控应用的PID，启动时可指定PID路径：--spring.pid.file=/home/<USER>/app.pid
                // 或者在 application.yml 添加文件路径，方便 kill，kill `cat /home/<USER>/app.pid`
                springApplication.addListeners(new ApplicationPidFileWriter());
                springApplication.run(args);
        }

}