/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;

import jakarta.persistence.Transient;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-04-15
**/
@Data
public class PositionDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 命令名称 */
    private String name;

    /**
     * 放置物
     */
    private String objectName;

    /** 命令描述 */
    private String description;

    /** 关联设备实例ID */
    private Long deviceInstanceId;

    /** 在该设备实例中的索引位置 */
    private Integer devicePosIndex;

    /** 机器人定义的位置编号，用于机器人点位唯一识别 */
    private String robotPosCode;
    /** 服务码 */
    private String serviceCode;


    /** 点位类型:DEFAULT,BOARD */
    private String type;


    private String nodeId;
    /** 所属板卡图形节点ID */
    private String boardNodeId;

    private BigDecimal xpos;

    private BigDecimal ypos;

    private BigDecimal zpos;

    /**
     * 是否可见: Y/N
     */
    private String visible;

    /** 点位状态, IDLE, HOLD,LEAVE */
    private String status;

    /** 初始点位状态,点位还原时的参考 */
    private String initStatus;

    /**
     * 占用级别:TASK/METHOD/STEP/ACTION
     */
    private String holdLevel;

    /** 所占用的任务 */
    private Long holdByTaskId;

    private Long holdByTaskMethodId;

    private Long holdByTaskStepId;

    /** 所占用的动作 */
    private Long holdByTaskActionId;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}