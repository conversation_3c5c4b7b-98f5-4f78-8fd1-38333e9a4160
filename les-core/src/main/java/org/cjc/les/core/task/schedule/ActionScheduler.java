/*
 *  Copyright 2024-2024 ximei.co.ltd
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.cjc.les.core.task.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.Station;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.service.TaskStepService;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskStepExecutor;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 步骤调度器
 */
@Log4j2
@RequiredArgsConstructor
@Component
public class ActionScheduler {

    private final int POOL_SIZE = 16;

    private static volatile Object lockObj = new Object();

    /**
     * 工作站队列表
     */
    private ConcurrentHashMap<Long, StationQueue> stationMap = new ConcurrentHashMap<>();

    public ConcurrentHashMap<Long, StationQueue> getStationMap() {
        return stationMap;
    }

    public StationQueue getStationQueueByStationId(Long stationId) {
        return stationMap.get(stationId);
    }

    /**
     * 提交任务步骤到执行队列，并等待完成
     *
     * @param taskAction
     */
    public void execute(TaskAction taskAction, ExecutorConfig config) {
        Station station = taskAction.getStation();
        StationQueue stationQueue = getOrCreateStationQueue(station);
        stationQueue.execute(taskAction, config);
    }

    /**
     * 提交任务步骤到执行队列，立即返回
     *
     * @param taskAction
     */
    public void submit(TaskAction taskAction, ExecutorConfig config) {
        Station station = taskAction.getStation();
        StationQueue stationQueue = getOrCreateStationQueue(station);
        stationQueue.submit(taskAction, config);
    }

    public void cancel(TaskAction taskAction) {
        Station station = taskAction.getStation();
        StationQueue stationQueue = getOrCreateStationQueue(station);
        stationQueue.cancel(taskAction);
    }

    private StationQueue getOrCreateStationQueue(Station station) {
        synchronized (lockObj){
            StationQueue stationQueue = stationMap.get(station.getId());
            if (stationQueue == null) {
                stationQueue = new StationQueue(station);
                stationMap.put(station.getId(), stationQueue);
            }
            return stationQueue;
        }
    }

}
