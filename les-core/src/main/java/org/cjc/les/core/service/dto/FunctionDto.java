/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-17
**/
@Data
public class FunctionDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 函数名称 */
    private String name;

    /** 函数显示提示信息 */
    private String tip;

    /** 函数描述 */
    private String description;

    /** 参数定义 */
    private String parameters;

    /** 返回类型 */
    private String returnType;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}