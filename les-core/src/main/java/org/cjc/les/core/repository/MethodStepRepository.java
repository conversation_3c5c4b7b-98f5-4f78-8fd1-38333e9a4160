/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.MethodStep;
import org.cjc.les.core.domain.Step;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
public interface MethodStepRepository extends JpaRepository<MethodStep, Long>, JpaSpecificationExecutor<MethodStep> {

    @Modifying
    @Query(value="update MethodStep set evaluateExecutingStart=?2, evaluateExecutingDuration=?3,evaluatedTime=CURRENT_TIMESTAMP, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateEvaluateTime(Long id, Long evaluateExecutingStart, Long evaluateExecutingDuration);
}