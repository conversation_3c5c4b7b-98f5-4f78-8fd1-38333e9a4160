/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.cjc.base.LesBaseEntity;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_task")
public class Task extends TaskBaseEntity {

    private String taskNumber;

    private String taskName;

    /**
     * 标签值
     */
    private String tag;

    /**
     * 任务批次ID
     */
    private Long batchId;

    @Transient
    private String name;

    private Long procedureId;

    transient private Long prepareTaskId;

    private String message;

    private Long scheduleEntryTime;

    /**
     * 进样方法
     */
    public Optional<TaskMethod> getEntryMethod(){
        for (TaskMethod mth : methodList) {
            if (StringUtils.equalsIgnoreCase(mth.getType(),"ENTRY")){
                return Optional.of(mth);
            }
        }
        return Optional.empty();
    }

    /**
     * 进程方法，顺序执行
     */

    @OneToMany(mappedBy = "task", targetEntity = TaskMethod.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<TaskMethod> methodList = new ArrayList<>();

    /**
     * 主体业务方法列表
     * @return
     */
    public List<TaskMethod> getMainMethodList() {
        return methodList.stream().filter(mth->{ return StringUtils.equalsIgnoreCase(mth.getType(),"MAIN");})
                .collect(Collectors.toList());
    }

    /**
     * 出样方法
     */
    public Optional<TaskMethod> getExitMethod(){
        for (TaskMethod mth : methodList) {
            if (StringUtils.equalsIgnoreCase(mth.getType(),"EXIT")){
                return Optional.of(mth);
            }
        }
        return Optional.empty();
    }

    transient private Procedure procedure;

    @JSONField(serialize = false)
    @JoinColumn(name = "sample_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = Sample.class)
    @Schema(description = "样品", hidden = true)
    private Sample sample;

    @OneToOne(mappedBy = "task", cascade = CascadeType.ALL)
    private Result result;


    /**
     * 从方法定义中拷贝
     * @param procedure
     * @param isDeep 是否深拷贝，默认false
     */
    public void copy(Procedure procedure, boolean isDeep){
        BeanUtil.copyProperties(procedure,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","methodList"));
        this.setProcedure(procedure);
        this.setProcedureId(procedure.getId());

        if (isDeep) {

            methodList.clear();
            for (ProcedureMethod method : procedure.getMethods()) {
                TaskMethod taskMethod = new TaskMethod();
                taskMethod.copy(method, true);
                taskMethod.setTask(this);
                methodList.add(taskMethod);
                if (StringUtils.equals(method.getParallel(), "Y")){
                    TaskMethod copyTaskMethod = new TaskMethod();
                    copyTaskMethod.copy(method, true);
                    copyTaskMethod.setName(copyTaskMethod.getName()+"(副本)");
                    copyTaskMethod.setTask(this);
                    methodList.add(copyTaskMethod);
                }
            }

        }

    }

    @Override
    public String toString() {
        return "Task{" +
                "id='" + this.getId() + '\'' +
                ", taskNumber='" + taskNumber + '\'' +
                ", taskName='" + taskName + '\'' +
                ", procedureId=" + procedureId +
                ", prepareTaskId=" + prepareTaskId +
                ", status='" + this.getStatus() + '\'' +
                '}';
    }
}
