/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Method;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.ProcedureMethod;
import org.cjc.les.core.service.dto.ProcedureDto;
import org.cjc.les.core.service.dto.ProcedureQueryCriteria;
import org.cjc.les.core.service.dto.ProcedureSmallDto;
import org.cjc.les.core.service.dto.ProcedureWithVariablesDto;
import org.cjc.les.core.vo.ConfigItemVo;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-08
**/
public interface ProcedureService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ProcedureQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ProcedureDto>
    */
    List<ProcedureDto> queryAll(ProcedureQueryCriteria criteria);

    /**
     * 查询检测流程选项
     * @param criteria
     * @return
     */
    List<ProcedureSmallDto> queryProceduresForSelection(ProcedureQueryCriteria criteria);

    /**
     * 查询所有未引用的方法，步骤，动作
     * @return
     */
    ProcedureDto queryAllUnRefProcNodes();

    void cleanUnRefProcNodes(ProcedureDto resources);

    /**
     * 根据ID查询
     * @param id ID
     * @return ProcedureDto
     */
    ProcedureDto findById(Long id);

    Procedure findProcedureById(Long id);

    /**
     * 通过ActionID查询该动作下的所有命令环境变量
     * @param actionId
     * @return
     */
    List<ConfigItemVo> queryActionVarDefinesByActionId(Long actionId);

    /**
     * 通过ActionID查询该动作下的所有命令环境变量
     * @param stepId
     * @return
     */
    List<ConfigItemVo> queryStepVarDefinesByStepId(Long stepId);

    /**
     * 通过ActionID查询该动作下的所有命令环境变量
     * @param methodId
     * @return
     */
    List<ConfigItemVo> queryMethodVarDefinesByMethodId(Long methodId);

    /**
    * 创建
    * @param resources /
    * @return ProcedureDto
    */
    ProcedureDto create(Procedure resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Procedure resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ProcedureDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查找派生过程或创建
     * @param procDtos
     * @return
     */
    Procedure queryOrCreateDeriveProc(List<ProcedureSmallDto> procDtos);

    List<String> queryMainMethodNamesForSelection();
}