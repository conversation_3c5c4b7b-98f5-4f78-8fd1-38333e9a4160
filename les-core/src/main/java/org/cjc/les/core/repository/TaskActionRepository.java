/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.TaskAction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskActionRepository extends JpaRepository<TaskAction, Long>, JpaSpecificationExecutor<TaskAction> {

    @Modifying
    @Query(value="update TaskAction set status=?2, message=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateStatus(Long id, String status, String message);

    @Modifying
    @Query(value="update TaskAction set executedStart=?2, executedDuration=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateExecutedTime(Long id, Long startTime, Long durationTime);

    @Query(value = "select act.* from les_task_action act join les_task_command tcmd on tcmd.task_action_id=act.id where tcmd.id= ?1 ", nativeQuery = true)
    TaskAction findTaskActionByTaskCommandId(Long taskCommandId);
}