/*
 *  Copyright 2025-2025 Wuhan Annis Robot Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.util;

import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskCommandExecutor;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.utils.StringUtils;

/**
 * 任务命令执行工具类
 */
public class TaskCommandExecutorUtil {

    public static void execute(TaskCommand taskCommand, ExecutorConfig config) {
        new TaskCommandExecutor(config).execute(taskCommand);
    }

    public static void execute(TaskCommand taskCommand) {
        ExecutorConfig config = TaskExecutorContext.getContext().getExecutorConfig() == null
                ? ExecutorConfig.defaultConfig : TaskExecutorContext.getContext().getExecutorConfig();
        new TaskCommandExecutor(config).execute(taskCommand);
    }

    /**
     * 通过可选项编码执行指令
     * 从当前动作的可选指令列表中选取
     *
     * @param optionCode
     */
    public static void executeByOptionCode(String optionCode) {
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand != null) {
            TaskAction taskAction = taskCommand.getTaskAction();
            for (TaskCommand command : taskAction.getCommands()) {
                if (StringUtils.equals(command.getOptionCode(), optionCode)) {
                    command.setStatus(RunStatusEnum.READY.name());
                    execute(command);
                }
            }
            // 恢复之前的指令上下文
            TaskExecutorContext.setTaskCommand(taskCommand);
        }

    }

}
