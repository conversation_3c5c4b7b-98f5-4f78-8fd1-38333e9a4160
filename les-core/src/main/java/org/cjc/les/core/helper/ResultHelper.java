/**
 *  Copyright 2024-2024 <PERSON>han Annis Robot Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.jexl3.JexlContext;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.service.ResultService;
import org.cjc.les.core.service.dto.FeatureConfigQueryCriteria;
import org.cjc.les.core.service.dto.ResultFeatureDto;
import org.cjc.les.core.service.dto.ResultItemDto;
import org.cjc.les.core.service.mapstruct.ResultFeatureMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.VariableValueWrapper;
import org.cjc.utils.SpringContextHolder;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 结果处理帮助类
 */
@Log4j2
public class ResultHelper {


    public static void saveResultItem(ResultItemDto resultItemDto, int stage){

    }

    /**
     * 检查并行检测结果
     * @return
     */
    public static boolean checkParallelResult() {
        Task task = TaskExecutorContext.getTask();
        if (task == null || task.getResult() == null) {
            return false;
        }
        List<ResultItem> resultItems = task.getResult().getItems();

        // 具有相同检测方法的检测结果被识别为并行检测方法
        TaskMethod curTaskMethod = TaskExecutorContext.getTaskMethod();
        if (curTaskMethod == null){
            return false;
        }
        List<ResultItem> existedItems = resultItems.stream().filter(item -> {
            return item.getTaskMethod().getMethodId().equals(curTaskMethod.getMethodId());
        }).collect(Collectors.toList());

        if (CollectionUtils.size(existedItems)<2){
            return false;
        }

        List<ResultFeature> firstRsList = existedItems.get(0).getResultFeatures();
        List<ResultFeature> secondRsList = existedItems.get(1).getResultFeatures();
        if (CollectionUtils.isEmpty(firstRsList) || CollectionUtils.isEmpty(secondRsList)){
            return false;
        }
        if (firstRsList.size() != secondRsList.size()){
            return false;
        }

        for (ResultFeature feature : firstRsList) {
            Optional<ResultFeature> secondFeatureOpt = secondRsList.stream().filter(fea->{
                return StringUtils.equals(fea.getCode(), feature.getCode());
            }).findFirst();
            if (secondFeatureOpt.isPresent()){
                ResultFeature secondFeature = secondFeatureOpt.get();
                if (!ConclusionValidator.validateParallelRsd(feature.getValue(), secondFeature.getValue(), feature.getCheckRules())){
                    return false;
                }
            }else{
                return false;
            }
        }

        return true;
    }

    /**
     * 保存检测结果
     * @param featureDto
     * @param stage
     */
    public static void saveResultFeature(ResultFeatureDto featureDto, int stage) {

        // 结果设置
        Task task = TaskExecutorContext.getTask();
        Result rs = task.getResult();
        if (rs == null) {
            rs = new Result();
            rs.setName(task.getTaskName() + "检测结果");
            rs.setNumber(task.getTaskNumber() + "-1");
            rs.setSample(task.getSample());
            task.setResult(rs);
            rs.setTask(task);
        }
        // 结果项设置
        List<ResultItem> resultItems = rs.getItems();
        if (resultItems == null) {
            resultItems = new ArrayList<>();
            rs.setItems(resultItems);
        }
        TaskMethod curTaskMethod = TaskExecutorContext.getTaskMethod();
        List<ResultItem> existedItems = resultItems.stream().filter(item -> {
            return item.getTaskMethod().getId().equals(curTaskMethod.getId());
        }).collect(Collectors.toList());
        ResultItem rsItem = new ResultItem();
        if (CollectionUtils.isNotEmpty(existedItems)) {
            rsItem = existedItems.get(0);
        } else {
            rsItem.setTaskMethod(curTaskMethod);
            rsItem.setResult(rs);
            rs.getItems().add(rsItem);
        }

        // 结果特性设置
        ResultFeatureMapper resultFeatureMapper = SpringContextHolder.getBean(ResultFeatureMapper.class);
        ResultFeature feature = resultFeatureMapper.toEntity(featureDto);

        fillFeatureConfig(feature, featureDto);

        List<ResultFeature> features = rsItem.getResultFeatures();
        if (features == null) {
            features = new ArrayList<>();
            rsItem.setResultFeatures(features);
        }

        final String code = feature.getCode();
        List<ResultFeature> existedFeatures = features.stream().filter(fea -> {
            return StringUtils.equals(fea.getCode(), code );
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existedFeatures)) {
             existedFeatures.get(0).copy(feature);
             feature = existedFeatures.get(0);
        } else {
            feature.setResultItem(rsItem);
            features.add(feature);
        }

        // 补充附加数据
        if (stage==0){
            feature.setFirstValue(featureDto.getValue());
            Map contextMap = new HashMap();
            setVariablesFromTaskContext(contextMap);
            String contextStr = JSON.toJSONString(contextMap);
            JSONObject obj = new JSONObject();
            obj.put("context",JSON.parseObject(contextStr));
            obj.put("firstRawValue", JSON.parseObject(featureDto.getRawValue()));
            feature.setRawValue(JSON.toJSONString(obj));
        }else if (stage==1){
            feature.setSecondValue(featureDto.getValue());
            JSONObject obj = JSON.parseObject(feature.getRawValue());
            obj.put("secondRawValue", JSON.parseObject(featureDto.getRawValue()));
            feature.setRawValue(JSON.toJSONString(obj));
        }

        // 校验结论
        checkConclusion(feature, featureDto, stage);

        saveResult(rs);
    }

    /**
     * 校验结论
     * @param feature
     * @param featureDto
     * @param stage
     * @return
     */
    private static void checkConclusion(ResultFeature feature, ResultFeatureDto featureDto, int stage) {
       boolean ret = ConclusionValidator.validate(feature, featureDto, stage);
       if (ret){
           feature.setConclusion("NORMAL");
       }else{
           feature.setConclusion("ABNORMAL");
       }
    }


    /**
     * 保存检测结果
     * @param code
     * @param name
     * @param value
     */
    public static void saveResultFeature(String code, String name, String value) {

        ResultFeatureDto featureDto = new ResultFeatureDto();
        featureDto.setCode(code);
        featureDto.setName(name);
        featureDto.setValue(value);

        saveResultFeature(featureDto, 0);

    }

    private static void fillFeatureConfig(ResultFeature feature, ResultFeatureDto featureDto){
        FeatureConfigQueryCriteria criteria = new FeatureConfigQueryCriteria();
        TaskMethod curTaskMethod = TaskExecutorContext.getTaskMethod();
        if (curTaskMethod != null){
            criteria.setMethodId(curTaskMethod.getMethodId());
            criteria.setInstrumentColumnName(featureDto.getName());
        }

        ResultService service = SpringContextHolder.getBean(ResultService.class);

        service.fillFeatureConfig(feature, criteria);

        if (StringUtils.isEmpty(feature.getCode())){
            feature.setCode(featureDto.getName());
        }

    }

    private static void setVariablesFromTaskContext(Map context) {

        TaskCommand currentCommand = TaskExecutorContext.getTaskCommand();
        if (currentCommand == null) {
            log.warn("No task executing environment.");
            return;
        }

        Long taskMethodId = currentCommand.getTaskMethodId();

        ConcurrentHashMap<String, VariableValueWrapper> varsMap = TaskExecutorContext.getContext().getVariableMap();
        Iterator<Map.Entry<String, VariableValueWrapper>> itr = varsMap.entrySet().iterator();
        while (itr.hasNext()) {
            Map.Entry<String, VariableValueWrapper> entry = itr.next();
            String key = entry.getKey();
            VariableValueWrapper entryValue = entry.getValue();
            Object valueObj = entryValue.getValue();
            if (valueObj instanceof List) {
                List<VariableValueWrapper> valueList = (List<VariableValueWrapper>) valueObj;
                List<Object> oList = valueList.stream().filter(wrapper -> {
                            return taskMethodId.equals(wrapper.getTaskMethodId());
                        })
                        .map(VariableValueWrapper::getValue).collect(Collectors.toList());
                context.put(key, oList);
            } else {
                context.put(key, valueObj);
            }
        }

    }

    private static void saveResult(Result rs) {
        ResultService service = SpringContextHolder.getBean(ResultService.class);
        service.update(rs);
    }
}
