/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Station;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.service.dto.StationDto;
import org.cjc.les.core.service.dto.StationQueryCriteria;
import org.cjc.les.core.service.dto.StationRunningDto;
import org.cjc.les.core.service.dto.TaskActionDto;
import org.cjc.les.exception.ConfigApplicationException;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-07-14
**/
public interface StationService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(StationQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<StationDto>
    */
    List<StationDto> queryAll(StationQueryCriteria criteria);

    /**
     * 查询工作站运行时的信息
     * 返回对象中去掉了配置信息，以减少对象大小
     * @param criteria
     * @return
     */
    List<StationRunningDto> queryAllStationsInRunning(StationQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return StationDto
     */
    StationDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return StationDto
    */
    StationDto create(Station resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Station resources)  throws ConfigApplicationException;

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids) throws ConfigApplicationException;

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<StationDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查询站点队列中的任务动作清单
     * @param stationId
     * @return
     */
    List<TaskActionDto> queryActionQueueByStationId(Long stationId);
}