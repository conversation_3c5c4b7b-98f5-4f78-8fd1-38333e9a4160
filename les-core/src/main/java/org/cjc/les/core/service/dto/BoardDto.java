/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-04-08
**/
@Data
public class BoardDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 命令名称 */
    private String name;

    /** 命令描述 */
    private String tag;

    private Integer rows = 2;

    private Integer columns = 4;

    private Integer width = 84;

    private Integer height = 40;

    private List<Integer> positions = new ArrayList<>();

    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}