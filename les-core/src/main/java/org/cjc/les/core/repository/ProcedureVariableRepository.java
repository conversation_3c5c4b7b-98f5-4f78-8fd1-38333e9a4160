/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.ProcedureVariable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-27
**/
public interface ProcedureVariableRepository extends JpaRepository<ProcedureVariable, Long>, JpaSpecificationExecutor<ProcedureVariable> {
    List<ProcedureVariable> findByProcedureId(Long procedureId);
}