/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.JSON;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 设备实体对象，可通过继承该类实现具体的设备
 */
@Log4j2
@Entity
@Data
@Table(name="les_device")
@SQLDelete(sql = "UPDATE les_device SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class Device extends LesConfigBaseEntity {

    @Column(name = "name",nullable = false)
    @NotBlank
    @Schema(description = "设备名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "设备描述")
    private String description;

    /**
     * 设备类型,
     * SYS: 系统软件内部虚拟的设备,通常用来执行系统等待，数据存储，调度协调等指令;
     * ROBOT: 机器人设备，用来搬运物料,
     * CONTROL: 控制设备如PLC等, 用来控制外设的启停,通常不具有点位属性;
     * PERIPHERAL:外设如各种检测仪器, 台架等，一般具有点位属性,是控制设备操作的对象
     * STORAGE: 存储设备，用来存储物料，如烧杯架，样品架等.
     */
    @Column(name = "type")
    @Schema(description = "设备类型:SYS,ROBOT,CONTROL,PERIPHERAL,STORAGE")
    private String type;

    /**
     * 制造厂商
     */
    private String manufacturer;

    /**
     * 设备型号
     */
    private String model;

    @Column(name = "layout_image")
    @Schema(description = "设备布局中的显示图片地址")
    private String layoutImage;

    @Column(name = "layout_width")
    @Schema(description = "设备图片宽度")
    private Integer layoutWidth;

    @Column(name = "layout_height")
    @Schema(description = "设备图片高度")
    private Integer layoutHeight;

    @Column(name = "layout_visible")
    private Boolean layoutVisible = true;

    @Column(name = "position_config")
    @Schema(description = "设备图片点位配置")
    private String positionConfig;

    @OneToMany(mappedBy = "device", targetEntity = Command.class, cascade = {CascadeType.PERSIST,CascadeType.ALL},
            fetch = FetchType.EAGER, orphanRemoval = false)
    private List<Command> commands = new ArrayList<>();

    /**
     * 设备配置
     */
    @Column(name="config_template")
    private String config;

    /**
     * 设备模拟配置
     */
    transient private String mockConfig;
    /**
     * 启用模拟设备，默认禁用
     */
    transient private boolean enableMock = false;



  //  @Column(name = "config_template")
  //  @Schema(description = "配置信息，JSON串，或者普通的字符串")
  //  private String configTemplate;

    @Column(name = "java_class_name",unique = true)
    @Schema(description = "该设备自定义的Java类名")
    private String javaClassName;

    @Column(name = "config_java_class_name")
    @Schema(description = "该设备自定义的Java配置类名")
    private String configJavaClassName;

    /**
     * 是否已被初始化完成，默认未初始化
     */
    transient private AtomicBoolean initialized = new AtomicBoolean(false);

    public boolean isInitialized() {
        return initialized.get();
    }

    public void setInitialized(boolean bInit) {
        initialized.set(bInit);
    }

    /**
     * 创建真实的设备实例
     *
     * @return
     */
    public Device createActualInstance() {

        Object obj = null;
        try {
           //Class<?> clz = Thread.currentThread().getContextClassLoader().loadClass(this.javaClassName);

           Class clz = Class.forName(this.javaClassName);
            obj = clz.newInstance();
            BeanUtils.copyProperties(this, obj);
        } catch (InstantiationException e) {
            log.error("InstantiationException exception: {}", e.getMessage(), e);
        } catch (IllegalAccessException e) {
            log.error("IllegalAccessException exception: {}", e.getMessage(), e);
        } catch (ClassNotFoundException e) {
            log.error("ClassNotFoundException exception: {}", e.getMessage(), e);
        }
        return (Device) obj;
    }

    /**
     * 设备初始化
     *
     * @return true/false
     */
    public boolean init() {

        boolean ret = false;

        if (StringUtils.isEmpty(this.configJavaClassName)){
            ret = this.doInit();

            if (ret) {
                this.setInitialized(true);
            } else {
                this.setInitialized(false);
            }
            return ret;
        }

        try {
            Class<?> clz = this.getClass();
            Class<?> configClz = clz.getClassLoader().loadClass(this.configJavaClassName);
            Method initMethod = clz.getDeclaredMethod("doInit", configClz);
            String configStr = (this.enableMock) ? this.mockConfig : this.config;
            Object configObj = null;
            if (configClz.equals(String.class)){
                configObj = configStr;
            }else {
                configObj = JSON.parseObject(configStr, configClz);
            }
            Class<?> returnType = initMethod.getReturnType();
            if (!StringUtils.equalsIgnoreCase(returnType.getName(), "boolean")) {
                log.error("doInit method shoule return type: Boolean");
                return false;
            }
            log.info("Initializing Device:{},{}， config={} ...", this.getName(), clz.getName(), configObj);
            ret = (Boolean) initMethod.invoke(this, configObj);
            log.info("Initializing Device:{},{} is done, ret=***** {} *****", this.getName(), clz.getName(), ret);
        } catch (IllegalAccessException e) {
            log.error("IllegalAccessException exception: {}", e.getMessage(), e);
            ret = false;
        } catch (InvocationTargetException e) {
            log.error("InvocationTargetException exception: {}", e.getMessage(), e);
            ret = false;
        } catch (ClassNotFoundException e) {
            log.error("ClassNotFoundException exception: {}", e.getMessage(), e);
            ret = false;
        } catch (NoSuchMethodException e) {
            log.error("NoSuchMethodException exception: {}", e.getMessage(), e);
            ret = false;
        }

        if (ret) {
            this.setInitialized(true);
        } else {
            this.setInitialized(false);
        }
        return ret;
    }

    protected boolean doInit() {
        return false;
    }


    /**
     * 设备退出，执行清理资源
     */
    public void finalize() {
        doFinalize();
    }

    protected void doFinalize() {

    }

    public boolean makeSureInitialized(){
        if (!this.getInitialized().get()){
            return init();
        }
        return true;
    }

}
