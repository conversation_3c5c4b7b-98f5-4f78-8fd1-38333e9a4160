/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.constants.TaskPrepareStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.ProcedureRepository;
import org.cjc.les.core.repository.TaskPrepareRepository;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.mapstruct.TaskPrepareMapper;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskStepHook;
import org.cjc.les.core.task.schedule.EntryScheduler;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * 样品标签阅读执行器
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class TagReaderExecutor {

    /**
     * 任务预处理队列
     */
    private LinkedBlockingQueue<TaskPrepare> taskPrepareQueue = new LinkedBlockingQueue<>();

    /**
     * 样品进样器
     */
    private ExecutorService executorService = Executors.newSingleThreadExecutor();

    private final ProcedureRepository procedureRepository;

    private final TaskService taskService;

    private final PositionService positionService;

    private final TaskPrepareRepository taskPrepareRepository;
    private final TaskPrepareMapper taskPrepareMapper;

    private final AlertLogService alertLogService;

    /**
     * 批量提交任务预处理
     * 返回false, 已有正在等候处理的样品待传送，处理策略： 1.替换原有任务， 2.排队等候，3.拒绝提交，提醒已有任务处理中.
     *
     * @param taskPrepare 任务预处理列表
     * @return
     */
    synchronized public boolean submit(TaskPrepare taskPrepare) {
        if (!taskPrepareQueue.isEmpty()) {
            log.warn("There is another TaskPrepare list in the queue");
            return false;
        }

        taskPrepareQueue.offer(taskPrepare);
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    TaskPrepare taskPrepare = taskPrepareQueue.peek();
                    execute(taskPrepare);
                } catch (Throwable e) {
                    log.error("Exception in conveyorExecutorService, error: {}", e.getMessage(), e);
                } finally {
                    taskPrepareQueue.poll();
                }
            }


        });

        return true;
    }

    private void execute(TaskPrepare paramTaskPrepare) {

        Task task = taskService.createTaskWithEntryMethod(paramTaskPrepare);

        // 同步方式执行第一步

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);
        setScheduleEntryTime(task);

        TaskStep firstStep = task.getEntryMethod().get().getSteps().get(0);
        TaskExecutorContext.setTaskStepHook(firstStep, new TaskStepHook() {
            @Override
            public CommandReturn<TaskStep> executeBefore(TaskStep step) {
                paramTaskPrepare.setStatus("WAIT_TAG_INPUT");

                return null;
            }

            @Override
            public CommandReturn<TaskStep> executeAfter(TaskStep step) {
                // paramTaskPrepare.setStatus("TAG_INPUT_DONE");
                paramTaskPrepare.setStatus(TaskPrepareStatusEnum.READY.name());

                if ( StringUtils.equals( step.getStatus(),"SUCCESS")) {
                    responseRfTag(task, firstStep);
                }

                CommandReturn ret = new CommandReturn();
                ret.setResult(step);
                ret.setStatus(RunStatusEnum.STOPPED);
                return ret;
            }
        });
        ExecutorConfig executorConfig = new ExecutorConfig();

        StepScheduler stepScheduler = SpringContextHolder.getBean(StepScheduler.class);
        List<TaskStep> existedSteps = stepScheduler.getExistedSameSteps(firstStep);
        if (CollectionUtils.isNotEmpty(existedSteps)) {
            log.error("existedSteps={}", existedSteps);
            cancelExistedEntryMethods(existedSteps);
        }
        TaskMethod entryMethod = task.getEntryMethod().get();
        EntryScheduler entryScheduler = SpringContextHolder.getBean(EntryScheduler.class);
        entryScheduler.submit(entryMethod);

        // 等待步骤一执行完成
        while (!StringUtils.equalsAny(entryMethod.getStatus(),
                RunStatusEnum.SUCCESS.name(),
                RunStatusEnum.FAILED.name(),
                RunStatusEnum.CANCELLED.name(),
                RunStatusEnum.STOPPED.name())) {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
                break;
            }
        }
    }

    private void setScheduleEntryTime(Task task) {
        if (task.getScheduleEntryTime() == null) {
            task.setScheduleEntryTime(System.currentTimeMillis());
        }
        for (TaskMethod mth : task.getMethodList()) {
            for (TaskStep step : mth.getSteps()) {
                if (step.getScheduleEntryTime() == null) {
                    step.setScheduleEntryTime(task.getScheduleEntryTime());
                }
            }
        }
    }

    private void cancelExistedEntryMethods(List<TaskStep> existedSteps) {
        StepScheduler stepScheduler = SpringContextHolder.getBean(StepScheduler.class);
        for (TaskStep step : existedSteps) {
            TaskMethod method = step.getTaskMethod();
            for (int i = method.getSteps().size() - 1; i >= 0; i--) {
                TaskStep s = method.getSteps().get(i);
                stepScheduler.cancel(s);
            }
        }
    }

    private void responseRfTag(Task task, TaskStep firstStep) {
        Long prepareId = task.getPrepareTaskId();
        Optional<TaskPrepare> prepareOpt = taskPrepareRepository.findById(prepareId);
        if (!prepareOpt.isPresent()) {
            log.error("Could not found TaskPrepare by Id: {}", prepareId);
            return;
        }
        TaskPrepare taskPrepare = prepareOpt.get();
        Object rfId = task.getContext().getVarObj("RF_TAG");
        taskPrepare.setRfTag(rfId.toString());
        taskPrepare.setStatus(TaskPrepareStatusEnum.READY.name());
        taskPrepareRepository.save(taskPrepare);
        TaskPrepareDto dto = taskPrepareMapper.toDto(taskPrepare);

        SocketMsg msg = new SocketMsg(JSON.toJSONString(dto), MsgType.INFO);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"responseRfTag"}, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 是否等待TAG输入
     * @return
     */
    public boolean isWaitingForTagInput(TaskPrepareDto dto){
        TaskPrepare tp = taskPrepareQueue.peek();
        if (tp==null){
            return false;
        }
        if (!tp.getId().equals(dto.getId())) {
            return false;
        }
        dto.setStatus(tp.getStatus());
        return (StringUtils.equalsAny(tp.getStatus(),"WAIT_TAG_INPUT"));
    }

}
