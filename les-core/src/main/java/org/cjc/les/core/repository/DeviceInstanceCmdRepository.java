/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-07-19
**/
public interface DeviceInstanceCmdRepository extends JpaRepository<DeviceInstanceCmd, Long>, JpaSpecificationExecutor<DeviceInstanceCmd> {

    Optional<DeviceInstanceCmd> findDeviceInstanceCmdByBindControlCode(String bindControlCode);

    @Modifying
    @Query(value="update DeviceInstanceCmd set testParameter=?2, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateTestParameter(Long id, String testParameter);
}