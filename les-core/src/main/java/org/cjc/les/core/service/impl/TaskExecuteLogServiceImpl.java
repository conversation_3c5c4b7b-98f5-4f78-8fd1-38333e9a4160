/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.TaskLogTypeEnum;
import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskExecuteLog;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.repository.*;
import org.cjc.les.core.service.TaskExecuteLogService;
import org.cjc.les.core.service.dto.TaskExecuteLogDto;
import org.cjc.les.core.service.dto.TaskExecuteLogQueryCriteria;
import org.cjc.les.core.service.mapstruct.TaskExecuteLogMapper;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-08-23
**/
@Service
@RequiredArgsConstructor
public class TaskExecuteLogServiceImpl implements TaskExecuteLogService {

    private final TaskPrepareRepository taskPrepareRepository;
    private final TaskRepository taskRepository;
    private final TaskMethodRepository taskMethodRepository;
    private final TaskStepRepository taskStepRepository;
    private final TaskActionRepository taskActionRepository;
    private final TaskCommandRepository taskCommandRepository;

    private final TaskExecuteLogRepository taskExecuteLogRepository;
    private final TaskExecuteLogMapper taskExecuteLogMapper;

    @Override
    public Map<String,Object> queryAll(TaskExecuteLogQueryCriteria criteria, Pageable pageable){
        Page<TaskExecuteLog> page = taskExecuteLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskExecuteLogMapper::toDto));
    }

    @Override
    public Map<String, Object> queryLatestTaskExecuteLog(TaskExecuteLogQueryCriteria criteria, Pageable pageable0) {
        Timestamp beginTime = Timestamp.valueOf(LocalDateTime.now().minus(Duration.ofHours(24)));
        Timestamp endTime = Timestamp.valueOf(LocalDateTime.now());
        criteria.setUpdateTime(new ArrayList<>());
        criteria.getUpdateTime().add(beginTime);
        criteria.getUpdateTime().add(endTime);
        PageRequest pageable = PageRequest.of(0, 100, Sort.by(new Sort.Order(Sort.Direction.DESC,"id")));

        Page<TaskExecuteLog> page = taskExecuteLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
       // Collections.sort(page.getContent(), Comparator.comparingLong(TaskExecuteLog::getId).reversed());
      //  page.getContent().sort((a1, a2)->{
     //       return (int) (a2.getId().longValue() - a1.getId().longValue() );
     //   });

        Map<String, Object> out = PageUtil.toPage(page.map(taskExecuteLogMapper::toDto));
        ArrayList<TaskExecuteLogDto> newList = new ArrayList<>((Collection)out.get("content"));
        Collections.sort(newList, Comparator.comparingLong(a->a.getId()));
        out.put("content", newList);
        return out;
    }

    @Override
    public List<TaskExecuteLogDto> queryTaskExecuteLogForNode(TaskExecuteLogQueryCriteria criteria) {
        if (StringUtils.equalsIgnoreCase(criteria.getNodeType(),"TASK")){
            return taskExecuteLogMapper.toDto(taskExecuteLogRepository.findByTaskIdOrderByIdAsc(criteria.getId()));
        }else if(StringUtils.equalsIgnoreCase(criteria.getNodeType(),"METHOD")){
            return taskExecuteLogMapper.toDto(taskExecuteLogRepository.findByTaskMethodIdOrderByIdAsc(criteria.getId()));
        }else if(StringUtils.equalsIgnoreCase(criteria.getNodeType(),"STEP")){
            return taskExecuteLogMapper.toDto(taskExecuteLogRepository.findByTaskStepIdOrderByIdAsc(criteria.getId()));
        }else if(StringUtils.equalsIgnoreCase(criteria.getNodeType(),"ACTION")){
            return taskExecuteLogMapper.toDto(taskExecuteLogRepository.findByTaskActionIdOrderByIdAsc(criteria.getId()));
        }else if(StringUtils.equalsIgnoreCase(criteria.getNodeType(),"COMMAND")){
            return taskExecuteLogMapper.toDto(taskExecuteLogRepository.findByTaskCommandIdOrderByIdAsc(criteria.getId()));
        }
        throw new IllegalArgumentException("IllegalArgument nodeType="+criteria.getNodeType());
    }

    @Override
    public List<TaskExecuteLogDto> queryAll(TaskExecuteLogQueryCriteria criteria){
        return taskExecuteLogMapper.toDto(taskExecuteLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskExecuteLogDto findById(Long id) {
        TaskExecuteLog taskExecuteLog = taskExecuteLogRepository.findById(id).orElseGet(TaskExecuteLog::new);
        ValidationUtil.isNull(taskExecuteLog.getId(),"TaskExecuteLog","id",id);
        return taskExecuteLogMapper.toDto(taskExecuteLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskExecuteLogDto create(TaskExecuteLog resources) {
        if (StringUtils.equals(TaskLogTypeEnum.TASK.name(),resources.getLogType())){
            taskRepository.updateStatus(resources.getTaskId(),resources.getStatus(), resources.getMessage());
            if (StringUtils.equalsAny(resources.getStatus(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())){
                TaskPrepare tp = taskPrepareRepository.findTaskPrepareByTaskId(resources.getTaskId());
                if (tp != null) {
                    taskPrepareRepository.updateStatus(tp.getId(), resources.getStatus());
                }
            }
        }else if (StringUtils.equals(TaskLogTypeEnum.METHOD.name(),resources.getLogType())){
            taskMethodRepository.updateStatus(resources.getTaskMethodId(),resources.getStatus(), resources.getMessage());
        }else if (StringUtils.equals(TaskLogTypeEnum.STEP.name(),resources.getLogType())){
            taskStepRepository.updateStatus(resources.getTaskStepId(),resources.getStatus(), resources.getMessage());
        }else if (StringUtils.equals(TaskLogTypeEnum.ACTION.name(),resources.getLogType())){
            taskActionRepository.updateStatus(resources.getTaskActionId(),resources.getStatus(), resources.getMessage());
        }else if (StringUtils.equals(TaskLogTypeEnum.COMMAND.name(),resources.getLogType())){
            taskCommandRepository.updateStatus(resources.getTaskCommandId(),resources.getStatus(), resources.getMessage());
        }

        return taskExecuteLogMapper.toDto(taskExecuteLogRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskExecuteLog resources) {
        TaskExecuteLog taskExecuteLog = taskExecuteLogRepository.findById(resources.getId()).orElseGet(TaskExecuteLog::new);
        ValidationUtil.isNull( taskExecuteLog.getId(),"TaskExecuteLog","id",resources.getId());
        taskExecuteLog.copy(resources);
        taskExecuteLogRepository.save(taskExecuteLog);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            taskExecuteLogRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TaskExecuteLogDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskExecuteLogDto taskExecuteLog : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("任务lD", taskExecuteLog.getTaskId());
            map.put("任务方法ID", taskExecuteLog.getTaskMethodId());
            map.put("任务步骤ID", taskExecuteLog.getTaskStepId());
            map.put("任务动作ID", taskExecuteLog.getTaskActionId());
            map.put("任务命令ID", taskExecuteLog.getTaskCommandId());
            map.put("名称，根据日志类型分别表示对应的日志对象名称", taskExecuteLog.getName());
            map.put("状态: READY,RUNNING,SUCCESS,FAILED", taskExecuteLog.getStatus());
            map.put("执行详情", taskExecuteLog.getMessage());
            map.put("日志类型:TASK,METHOD,STEP,ACTION,COMMAND", taskExecuteLog.getLogType());
            map.put("日志级别:DEBUG,INFO,WARN,ERROR", taskExecuteLog.getLogLevel());
            map.put("创建人", taskExecuteLog.getCreateBy());
            map.put("创建时间", taskExecuteLog.getCreateTime());
            map.put("更新人", taskExecuteLog.getUpdateBy());
            map.put("更新时间", taskExecuteLog.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}