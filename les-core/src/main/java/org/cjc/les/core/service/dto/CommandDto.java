/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Data
public class CommandDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 命令名称 */
    private String name;

    /** 命令描述 */
    private String description;

    /** 关联设备ID */
    private Long deviceId;

    /** 该命令对应的Java方法名 */
    private String javaMethodName;

    /** 命令参数配置，JSON串，或者普通的字符串 */
    private String parameterTemplate;

    /** 命令参数的java类 */
    private String parameterType;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}