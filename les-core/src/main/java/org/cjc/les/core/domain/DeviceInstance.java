package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.helper.DeviceProxyHelper;
import org.cjc.les.core.helper.DeviceProxyWrapper;
import org.cjc.les.core.service.DeviceExecuteService;
import org.cjc.utils.SpringContextHolder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.springframework.beans.BeanUtils;

import jakarta.persistence.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 设备实体对象，可通过继承该类实现具体的设备
 */
@Log4j2
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_device_instance")
@SQLDelete(sql = "update les_device_instance set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class DeviceInstance extends LesConfigBaseEntity {

    /**
     * 设备名称
     */
    private String name;
    /**
     * 设备描述
     */
    private String description;

    /**
     * 设备类型,
     * SYS: 系统软件内部虚拟的设备,通常用来执行系统等待，数据存储，调度协调等指令;
     * ROBOT: 机器人设备，用来搬运物料,
     * CONTROL: 控制设备如PLC等, 用来控制外设的启停,通常不具有点位属性;
     * PERIPHERAL:外设如各种检测仪器, 台架等，一般具有点位属性,是控制设备操作的对象
     */
    private String type;

    /**
     * 引用实际物理设备对象
     */
    @OneToOne
    @JoinColumn(name = "device_id")
    private Device device;

    /**
     * 该设备拥有的点位列表
     */
    @OneToMany(mappedBy = "deviceInstance", targetEntity = Position.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name = "device_pos_index")
    private List<Position> positions = new ArrayList<>();

    /**
     * 该设备拥有的板卡点位列表
     */
    @OneToMany(mappedBy = "deviceInstance", targetEntity = BoardPos.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name = "device_pos_index")
    private List<BoardPos> boardPositions = new ArrayList<>();

    /**
     * 该设备拥有的命令列表
     */
    @OneToMany(mappedBy = "deviceInstance", targetEntity = DeviceInstanceCmd.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    private List<DeviceInstanceCmd> commands = new ArrayList<>();

    /**
     * 该设备实例所在的布局图
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "layout_id", referencedColumnName = "id", nullable = false)
    @ManyToOne(fetch=FetchType.LAZY, targetEntity = DeviceLayout.class)
    private DeviceLayout deviceLayout;

    /**
     * 布局节点ID
     */
    @Column(name="layout_node_id")
    private String layoutNodeId;


    @Column(name = "layout_image")
    @Schema(description = "设备布局中的显示图片地址")
    private String layoutImage;

    @Column(name = "layout_width")
    @Schema(description = "设备图片宽度")
    private Integer layoutWidth;

    @Column(name = "layout_height")
    @Schema(description = "设备图片高度")
    private Integer layoutHeight;

    @Column(name = "layout_visible")
    private Boolean layoutVisible = true;

    @Column(name = "position_config")
    @Schema(description = "设备图片点位配置")
    private String positionConfig;

    @Column(name = "position_detail_config")
    private String positionDetailConfig;

    /**
     * 设备配置
     */
    private String config;

    /**
     * 设备模拟配置
     */
    private String mockConfig;
    /**
     * 启用模拟设备，默认禁用
     */
    private boolean enableMock = false;

    public String getRealConfig(){
        return enableMock?mockConfig:config;
    }

    /**
     * 实现该设备的JAVA类名
     */
    //private String javaClassName;

    /**
     * 初始化配置参数类名
     */
   //private String configJavaClassName;

    /**
     * 是否已被初始化完成，默认未初始化
     */
    @JSONField(serialize = true)
    @Transient
    private AtomicBoolean initialized = new AtomicBoolean(false);

    public boolean isInitialized() {
        return initialized.get();
    }

    public void setInitialized(boolean bInit) {
        initialized.set(bInit);
    }

    /**
     * 创建真实的设备实例
     *
     * @return
     */
    /*
    @JSONField(serialize = false)
    public Device getDriverlInstance() {
        if (this.getDevice() == null || this.getDevice().getJavaClassName() == null){
            log.warn("This device have no driver, could not create Driver instance.");
            return null;
        }
        DeviceExecuteService deviceExecuteService =  SpringContextHolder.getBean(DeviceExecuteService.class);
        return deviceExecuteService.getOrCreateDevice(this);
    }
     */

    /**
     * 获取或创建命令实现代理
     * @return 代理对象
     */
    @JSONField(serialize = false)
    public DeviceProxyWrapper getProxy() {
        if (this.getDevice() == null || this.getDevice().getJavaClassName() == null){
            log.warn("This device have no driver, could not create Driver instance.");
            return null;
        }
        return DeviceProxyHelper.getInstance().getOrCreateDeviceProxy(this);
    }

    public void copy(DeviceInstance source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("positions","boardPositions","commands","deviceLayout"));

        // 点位列表
        if (this.getPositions() == null) {
            this.setPositions(new ArrayList<>());
        }

        List<Position> deleteList = new ArrayList<>();
        for (Position position : this.getPositions()) {
            Optional<Position> optOther = source.getPositions().stream().filter(actOther -> {
                boolean b = Objects.equals(actOther.getId(), position.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                position.copy(optOther.get());
                position.setDeviceInstance(this);
            } else {
                deleteList.add(position);
            }

        }

        source.getPositions().forEach(pos -> {
            if (pos.getId() == null) {
                this.getPositions().add(pos);
                pos.setDeviceInstance(this);
                if (CollectionUtils.isNotEmpty(pos.getRobots())){
                    for (PositionRobot posRobot : pos.getRobots()){
                        posRobot.setPosition(pos);
                    }
                }
            }
        });

        if (CollectionUtils.isNotEmpty(deleteList)) {
            this.getPositions().removeAll(deleteList);
        }
        // 板位列表
        if (this.getBoardPositions() == null) {
            this.setBoardPositions(new ArrayList<>());
        }

        List<BoardPos> deleteBoardList = new ArrayList<>();
        for (BoardPos boardPos : this.getBoardPositions()) {
            Optional<BoardPos> optOther = source.getBoardPositions().stream().filter(actOther -> {
                boolean b = Objects.equals(actOther.getId(), boardPos.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                boardPos.copy(optOther.get());
                boardPos.setDeviceInstance(this);
            } else {
                deleteBoardList.add(boardPos);
            }

        }

        source.getBoardPositions().forEach(act -> {
            if (act.getId() == null) {
                this.getBoardPositions().add(act);
                act.setDeviceInstance(this);
            }
        });

        if (CollectionUtils.isNotEmpty(deleteBoardList)) {
            this.getBoardPositions().removeAll(deleteBoardList);
        }

        // 命令列表
        if (this.getCommands() == null) {
            this.setCommands(new ArrayList<>());
        }

        List<DeviceInstanceCmd> deleteCmdList = new ArrayList<>();
        for (DeviceInstanceCmd instanceCmd : this.getCommands()) {
            Optional<DeviceInstanceCmd> optOther = source.getCommands().stream().filter(actOther -> {
                boolean b = Objects.equals(actOther.getId(), instanceCmd.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                instanceCmd.copy(optOther.get());
                instanceCmd.setDeviceInstance(this);
            } else {
                deleteCmdList.add(instanceCmd);
            }

        }

        source.getCommands().forEach(act -> {
            if (act.getId() == null) {
                this.getCommands().add(act);
                act.setDeviceInstance(this);
            }
        });

        if (CollectionUtils.isNotEmpty(deleteCmdList)) {
            this.getCommands().removeAll(deleteCmdList);
        }

    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("name", name)
                .append("description", description)
                .append("layoutNodeId", layoutNodeId)
                .append("config", config)
                .append("mockConfig", mockConfig)
                .append("enableMock", enableMock)
                .append("initialized", initialized)
                .toString();
    }
}
