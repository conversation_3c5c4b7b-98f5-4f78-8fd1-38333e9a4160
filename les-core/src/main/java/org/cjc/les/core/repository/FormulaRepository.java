/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Formula;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-15
**/
public interface FormulaRepository extends JpaRepository<Formula, Long>, JpaSpecificationExecutor<Formula> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Formula findByName(String name);

    /**
     * 通过类型过滤查询
     * @param type 脚本类型
     * @return
     */
    List<Formula> findFormulasByType(String type);
}