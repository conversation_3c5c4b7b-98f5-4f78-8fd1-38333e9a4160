/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.StationRepository;
import org.cjc.les.core.service.StationService;
import org.cjc.les.core.service.dto.*;
import org.cjc.les.core.service.mapstruct.StationMapper;
import org.cjc.les.core.service.mapstruct.StationRunningMapper;
import org.cjc.les.core.service.mapstruct.TaskActionMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.ActionScheduler;
import org.cjc.les.core.task.schedule.StationQueue;
import org.cjc.les.exception.ConfigApplicationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-07-14
**/
@Service
@RequiredArgsConstructor
public class StationServiceImpl implements StationService {

    private final StationRepository stationRepository;
    private final StationMapper stationMapper;
    private final StationRunningMapper stationRunningMapper;

    private final TaskActionMapper taskActionMapper;

    private final ActionScheduler actionScheduler;

    @Override
    public Map<String,Object> queryAll(StationQueryCriteria criteria, Pageable pageable){
        Page<Station> page = stationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        Map<String,Object>  out = PageUtil.toPage(page.map(stationMapper::toDto));

        Collection<StationDto> stationList = (Collection<StationDto>)out.get("content");
        for (StationDto dto : stationList){
            StationQueue stationQueue = actionScheduler.getStationQueueByStationId(dto.getId());
            if (stationQueue == null){
                continue;
            }
            dto.setActionQueueSize(stationQueue.getActionQueueSize());
        }
        return out;
    }

    @Override
    public List<StationDto> queryAll(StationQueryCriteria criteria){
        return stationMapper.toDto(stationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<StationRunningDto> queryAllStationsInRunning(StationQueryCriteria criteria) {
        List<Station> stationList = stationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder));
        List<StationRunningDto> dtoList = stationRunningMapper.toDto(stationList);
        for (StationRunningDto dto : dtoList){
            // 获取设备图像列表
            Station station = stationList.stream().filter(sta->{return sta.getId().equals( dto.getId() ); }).findFirst().get();
            List<String> imgList = new ArrayList<>();
            for (DeviceInstance devIns : station.getDevices()) {
                if (devIns.getLayoutVisible()==null || devIns.getLayoutVisible().booleanValue()) {
                    if (StringUtils.isNotEmpty(devIns.getLayoutImage())) {
                        imgList.add(devIns.getLayoutImage());
                    } else {
                        imgList.add(devIns.getDevice().getLayoutImage());
                    }
                }
            }
            dto.setDeviceImgList(imgList);

            dto.setStatus(RunStatusEnum.READY.name());

            StationQueue stationQueue = actionScheduler.getStationQueueByStationId(dto.getId());
            if (stationQueue == null){
                continue;
            }
            // 获取执行中的动作列表
            dto.setActionQueueSize(stationQueue.getActionQueueSize());
            if (dto.getActionQueueSize().intValue()>0){
                List<TaskActionDto> taskActionDtoList = this.queryActionQueueByStationId(dto.getId());
                dto.setTaskActions(taskActionDtoList);
                dto.setStatus(RunStatusEnum.RUNNING.name());
            }

        }
        return dtoList;
    }

    @Override
    @Transactional
    public StationDto findById(Long id) {
        Station station = stationRepository.findById(id).orElseGet(Station::new);
        ValidationUtil.isNull(station.getId(),"Station","id",id);
        return stationMapper.toDto(station);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StationDto create(Station resources) {
        if(stationRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Station.class,"name",resources.getName());
        }

        for (Action act : resources.getActions() ) {
            act.setStation(resources);
            for (ActionCommand actCmd : act.getCommands()) {
                actCmd.setAction(act);
            }
        }

        return stationMapper.toDto(stationRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Station resources)  throws ConfigApplicationException{
        Station station = stationRepository.findById(resources.getId()).orElseGet(Station::new);
        ValidationUtil.isNull( station.getId(),"Station","id",resources.getId());
        validateUsedProcActions(station, resources);

        Station station1 = null;
        station1 = stationRepository.findByName(resources.getName());
        if(station1 != null && !station1.getId().equals(station.getId())){
            throw new EntityExistException(Station.class,"name",resources.getName());
        }
        station.copy(resources);
        stationRepository.save(station);
    }

    private void validateUsedProcActions(Station rawStation, Station updatedStation) throws ConfigApplicationException{
        List<Action> toRemoveActionList = new ArrayList<>();
        for (Action action : rawStation.getActions()) {
            if (!updatedStation.getActions().stream().anyMatch(act->{
                return action.getId().equals( act.getId());})){
                toRemoveActionList.add(action);
            }
        }
        if (CollectionUtils.isNotEmpty(toRemoveActionList)) {
            Long[] actionIds = toRemoveActionList.stream().map(Action::getId).toArray(Long[]::new);
            List<Map> validList = stationRepository.findUsedProcActionsByActionIds(actionIds);
            if (CollectionUtils.isNotEmpty(validList)){
                ConfigApplicationException expt = new ConfigApplicationException("Existed actions in used procedures.");
                expt.setData(validList);
                throw expt;
            }
        }
    }

    @Override
    public void deleteAll(Long[] ids) throws ConfigApplicationException{

        // 校验待删除的工作站下已存在被引用的动作，列出被引用的步骤名 > 动作名
        List<Map> validList = new ArrayList<>();
        for (Long id : ids) {
            List<Map> procItemNamesDtos = stationRepository.findUsedProcActionsByStationId(id);
            validList.addAll(procItemNamesDtos);
        }
        if (CollectionUtils.isNotEmpty(validList)){
            ConfigApplicationException expt = new ConfigApplicationException("Existed actions in used procedures.");
            expt.setData(validList);
            throw expt;
        }
        for (Long id : ids) {
            stationRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<StationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (StationDto station : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("工作站名称", station.getName());
            map.put("工作站描述", station.getDescription());
            map.put("工作站状态", station.getStatus());
            map.put("是否已被删除,Y/N", station.getDeleteFlag());
            map.put("创建人", station.getCreateBy());
            map.put("创建时间", station.getCreateTime());
            map.put("更新人", station.getUpdateBy());
            map.put("更新时间", station.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<TaskActionDto> queryActionQueueByStationId(Long stationId) {
        List<TaskActionDto> outList = new ArrayList<>();
        List<TaskAction> tempist = new ArrayList<>();
        StationQueue stationQueue = actionScheduler.getStationQueueByStationId(stationId);
        if (stationQueue == null){
            return outList;
        }
        tempist.addAll(stationQueue.getTaskActionsQueue());
        tempist.addAll(stationQueue.getPendingTaskActionsQueue());

        outList =  taskActionMapper.toDto(tempist);
        for (TaskActionDto dto : outList) {
            Optional<TaskAction> taskActionOpt = tempist.stream().filter(a->{return a.getId().longValue() == dto.getId().longValue();}).findFirst();
            if (taskActionOpt.isPresent()){
                TaskExecutorContext context = taskActionOpt.get().getContext();
                Task task = null;
                if (context != null && ((task = context.getTask()) != null)){
                    dto.setTaskNumber(task.getTaskNumber());
                    dto.setTaskName(task.getTaskName());
                    if (context.getLastTaskMethod()!=null) {
                        dto.setTaskMethodName(context.getLastTaskMethod().getName());
                    }
                }
            }
        }

        return outList;
    }
}