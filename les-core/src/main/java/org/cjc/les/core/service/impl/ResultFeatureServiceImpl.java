/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.ResultFeature;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ResultFeatureRepository;
import org.cjc.les.core.service.ResultFeatureService;
import org.cjc.les.core.service.dto.ResultFeatureDto;
import org.cjc.les.core.service.dto.ResultFeatureQueryCriteria;
import org.cjc.les.core.service.mapstruct.ResultFeatureMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-25
**/
@Service
@RequiredArgsConstructor
public class ResultFeatureServiceImpl implements ResultFeatureService {

    private final ResultFeatureRepository resultFeatureRepository;
    private final ResultFeatureMapper resultFeatureMapper;

    @Override
    public Map<String,Object> queryAll(ResultFeatureQueryCriteria criteria, Pageable pageable){
        Page<ResultFeature> page = resultFeatureRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(resultFeatureMapper::toDto));
    }

    @Override
    public List<ResultFeatureDto> queryAll(ResultFeatureQueryCriteria criteria){
        return resultFeatureMapper.toDto(resultFeatureRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public ResultFeatureDto findById(Long id) {
        ResultFeature resultFeature = resultFeatureRepository.findById(id).orElseGet(ResultFeature::new);
        ValidationUtil.isNull(resultFeature.getId(),"ResultFeature","id",id);
        return resultFeatureMapper.toDto(resultFeature);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultFeatureDto create(ResultFeature resources) {
        return resultFeatureMapper.toDto(resultFeatureRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ResultFeature resources) {
        ResultFeature resultFeature = resultFeatureRepository.findById(resources.getId()).orElseGet(ResultFeature::new);
        ValidationUtil.isNull( resultFeature.getId(),"ResultFeature","id",resources.getId());
        resultFeature.copy(resources);
        resultFeatureRepository.save(resultFeature);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            resultFeatureRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ResultFeatureDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ResultFeatureDto resultFeature : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联结果ID", resultFeature.getResultId());
            map.put("结果项ID", resultFeature.getResultItemId());
            map.put("结果特性名称", resultFeature.getName());
            map.put("结果特性描述", resultFeature.getDescription());
            map.put("结果特性值", resultFeature.getValue());
            map.put("是否已被删除,Y/N", resultFeature.getDeleteFlag());
            map.put("创建人", resultFeature.getCreateBy());
            map.put("创建时间", resultFeature.getCreateTime());
            map.put("更新人", resultFeature.getUpdateBy());
            map.put("更新时间", resultFeature.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}