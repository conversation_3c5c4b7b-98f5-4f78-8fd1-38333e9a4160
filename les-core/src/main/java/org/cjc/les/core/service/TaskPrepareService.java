/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.service.dto.BoardDto;
import org.cjc.les.core.service.dto.SampleEntryStepStatusDto;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.dto.TaskPrepareQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-10
**/
public interface TaskPrepareService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskPrepareQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskPrepareDto>
    */
    List<TaskPrepareDto> queryAll(TaskPrepareQueryCriteria criteria);

    /**
     * 查询所有未提交任务的板号，提供给前端用户编辑提交
     * @param criteria 条件参数
     * @return List<TaskPrepareDto>
     */
    List<BoardDto> queryTagsToEdit(TaskPrepareQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskPrepareDto
     */
    TaskPrepareDto findById(Long id);

    /**
     * 查找最近创建的预处理任务
     * @return
     */
    TaskPrepareDto findLatestTaskPrepare();

    /**
    * 创建
    * @param resources /
    * @return TaskPrepareDto
    */
    TaskPrepareDto create(TaskPrepare resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskPrepare resources);

    /**
     * 编辑
     * @param resources /
     */
    TaskPrepare save(TaskPrepare resources);

    /**
     * 失效任务预处理
     *
     * @param taskPrepare
     */
    void cancel(TaskPrepareDto taskPrepare);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskPrepareDto> all, HttpServletResponse response) throws IOException;

    /**
     * 读取RF TAG
     * @param taskPrepare
     */
    void readRfTag( TaskPrepareDto taskPrepare);

    /**
     * 读取RF TAG
     * @param taskPrepare
     */
    void readRfTag2( TaskPrepareDto taskPrepare);

    void acceptSampleToEntryConveyor(List<TaskPrepareDto> taskPrepareDtos);

    /**
     * 查找样品进样第一步状态
     * @param criteria
     * @return
     */
    SampleEntryStepStatusDto querySampleEntryStepStatus(SampleEntryStepStatusDto criteria);

    /**
     * 检查绑定的流程是否存在进样方法
     * @param paramTp
     * @return
     */
    boolean checkIfEntryMethodPresent(TaskPrepare paramTp);

    /**
     * 批量保存
     *
     * @param resources
     */
    List<TaskPrepareDto>  batchSaveTaskPrepares(List<TaskPrepareDto> resources);
}