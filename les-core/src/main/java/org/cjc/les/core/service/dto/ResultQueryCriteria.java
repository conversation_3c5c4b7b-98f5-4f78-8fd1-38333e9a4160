/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import org.cjc.annotation.Query;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2024-06-25
 **/
@Data
public class ResultQueryCriteria {

    /**
     * 检测编号
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String number;

    /**
     * 检测名称
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String name;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime;
}