/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Action;
import org.cjc.les.core.domain.Method;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.Step;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
public interface ProcedureRepository extends JpaRepository<Procedure, Long>, JpaSpecificationExecutor<Procedure> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Procedure findByName(String name);

    /**
     * 通过派生的过程ID查找是否存在派生过程对象
     * @param ids
     * @return
     */
    Optional<Procedure> findProcedureByDeriveProcIds(String ids);

    List<Procedure> findProceduresByIdIn(List<Long> ids);

    List<Procedure> findProceduresByType(String type);
}