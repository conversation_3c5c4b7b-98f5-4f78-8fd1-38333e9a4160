/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.jexl3.*;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.service.FormulaService;
import org.cjc.les.core.service.dto.FormulaDto;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.VariableValueWrapper;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Log4j2
public class FormulaExecutionHelper {

    /**
     * 缓存当前注册的设备代理列表
     */
    private ConcurrentHashMap<Long, DeviceProxyWrapper> deviceHashMap = new ConcurrentHashMap<Long, DeviceProxyWrapper>();

    private JexlEngine jexlEngine;

    private FormulaService formulaService;

    private static class FormulaExecutionHelperHolder {
        private static FormulaExecutionHelper instance = new FormulaExecutionHelper();
    }

    private FormulaExecutionHelper(){

        FormulaFunctionsImpl funcImpl = SpringContextHolder.getBean(FormulaFunctionsImpl.class);
        formulaService = SpringContextHolder.getBean(FormulaService.class);

        Map<String, Object> functions = new HashMap<>();
        functions.put(null, funcImpl);
        jexlEngine = new JexlBuilder()
                .namespaces(functions) // 注册函数
                .create();
    }

    public static FormulaExecutionHelper getInstance(){
        return FormulaExecutionHelperHolder.instance;
    }

    public Map execute(Long scriptId) {
        return execute(scriptId, new HashMap<>());
    }

    public Map execute(Long scriptId, Object param) {
        FormulaDto formulaDto = formulaService.findById(scriptId);
        if (formulaDto == null) {
            throw new TaskRunningException("Could not found script definition by scriptId:"+scriptId);
        }
        return executeScript(formulaDto.getContent(), param);
    }

    public Map execute(String scriptName) {
        return execute(scriptName, new HashMap<>());
    }

    public Map execute(String scriptName, Object param) {
        String formulaContent = formulaService.findFormulaContentByName(scriptName);
        if (StringUtils.isEmpty(formulaContent)) {
            throw new TaskRunningException("Could not found script definition by scriptName:" + scriptName);
        }
        return executeScript(formulaContent, param);
    }

    public boolean executeBooleanScript(String scriptName, Object param) {
        Map retMap = execute( scriptName,  param);
        return StringUtils.equalsAnyIgnoreCase(retMap.get("status").toString(),RunStatusEnum.SUCCESS.name());
    }
    
    public Object evaluate(Long formulaId) {
        FormulaDto formulaDto = formulaService.findById(formulaId);
        if (formulaDto == null) {
            throw new TaskRunningException("Could not found formula definition by scriptId:"+formulaId);
        }
        return executeExpression(formulaDto.getContent());
    }

    public Object evaluate(String formulaName) {
        String formulaContent = formulaService.findFormulaContentByName(formulaName);
        if (StringUtils.isEmpty(formulaContent)) {
            throw new TaskRunningException("Could not found formula definition by formulaName:" + formulaName);
        }
        return executeExpression(formulaContent);
    }

    public Object executeExpression(String express) {

        // 5. 创建上下文并设置变量
        JexlContext context = new MapContext();

        setVariablesFromTaskContext(context);

        JexlExpression customExpression = this.jexlEngine.createExpression(express);

        // 7. 执行表达式
        try {
            Object result = customExpression.evaluate(context);
            log.info("Evaluated expression: {} , returned:{}", express, result);

            return result;
        } catch(JexlException e){
            Throwable throwable = e.getCause();
            if (throwable instanceof TaskRunningException){
                throw (TaskRunningException)throwable;
            }
            log.error("Unknown exception in executeExpression: {}", e.getMessage(), e);
            throw new TaskRunningException("Unknown exception in executeExpression:"+e.getMessage());
        }catch(Throwable e){
            log.error("Unknown exception in executeExpression: {}", e.getMessage(), e);
            throw new TaskRunningException("Unknown exception in executeExpression:"+e.getMessage());
        }
    }

    /**
     * Map out = new HashMap();
     * Object param = new HashMap();
     *  if (param.get("status").equals("SUCCESS")) {
     *      SET_VAR("BALANCE_VALUE",param.get("data").get("actualValue"));
     *      out.put("status", "SUCCESS");
     *      return out;
     *  }else{
     *      out.put("status", "FAILED");
     *      out.put("failedThen", "RETRY");
     *      return out;
     *  }
     * @param script 脚本内容
     * @param param 入参
     * @return 结果对象
     */
    public Map executeScript(String script, Object param) {

        // 5. 创建上下文并设置变量
        JexlContext context = new MapContext();
        context.set("param", param);
        HashMap out = new HashMap();
        out.put("status", RunStatusEnum.SUCCESS.name());
        context.set("out", out);
        setVariablesFromTaskContext(context);
        script = script.replace("\n", "").replace("\t","");
        JexlScript jexlScript = this.jexlEngine.createScript(script);

        // 7. 执行表达式
        try {
            Object result = jexlScript.execute(context);
            log.info("Evaluated script: {} , returned:{}", script, result);
            if (result instanceof Boolean) {
                boolean boolRs = (Boolean)result;
                if (!boolRs) {
                    out.put("status", RunStatusEnum.FAILED.name());
                }
            }
            return out;
        } catch(JexlException e){
            Throwable throwable = e.getCause();
            if (throwable instanceof TaskRunningException){
                throw (TaskRunningException)throwable;
            }
            log.error("Unknown exception in executeScript: {}", e.getMessage(), e);
            throw new TaskRunningException("Unknown exception in executeScript:"+e.getMessage());
        }catch(Throwable e){
            log.error("Unknown exception in executeScript: {}", e.getMessage(), e);
            throw new TaskRunningException("Unknown exception in executeScript:"+e.getMessage());
        }
    }

    private void setVariablesFromTaskContext(JexlContext context) {

        TaskCommand currentCommand = TaskExecutorContext.getTaskCommand();
        if (currentCommand == null) {
            log.warn("No task executing environment.");
            return;
        }


        Long taskMethodId = currentCommand.getTaskMethodId();

        ConcurrentHashMap<String, VariableValueWrapper> varsMap = TaskExecutorContext.getContext().getVariableMap();
        Iterator<Map.Entry<String, VariableValueWrapper>> itr = varsMap.entrySet().iterator();
        while (itr.hasNext()) {
            Map.Entry<String, VariableValueWrapper> entry = itr.next();
            String key = entry.getKey();
            VariableValueWrapper entryValue = entry.getValue();
            Object valueObj = entryValue.getValue();
            if (valueObj instanceof List) {
                List<VariableValueWrapper> valueList = (List<VariableValueWrapper>) valueObj;
                List<Object> oList = valueList.stream().filter(wrapper -> {
                    return taskMethodId.equals(wrapper.getTaskMethodId());
                })
                        .map(VariableValueWrapper::getValue).collect(Collectors.toList());
                context.set(key, oList);
            }
            //else if (taskMethodId.equals(entryValue.getTaskMethodId())){
            else {
                context.set(key, valueObj);
            }
        }

    }


    public static class FunctionObj {

        public static  double max(int a, int b){
            return Math.max(a, b);
        }

        public static double square(double c){
            return c * c;
        }
    }


    @FunctionalInterface
    interface JexlScriptFunction {
        Object apply(Object[] argsList);
    }

    public static void main(String[] args) {

        // 2. 定义表达式
        //String expressionString = "max(a, b) + square(c) + doubleList.get(0)+doubleList.get(doubleList.size()-1)";
        String expressionString = "if (1==1){ return true}else{return false}";
        FormulaExecutionHelper.getInstance().executeExpression(expressionString);

    }
}
