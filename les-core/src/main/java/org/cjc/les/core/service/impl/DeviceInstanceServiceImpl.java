/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.apache.commons.beanutils.BeanUtils;
import org.cjc.les.core.domain.DeviceInstance;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.core.repository.DeviceInstanceRepository;
import org.cjc.les.core.service.DeviceInstanceService;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.dto.DeviceInstanceQueryCriteria;
import org.cjc.les.core.service.mapstruct.DeviceInstanceMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-07-30
**/
@Service
@RequiredArgsConstructor
public class DeviceInstanceServiceImpl implements DeviceInstanceService {

    private final DeviceInstanceRepository deviceInstanceRepository;
    private final DeviceInstanceMapper deviceInstanceMapper;

    @Override
    public Map<String,Object> queryAll(DeviceInstanceQueryCriteria criteria, Pageable pageable){
        Page<DeviceInstance> page = deviceInstanceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(deviceInstanceMapper::toDto));
    }

    @Override
    public List<DeviceInstanceDto> queryAll(DeviceInstanceQueryCriteria criteria){
        return deviceInstanceMapper.toDto(deviceInstanceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public DeviceInstanceDto findById(Long id) {
        DeviceInstance deviceInstance = deviceInstanceRepository.findById(id).orElseGet(DeviceInstance::new);
        ValidationUtil.isNull(deviceInstance.getId(),"DeviceInstance","id",id);
        return deviceInstanceMapper.toDto(deviceInstance);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceInstanceDto create(DeviceInstance resources) {
        return deviceInstanceMapper.toDto(deviceInstanceRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceInstance resources) {
        DeviceInstance deviceInstance = deviceInstanceRepository.findById(resources.getId()).orElseGet(DeviceInstance::new);
        ValidationUtil.isNull( deviceInstance.getId(),"DeviceInstance","id",resources.getId());
        deviceInstance.copy(resources);
        deviceInstanceRepository.save(deviceInstance);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            deviceInstanceRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DeviceInstanceDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DeviceInstanceDto deviceInstance : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("设备名称", deviceInstance.getName());
            map.put("设备描述", deviceInstance.getDescription());
            map.put("设备定义ID", deviceInstance.getDeviceId());
            map.put("配置信息，JSON串，或者普通的字符串", deviceInstance.getConfig());
            map.put("模拟配置, ", deviceInstance.getMockConfig());
            map.put("1:mock_config配置生效，0:config配置生效", deviceInstance.isEnableMock());
            map.put("设备布局ID", deviceInstance.getLayoutId());
            map.put("设备布局节点ID", deviceInstance.getLayoutNodeId());
            map.put("是否已被删除,Y/N", deviceInstance.getDeleteFlag());
            map.put("创建人", deviceInstance.getCreateBy());
            map.put("创建时间", deviceInstance.getCreateTime());
            map.put("更新人", deviceInstance.getUpdateBy());
            map.put("更新时间", deviceInstance.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(readOnly = true)
    public void reload(DeviceInstance deviceInstance) {
        DeviceInstance deviceInstanceInDb = deviceInstanceRepository.findById(deviceInstance.getId()).orElseGet(DeviceInstance::new);
        ValidationUtil.isNull( deviceInstance.getId(),"DeviceInstance","id",deviceInstance.getId());

        try {
            List<DeviceInstanceCmd> cmdList = new ArrayList<>(deviceInstanceInDb.getCommands());
            BeanUtils.copyProperties(deviceInstance, deviceInstanceInDb);
            deviceInstance.getCommands().clear();
            deviceInstance.getCommands().addAll(cmdList);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePositionDetailConfig(DeviceInstanceDto resources) {
        DeviceInstance deviceInstanceInDb = deviceInstanceRepository.findById(resources.getId()).orElseGet(DeviceInstance::new);
        ValidationUtil.isNull( resources.getId(),"DeviceInstance","id",resources.getId());
        deviceInstanceInDb.setPositionDetailConfig(resources.getPositionDetailConfig());
        deviceInstanceRepository.save(deviceInstanceInDb);
    }
}