/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.TaskAction;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.repository.TaskActionRepository;
import org.cjc.les.core.service.TaskActionService;
import org.cjc.les.core.service.dto.TaskActionDto;
import org.cjc.les.core.service.dto.TaskActionQueryCriteria;
import org.cjc.les.core.service.mapstruct.TaskActionMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.cjc.exception.EntityExistException;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-11
**/
@Service
@RequiredArgsConstructor
public class TaskActionServiceImpl implements TaskActionService {

    private final TaskActionRepository taskActionRepository;
    private final TaskActionMapper taskActionMapper;

    @Override
    public Map<String,Object> queryAll(TaskActionQueryCriteria criteria, Pageable pageable){
        Page<TaskAction> page = taskActionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskActionMapper::toDto));
    }

    @Override
    public List<TaskActionDto> queryAll(TaskActionQueryCriteria criteria){
        return taskActionMapper.toDto(taskActionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskActionDto findById(Long id) {
        TaskAction taskAction = taskActionRepository.findById(id).orElseGet(TaskAction::new);
        ValidationUtil.isNull(taskAction.getId(),"TaskAction","id",id);
        return taskActionMapper.toDto(taskAction);
    }

    @Override
    public TaskAction findTaskActionByTaskCommandId(Long taskCommandId) {
        TaskAction taskAction = taskActionRepository.findTaskActionByTaskCommandId(taskCommandId);
        return taskAction;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskActionDto create(TaskAction resources) {
        return taskActionMapper.toDto(taskActionRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskAction resources) {
        TaskAction taskAction = taskActionRepository.findById(resources.getId()).orElseGet(TaskAction::new);
        ValidationUtil.isNull( taskAction.getId(),"TaskAction","id",resources.getId());
        taskAction.copy(resources);
        taskActionRepository.save(taskAction);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            taskActionRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TaskActionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskActionDto taskAction : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("命令名称", taskAction.getName());
            map.put("命令描述", taskAction.getDescription());
            map.put("关联设备ID", taskAction.getStationId());
            map.put("动作运行状态:READY,RUNNING,SUCCESS,FAILED", taskAction.getStatus());
            map.put("任务ID", taskAction.getTaskId());
            map.put("任务方法ID", taskAction.getTaskMethodId());
            map.put("任务步骤ID", taskAction.getTaskStepId());
            map.put("配置的动作ID", taskAction.getActionId());
            map.put("是否已被删除,Y/N", taskAction.getDeleteFlag());
            map.put("创建人", taskAction.getCreateBy());
            map.put("创建时间", taskAction.getCreateTime());
            map.put("更新人", taskAction.getUpdateBy());
            map.put("更新时间", taskAction.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateExecutedTime(TaskAction resources) {
        taskActionRepository.updateExecutedTime(resources.getId(), resources.getExecutedStart(), resources.getExecutedDuration());
    }
}