/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Function;
import org.cjc.les.core.constants.SysVariableNameEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.ResultFeatureDto;
import org.cjc.les.core.service.dto.ResultItemDto;
import org.cjc.les.core.service.dto.TaskToCheckSamplesReqDto;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskGlobalConfig;
import org.cjc.les.core.task.execute.VariableValueWrapper;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Log4j2
@Service
@RequiredArgsConstructor
public class FormulaFunctionsImpl {

    public static double max(int a, int b) {
        return Math.max(a, b);
    }

    public static double square(double c) {
        return c * c;
    }

    @Function(name = "SET_VAR", tip = "设置环境变量",
            usage = "SET_VAR(变量名,变量值)")
    public static void SET_VAR(String varName, Object varValue) {
        TaskExecutorContext.setVar(varName, varValue);
    }
    @Function(name = "ADD_VAR", tip = "新增环境变量",
            usage = "ADD_VAR(变量名,变量值)")
    public static void ADD_VAR(String varName, Object varValue) {
        TaskExecutorContext.addVar(varName, varValue);
    }

    @Function(name = "GET_VAL", tip = "获取变量值,若变量类型为List则获取最后一个，否则返回本身",
            usage = "GET_VAL(变量)")
    public static Object GET_VAL(Object varValue) {
        if (varValue instanceof List) {
            List<Object> oList = (List)varValue;
            if (CollectionUtils.isEmpty(oList)){
                return null;
            }
            return oList.get(oList.size()-1);
        }
        return varValue;
    }
    public static Object GET_VAL(Object varValue, int index) {
        if (varValue instanceof List) {
            List<Object> oList = (List)varValue;
            if (CollectionUtils.isEmpty(oList)){
                return null;
            }
            return oList.get(index);
        }
        return varValue;
    }

    @Function(name = "GET_NUM", tip = "获取变量值,若变量类型为List则获取最后一个，否则返回本身，转换为数字类型",
            usage = "GET_NUM(变量)")
    public static BigDecimal GET_NUM(Object varValue) {
        Object ret = GET_VAL(varValue);
        if (ret instanceof BigDecimal){
            return (BigDecimal)ret;
        }else{
            return new BigDecimal(ret.toString());
        }
    }
    public static BigDecimal GET_NUM(Object varValue, int index) {
        Object ret = GET_VAL(varValue, index);
        if (ret instanceof BigDecimal){
            return (BigDecimal)ret;
        }else{
            return new BigDecimal(ret.toString());
        }
    }

    @Function(name = "DELAY_MS", tip = "延迟(millisecond)",
            usage = "DELAY_MS(毫秒数)")
    public static void DELAY_MS(long milliSec) {
        try {
            Thread.sleep(milliSec);
        } catch (InterruptedException e) {
            e.printStackTrace();
            log.error("DELAY_MS error: {}", e.getMessage(), e);
        }
    }

    @Function(name = "LOG", tip = "记录日志",
            usage = "LOG(Object)")
    public static void LOG(Object msg) {
        log.info("LOG: {}", msg);
    }

    @Function(name = "CALL_FORMULA", tip = "公式调用,返回计算结果值",
            usage = "CALL_FORMULA(公式名称)")
    public static Object CALL_FORMULA(String formulaName) {
        return FormulaExecutionHelper.getInstance().evaluate(formulaName);
    }

    @Function(name = "CALL_SCRIPT", tip = "脚本调用,返回执行状态MAP",
            usage = "CALL_SCRIPT(脚本名称)")
    public static Map CALL_SCRIPT(String scriptName) {
        return FormulaExecutionHelper.getInstance().execute(scriptName);
    }


    @Function(name = "SAVE_RS", tip = "保存结果",
            usage = "SAVE_RS(code,name,value)")
    public static void SAVE_RS(String code, String name, Object value) {
        ResultHelper.saveResultFeature(code, name, value == null ? "" : value.toString());
    }
    public static void SAVE_RS(ResultFeatureDto featureDto) {
        ResultHelper.saveResultFeature(featureDto,0);
    }
    public static void SAVE_RS(ResultFeatureDto featureDto, int stage) {
        ResultHelper.saveResultFeature(featureDto,stage);
    }

    @Function(name = "GET_POSITION_CODE", tip = "获取点位编码",
            usage = "GET_POSITION_CODE(deviceInstanceId, deviceInstanceName, positionName, positionStatus, options)")
    public static String GET_POSITION_CODE(long deviceInstanceId,
                                           String deviceInstanceName,
                                           String positionName,
                                           String positionStatus){
        return GET_POSITION_CODE(deviceInstanceId, deviceInstanceName, positionName, positionStatus, null);
    }
    public static String GET_POSITION_CODE(long deviceInstanceId,
                                           String deviceInstanceName,
                                           String positionName,
                                           String positionStatus, String options) {
        return PositionHelper.getPositionCode(deviceInstanceId, deviceInstanceName, positionName, positionStatus, options);
    }

    @Function(name = "GET_LAST_POS_SERVICE_CODE", tip = "获取最后一次选取的点位服务码",
            usage = "GET_LAST_POS_SERVICE_CODE()")
    public static String GET_LAST_POS_SERVICE_CODE() {
        Optional<Position> posOpt = TaskExecutorContext.getContext().getTaskPositionBuffer().getLastPosition();
        if (posOpt.isPresent()){
            String serviceCode = posOpt.get().getServiceCode();
            if (StringUtils.isEmpty(serviceCode)){
                throw new TaskRunningException("Could not found serviceCode, pos: " + posOpt.get());
            }
            return serviceCode;
        }
        throw new TaskRunningException("Could not found last position.");
    }

    @Function(name = "SAVE_REDIRECT", tip = "保存重定向点位信息",
            usage = "SAVE_REDIRECT()")
    public static void SAVE_REDIRECT() {
        Optional<Position> posOpt = TaskExecutorContext.getContext().getTaskPositionBuffer().getLastPosition();
        if (posOpt.isPresent()){
            RepoManagementHelper repoManagementHelper = SpringContextHolder.getBean(RepoManagementHelper.class);
            repoManagementHelper.computePositionAfterRedirect(posOpt.get());
            return;
        }
        throw new TaskRunningException("Could not found last position.");
    }

    /**
     * {"tag":"VFREDDFE","sampleCount":0,"sampleList":[{"posIndex":0}]}
     * @return
     */
    @Function(name = "GET_TO_CHECK_SAMPLES", tip = "获取待检测的样品清单",
            usage = "GET_TO_CHECK_SAMPLES()")
    public static String GET_TO_CHECK_SAMPLES() {
        TaskToCheckSamplesReqDto dto = new TaskToCheckSamplesReqDto();

        Task task = TaskExecutorContext.getTask();
        if (task!=null && task.getBatchId() != null) {
            Long batchId = task.getBatchId();
            TaskService taskService = SpringContextHolder.getBean(TaskService.class);
            dto = taskService.getTaskToCheckSamplesReqByBatchId(batchId);
        }
        return JSON.toJSONString(dto);
    }

    /**
     * 校验通过后的样品绑定到对应任务
     * @param boardPosName 给定的仓库进样板位名称
     */
    @Function(name = "BIND_ENTRY_SAMPLES_TO_TASK", tip = "校验通过后的样品绑定到对应任务",
            usage = "BIND_ENTRY_SAMPLES_TO_TASK(boardPosName)")
    public static void BIND_ENTRY_SAMPLES_TO_TASK(String boardPosName) {
        Task task = TaskExecutorContext.getTask();
        if (task!=null && task.getBatchId() != null) {
            Long batchId = task.getBatchId();
            TaskService taskService = SpringContextHolder.getBean(TaskService.class);
            TaskToCheckSamplesReqDto dto = taskService.getTaskToCheckSamplesReqByBatchId(batchId);
            dto.setTaskId(task.getId());
            PositionService positionService = SpringContextHolder.getBean(PositionService.class);
            positionService.bindBoardToTasks(dto, boardPosName);
        }

    }

    @Function(name = "GET_POSITION_STATUS", tip = "通过设备实例ID和点位名称查找点位状态",
            usage = "GET_POSITION_STATUS(deviceInstanceId, positionName )")
    public static String GET_POSITION_STATUS(long deviceInstanceId,
                                             String positionName) {
        PositionService positionService = SpringContextHolder.getBean(PositionService.class);
        String ret = positionService.findPositionStatus(deviceInstanceId, positionName);
        return ret;
    }

    @Function(name = "CHECK_ALL_ENTRY_DONE", tip = "齐套计算，应用在放置目标完成后，目标点位是否批次到齐，到齐后设置状态为HOLD",
            usage = "CHECK_ALL_ENTRY_DONE( )")
    public static void CHECK_ALL_ENTRY_DONE() {
        Object var = TaskExecutorContext.getContext().getLocalVarObj(SysVariableNameEnum.SELECTED_POSITIONS.name());
        if (var instanceof List) {
            List<VariableValueWrapper> vaList = (List<VariableValueWrapper>) var;
            if (CollectionUtils.isNotEmpty(vaList) && vaList.size() >= 2) {
                VariableValueWrapper va = vaList.get(1); // 获取目标点位
                Object value = va.getValue();
                if (value instanceof Position) {
                    Position pos = (Position) value;
                    PositionService positionService = SpringContextHolder.getBean(PositionService.class);
                    positionService.checkAllEntryDone(pos);

                }
            }
        }
    }

    @Function(name = "CHECK_ALL_EXIT_RESET", tip = "齐套重置，应用在移除所有源物后，源点位是否批次清空，清空后设置状态为IDLE",
            usage = "CHECK_ALL_EXIT_RESET( )")
    public static void CHECK_ALL_EXIT_RESET() {
        Object var = TaskExecutorContext.getContext().getLocalVarObj(SysVariableNameEnum.SELECTED_POSITIONS.name());
        if (var instanceof List) {
            List<VariableValueWrapper> vaList = (List<VariableValueWrapper>) var;
            if (CollectionUtils.isNotEmpty(vaList) && vaList.size() >= 2) {
                VariableValueWrapper va = vaList.get(0); // 获取目标点位
                Object value = va.getValue();
                if (value instanceof Position) {
                    Position pos = (Position) value;
                    PositionService positionService = SpringContextHolder.getBean(PositionService.class);
                    positionService.checkAllExitReset(pos);
                }
            }
        }
    }

    @Function(name = "VALIDATE_TAG", tip = "校验扫描得到的TAG值，是否为已提交的任务? 若是，选择对应的任务继续执行，否则，提示校验错误并回收样品",
            usage = "VALIDATE_TAG( )")
    public static String VALIDATE_TAG(String tagVarName) {

        String tag = TaskExecutorContext.getVarAsString(tagVarName);
        TaskService taskService = SpringContextHolder.getBean(TaskService.class);

        return taskService.validateTag(tag);
    }

    @Function(name = "INIT_INDICATOR_TO_CONFIRM", tip = "门框解锁后, 设置所有INDICATOR/CONTROLLER点位为待确认状态",
            usage = "INIT_INDICATOR_TO_CONFIRM()")
    public static void INIT_INDICATOR_TO_CONFIRM() {

        PositionService positionService = SpringContextHolder.getBean(PositionService.class);
        positionService.resetAllIndicatorsToConfirm();
    }

    @Function(name = "ALL_INDICATOR_CONFIRMED", tip = "检查所有INDICATOR/CONTROLLER点位为待确认状态",
            usage = "ALL_INDICATOR_CONFIRMED()")
    public static boolean ALL_INDICATOR_CONFIRMED() {
        PositionService positionService = SpringContextHolder.getBean(PositionService.class);
        return positionService.checkAllIndicatorConfirmed();
    }


    @Function(name = "IS_TASK_STARTED", tip = "检查任务是否开启",
            usage = "IS_TASK_STARTED()")
    public static boolean IS_TASK_STARTED() {
        return TaskGlobalConfig.getInstance().getInfoDto().isStarted();
    }


    @Function(name = "CHECK_SAMPLE_USED_DONE", tip = "检查样品是否使用完成, 当前任务的所有检测方法是否都已使用过, 若是继续设置状态位DONE，否则设置位HOLD",
            usage = "CHECK_SAMPLE_USED_DONE()")
    public static void CHECK_SAMPLE_USED_DONE() {
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        Task task = TaskExecutorContext.getTask();
        Object objSampleUsedCount = TaskExecutorContext.getVar("SAMPLE_USED_COUNT");
        int sampleUsedCount = 0;
        if (objSampleUsedCount != null) {
            sampleUsedCount = (Integer) objSampleUsedCount;
        }
        int mainMethodCount = task.getMainMethodList().size();

        if (mainMethodCount - 1 == sampleUsedCount) {
            // Default: DONE
        } else {
            TaskExecutorContext.setVar("SAMPLE_USED_COUNT", Integer.valueOf(sampleUsedCount + 1));
            List<String> toUpdateStatusArr = taskCommand.getPositionExecutedStatusAsArray();
            String lastStatus = toUpdateStatusArr.get(toUpdateStatusArr.size() - 1);
            String[] statusArr = lastStatus.split("@");
            if (statusArr.length == 2) {
                lastStatus = "HOLD@" + statusArr[1];
            } else {
                statusArr = lastStatus.split(":");
                if (statusArr.length == 2) {
                    lastStatus = "HOLD:" + statusArr[1];
                } else {
                    lastStatus = "HOLD";
                }
            }
            toUpdateStatusArr.set(toUpdateStatusArr.size() - 1, lastStatus);
            taskCommand.setPositionExecutedStatus(JSON.toJSONString(toUpdateStatusArr));
        }

    }


    @Function(name = "METHOD_IS_PARALLEL", tip = "当前运行的任务方法是否为并行检测",
            usage = "METHOD_IS_PARALLEL()")
    public static boolean METHOD_IS_PARALLEL() {
        TaskMethod taskMethod = TaskExecutorContext.getTaskMethod();
        if (taskMethod != null) {
            return StringUtils.equalsAny(taskMethod.getParallel(), "Y");
        }
        return false;
    }

    @Function(name = "PARALLEL_METHOD_FIRST_RS_ALL_DONE", tip = "平行样首次检测结果是否都已完成",
            usage = "PARALLEL_METHOD_FIRST_RS_ALL_DONE()")
    public static boolean PARALLEL_METHOD_FIRST_RS_ALL_DONE() {
        TaskMethod taskMethod = TaskExecutorContext.getTaskMethod();
        if (taskMethod != null) {
            Long methodId = taskMethod.getMethodId();
            List<TaskMethod> taskMethodList = TaskExecutorContext.getTaskMethodsByMethodId(methodId);
            if (CollectionUtils.isEmpty(taskMethodList)){
                return false;
            }
            Object obj = TaskExecutorContext.getVar("ICP_SAMPLE_ITEM");
            if (obj instanceof List) {
                List<VariableValueWrapper> listValues = (List<VariableValueWrapper>) obj;
                if (CollectionUtils.isEmpty(listValues)) {
                    return false;
                }
                long rsCount = taskMethodList.stream().filter(tmd->{
                    for (VariableValueWrapper wrapper : listValues) {
                        ResultItemDto rsItem = (ResultItemDto) wrapper.getValue();
                        if ( tmd.getId().equals( wrapper.getTaskMethodId()) ) {
                            return true;
                        }
                    }
                    return false;
                }).count();

                return rsCount == taskMethodList.size();
            }

            return StringUtils.equalsAny(taskMethod.getParallel(), "Y");
        }
        return false;
    }

    @Function(name = "PARALLEL_METHOD_NEED_TO_REDO", tip = "并行检测方法是否需要重新执行",
            usage = "PARALLEL_METHOD_NEED_TO_REDO()")
    public static boolean PARALLEL_METHOD_NEED_TO_REDO() {
        TaskMethod taskMethod = TaskExecutorContext.getTaskMethod();
        if (taskMethod != null && !StringUtils.equalsAny(taskMethod.getParallel(), "Y")) {
            return false;
        }
        return ResultHelper.checkParallelResult();
    }



    public static void main(String[] args) {
        List<String> strList = new ArrayList<>();
        strList.add("a");
        strList.add("b");
        String out = JSON.toJSONString(strList);
        System.out.println("out="+out);
    }




}
