/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.service.dto.AlertLogDto;
import org.cjc.les.core.service.dto.AlertLogQueryCriteria;
import org.cjc.les.core.service.dto.AlertStatItemDto;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-10-14
**/
public interface AlertLogService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(AlertLogQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<AlertLogDto>
    */
    List<AlertLogDto> queryAll(AlertLogQueryCriteria criteria);

    /**
     * 查询所有数据不分页
     * @return List<AlertLogDto>
     */
    List<AlertLogDto> findLatestAlertLogInInitStatus();

    List<AlertStatItemDto> findAlertLogStatsLevelInInitStatus();

    /**
     * 根据ID查询
     * @param id ID
     * @return AlertLogDto
     */
    AlertLogDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return AlertLogDto
    */
    AlertLogDto create(AlertLog resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(AlertLog resources);

    void createOrUpdate(AlertLog resources);

    void fixLog(AlertLog resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<AlertLogDto> all, HttpServletResponse response) throws IOException;
}