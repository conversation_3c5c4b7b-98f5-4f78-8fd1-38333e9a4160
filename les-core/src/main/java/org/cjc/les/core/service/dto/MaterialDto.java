/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.cjc.les.core.domain.DeviceInstance;

import jakarta.persistence.Column;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-03-26
**/
@Data
public class MaterialDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 物料编号 */
    private String number;

    /** 物料名称 */
    private String name;

    /** 物料描述 */
    private String description;

    private String image;

    private BigDecimal totalValue;

    private BigDecimal remainValue;

    /** 预估使用量 */
    private BigDecimal toUseValue;

    private String unit;

    private Long deviceInstanceId;

    private String deviceInstanceName;

    private String lastAddedBy;

    private Timestamp lastAddedTime;

    private BigDecimal warnValue;

    private Timestamp lastWarnTime;

    private BigDecimal errorValue;

    private Timestamp lastErrorTime;

    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}