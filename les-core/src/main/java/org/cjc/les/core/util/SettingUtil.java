package org.cjc.les.core.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.service.SettingService;
import org.cjc.les.core.service.dto.SettingDto;
import org.cjc.utils.SpringContextHolder;

@Log4j2
public class SettingUtil {

    /**
     * 获取全局设置
     * @param path
     * @return
     */
    public static boolean getGlobalSettingAsBoolean(String path) {
        SettingService settingService = SpringContextHolder.getBean(SettingService.class);
        SettingDto dto = settingService.queryLatestOne();
        String globalSettingStr = dto.getGlobalSettings();
        if (StringUtils.isEmpty(globalSettingStr)){
            return false;
        }
        JSONObject rootObj = JSON.parseObject(globalSettingStr);
        Object retObj = JSONPath.eval(rootObj,path);
        if (retObj instanceof Boolean){
            return ((Boolean) retObj).booleanValue();
        }

        log.warn("Could not found path {} as type boolean", path);
        return false;
    }
}
