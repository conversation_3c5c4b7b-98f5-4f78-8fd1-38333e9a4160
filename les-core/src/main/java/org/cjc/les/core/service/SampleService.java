/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.service.dto.SampleDto;
import org.cjc.les.core.service.dto.SampleQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-11
**/
public interface SampleService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SampleQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SampleDto>
    */
    List<SampleDto> queryAll(SampleQueryCriteria criteria);

    /**
     * 检测样品名称
     * @param resource
     * @return
     */
    SampleDto checkSampleName(Sample resource);

    /**
     * 查询最近一次提交的样品
     * @return
     */
    SampleDto queryLatestSample();

    /**
     * 根据ID查询
     * @param id ID
     * @return SampleDto
     */
    SampleDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return SampleDto
    */
    SampleDto create(SampleDto resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Sample resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
     * 保存
     * @param resources
     * @return
     */
    Sample save(Sample resources);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SampleDto> all, HttpServletResponse response) throws IOException;
}