/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-03-26
**/
@Entity
@Data
@Table(name="les_material")
@SQLDelete(sql = "UPDATE les_material SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class Material extends LesConfigBaseEntity {


    public static enum StatusEnum {
        NORMAL,
        WARN,
        ERROR
    };

    @Column(name = "number")
    @Schema(description = "物料编号")
    private String number;

    @Column(name = "name",unique = true)
    @Schema(description = "物料名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "物料描述")
    private String description;

    @Column(name = "image")
    @Schema(description = "物料图标")
    private String image;

    @Column(name = "total_value")
    @Schema(description = "总量")
    private BigDecimal totalValue;

    @Column(name = "remain_value")
    @Schema(description = "剩余量")
    private BigDecimal remainValue;

    @Column(name = "unit")
    @Schema(description = "物料单位")
    private String unit;

    @JoinColumn(name = "device_instance_id")
    private Long deviceInstanceId;

    @JoinColumn(name = "device_instance_name")
    private String deviceInstanceName;


    @Column(name = "last_added_by")
    @Schema(description = "最后添料人")
    private String lastAddedBy;

    @Column(name = "last_added_time")
    @Schema(description = "最后添料时间")
    private Timestamp lastAddedTime;

    @Column(name = "warn_value")
    @Schema(description = "警告量")
    private BigDecimal warnValue;

    @Column(name = "last_warn_time")
    @Schema(description = "最后警告时间")
    private Timestamp lastWarnTime;

    @Column(name = "error_value")
    @Schema(description = "错误量")
    private BigDecimal errorValue;

    @Column(name = "last_error_time")
    @Schema(description = "最后错误时间")
    private Timestamp lastErrorTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    @Schema(description = "物料状态:NORMAL,WARN,ERROR")
    private StatusEnum status;

    public void copy(Material source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}