/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import org.cjc.annotation.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-25
**/
@Data
public class ResultFeatureQueryCriteria{

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime = new ArrayList<>();

}