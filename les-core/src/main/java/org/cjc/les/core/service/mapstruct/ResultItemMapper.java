/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.ResultItem;
import org.cjc.les.core.service.dto.ResultItemDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-25
**/
@Mapper(componentModel = "spring",uses = {ResultFeatureMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ResultItemMapper extends BaseMapper<ResultItemDto, ResultItem> {

    @Mapping(source = "taskMethod.name", target = "taskMethodName")
    @Override
    ResultItemDto toDto(ResultItem entity);
}