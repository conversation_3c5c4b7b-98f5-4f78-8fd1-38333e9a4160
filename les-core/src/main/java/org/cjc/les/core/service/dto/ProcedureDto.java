/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.ProcedureMethod;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
 *
 * 用于流程的编排（新增，编辑，查询详情）
 *
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Data
public class ProcedureDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工作站名称 */
    private String name;

    /** 工作站描述 */
    private String description;

    /** 开启进程方法ID */
    private Long entryMethodId;

    /** 结束进程方法ID */
    private Long exitMethodId;

    /** DAG图,X6实现 */
    private String dagJson;

    private String dagNodeId;

    /**
     * 流程类型,
     * RAW: 原始类型,最多只存在一个主体方法
     * DERIVE: 派生类型, 从一个或多个RAW原始类型进程中派生出来，该类型进程通常由用户在前端界面选择生成，
     *         其选择的多个流程应该具有相同的进样方法,
     *         该类型进程不允许直接编辑其关联的方法及其下的子节点
     */
    private String type;

    /**
     * 当type=DERIVE时，该字段存储对应被派生的流程ID列表，存在多个被派生流程ID时，以英文逗号分割
     */
    private String deriveProcIds;

    /**
     * 当type=DERIVE时，该字段存储对应被派生的流程名称列表，存在多个被派生流程时，以英文逗号分割
     */
    private List<String> deriveProcNames;

    /**
     * 流程编排时以此列表存储数据
     */
    private List<ProcedureMethod> methods;

    /**
     * 流程绑定的变量列表
     */
    private List<ProcedureVariableDto> variables;

    /** 工作站状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}