/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.SampleCategory;
import org.cjc.les.core.service.dto.SampleCategoryDto;
import org.cjc.les.core.service.dto.SampleCategoryQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-11-22
**/
public interface SampleCategoryService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SampleCategoryQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SampleCategoryDto>
    */
    List<SampleCategoryDto> queryAll(SampleCategoryQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return SampleCategoryDto
     */
    SampleCategoryDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return SampleCategoryDto
    */
    SampleCategoryDto create(SampleCategory resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(SampleCategory resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SampleCategoryDto> all, HttpServletResponse response) throws IOException;
}