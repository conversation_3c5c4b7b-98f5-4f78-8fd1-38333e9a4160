/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-27
**/
@Entity
@Data
@Table(name="les_procedure_variable")
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@SQLDelete(sql = "update les_procedure_variable set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class ProcedureVariable  extends LesConfigBaseEntity {

    @JSONField(serialize = false)
    @ManyToOne
    @JoinColumn(name = "procedure_id", referencedColumnName = "id")
    private Procedure procedure;

    @ManyToOne
    @JoinColumn(name = "variable_id", referencedColumnName = "id")
    private Variable variable;

    @Column(name = "default_value")
    @Schema(description = "默认值")
    private String defaultValue;

    @Column(name = "value")
    @Schema(description = "当前值")
    private String value;

    @Column(name = "order_index")
    @Schema(description = "列表顺序下标")
    private Integer orderIndex;

    public void copy(ProcedureVariable source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}