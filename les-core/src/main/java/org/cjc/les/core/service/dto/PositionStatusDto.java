/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.Position;

import java.io.Serializable;

@Data
public class PositionStatusDto implements Serializable {
    /**
     * 点位ID
     */
    private Long id;
    /**
     * 设备实例ID
     */
    private Long deviceInstanceId;
    /**
     * 设备实例名称
     */
    private String deviceInstanceName;
    /**
     * 点位名称
     */
    private String name;
    /**
     * 点位状态
     */
    private Position.StatusEnum status;

    /**
     * 状态变更命令: reset: 重置为原始状态， 主要用在TO_CONFIRM状态还原为INIT_STATUS,
     */
    private String changeCmd;

    /**
     * 当changeCmd=reset时有效, true: 所有与当前点位关联的点位及物料被重置, false则只重置当前点位
     */
    private boolean resetAssociations = false;
}
