/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Formula;
import org.cjc.les.core.service.dto.FormulaDto;
import org.cjc.les.core.service.dto.FormulaQueryCriteria;
import org.cjc.les.core.service.dto.FormulaSmallDto;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-11-15
**/
public interface FormulaService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(FormulaQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<FormulaDto>
    */
    List<FormulaDto> queryAll(FormulaQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return FormulaDto
     */
    FormulaDto findById(Long id);

    /**
     * 通过脚本名称查找详情
     * @param name
     * @return
     */
    FormulaDto queryByName(String name);

    /**
     * 通过公式名称查询公式内容
     * @param name
     * @return
     */
    String findFormulaContentByName(String name);

    /**
    * 创建
    * @param resources /
    * @return FormulaDto
    */
    FormulaDto create(Formula resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Formula resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<FormulaDto> all, HttpServletResponse response) throws IOException;

    /**
     * 按照类型过滤脚本列表
     * @param type 脚本类型
     * @return
     */
    List<FormulaSmallDto> queryForSelection(String type);
}