/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.Station;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskActionExecutor;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.utils.HttpClientUtil;
import org.cjc.utils.SpringContextHolder;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 工作站队列, 管理该工作站待执行的动作清单
 */
@Log4j2
public class StationQueue {

    private ExecutorService executorService ;

    private AtomicBoolean daemonStarted = new AtomicBoolean(false);
    private ExecutorService daemonService = Executors.newSingleThreadExecutor();

    private Station station;

    private LinkedBlockingQueue<Runnable> taskActionsExecuteQueue = new LinkedBlockingQueue<>();

    /**
     * 动作执行队列
     */
    private LinkedBlockingQueue<TaskAction> taskActionsQueue = new LinkedBlockingQueue<>();
    /**
     * 挂起动作队列, 当不满足动作谓词条件时，进入挂起队列，并轮询直到条件满足时，重新推送至执行队列
     */
    private LinkedBlockingQueue<TaskAction> pendingTaskActionsQueue = new LinkedBlockingQueue<>();


    public StationQueue(Station station) {
        this.station = station;
        Integer maxThreadSize = station.getMaxThreadSize()==null?1:station.getMaxThreadSize();

        executorService = new ThreadPoolExecutor(maxThreadSize, maxThreadSize,
                60, TimeUnit.MILLISECONDS,
                taskActionsExecuteQueue, new StationThreadFactory(), new WaitingInRunningQueuePolicy());
    }

    public int getActionQueueSize(){
        return taskActionsQueue.size();
    }
    public LinkedBlockingQueue<TaskAction> getTaskActionsQueue() {
        return taskActionsQueue;
    }
    public LinkedBlockingQueue<TaskAction> getPendingTaskActionsQueue() {
        return pendingTaskActionsQueue;
    }

    /**
     * A handler for rejected tasks that throws a
     * {@code RejectedExecutionException}.
     */
    static class WaitingInRunningQueuePolicy implements RejectedExecutionHandler {
        /**
         * Creates an {@code AbortPolicy}.
         */
        public WaitingInRunningQueuePolicy() { }

        /**
         * Always throws RejectedExecutionException.
         *
         * @param r the runnable task requested to be executed
         * @param e the executor attempting to execute this task
         * @throws RejectedExecutionException always
         */
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            if (r instanceof  TaskActionRunnable){
                TaskActionRunnable taskActionRunnable = (TaskActionRunnable)r;
                TaskAction tAct = taskActionRunnable.getTaskAction();
                tAct.setStatus(RunStatusEnum.IN_RUNNING_QUE.name());
            }

        }
    }

    /**
     * The default thread factory
     */
     class StationThreadFactory implements ThreadFactory {
        private final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        StationThreadFactory() {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() :
                    Thread.currentThread().getThreadGroup();
            namePrefix = "station-" + station.getId()+"-"+
                    poolNumber.getAndIncrement() +
                    "-thread-";
        }

        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r,
                    namePrefix + threadNumber.getAndIncrement(),
                    0);
            if (t.isDaemon())
                t.setDaemon(false);
            if (t.getPriority() != Thread.NORM_PRIORITY)
                t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }
    }


    /**
     * 记录进入队列执行对象详情
     */
    @Data
    public class TaskActionRunnable implements Runnable {

        private TaskExecutorContext context = TaskExecutorContext.getContext();

        private TaskAction taskAction;

        private ExecutorConfig config;

        public TaskActionRunnable(TaskAction taskAction, ExecutorConfig config) {
            this.taskAction = taskAction;
            this.config = config;
        }

        @Override
        public void run() {
            PredicateValidator validator = SpringContextHolder.getBean(PredicateValidator.class);

            try {
                taskAction.setThread(Thread.currentThread());
                TaskExecutorContext.copy(context, taskAction);
                PredicateValidator.ValidateResult rs = validator.validate(taskAction);
                if (rs.isValid()) {
                    new TaskActionExecutor(this.config).execute(taskAction);
                }else{
                    // 不满足预设条件，默认或者WAIT策略，挂起等待重试，SKIP退出当前动作执行
                    if (StringUtils.equalsAny("WAIT", rs.getUnmatchedThen())){
                        taskAction.setStatus(RunStatusEnum.SUSPEND.name());
                        if (config.isRecordLog()) {
                            TaskExecuteLogUtil.logAction(taskAction);
                        }
                        taskActionsQueue.remove(taskAction);
                        pendingTaskActionsQueue.offer(taskAction);
                    }else if (StringUtils.equalsAny("SKIP", rs.getUnmatchedThen())){
                        taskAction.setStatus(RunStatusEnum.SKIPPED.name());
                        if (config.isRecordLog()) {
                            TaskExecuteLogUtil.logAction(taskAction);
                        }
                    }else {
                        if (StringUtils.equalsAny("SKIP", taskAction.getUnmatchedThen())) {
                            taskAction.setStatus(RunStatusEnum.SKIPPED.name());
                            if (config.isRecordLog()) {
                                TaskExecuteLogUtil.logAction(taskAction);
                            }
                        } else {
                            taskAction.setStatus(RunStatusEnum.SUSPEND.name());
                            if (config.isRecordLog()) {
                                TaskExecuteLogUtil.logAction(taskAction);
                            }
                            taskActionsQueue.remove(taskAction);
                            pendingTaskActionsQueue.offer(taskAction);
                        }
                    }
                }
            }catch (Throwable e){
                log.error("Unknown exception e: {}", e.getMessage(), e);
            }finally {
                validator.unlock(taskAction.getPredicates());
                taskActionsQueue.remove(taskAction);
                TaskExecutorContext.clear();
            }
        }
    }


    private void submit(Runnable runnable) {
        executorService.submit(runnable);
    }

    public void submit(TaskAction taskAction, ExecutorConfig config) {
        taskAction.setContext(TaskExecutorContext.getContext());
        taskAction.setExecutorConfig(config);
        taskAction.setStatus(RunStatusEnum.IN_SCHEDULE_QUE.name());
        if (config.isRecordLog()) {
            TaskExecuteLogUtil.logAction(taskAction);
        }
        taskActionsQueue.offer(taskAction);
        taskAction.setEnterQueueTime(Timestamp.valueOf(LocalDateTime.now()));
        executorService.submit(new TaskActionRunnable(taskAction, config));
    }

    public void execute(TaskAction taskAction, ExecutorConfig config) {
        startDaemon();

        submit(taskAction, config);

        do {
            if (StringUtils.equalsAny(taskAction.getStatus(),
                    RunStatusEnum.SUCCESS.name(),
                    RunStatusEnum.FAILED.name(),
                    RunStatusEnum.CANCELLED.name(),
                    RunStatusEnum.SKIPPED.name())) {
                taskActionsQueue.remove(taskAction);
                break;
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } while (true);
    }

    public void cancel(TaskAction taskAction) {
        TaskAction actionInQue = taskAction;
        for (TaskAction action : taskActionsQueue) {
            if (action.getId().equals(taskAction.getId())){
                actionInQue = action;
            }
        }
        if (!StringUtils.equalsAny(actionInQue.getStatus(),
                RunStatusEnum.CANCELLED.name(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name())) {
            actionInQue.setStatus(RunStatusEnum.CANCELLED.name());
            for (TaskCommand cmd : actionInQue.getCommands()) {
                if (!StringUtils.equalsAny(cmd.getStatus(),
                        RunStatusEnum.CANCELLED.name(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name())) {
                    cmd.setStatus(RunStatusEnum.CANCELLED.name());
                }
            }
        }

        taskActionsQueue.remove(actionInQue);
        if (actionInQue.getThread() != null) {
            actionInQue.getThread().interrupt();
        }

        pendingTaskActionsQueue.remove(actionInQue);

    }


    public void startDaemon() {
        if (daemonStarted.compareAndSet(false, true)) {
            daemonService.submit(new Runnable() {
                @Override
                public void run() {
                    checkPendingQueue();
                }
            });
        }
    }

    private void checkPendingQueue() {
        PredicateValidator validator = SpringContextHolder.getBean(PredicateValidator.class);

        while (true) {
            Iterator<TaskAction> itrPendingTaskActions = pendingTaskActionsQueue.iterator();
            while (itrPendingTaskActions.hasNext()){
                TaskAction taskAction = itrPendingTaskActions.next();
                TaskExecutorContext context = taskAction.getContext();
                TaskExecutorContext.copy(context, taskAction);
                PredicateValidator.ValidateResult rs = validator.validate(taskAction);
                if (rs.isValid()) {
                    itrPendingTaskActions.remove();
                    submit(taskAction, taskAction.getExecutorConfig());
                }
            }

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

}
