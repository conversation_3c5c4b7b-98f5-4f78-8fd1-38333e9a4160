/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.ProcedureMethod;
import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.service.dto.ProcedureDto;
import org.cjc.les.core.service.dto.ProcedureSmallDto;
import org.cjc.les.core.service.dto.SampleDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProcedureSmallMapper extends BaseMapper<ProcedureSmallDto, Procedure> {


    @Mapping(target = "entryMethodId", expression = "java(toEntryMethodId(entity))")
    @Mapping(target = "entryMethodName", expression = "java(toEntryMethodName(entity))")
    @Override
    ProcedureSmallDto toDto(Procedure entity);

    default Long toEntryMethodId(Procedure entity) {
        Optional<ProcedureMethod> entryMethodOpt = entity.getEntryMethod();
        if (entryMethodOpt.isPresent()) {
            return entryMethodOpt.get().getMethod().getId();
        }
        return null;
    }

    default String toEntryMethodName(Procedure entity) {
        Optional<ProcedureMethod> entryMethodOpt = entity.getEntryMethod();
        if (entryMethodOpt.isPresent()) {
            return entryMethodOpt.get().getMethod().getName();
        }
        return null;
    }
}