/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Variable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-17
**/
public interface VariableRepository extends JpaRepository<Variable, Long>, JpaSpecificationExecutor<Variable> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Variable findByName(String name);

    /**
     * 从指令参数中查找
     * @return
     */
    @Query(value="select insc.parameter from les_device_instance_cmd  insc where insc.delete_flag='N' and insc.parameter like :pattern " +
            "UNION " +
            "select actc.parameter from les_action_command actc where actc.delete_flag='N' and actc.parameter like :pattern " +
            "UNION " +
            "select sact.variables from les_step_action sact where sact.delete_flag='N' and sact.variables like :pattern " +
            "UNION " +
            "select sact.variables from les_method_step sact where sact.delete_flag='N' and sact.variables like :pattern " +
            "UNION " +
            "select sact.variables from les_procedure_method sact where sact.delete_flag='N' and sact.variables like :pattern ", nativeQuery = true)
    List<String> findAllVarStrFromCmdParameters(@Param("pattern") String pattern);

    @Query(value="select distinct proc.id from les_device_instance_cmd  inscmd " +
            "join les_action_command actcmd on actcmd.command_id=inscmd.id " +
            "join les_step_action stpact on stpact.action_id=actcmd.action_id " +
            "join les_method_step mtdstp on mtdstp.step_id=stpact.step_id " +
            "join les_procedure_method prcmtd on prcmtd.method_id=mtdstp.method_id " +
            "join les_procedure proc on proc.id=prcmtd.procedure_id " +
            " where inscmd.delete_flag='N' and actcmd.delete_flag='N' and stpact.delete_flag='N' and mtdstp.delete_flag='N' and prcmtd.delete_flag='N' and proc.delete_flag='N' " +
            " and inscmd.parameter like :pattern " +
            "UNION " +
            "select distinct proc.id from les_action_command actcmd " +
            "join les_step_action stpact on stpact.action_id=actcmd.action_id " +
            "join les_method_step mtdstp on mtdstp.step_id=stpact.step_id " +
            "join les_procedure_method prcmtd on prcmtd.method_id=mtdstp.method_id " +
            "join les_procedure proc on proc.id=prcmtd.procedure_id " +
            " where actcmd.delete_flag='N' and stpact.delete_flag='N' and mtdstp.delete_flag='N' and prcmtd.delete_flag='N' and proc.delete_flag='N' " +
            " and actcmd.parameter like :pattern " +
            "UNION " +
            "select distinct proc.id from les_step_action stpact " +
            "join les_method_step mtdstp on mtdstp.step_id=stpact.step_id " +
            "join les_procedure_method prcmtd on prcmtd.method_id=mtdstp.method_id " +
            "join les_procedure proc on proc.id=prcmtd.procedure_id " +
            " where stpact.delete_flag='N' and mtdstp.delete_flag='N' and prcmtd.delete_flag='N' and proc.delete_flag='N' " +
            " and stpact.variables like :pattern " +
            "UNION " +
            "select distinct proc.id from les_method_step mtdstp " +
            "join les_procedure_method prcmtd on prcmtd.method_id=mtdstp.method_id " +
            "join les_procedure proc on proc.id=prcmtd.procedure_id " +
            " where mtdstp.delete_flag='N' and prcmtd.delete_flag='N' and proc.delete_flag='N' " +
            " and mtdstp.variables like :pattern " +
            "UNION " +
            "select distinct proc.id from les_procedure_method prcmtd " +
            "join les_procedure proc on proc.id=prcmtd.procedure_id " +
            " where prcmtd.delete_flag='N' and proc.delete_flag='N' " +
            " and prcmtd.variables like :pattern ", nativeQuery = true)
    List<Long> findBindProcedureIdsByVarNamePattern(@Param("pattern") String pattern);

    /**
     * 从公式脚本中查找
     * @return
     */
    @Query(value = "select f.content from les_formula f where f.delete_flag = 'N' and f.content like '%VAR%'", nativeQuery = true)
    List<String> findAllVarStrFromFormulaContent();

    @Query(value="select distinct proc.id from les_formula  fm " +
            "join les_action_command actcmd on actcmd.post_execution=fm.name " +
            "join les_step_action stpact on stpact.action_id=actcmd.action_id " +
            "join les_method_step mtdstp on mtdstp.step_id=stpact.step_id " +
            "join les_procedure_method prcmtd on prcmtd.method_id=mtdstp.method_id " +
            "join les_procedure proc on proc.id=prcmtd.procedure_id " +
            " where fm.delete_flag='N' and actcmd.delete_flag='N' and stpact.delete_flag='N' and mtdstp.delete_flag='N' and prcmtd.delete_flag='N' and proc.delete_flag='N' " +
            " and fm.content like :pattern ", nativeQuery = true)
    List<Long> findBindProcedureIdsByVarNameInFormula(@Param("pattern") String pattern);
}