/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.ProcedureVariable;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 用于流程的编排（新增，编辑，查询详情）
 *
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Data
public class ProcedureWithVariablesDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工作站名称 */
    private String name;

    /** 工作站描述 */
    private String description;

    /** 流程类型:RAW, DERIVE */
    private String type;

    /**
     * 流程绑定的变量列表
     */
    private List<ProcedureVariableDto> variables = new ArrayList<>();

    /** 工作站状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}