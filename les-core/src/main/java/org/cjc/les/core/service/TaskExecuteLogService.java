/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.TaskExecuteLog;
import org.cjc.les.core.service.dto.TaskExecuteLogDto;
import org.cjc.les.core.service.dto.TaskExecuteLogQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-08-23
**/
public interface TaskExecuteLogService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskExecuteLogQueryCriteria criteria, Pageable pageable);

    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryLatestTaskExecuteLog(TaskExecuteLogQueryCriteria criteria, Pageable pageable);

    /**
     * 查询指定节点的运行日志
     * @param criteria 条件参数
     * @return List<TaskExecuteLogDto>
     */
    List<TaskExecuteLogDto> queryTaskExecuteLogForNode(TaskExecuteLogQueryCriteria criteria);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskExecuteLogDto>
    */
    List<TaskExecuteLogDto> queryAll(TaskExecuteLogQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskExecuteLogDto
     */
    TaskExecuteLogDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return TaskExecuteLogDto
    */
    TaskExecuteLogDto create(TaskExecuteLog resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskExecuteLog resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskExecuteLogDto> all, HttpServletResponse response) throws IOException;
}