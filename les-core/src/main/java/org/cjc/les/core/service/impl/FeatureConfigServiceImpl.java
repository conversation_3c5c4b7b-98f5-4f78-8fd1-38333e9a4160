/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.FeatureConfig;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.FeatureConfigRepository;
import org.cjc.les.core.service.FeatureConfigService;
import org.cjc.les.core.service.dto.FeatureConfigDto;
import org.cjc.les.core.service.dto.FeatureConfigQueryCriteria;
import org.cjc.les.core.service.mapstruct.FeatureConfigMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-07-16
**/
@Service
@RequiredArgsConstructor
public class FeatureConfigServiceImpl implements FeatureConfigService {

    private final FeatureConfigRepository featureConfigRepository;
    private final FeatureConfigMapper featureConfigMapper;

    @Override
    public Map<String,Object> queryAll(FeatureConfigQueryCriteria criteria, Pageable pageable){
        Page<FeatureConfig> page = featureConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(featureConfigMapper::toDto));
    }

    @Override
    public List<FeatureConfigDto> queryAll(FeatureConfigQueryCriteria criteria){
        return featureConfigMapper.toDto(featureConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FeatureConfigDto findById(Long id) {
        FeatureConfig featureConfig = featureConfigRepository.findById(id).orElseGet(FeatureConfig::new);
        ValidationUtil.isNull(featureConfig.getId(),"FeatureConfig","id",id);
        return featureConfigMapper.toDto(featureConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FeatureConfigDto create(FeatureConfig resources) {
        return featureConfigMapper.toDto(featureConfigRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FeatureConfig resources) {
        FeatureConfig featureConfig = featureConfigRepository.findById(resources.getId()).orElseGet(FeatureConfig::new);
        ValidationUtil.isNull( featureConfig.getId(),"FeatureConfig","id",resources.getId());
        featureConfig.copy(resources);
        featureConfigRepository.save(featureConfig);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            featureConfigRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FeatureConfigDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FeatureConfigDto featureConfig : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联les_method，检测方法标识", featureConfig.getMethodId());
            map.put("特性编码", featureConfig.getCode());
            map.put("特性名称", featureConfig.getName());
            map.put("仪器对应字段名", featureConfig.getInstrumentColumnName());
            map.put("结果特性描述", featureConfig.getDescription());
            map.put("数值单位", featureConfig.getUnit());
            map.put("结论判定规则集", featureConfig.getCheckRules());
            map.put("是否已被删除,Y/N", featureConfig.getDeleteFlag());
            map.put("创建人", featureConfig.getCreateBy());
            map.put("创建时间", featureConfig.getCreateTime());
            map.put("更新人", featureConfig.getUpdateBy());
            map.put("更新时间", featureConfig.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}