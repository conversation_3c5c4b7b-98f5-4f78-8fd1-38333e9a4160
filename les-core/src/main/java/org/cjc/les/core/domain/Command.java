/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;

/**
 * 命令对象
 */
@Log4j2
@Entity
@Data
@Table(name="les_command")
@SQLDelete(sql = "UPDATE les_command SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class Command extends LesConfigBaseEntity {
    /**
     * 命令名称，界面显示用，支持中英文
     */
    private String name;

    /**
     * 命令描述
     */
    private String description;

    /**
     * 命令类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name="type")
    private CommandTypeEnum type = CommandTypeEnum.NORMAL;

    /**
     * 该命令绑定的Action
     */
    transient private Action action;

    transient private Long deviceId;

    /**
     * 该命令所属的设备
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "device_id", referencedColumnName = "id", nullable = false)
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = Device.class)
    @Schema(description = "关联设备", hidden = true)
    private Device device;

    /**
     * 执行的JAVA方法名称
     */
    private String javaMethodName;

    /**
     * 执行方法的入参
     */
    @Column(name="parameter_template")
    private String parameterTemplate;

    transient private Object parameter;

    /**
     * 参数类型, String,Integer, etc..
     */
    private String parameterType;

    /**
     *
     * 命令执行失败后，执行的动作
     * PAUSE: 暂停，等待人工选择处理方式，, 默认, 未知异常时，让人工确认执行方式，防止误操作导致撞击.
     * CONTINUE:继续下一个命令,
     * RETRY:重试,
     * RETURN_ACTION:返回到ACTION,
     * RETURN_STEP:返回到步骤,
     * RETURN_METHOD: 返回到方法,
     * RETURN_PROCEDURE:返回到进程
     */
    transient private FailedThenEnum failedThen = FailedThenEnum.PAUSE;

    /**
     * 备注说明
     */
    transient private String comment;

    /**
     * 命令运行状态
     */
    transient private CommandStatusEnum status = CommandStatusEnum.READY;

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("name", name)
                .append("description", description)
                .append("javaMethodName", javaMethodName)
                .append("parameterTemplate", parameterTemplate)
                .append("parameterType", parameterType)
                .append("comment", comment)
                .append("status", status)
                .toString();
    }
}
