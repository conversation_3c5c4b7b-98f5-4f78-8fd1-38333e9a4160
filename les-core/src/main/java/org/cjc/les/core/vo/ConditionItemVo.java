/*
 *  Copyright 2024-2024 Wuhan Annis Robot Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.vo;

import lombok.Data;

/**
 * 谓词条件项：
 */
@Data
public class ConditionItemVo {

    public static enum CompareOperatorEnum {
        eq,
        ne,
        gt,
        lt,
        gte,
        lte
    }

    public static enum JoinOperatorEnum {
        and,
        or
    }

    /**
     * 连接操作符：逻辑操作符号, and, or
     */
    private String joinOperator;

    /**
     * 条件名，显示在前端界面
     */
    private String name;

    /**
     * 源值选取类型：position-select, command
     */
    private String sourceType;

    /**
     * 源值参数，配合sourceType用来生成源值
     */
    private String sourceParameter;

    /**
     * 比较操作符，当前支持如下 等于：eq, 不等于：ne, 大于：gt, 小于：lt
     */
    private String compareOperator;

    /**
     * 比较值
     */
    private String targetValue;

    /**
     * 当条件不满足时的处理方式：
     * WAIT: 当前步骤或动作进入挂起队列，其后续条件项不再执行, 等待条件满足后继续执行
     * SKIP: 跳过当前步骤或动作，其后续条件项不再执行
     * DEFAULT或空: 默认根据joinOperator确定后续条件想如何执行
     */
    private String unmatchedThen;
}
