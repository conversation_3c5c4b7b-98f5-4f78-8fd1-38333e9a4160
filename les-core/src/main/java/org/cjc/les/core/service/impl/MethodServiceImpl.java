/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.exception.EntityExistException;
import org.cjc.les.core.domain.Method;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.MethodRepository;
import org.cjc.les.core.service.MethodService;
import org.cjc.les.core.service.dto.MethodDto;
import org.cjc.les.core.service.dto.MethodQueryCriteria;
import org.cjc.les.core.service.mapstruct.MethodMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-08
**/
@Service
@RequiredArgsConstructor
public class MethodServiceImpl implements MethodService {

    private final MethodRepository methodRepository;
    private final MethodMapper methodMapper;

    @Override
    public Map<String,Object> queryAll(MethodQueryCriteria criteria, Pageable pageable){
        Page<Method> page = methodRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(methodMapper::toDto));
    }

    @Override
    public List<MethodDto> queryAll(MethodQueryCriteria criteria){
        return methodMapper.toDto(methodRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public MethodDto findById(Long id) {
        Method method = methodRepository.findById(id).orElseGet(Method::new);
        ValidationUtil.isNull(method.getId(),"Method","id",id);
        return methodMapper.toDto(method);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MethodDto create(Method resources) {
        if(methodRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Method.class,"name",resources.getName());
        }
        return methodMapper.toDto(methodRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Method resources) {
        Method method = methodRepository.findById(resources.getId()).orElseGet(Method::new);
        ValidationUtil.isNull( method.getId(),"Method","id",resources.getId());
        Method method1 = null;
        method1 = methodRepository.findByName(resources.getName());
        if(method1 != null && !method1.getId().equals(method.getId())){
            throw new EntityExistException(Method.class,"name",resources.getName());
        }
        method.copy(resources);
        methodRepository.save(method);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            methodRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<MethodDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MethodDto method : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("工作站名称", method.getName());
            map.put("工作站描述", method.getDescription());
            map.put("工作站状态", method.getStatus());
            map.put("是否已被删除,Y/N", method.getDeleteFlag());
            map.put("创建人", method.getCreateBy());
            map.put("创建时间", method.getCreateTime());
            map.put("更新人", method.getUpdateBy());
            map.put("更新时间", method.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}