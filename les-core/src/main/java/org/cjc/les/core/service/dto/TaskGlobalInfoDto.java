/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 任务全局信息
 *
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-06-11
 **/
@Data
public class TaskGlobalInfoDto implements Serializable {

    /**
     * 任务开启状态, True/False
     */
    private volatile boolean started;

    /**
     * 任务开启时间
     */
    private Timestamp startTime;

    /**
     * 最后一次运行状态
     */
    private String lastStatus;

    /**
     * 消息详情
     */
    private String message;

    /**
     * 最后一次执行的任务命令ID
     */
    private Long lastExecutedTaskCommandId;
}