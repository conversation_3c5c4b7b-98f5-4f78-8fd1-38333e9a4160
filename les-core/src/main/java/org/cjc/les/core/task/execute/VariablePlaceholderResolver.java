/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.helper.FormulaExecutionHelper;
import org.cjc.les.core.vo.ConfigItemVo;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量占位符解析
 */
public class VariablePlaceholderResolver {
    //private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{(.+?)(:(.+?))?\\}");
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{((?:[^:{}'\"\\[\\]()]+|'[^']*'|\"[^\"]*\"|\\{[^}]*\\}|\\([^)]*\\))+)(?:\\s*:\\s*([^}]+))?\\}");
    private static final Pattern PLACEHOLDER_PATTERN2 = Pattern.compile("\"\\#\\{(.+?)(:(.+?))?\\}\"");

    private static final Pattern PLACEHOLDER_PROPNAME_PATTERN = Pattern.compile("\\#(.+?)");
    private static final Pattern PLACEHOLDER_FUNC_PATTERN = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\((?:[^()]+|\\([^)]*\\))*\\)");


    public static String resolve(String input) {
        String out = resolve(input, PLACEHOLDER_PATTERN);
        out = resolve(out, PLACEHOLDER_PATTERN2);
        return out;
    }

    public static String resolve(String input, Pattern pattern) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }
        Matcher matcher = pattern.matcher(input);

        StringBuffer resolvedString = new StringBuffer();

        while (matcher.find()) {
            int groupCount = matcher.groupCount();

            String propertyName = matcher.group(1); // 提取属性名
            String defaultValue = matcher.group(2); // 提取默认值

            //String defaultValue = matcher.group(3);

            StringBuffer innerResolvedStr = new StringBuffer();

            if (StringUtils.isEmpty(defaultValue)) {

                boolean isResolved = resolvePropertyValue(propertyName, innerResolvedStr);
            } else {
                // 尝试获取环境变量的值
                boolean isResolved = resolvePropertyValue(propertyName, innerResolvedStr);

                if (!isResolved) {
                    if (innerResolvedStr.length()>0) {
                        innerResolvedStr.delete(0, innerResolvedStr.length() );
                    }
                    // 如果环境变量不存在或为空，则使用默认值
                    isResolved = resolvePropertyValue(defaultValue, innerResolvedStr);
                }
            }

            // 替换占位符为解析后的值
            //    matcher.appendReplacement(resolvedString, resolvedValue);
            // 替换占位符为解析后的值
            String tmpStr = Matcher.quoteReplacement(innerResolvedStr.toString());
            matcher.appendReplacement(resolvedString, tmpStr);
        }

        if (resolvedString.length() == 0) {
            return input;
        } else {

            matcher.appendTail(resolvedString);
            return resolvedString.toString();
        }
    }

    private static boolean resolvePropertyValue(String propertyName, StringBuffer outBuf) {
        // 尝试获取环境变量的值
        String resolvedValue = TaskExecutorContext.getVarAsString(propertyName);
        if (StringUtils.isNotEmpty(resolvedValue)) {
            outBuf.append(resolvedValue);
            return true;
        }

        Matcher matcher = PLACEHOLDER_PROPNAME_PATTERN.matcher(propertyName);

        if (matcher.find()) {
            int groupCount = matcher.groupCount();

            String name = matcher.group(1);

            outBuf.append(executeFormula(name));
            return true;

        }
        Matcher matcherFunc = PLACEHOLDER_FUNC_PATTERN.matcher(propertyName);
        if (matcherFunc.find()){
            outBuf.append(executeFunc(propertyName));
            return true;
        }

        outBuf.append(propertyName);

        return false;
    }

    private static String executeFormula(String formula) {
        Object outResult = FormulaExecutionHelper.getInstance().evaluate(formula);
        return outResult.toString();
    }

    private static String executeFunc(String funcStr) {
        Object outResult = FormulaExecutionHelper.getInstance().executeExpression(funcStr);
        return outResult.toString();
    }

    public static Optional<ConfigItemVo> find(String input) {
        if (StringUtils.isEmpty(input)) {
            return Optional.empty();
        }
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(input);

        while (matcher.find()) {
            String propertyName = matcher.group(1); // 提取属性名
            String defaultValue = matcher.group(2); // 提取默认值
            ConfigItemVo outItem = new ConfigItemVo();
            outItem.setKey(propertyName);
            outItem.setDefaultValue(defaultValue);

            return Optional.of(outItem);
        }
        return Optional.empty();
    }
    public static boolean find(String input, ConfigItemVo outItem) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(input);

        while (matcher.find()) {
            String propertyName = matcher.group(1); // 提取属性名
            String defaultValue = matcher.group(2); // 提取默认值
            outItem.setKey(propertyName);
            outItem.setDefaultValue(defaultValue);

            return true;
        }
        return false;
    }

    /**
     * 查找适配的所有占位符
     *
     * @param input       解析字符串
     * @param outItemList 返回的列表
     * @return
     */
    public static boolean find(String input, List<ConfigItemVo> outItemList) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        if (outItemList == null) {
            throw new IllegalArgumentException("outItemList is null");
        }
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(input);

        while (matcher.find()) {
            String propertyName = matcher.group(1); // 提取属性名
            String defaultValue = matcher.group(2); // 提取默认值
            if (defaultValue!=null && defaultValue.startsWith(":")){
                defaultValue = defaultValue.substring(1);
            }
            ConfigItemVo outItem = new ConfigItemVo();
            outItem.setKey(propertyName);
            outItem.setDefaultValue(defaultValue);
            outItemList.add(outItem);
        }
        return outItemList.size() > 0;
    }

    // 递归遍历 JSONObject 并转换字段值的方法
    public static void deepTraverseAndConvert(JSONObject jsonObject) {
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);

            if (value instanceof JSONObject) {
                // 如果值是 JSONObject，则递归调用
                deepTraverseAndConvert((JSONObject) value);
            } else if (value instanceof JSONArray) {
                // 如果值是 JSONArray，则遍历数组并递归处理每个元素
                JSONArray array = (JSONArray) value;
                for (int i = 0; i < array.size(); i++) {
                    Object arrayElement = array.get(i);
                    if (arrayElement instanceof JSONObject) {
                        deepTraverseAndConvert((JSONObject) arrayElement);
                    } else {
                        // 对数组中的基本类型元素进行转换
                        array.set(i, convertValue(arrayElement));
                    }
                }
            } else {
                // 对基本类型的字段值进行转换
                jsonObject.put(key, convertValue(value));
            }
        }
    }

    // 字段值转换方法，可以根据需求进行修改
    public static Object convertValue(Object value) {
        if (value == null){
            return null;
        }
        if (value instanceof String) {
            // 例如，将字符串转换为大写
            return resolve((String) value);
        } else {
            return resolve(value.toString());
        }
    }

    public static void main(String[] args) {
        String[] inputs = {
                "${GET_POSITION_CODE(33,'容器货架设备','货架烧杯点位','READY','{useRedirectedPos:true}'):'Aa'}",
                "${GET_POSITION_CODE(33,'容器货架设备','货架烧杯点位','READY','{useRedirectedPos:true}')}",
                "${TASK_ID}",
                "${ICP_CHECK_REDO:true}",
                "${ICP_PATH:D:\\DDa\\er.esws}"
        };

        Pattern pattern = Pattern.compile("\\$\\{((?:[^:{}'\"\\[\\]()]+|'[^']*'|\"[^\"]*\"|\\{[^}]*\\}|\\([^)]*\\))+)(?:\\s*:\\s*([^}]+))?\\}");

        for (String input : inputs) {
            Matcher matcher = pattern.matcher(input);
            if (matcher.find()) {
                System.out.println("Input: " + input);
                System.out.println("Expression: " + matcher.group(1));
                System.out.println("Default Value: " + matcher.group(2));
                System.out.println();
            } else {
                System.out.println("No match: " + input);
            }
        }
StringBuffer strBuf = new StringBuffer();
     //   String input = "324";
        String result = null;
/*
        boolean ret = resolvePropertyValue("FORMULA()", strBuf);
        System.out.println("strBuf = " + strBuf + ", ret=" + ret);

        TaskExecutorContext.setVar("my.property.name", "AA");
        // String input = "${my.property.name:defaultValue}";
        String input = "324";
        String result = resolve(input);
        System.out.println("Resolved " + input + " is value: " + result);

        input = "${my.property.name:324}";
        result = resolve(input);
        System.out.println("Resolved " + input + " is value: " + result);

        TaskExecutorContext.setVar("my.property.name", "");
        input = "${my.property.name: FORMULA()}";
        //TaskExecutorContext.setVar("my.property.name", "ee");
        result = resolve(input);
        System.out.println("Resolved " + input + " is value: " + result);

        input = "${ FORMULA() }";
        //TaskExecutorContext.setVar("my.property.name", "ee");
        result = resolve(input);
        System.out.println("Resolved " + input + " is value: " + result);


        input = "${VAR_EE}";
        //TaskExecutorContext.setVar("my.property.name", "ee");
        result = resolve(input);
        System.out.println("Resolved " + input + " is value: " + result);
*/
        String input = "{\"taskId\":\"${TASK_ID}\"}";
        TaskExecutorContext.setVar("TASK_ID", "123");
        result = resolve(input);
        System.out.println("Resolved " + input + " is value: " + result);

    }
}
