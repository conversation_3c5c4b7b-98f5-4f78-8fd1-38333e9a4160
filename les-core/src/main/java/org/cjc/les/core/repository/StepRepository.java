/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Step;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
public interface StepRepository extends JpaRepository<Step, Long>, JpaSpecificationExecutor<Step> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Step findByName(String name);
    @Query(value = "select stp.* from les_step stp where stp.delete_flag='N' and not exists (select 1 from les_method_step mstp join les_method mth on mth.id=mstp.method_id and mth.delete_flag='N' where mstp.step_id=stp.id and mstp.delete_flag='N')", nativeQuery = true)
    List<Step> findUnRefSteps();
}