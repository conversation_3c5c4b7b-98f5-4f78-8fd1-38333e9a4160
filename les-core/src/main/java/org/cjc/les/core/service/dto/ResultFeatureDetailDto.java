/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-25
**/
@Data
public class ResultFeatureDetailDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 关联结果ID */
    private Long resultId;

    /** 检测编号 */
    private String resultNumber;

    /** 检测名称 */
    private String resultName;

    /** 检测描述 */
    private String resultDescription;

    /** 样品编号 */
    private String sampleNumber;

    /**
     * 样品标签
     */
    private String sampleTag;

    /**
     * 样品名称
     */
    private String sampleName;

    /**
     * 样品分类
     */
    private String sampleCategory;

    /**
     * 送样客户
     */
    private String sampleCustomer;

    /** 结果项ID */
    private Long resultItemId;

    /**
     * 检测方法名称
     */
    private String taskMethodName;

    /**
     * 特性编码
     */
    private String code;

    /** 结果特性名称 */
    private String name;

    /** 结果特性描述 */
    private String description;

    /** 结果特性值 */
    private String value;

    private String unit;

    /**
     * 标准偏差
     */
    private String rsd;

    /**
     * 第一次检测结果
     */
    private String firstValue;

    /**
     * 第二次检测结果
     */
    private String secondValue;

    /**
     * 原始检测结果
     */
    private String rawValue;

    /**
     * 结论判定规则
     */
    private String checkRules;

    /**
     * 结论
     */
    private String conclusion;

    /**
     * 异常原因
     */
    private String reason;

    /**
     * 配置ID
     */
    private Long featureConfigId;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}