/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.Position;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-10-31
**/
@Data
public class DevInstStatusDto implements Serializable {

    static public enum OperationEnum {
        POSITION_CHANGE,
        DEV_INST_STATUS_CHANGE
    }

    /**
     * 消息操作类别: POSITION_CHANGE, DEV_INST_STATUS_CHANGE
     */
    private OperationEnum operation;

    /** 主键Position ID or DeviceInstanceID */
    private Long id;

    /** 设备名称 */
    private String name;

    /** 设备类型 */
    private String type;

    /** 设备描述 */
    private String description;

    /** 设备定义ID */
    private Long deviceId;

    /**
     * 设备实例初始化状态: true, false
     */
    private boolean initialized;

    /**
     * 设备实例状态
     */
    private String status;
    /**
     * 错误消息
     */
    private String message;
}