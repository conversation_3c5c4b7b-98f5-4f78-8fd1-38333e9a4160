/*
 *  Copyright 2024-2024 <PERSON>han Annis Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.SysVariableNameEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.CommandExecuteHook;
import org.cjc.les.core.helper.DeviceProxyWrapper;
import org.cjc.les.core.helper.FormulaExecutionHelper;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.DeviceInstanceService;
import org.cjc.les.core.service.MaterialService;
import org.cjc.les.core.service.TaskCommandService;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.core.util.TaskBreakPointUtil;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.core.vo.MaterialConsumptionVo;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
public class TaskCommandExecutor implements TaskExecutorInterface<TaskCommand>, CommandExecuteHook {
    private int retries = 0;

    private Task task = new Task();
    private ExecutorConfig config;

    private TaskCommand taskCommand;

    private TaskCommandService taskCommandService;

    public TaskCommandExecutor(ExecutorConfig config) {
        this.config = config;
        Task t = TaskExecutorContext.getTask();
        if (t != null) {
            this.task = t;
        }
        this.taskCommandService = SpringContextHolder.getBean(TaskCommandService.class);
    }

    @Override
    public CommandReturn execute(TaskCommand taskCommand) {
        TaskBreakPointUtil.checkBreakPoint(taskCommand);

        this.taskCommand = taskCommand;
        TaskExecutorContext.setTaskCommand(taskCommand);

        CommandReturn cmdRet = new CommandReturn();
        cmdRet.setStatus(RunStatusEnum.SUCCESS);
        // 已完成状态的，不再执行
        if (StringUtils.equalsAny(taskCommand.getStatus(),
                CommandStatusEnum.SUCCESS.name(), CommandStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())) {
            log.error("The command: {} has been executed yet, do not execute it repeat.", taskCommand.getCommand().getName());
            cmdRet.setStatus(RunStatusEnum.FAILED);
            cmdRet.setErrorCode(ErrorCodeEnum.WARN);
            cmdRet.setErrorMsg("The command has been executed yet, do not execute it repeat.");
            return cmdRet;
        }

        DeviceInstance deviceInstance = taskCommand.getCommand().getRealDeviceInstance();
        DeviceProxyWrapper deviceProxy = deviceInstance.getProxy();
        if (deviceProxy == null) {
            log.error("Not found device for this command: {}", taskCommand.getCommand().getName());
            taskCommand.setStatus(CommandStatusEnum.FAILED.name());
            taskCommand.setMessage("Not found device");
            cmdRet.setStatus(RunStatusEnum.FAILED);
            cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
            cmdRet.setErrorMsg("Not found device");
            if (this.config.isRecordLog()) {
                TaskExecuteLogUtil.logCommand(taskCommand);
            }
            waitToManualOperation(taskCommand, cmdRet);
            return cmdRet;
        }

        while (true) {
            if (!deviceProxy.makeSureInitialized()) {
                log.error("Device:{} is not initialized for this command: {}", deviceInstance.getName(), taskCommand.getCommand().getName());
                taskCommand.setStatus(CommandStatusEnum.FAILED.name());
                taskCommand.setMessage("Device is not initialized");
                cmdRet.setStatus(RunStatusEnum.FAILED);
                cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                cmdRet.setErrorMsg("Device is not initialized");
                if (this.config.isRecordLog()) {
                    TaskExecuteLogUtil.logCommand(taskCommand);
                }
                // 记录异常告警
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("TASK_CMD");
                alertLog.setAlertSourceId(taskCommand.getId());
                alertLog.setAlertLevel(AlertLog.AlertLevelEnum.ERROR.name());
                alertLog.setMessage(taskCommand.getMessage());
                alertLog.setAlertCode("TASK_CMD_INIT_ERROR");
                alertLog.setAlertName(taskCommand.getMessage());
                AlertLogService alertLogService = SpringContextHolder.getBean(AlertLogService.class);
                alertLogService.createOrUpdate(alertLog);

                // 等待用户处理异常
                waitToManualOperation(taskCommand, cmdRet);

                // 修复异常告警
                alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
                alertLog.setFixedBy("System");
                alertLogService.fixLog(alertLog);

                if (StringUtils.equalsIgnoreCase(taskCommand.getFailureFixAs(), FailedThenEnum.RETRY.name())
                        && !StringUtils.equalsAny(taskCommand.getStatus(), RunStatusEnum.CANCELLED.name())) {
                    continue;
                }
                return cmdRet;
            }
            break;
        }


        cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);
        do {
            taskCommand.setStatus(CommandStatusEnum.RUNNING.name());
            taskCommand.setMessage(taskCommand.getComment());
            // 前端界面用户提示信息
            if (this.config.isRecordLog()) {
                TaskExecuteLogUtil.logCommand(taskCommand);
            }

            // 执行命令
            cmdRet = executeCommand(taskCommand);

            if (RunStatusEnum.FAILED.equals(cmdRet.getStatus())) {
                if (FailedThenEnum.RETRY.equals(cmdRet.getFailedThen()) ){
                    retries++;
                    continue;
                }
                if (!StringUtils.equalsIgnoreCase(taskCommand.getStatus(), RunStatusEnum.CANCELLED.name())) {
                    taskCommand.setStatus(CommandStatusEnum.FAILED.name());
                }
                taskCommand.setMessage(cmdRet.getErrorMsg());
                if (this.config.isRecordLog()) {
                    TaskExecuteLogUtil.logCommand(taskCommand);
                }
                // 已被撤销的指令无需重试修复
                if (StringUtils.equalsIgnoreCase(taskCommand.getStatus(), RunStatusEnum.CANCELLED.name())) {
                    break;
                }

                recordAlertLog();
                if (StringUtils.equalsAny(cmdRet.getErrorCode().name(), ErrorCodeEnum.FATAL.name())
                        || FailedThenEnum.PAUSE.equals(taskCommand.getFailedThen())) {
                    waitToManualOperation(taskCommand, cmdRet);
                    if (StringUtils.equalsIgnoreCase(taskCommand.getFailureFixAs(), FailedThenEnum.RETRY.name())) {
                        retries++;
                        continue;
                    }
                }
            } else {
                taskCommand.setStatus(CommandStatusEnum.SUCCESS.name());
                taskCommand.setMessage("执行成功");
                if (this.config.isRecordLog()) {
                    TaskExecuteLogUtil.logCommand(taskCommand);
                }
            }
            break;
        } while (true);

        if (cmdRet.getErrorCode().equals(ErrorCodeEnum.SUCCESS)) {
            taskCommand.setStatus(CommandStatusEnum.SUCCESS.name());
        } else if (!StringUtils.equalsAny(taskCommand.getStatus(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.CANCELLED.name())) {
            taskCommand.setStatus(CommandStatusEnum.FAILED.name());
        }

        return cmdRet;
    }

    private CommandReturn executeCommand(TaskCommand taskCommand) {
        try{
            DeviceInstance deviceInstance = taskCommand.getCommand().getRealDeviceInstance();
            return deviceInstance.getProxy().executeCommand(taskCommand.getCommand(), this);
        } catch (Throwable e) {
            log.error("Could not executeCommand: {}, Exception:{}", taskCommand.getName(), e.getMessage(), e);

            CommandReturn cmdRet = new CommandReturn();
            cmdRet.setStatus(RunStatusEnum.FAILED);
            cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
            cmdRet.setErrorMsg(e.getMessage());
            taskCommand.setFailedThen(FailedThenEnum.PAUSE);
            return cmdRet;
        }

    }
/*
    private Object invokeMethod(Method mth, Object deviceProxy, Object parameterValue, boolean withParam) throws InvocationTargetException, IllegalAccessException {
        beforeInvokeMethod();
        Object ret = (withParam) ? mth.invoke(deviceProxy, parameterValue) : mth.invoke(deviceProxy);
        afterInvokeMethod();
        return ret;
    }

    private Object invokeMethod(Method mth, Object deviceProxy) throws InvocationTargetException, IllegalAccessException {
        return invokeMethod(mth, deviceProxy, null, false);
    }

    private Object invokeMethod(Method mth, Object deviceProxy, Object parameterValue) throws InvocationTargetException, IllegalAccessException {
        return invokeMethod(mth, deviceProxy, parameterValue, true);
    }
*/

    @Override
    public CommandReturn beforeInvokeMethod(DeviceInstanceCmd instanceCmd, CommandReturn cmdRet) {
        handlePositionCheckingStatus();
        // 执行前点位状态设置
        handlePositionPreExecutionStatus();
        handlePromptBefore();
        return cmdRet;
    }

    @Override
    public CommandReturn afterInvokeMethod(DeviceInstanceCmd instanceCmd, CommandReturn cmdRet) {
        if (RunStatusEnum.SUCCESS.equals(cmdRet.getStatus())) {
            handlePostExecution(cmdRet);
        }
        if (RunStatusEnum.SUCCESS.equals(cmdRet.getStatus())) {
            handleExecutedDelay();
            handlePositionExecutedStatus();
            handleMaterialConsumption();
            handlePromptAfter();
        }
        return cmdRet;
    }

    private void handlePostExecution(CommandReturn cmdRet) {
        TaskCommand taskCommand = this.getTaskCommand();
        if (StringUtils.isEmpty(taskCommand.getPostExecution())) {
            return;
        }
        Map<String, Object> out = FormulaExecutionHelper.getInstance().execute(taskCommand.getPostExecution(), cmdRet.getResult());
        String retStatus = out.get("status") == null ? "" : out.get("status").toString();
        String retFailedThen = out.get("failedThen") == null ? "" : out.get("failedThen").toString();
        if (StringUtils.equalsAny(RunStatusEnum.SUCCESS.name(), retStatus)) {
            cmdRet.setStatus(RunStatusEnum.SUCCESS);
        } else if (StringUtils.equalsAny(RunStatusEnum.FAILED.name(), retStatus)) {
            cmdRet.setStatus(RunStatusEnum.FAILED);
            if (StringUtils.equalsAny(FailedThenEnum.RETRY.name(), retFailedThen)) {
                cmdRet.setFailedThen(FailedThenEnum.RETRY);
            }
        }
    }

    private void handlePromptBefore() {
        TaskCommand taskCommand = this.getTaskCommand();
        if (taskCommand.getComment() == null) {
            return;
        }
        AlertLogService alertLogService = SpringContextHolder.getBean(AlertLogService.class);
        AlertLog alertLog = new AlertLog();
        alertLog.setAlertCategory("TASK_CMD");
        alertLog.setAlertSourceId(taskCommand.getId());
        alertLog.setAlertLevel(AlertLog.AlertLevelEnum.WARN.name());
        alertLog.setMessage(taskCommand.getComment());
        alertLog.setAlertCode("TASK_CMD_PROMPT");
        alertLog.setAlertName(taskCommand.getComment());
        alertLogService.createOrUpdate(alertLog);

    }

    private void handleMaterialConsumption() {
        TaskCommand taskCommand = this.getTaskCommand();
        if (StringUtils.isEmpty(taskCommand.getMaterialConsumption())) {
            return;
        }
        MaterialConsumptionVo vo = MaterialConsumptionVo.parse(taskCommand.getMaterialConsumption(), taskCommand);

        MaterialService materialService = SpringContextHolder.getBean(MaterialService.class);
        Material material = materialService.deductRemainValue(vo.getMaterialId(), vo.getMaterialValue());
        Optional<Position> posOpt = materialService.queryIndicatorPosition(material);
        if (posOpt.isPresent()) {
            sendPositionStateChangeMsg(posOpt.get());
        }
    }


    private void handlePromptAfter() {
        TaskCommand taskCommand = this.getTaskCommand();
        if (taskCommand.getComment() == null) {
            return;
        }

        AlertLogService alertLogService = SpringContextHolder.getBean(AlertLogService.class);
        AlertLog alertLog = new AlertLog();
        alertLog.setAlertCategory("TASK_CMD");
        alertLog.setAlertSourceId(taskCommand.getId());
        alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
        alertLog.setFixedBy("System");
        alertLogService.fixLog(alertLog);

    }

    /**
     * 处理执行后的延迟
     */
    private void handleExecutedDelay() {
        TaskCommand taskCommand = this.getTaskCommand();

        if (taskCommand.getExecutedDelay() == null) {
            return;
        }
        try {
            Thread.sleep(taskCommand.getExecutedDelay().longValue());
        } catch (InterruptedException e) {
            log.error("Thread.sleep({}), error:{}", taskCommand.getExecutedDelay(), e.getMessage(), e);
            throw new TaskRunningException("Thread.sleep InterruptedException");
        }
    }

    private Position getSelectedPos(TaskCommand taskCommand, int index, int statusListSize, PositionRequestVo posReqVo) {
        List<Position> preSelectPosList = taskCommand.getSelectedDevicePositions();
        if (index<preSelectPosList.size()
                && StringUtils.equalsAny(preSelectPosList.get(index).getName(), posReqVo.getName()) ){
            Position pos = preSelectPosList.get(index);
            log.debug("updatePositionStatus: getSelectedPos:preSelectPosList TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                    taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());
            return pos;
        }

        // 优先取本命令绑定的点位
        Position selectedPos = taskCommand.getSelectedDevicePos();
        if (selectedPos != null) {
            log.debug("updatePositionStatus: getSelectedPos:getSelectedDevicePos TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                    taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), selectedPos.getId(),selectedPos.getName(),selectedPos.getStatus());

            return selectedPos;
        }
        // 其次取本命令所在的动作点位设置
        Object var = TaskExecutorContext.getContext().getLocalVarObj(SysVariableNameEnum.SELECTED_POSITIONS.name());
        if (var != null && var instanceof List) {
            List<VariableValueWrapper> variableValueWrapperList = (List<VariableValueWrapper>) var;
            List<VariableValueWrapper> actionScopedList = variableValueWrapperList.stream()
                    .filter(item -> item.getTaskActionId() == taskCommand.getTaskAction().getId().longValue())
                    .collect(Collectors.toList());
            if (index < actionScopedList.size()) {
                Position pos = (Position) actionScopedList.get(index).getValue();
                log.debug("updatePositionStatus: getSelectedPos:getLocalVarObj TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                        taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());

                return pos;
            }
        }

        if (index == statusListSize-1){
            Optional<Position> lastPosOpt = TaskExecutorContext.getContext().getTaskPositionBuffer().getLastPosition();
            if (lastPosOpt.isPresent() && StringUtils.equalsAny(lastPosOpt.get().getName(),posReqVo.getName()) ){
                Position pos = lastPosOpt.get();
                log.debug("updatePositionStatus: getSelectedPos:getTaskPositionBuffer.getLastPosition TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                        taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());

                return pos;
            }
        }else if (index == statusListSize-2){
            Optional<Position> lastPosOpt = TaskExecutorContext.getContext().getTaskPositionBuffer().getNextToLastPosition();
            if (lastPosOpt.isPresent() && StringUtils.equalsAny(lastPosOpt.get().getName(),posReqVo.getName()) ){
                Position pos = lastPosOpt.get();
                log.debug("updatePositionStatus: getSelectedPos:getTaskPositionBuffer.getNextToLastPosition TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                        taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());

                return pos;
            }
        }

        // 默认取第一个状态匹配的点位，
        DeviceInstance devInst = taskCommand.getDeviceInstance();
        if (devInst == null) {
            devInst = taskCommand.getCommand().getDeviceInstance();
        }
        DeviceInstanceDto insDto = getDeviceInstanceById(devInst.getId());
        List<Position> positions = insDto.getPositions();
        // 优先取当前任务绑定的点位
        Optional<Position> matchedPosOpt = positions.stream().filter(position -> {
            return position.getHoldByTaskId()!=null
                    && position.getHoldByTaskId().equals(taskCommand.getTaskId())
                    && StringUtils.equalsAny(posReqVo.getType(), position.getType());
        }).findFirst();
        if (matchedPosOpt.isPresent()){
            Position pos = matchedPosOpt.get();
            log.debug("updatePositionStatus: getSelectedPos:getHoldByTaskId matchedPosOpt TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                    taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());

            return pos;
        }

        // 默认取第一个状态匹配的点位，
        matchedPosOpt = positions.stream().findFirst();
        if (matchedPosOpt.isPresent()){
            Position pos = matchedPosOpt.get();
            log.debug("updatePositionStatus: getSelectedPos:findFirst matchedPosOpt TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                    taskCommand.getId(), taskCommand.getTaskId(),taskCommand.getTaskMethodId(), taskCommand.getTaskStepId(), taskCommand.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());

            return pos;
        }

        throw new TaskRunningException("Could not found available position, taskCommand: " + taskCommand.getName());

        // 默认选择点位，需要绑定至任务命令
        // taskCommand.setSelectedDevicePos(selectedPos);

        //return selectedPos;
    }

    /**
     * 对于板卡位，需要同步变更板卡中的点位状态
     * @param updatedStatusList
     */
    private void updatePositionExecutionStatus(List<String> updatedStatusList) {
        TaskCommand taskCommand = this.getTaskCommand();

        if (CollectionUtils.isEmpty(updatedStatusList)) {
            //  log.info("getPositionExecutedStatus is empty:{}", taskCommand);
            return;
        }
        // 倒序遍历, 在板位的状态变更中，目标点位需要复制变更之前的源点位状态
        for (int i=updatedStatusList.size()-1; i>=0; i--) {
            String updatedStatus = updatedStatusList.get(i);
            PositionRequestVo posReqVo = PositionRequestVo.parseFromStatusFormattedStr(updatedStatus);
            updatedStatus = posReqVo.getStatus();

            Position selectedPos = getSelectedPos(taskCommand, i, updatedStatusList.size(), posReqVo);
            if (Position.TypeEnum.BOARD.name().equals(selectedPos.getType())){
                log.info("BOARD selectedPos: {},{}",selectedPos, taskCommand);
                List<Position> defaultPositions = taskCommandService.findPositionsByBoardNodeId(selectedPos.getBoardNodeId());
                log.info("BOARD defaultPositions.size: {},{}",defaultPositions.size(), taskCommand);
                // 当前被更新的板位状态为NONE,IDLE时，直接变更该板中的所有点位为对应状态, 否则当作为目标板位更新时，需复制源板的点位状态及附加绑定信息;
                if (StringUtils.equalsAny(updatedStatus, Position.StatusEnum.NONE.name(), Position.StatusEnum.IDLE.name())) {
                    for (Position defPos : defaultPositions) {
                        defPos.setStatus(Position.StatusEnum.valueOf(updatedStatus));
                        defPos.setHoldByTaskId(task.getId());
                        selectedPos.setHoldByTaskMethodId(taskCommand.getTaskMethodId());
                        selectedPos.setHoldByTaskStepId(taskCommand.getTaskStepId());
                        defPos.setHoldByTaskNumber(task.getTaskNumber());
                        defPos.setHoldByTaskName(task.getTaskName());
                        defPos.setHoldByTaskActionId(taskCommand.getTaskAction().getId());
                        defPos.setHoldByTaskActionName(taskCommand.getTaskAction().getName());
                        defPos.setRunningStartTime(task.getScheduleEntryTime() + taskCommand.getTaskAction().getExecutedStart());
                        defPos.setRunningEndTime(task.getScheduleEntryTime() + taskCommand.getTaskAction().getExecutedStart() + taskCommand.getTaskAction().getEvaluateExecutingDuration());

                        //log.info("taskCommandService.updatePositionStatus:{}:{}", taskCommand, defPos);
                        taskCommandService.updatePositionStatus(taskCommand, defPos);
                        sendPositionStateChangeMsg(defPos);
                    }
                }else if (i>=1){ // 取前一个板位作为源位
                    Position srcPos = getSelectedPos(taskCommand, i-1, updatedStatusList.size(), posReqVo);
                    log.info("BOARD srcPos: {},{}",selectedPos, taskCommand);
                    List<Position> srcDefaultPositions = taskCommandService.findPositionsByBoardNodeId(srcPos.getBoardNodeId());
                    log.info("BOARD srcDefaultPositions.size: {},{}",srcDefaultPositions.size(), taskCommand);
                    for (int defPosIdx = 0; defPosIdx < defaultPositions.size(); defPosIdx ++) {
                        Position defPos = defaultPositions.get(defPosIdx);
                        if (defPosIdx>=srcDefaultPositions.size()){
                            break;
                        }
                        Position tmpPos = srcDefaultPositions.get(defPosIdx);
                        defPos.setStatus(tmpPos.getStatus());
                        defPos.setHoldByTaskId(tmpPos.getHoldByTaskId());
                        selectedPos.setHoldByTaskMethodId(taskCommand.getTaskMethodId());
                        selectedPos.setHoldByTaskStepId(taskCommand.getTaskStepId());
                        defPos.setHoldByTaskNumber(tmpPos.getHoldByTaskNumber());
                        defPos.setHoldByTaskName(tmpPos.getHoldByTaskName());
                        defPos.setHoldByTaskActionId(tmpPos.getHoldByTaskActionId());
                        defPos.setHoldByTaskActionName(tmpPos.getHoldByTaskActionName());
                        defPos.setRunningStartTime(tmpPos.getRunningStartTime());

                        //log.info("taskCommandService.updatePositionStatus:{}:{}", taskCommand, defPos);
                        taskCommandService.updatePositionStatus(taskCommand, defPos);
                        sendPositionStateChangeMsg(defPos);
                    }
                }else{
                    // 无需变更点位状态
                    log.info("No need to update dot positions: {}", taskCommand);
                }

            }

            selectedPos.setStatus(Position.StatusEnum.valueOf(updatedStatus));
            selectedPos.setHoldByTaskId(task.getId());
            selectedPos.setHoldByTaskMethodId(taskCommand.getTaskMethodId());
            selectedPos.setHoldByTaskStepId(taskCommand.getTaskStepId());
            selectedPos.setHoldByTaskNumber(task.getTaskNumber());
            selectedPos.setHoldByTaskName(task.getTaskName());
            selectedPos.setHoldByTaskActionId(taskCommand.getTaskAction().getId());
            selectedPos.setHoldByTaskActionName(taskCommand.getTaskAction().getName());
            selectedPos.setRunningStartTime(task.getScheduleEntryTime() + taskCommand.getTaskAction().getExecutedStart());
            selectedPos.setRunningEndTime(task.getScheduleEntryTime() + taskCommand.getTaskAction().getExecutedStart() + taskCommand.getTaskAction().getEvaluateExecutingDuration());

            //log.info("taskCommandService.updatePositionStatus:{}:{}", taskCommand, selectedPos);
            taskCommandService.updatePositionStatus(taskCommand, selectedPos);
            sendPositionStateChangeMsg(selectedPos);
        }
    }

    /**
     * 处理执行后的点位状态
     * getPositionExecutedStatus为数组格式
     */
    private void handlePositionExecutedStatus() {
        TaskCommand taskCommand = this.getTaskCommand();
        updatePositionExecutionStatus(taskCommand.getPositionExecutedStatusAsArray());

        // 后处理点位状态更新完成后，需要把之前的选择点位从上下文清除掉
        if (CollectionUtils.isNotEmpty(taskCommand.getPositionExecutedStatusAsArray())) {
            Object var = TaskExecutorContext.getContext().getLocalVarObj(SysVariableNameEnum.SELECTED_POSITIONS.name());
            if (var != null && var instanceof List) {
                List varList = (List)var;
                varList.clear();
            }
        }
    }

    /**
     * 处理执行前的点位状态设置
     */
    private void handlePositionPreExecutionStatus() {
        TaskCommand taskCommand = this.getTaskCommand();
        updatePositionExecutionStatus(taskCommand.getPositionPreExecutionStatusAsArray());
    }

    private void handlePositionCheckingStatus() {
        TaskCommand taskCommand = this.getTaskCommand();
        // 检查点位状态
        if (CollectionUtils.isEmpty(taskCommand.getPositionCheckingStatusAsArray())) {
            return;
        }
        Position selectedPos = taskCommand.getSelectedDevicePos();
        if (CollectionUtils.isEmpty(taskCommand.getSelectedDevicePositions())) {
            // 默认取第一个状态匹配的点位，
            Position matchedPos = null;
            DeviceInstance devInst = taskCommand.getDeviceInstance();
            if (devInst == null) {
                devInst = taskCommand.getCommand().getDeviceInstance();
            }
            DeviceInstanceDto insDto = getDeviceInstanceById(devInst.getId());
            List<Position> positions = insDto.getPositions();
            List<String> updatedStatusList = taskCommand.getPositionCheckingStatusAsArray();
            taskCommand.getSelectedDevicePositions().clear();
            for (int i = 0; i < updatedStatusList.size() ; i++) {
                String updatedStatus = updatedStatusList.get(i);
                PositionRequestVo posReqVo = PositionRequestVo.parseFromStatusFormattedStr(updatedStatus);
                // 优先取当前任务绑定的点位
                Optional<Position> matchedPosOpt = findMatchedPosWithStatus(positions, posReqVo);
                if (matchedPosOpt.isPresent()) {
                    matchedPos = matchedPosOpt.get();
                    taskCommand.getSelectedDevicePositions().add(matchedPos);
                }else{
                    throw new TaskRunningException("Could not found available position["+updatedStatus+"], taskCommand: " + taskCommand.getName());
                }
            }

        //    if (matchedPos == null) {
        //        throw new TaskRunningException("Could not found available position, taskCommand: " + taskCommand.getName());
        //    }
          //  taskCommand.setSelectedDevicePos(matchedPos);
        }
    }

    private Optional<Position> findMatchedPosWithStatus(List<Position> positions, PositionRequestVo posReqVo) {
        String posName = posReqVo.getName();
        String updatedStatus = posReqVo.getStatus();
        String posType = posReqVo.getType();
        if (StringUtils.isNotEmpty(posName)) {
            // 优先取当前任务绑定的点位+状态
            Optional<Position> matchedPosOpt = positions.stream().filter(position -> {
                return position.getHoldByTaskId()!=null
                        && position.getHoldByTaskId().equals(taskCommand.getTaskId())
                        && StringUtils.equalsAny(posName, position.getName())
                        && StringUtils.equalsAny(posType, position.getType())
                        && StringUtils.equalsAny(updatedStatus, position.getStatus().name());
            }).findFirst();
            if (matchedPosOpt.isPresent()){
                return matchedPosOpt;
            }
            // 其次取当前状态
            matchedPosOpt = positions.stream().filter(position -> {
                return  StringUtils.equalsAny(posName, position.getName())
                        && StringUtils.equalsAny(posType, position.getType())
                        && StringUtils.equalsAny(updatedStatus, position.getStatus().name());
            }).findFirst();
            if (matchedPosOpt.isPresent()){
                return matchedPosOpt;
            }

        }
        // 优先取当前任务绑定的点位+状态
        Optional<Position> matchedPosOpt = positions.stream().filter(position -> {
            return position.getHoldByTaskId()!=null
                    && position.getHoldByTaskId().equals(taskCommand.getTaskId())
                    && StringUtils.equalsAny(posType, position.getType())
                    && StringUtils.equalsAny(updatedStatus, position.getStatus().name());
        }).findFirst();
        if (matchedPosOpt.isPresent()){
            return matchedPosOpt;
        }
        // 其次取当前状态
        matchedPosOpt = positions.stream().filter(position -> {
            return StringUtils.equalsAny(posType, position.getType())
                    && StringUtils.equalsAny(updatedStatus, position.getStatus().name());
        }).findFirst();
        if (matchedPosOpt.isPresent()){
            return matchedPosOpt;
        }

        return matchedPosOpt;
    }


    private DeviceInstanceDto getDeviceInstanceById(Long devInstanceId) {
        DeviceInstanceService devInst = SpringContextHolder.getBean(DeviceInstanceService.class);
        DeviceInstanceDto devInstDto = devInst.findById(devInstanceId);

        return devInstDto;
    }

    private FailedThenEnum waitToManualOperation(TaskCommand command, CommandReturn cmdRet) {

        // sendError(command);
        TaskCommandService taskCommandService = SpringContextHolder.getBean(TaskCommandService.class);
        taskCommandService.waitUntilFailureHandledByManually(command);
        /*
            Scanner scanner = new Scanner(System.in);
            log.error("Please choice the operation when the command was executed failed: {}, command: {}", cmdRet.getErrorMsg(), command);
            FailedThenEnum choiced = FailedThenEnum.PAUSE;
            do {
                log.error("[PAUSE,CONTINUE,RETRY,RETURN_ACTION,RETURN_STEP,RETURN_METHOD,RETURN_PROCEDURE], please choice ==> ");
                String line = scanner.nextLine();
                if (StringUtils.equalsIgnoreCase(FailedThenEnum.RETRY.name(), line)) {
                    choiced = FailedThenEnum.RETRY;
                    break;
                }
            }while(true);
            scanner.close();
    */
        return FailedThenEnum.valueOf(command.getFailureFixAs());
    }


    private void sendPrompt(TaskCommand taskCommand) {
        sendMsg(taskCommand, MsgType.INFO);
    }

    private void sendError(TaskCommand taskCommand) {
        sendMsg(taskCommand, MsgType.ERROR);
    }

    private void sendSuccess(TaskCommand taskCommand) {
        sendMsg(taskCommand, MsgType.INFO);
    }

    private void sendMsg(TaskCommand taskCommand, MsgType msgType) {
        TaskCommandDto dto = new TaskCommandDto();
        dto.setId(taskCommand.getId());
        dto.setPrepareTaskId(this.task.getPrepareTaskId());
        dto.setTaskId(this.task.getId());
        dto.setActionId(taskCommand.getTaskAction().getActionId());
        dto.setComment(taskCommand.getComment());
        dto.setName(taskCommand.getName());
        dto.setStatus(taskCommand.getStatus());
        dto.setMessage(taskCommand.getMessage());
        dto.setFailedThen(taskCommand.getFailedThen().name());
        dto.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        SocketMsg msg = new SocketMsg(JSON.toJSONString(dto), msgType);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"taskMonitor", "taskMonitorLog"}, true);
        } catch (IOException e) {
            log.error("sendPrompt error, message={}", e.getMessage(), e);
        }

    }

    private void sendPositionStateChangeMsg(Position position) {

        SocketMsg msg = new SocketMsg(JSON.toJSONString(position), MsgType.INFO);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"positionStateChange"}, true);
        } catch (IOException e) {
            log.error("sendPositionStateChangeMsg error, message={}", e.getMessage(), e);
        }

    }

    private TaskCommand getTaskCommand() {
        return this.taskCommand;
    }

    private boolean checkPositionMatched(Position pos, String posName, String status) {
        if (StringUtils.equalsAny(Position.StatusEnum.NONE.name(), status)) {
            return StringUtils.equals(pos.getName(), posName);
        }
        if (StringUtils.equalsAny(status, Position.StatusEnum.IDLE.name(), Position.StatusEnum.READY.name())) {
            return StringUtils.equals(pos.getName(), posName) && StringUtils.equalsAny(pos.getStatus().name(), status);
        } else {
            Task task = TaskExecutorContext.getTask();
            return StringUtils.equals(pos.getName(), posName) && StringUtils.equalsAny(pos.getStatus().name(), status)
                    && pos.getHoldByTaskId() != null && task != null && pos.getHoldByTaskId().equals(task.getId());
        }
    }

    private void recordAlertLog() {
        // 记录异常告警
        AlertLog alertLog = new AlertLog();
        alertLog.setAlertCategory("TASK_CMD");
        alertLog.setAlertSourceId(taskCommand.getId());
        alertLog.setAlertLevel(AlertLog.AlertLevelEnum.ERROR.name());
        alertLog.setMessage(taskCommand.getMessage());
        alertLog.setAlertCode("TASK_CMD_ERROR");
        alertLog.setAlertName(taskCommand.getMessage());
        AlertLogService alertLogService = SpringContextHolder.getBean(AlertLogService.class);
        alertLogService.createOrUpdate(alertLog);
    }


}
