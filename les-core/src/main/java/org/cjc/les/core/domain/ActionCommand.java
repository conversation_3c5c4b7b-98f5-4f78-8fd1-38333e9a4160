/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Entity
@Data
@Table(name="les_action_command")
@SQLDelete(sql = "UPDATE les_action_command SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class ActionCommand extends LesConfigBaseEntity {

    private String name;

    private String description;

    @OneToOne
    @JoinColumn(name = "device_instance_id")
    @Schema(description = "设备实例ID")
    private DeviceInstance deviceInstance;
    /**
     * DAG图中的节点ID
     */
    private String dagNodeId;

    /**
     * 引用命令对象
     */
    @Schema(description = "关联命令")
    @OneToOne
    @JoinColumn(name = "command_id")
    private DeviceInstanceCmd command;

    @Column(name = "parameter")
    @Schema(description = "parameter")
    private String parameter;

    @Column(name = "comment")
    @Schema(description = "命令备注描述")
    private String comment;

    @Column(name = "failed_then")
    @Schema(description = "运行失败后的处理方式:CONTINUE/RETURN_ACTION/RETURN_STEP/RETURN_METHOD/RETURN_PROCEDURE")
    private String failedThen;

    /**
     * 失败后的重试次数
     */
    @Column(name = "failed_retries")
    private Integer failedRetries;

    /**
     * 指令执行前的点位检测状态
     */
    private String positionCheckingStatus;

    /**
     * 指令执行前的点位状态设置
     */
    private String positionPreExecutionStatus;

    /**
     * 指令执行后的脚本名称
     */
    private String postExecution;

    /**
     * 指令执行成功后的点位状态
     */
    private String positionExecutedStatus;

    /**
     * 执行完成后的延迟时长(ms)
     */
    @Column(name = "executed_delay")
    private Integer executedDelay;

    /**
     * 物料消耗量
     */
    private String materialConsumption;

    /**
     * 是否可选: Y/N
     */
    private String isOption;
    /**
     * 可选项编码
     */
    private String optionCode;

    private Long evaluateExecutingDuration;
    private Timestamp evaluatedTime;

    @Column(name = "status",nullable = false)
    @NotBlank
    @Schema(description = "状态")
    private String status = CommandStatusEnum.VALID.name();;

    @JSONField(serialize = false)
    @JoinColumn(name = "action_id", referencedColumnName = "id")
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = Action.class)
    @Schema(description = "动作", hidden = true)
    private Action action;

    public void bind(Action parentObj) {
        this.setAction(parentObj);
    }

    public void copy(ActionCommand source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreProperties("action","command"));

        if (this.getCommand()==null){
            this.setCommand(source.getCommand());
        }else{
            this.command.copy(source.getCommand());
        }
    }
}