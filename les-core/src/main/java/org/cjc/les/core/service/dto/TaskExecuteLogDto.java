/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-08-23
**/
@Data
public class TaskExecuteLogDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 任务lD */
    private Long taskId;

    /** 任务方法ID */
    private Long taskMethodId;

    /** 任务步骤ID */
    private Long taskStepId;

    /** 任务动作ID */
    private Long taskActionId;

    /** 任务命令ID */
    private Long taskCommandId;

    /** 名称，根据日志类型分别表示对应的日志对象名称 */
    private String name;

    /** 状态: READY,RUNNING,SUCCESS,FAILED */
    private String status;

    /** 执行详情 */
    private String message;

    /** 日志类型:TASK,METHOD,STEP,ACTION,COMMAND */
    private String logType;

    /** 日志级别:DEBUG,INFO,WARN,ERROR */
    private String logLevel;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}