/*
 *  Copyright 2024-2024 Wuhan Annis Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.TaskCommand;

import java.util.Optional;
import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 命令执行上下文对象，存储当前命令执行所需的环境信息
 */
@Data
@Log4j2
public class TaskPositionBuffer {

    private final int MAX_BUFFERED_POSITION_COUNT_PER_STEP = 2;

    /**
     * 保存步骤中最后两次选中的点位对象，用来传递该步骤中不同ACTION,不同COMMAND之间的点位参数
     */
    private ConcurrentHashMap<Long, Stack<VariableValueWrapper<Position>>> taskStepPositionsMap = new ConcurrentHashMap<>();

    private volatile Object muteLock = new Object();

    /**
     * 添加点位到缓存列表中
     * @param position 已选中的点位对象
     */
    public void add(Position position) {

        VariableValueWrapper<Position> varWrapper = new VariableValueWrapper();
        varWrapper.setData(position);
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand != null) {
            varWrapper.setTaskId(taskCommand.getTaskId());
            varWrapper.setTaskMethodId(taskCommand.getTaskMethodId());
            varWrapper.setTaskStepId(taskCommand.getTaskStepId());
            varWrapper.setTaskActionId(taskCommand.getTaskAction().getId());
            varWrapper.setTaskCommandId(taskCommand.getId());
        }
        if (varWrapper.getTaskStepId()==0L) {
            return;
        }
        synchronized (muteLock) {
            Stack<VariableValueWrapper<Position>> stepStack = taskStepPositionsMap.get(varWrapper.getTaskStepId());
            if (null == stepStack) {
                stepStack = new Stack<>();
                taskStepPositionsMap.put(varWrapper.getTaskStepId(), stepStack);
            }
            stepStack.push(varWrapper);
            if (stepStack.size()>MAX_BUFFERED_POSITION_COUNT_PER_STEP){
                stepStack.remove(0);
            }
        }
    }

    /**
     * 清除缓存，在步骤执行完成后调用
     * @param taskStepId
     */
    public void clearBuffer(Long taskStepId) {
        synchronized (muteLock) {
            taskStepPositionsMap.remove(taskStepId);
        }
    }

    /**
     * 获取倒数第一位
     * @return
     */
    public Optional<Position> getLastPosition() {
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand == null) {
            return Optional.empty();
        }
        Stack<VariableValueWrapper<Position>> stepStack = taskStepPositionsMap.get(taskCommand.getTaskStepId());
        if (stepStack==null || stepStack.isEmpty()){
            return Optional.empty();
        }
        return Optional.of(stepStack.lastElement().getData());

    }

    /**
     * 获取倒数第二位
     * @return
     */
    public Optional<Position> getNextToLastPosition() {
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand == null) {
            return Optional.empty();
        }
        Stack<VariableValueWrapper<Position>> stepStack = taskStepPositionsMap.get(taskCommand.getTaskStepId());
        if (stepStack.size()<2){
            return Optional.empty();
        }
        return Optional.of(stepStack.elementAt(stepStack.size()-2).getData());
    }

}
