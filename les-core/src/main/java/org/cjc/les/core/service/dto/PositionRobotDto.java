/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-04-16
**/
@Data
public class PositionRobotDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 点位ID */
    private Long positionId;

    /** 关联ROBOT设备实例ID */
    private Long robotDevInstanceId;

    /** 机器人定义的位置编号，用于机器人点位唯一识别 */
    private String robotPosCode;

    private BigDecimal xpos;

    private BigDecimal ypos;

    private BigDecimal zpos;

    /** 保留 */
    private String status;

    private BigDecimal upos;

    private BigDecimal vpos;

    private BigDecimal wpos;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}