/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.schedule;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.service.DeviceExecuteService;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

@Log4j2
@Component("sampleEntryScheduler")
@RequiredArgsConstructor
public class SampleEntryScheduler {

    private ConcurrentLinkedQueue<Result> resultEntryQueue = new ConcurrentLinkedQueue<>();

    private  final DeviceExecuteService deviceExecuteService;

    private final CommandListenerPool commandListenerPool;

    public void start() {
        // 数据库查找进样方法定义，不同的进样方法，启用不同的线程处理
        commandListenerPool.submit(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    Procedure procedure = parseProcedureFromConfigFile();

                    String procStr = JSON.toJSONString(procedure);
              //      log.info(procStr);

                    ProcedureMethod entrySampleMethod = procedure.getEntryMethod().get();
                    CommandContext.clear();
                    new MethodExecutor(). executeMethod(entrySampleMethod,procedure);

                    try {
                        // 等待0.2秒再次调度下一任务
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        });

    }

    /**
     * 开始调度进程, 当RFID Reader获取到扫描标签
     * @param rfTag
     */
    public void start(String rfTag) {
        log.info("Start TAG: {}", rfTag);
        // 通过标签识别，需要调度的进程，若没有匹配，则提示用户选择（通过勾选方法创建进程）,
        // 默认执行最后一次用户创建的进程，提示用户当前样品即将执行的进程信息（包括将执行的检测方法，将创建的产品名，产品编号等）

        // 样品对象，实际实现先查数据库
        Sample sample = new Sample();
        sample.setNumber(rfTag);
        sample.setName("测试样品");

        // 创建产品
        Result result = new Result();
        result.setName(sample.getName());
        Date now = new Date();
       // String strNow = new SimpleDateFormat("YYYYMMDDhh24miss").format(now);
        result.setNumber(sample.getNumber()+"-"+0);
        result.setSample(sample);

        // Test Procedure
        Procedure procedure = parseProcedureFromConfigFile();
     //   result.setProcedure(procedure);

        String procStr = JSON.toJSONString(procedure);
        log.info(procStr);

        // 推送至进样队列
        resultEntryQueue.offer(result);

        // 保存当前产品product进入数据库, 并执行进样操作
        executeEntrySampleMethod(procedure);

    }


    /**
     * 执行进样方法
     * @param procedure
     */
    private void executeEntrySampleMethod(Procedure procedure) {
        ProcedureMethod entrySampleMethod = procedure.getEntryMethod().get();
        new MethodExecutor(). executeMethod(entrySampleMethod,procedure);
    }

    private Procedure parseProcedureFromConfigFile(){
        File configFile = new File("test_moisture_checking_procedure.json");
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(configFile);
            Procedure proc = JSON.parseObject(fis, Procedure.class);
            appendDeviceForAllCommands(proc);
            return proc;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }

        return null;
    }
    private void appendDeviceForAllCommands(Procedure proc) throws ClassNotFoundException {

        List<ProcedureMethod> methods = new ArrayList<>();
        methods.add(proc.getEntryMethod().get());
        methods.addAll(proc.getMethods());
        methods.add(proc.getExitMethod().get());

        for (ProcedureMethod method : methods) {
            for (MethodStep step : method.getMethod().getSteps()) {
                for (StepAction action : step.getStep().getActions()) {
                    for (ActionCommand actionCommand : action.getAction().getCommands()) {
                        Command command = null;// actionCommand.getCommand();
                       if (command.getDeviceId() ==null){
                           continue;
                       }
                        command.setDevice(deviceExecuteService.getDevice(command.getDeviceId()));

                       Object objParameter = command.getParameter();
                       if (objParameter instanceof JSONObject && StringUtils.isNotEmpty(command.getParameterType())){
                           Class<?> clz = this.getClass().getClassLoader().loadClass( command.getParameterType());
                           objParameter = JSON.parseObject( JSON.toJSONString(objParameter) , clz);
                           command.setParameter(objParameter);
                       }

                    }
                }
            }
        }
        //

    }

}
