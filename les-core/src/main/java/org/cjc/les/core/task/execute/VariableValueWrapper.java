/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 变量值封装
 */
@Data
public class VariableValueWrapper<T> {

    /**
     * 变量设置级别, 0:Command, 1:Action, 2:Step, 3:Method, 4:Task
     */
    private int settingLevel;

    private long taskId;

    private long taskMethodId;

    private long taskStepId;

    private long taskActionId;

    private long taskCommandId;

    private Object value;

    private T data;

}
