/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.service.DeviceExecuteService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Log4j2
@Component
public class DeviceExecuteServiceImpl implements DeviceExecuteService {

    /**
     * 缓存当前注册的设备列表
     */
    private ConcurrentHashMap<Long, Device> deviceHashMap = new ConcurrentHashMap<Long, Device>();

    @Override
    public void addDevice(Device device) {
        deviceHashMap.put(device.getId(), device);
    }

    @Override
    public Device getDevice(long deviceId) {
        return deviceHashMap.get(deviceId);
    }

    @Override
    public Device getOrCreateDevice(DeviceInstance deviceInstance) {
        Long deviceId = deviceInstance.getId();
        Device dev = deviceHashMap.get(deviceId);
        if (dev != null) {
            return dev;
        }
        synchronized (DeviceExecuteServiceImpl.class) {
            if ((dev = deviceHashMap.get(deviceId)) != null) {
                return dev;
            }
            Device createdDev = createDevice(deviceInstance);
            deviceHashMap.put(deviceId, createdDev);
            return createdDev;
        }

    }

    private Device createDevice(DeviceInstance deviceInstance) {
        String javaClassName = deviceInstance.getDevice().getJavaClassName();
        Object obj = null;

        try {
            //Class<?> clz = Thread.currentThread().getContextClassLoader().loadClass(this.javaClassName);

            Class clz = Class.forName(javaClassName);
            obj = clz.newInstance();
            BeanUtil.copyProperties(deviceInstance.getDevice(), obj, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id"));
            BeanUtil.copyProperties(deviceInstance,obj, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("status","commands"));
        } catch (InstantiationException e) {
            log.error("InstantiationException exception: {}", e.getMessage(), e);
        } catch (IllegalAccessException e) {
            log.error("IllegalAccessException exception: {}", e.getMessage(), e);
        } catch (ClassNotFoundException e) {
            log.error("ClassNotFoundException exception: {}", e.getMessage(), e);
        }
        return (Device) obj;
    }

    @Override
    public Collection<Device> getAllDevices() {
        return deviceHashMap.values();
    }
}
