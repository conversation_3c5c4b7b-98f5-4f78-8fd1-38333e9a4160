/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskMethodExecutor;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.*;

@Log4j2
@RequiredArgsConstructor
@Component
public class MainScheduler {

    private final int POOL_SIZE = 16;

    private LinkedBlockingQueue<TaskMethod> methodsQueue = new LinkedBlockingQueue<>();

    public LinkedBlockingQueue<TaskMethod> getMethodsQueue() {
        return methodsQueue;
    }

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        executorService = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new TaskThreadFactory("main-method"));
    }

    private void submit(Runnable runnable){
        executorService.submit(runnable);
    }

    public void submit(TaskMethod taskMethod) {
        taskMethod.setStatus(RunStatusEnum.IN_SCHEDULE_QUE.name());
        taskMethod.setMessage("进入调度队列");
        TaskExecuteLogUtil.logMethod(taskMethod);
        final TaskExecutorContext context = TaskExecutorContext.getContext();
        methodsQueue.offer(taskMethod);
        submit(new Runnable() {
            @Override
            public void run() {
                TaskExecutorContext.copy(context, taskMethod);
                try {
                    new TaskMethodExecutor(ExecutorConfig.defaultConfig).execute(taskMethod);
                }catch(Throwable ex) {
                    log.error("Unknown throwable: {}", ex.getMessage(), ex);
                }finally {
                    TaskExecutorContext.clear();
                    methodsQueue.remove(taskMethod);
                }
            }
        });
    }
}
