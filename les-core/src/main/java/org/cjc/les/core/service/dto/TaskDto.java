/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class TaskDto implements Serializable {

    /** 主键ID */
    private Long id;

    private Long procedureId;

    /** 开启进程方法ID */
    private Long entryMethodId;

    /** 结束进程方法ID */
    private Long exitMethodId;

    /** 进程状态, READY,RUNNING,SUCCESS,FAILED */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /** 任务编号，前端用户唯一识别的任务号: sampleNumber-datetime-seq */
    private String taskNumber;

    /** 任务名称, 样品名-流程名 */
    private String taskName;

    /**
     * 标签值
     */
    private String tag;

    /**
     * 任务批次ID
     */
    private Long batchId;

    /**
     * 最后一次运行状态
     */
    private String lastStatus;

    /**
     * 消息详情
     */
    private String message;

    /**
     * 最后一次执行的任务命令ID
     */
    private Long lastExecutedTaskCommandId;

    /**
     * 任务执行方法列表
     */
    private List<TaskMethodDto> methodList;
}