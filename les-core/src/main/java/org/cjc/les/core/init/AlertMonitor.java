/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.init;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.Station;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.helper.DeviceProxyHelper;
import org.cjc.les.core.helper.DeviceProxyWrapper;
import org.cjc.les.core.service.ActionService;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.DeviceLayoutService;
import org.cjc.les.core.service.TaskActionService;
import org.cjc.les.core.service.dto.AlertLogDto;
import org.cjc.les.core.service.mapstruct.AlertLogMapper;
import org.cjc.les.core.task.schedule.ActionScheduler;
import org.cjc.les.core.task.schedule.StationQueue;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Log4j2
@Component
@RequiredArgsConstructor
public class AlertMonitor {

    private final AlertLogService alertLogService;
    private final AlertLogMapper alertLogMapper;

    private final ActionScheduler actionScheduler;
    private final TaskActionService taskActionService;

    private static ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public void init() {

        // 启动监控
        scheduler.scheduleAtFixedRate(new Runnable() {
            public void run() {
                AlertMonitor.this.startMonitor();
            }
        }, 30000,3000, TimeUnit.MILLISECONDS);
    }

    private void startMonitor() {
        // 检测在线设备运行状态
        checkOnlineDevices();

        // 检测在线运行的任务状态
        checkOnlineTasks();
    }

    private void checkOnlineDevices() {
        for (DeviceProxyWrapper proxy : DeviceProxyHelper.getInstance().getAllDeviceProxies()) {
            if (StringUtils.equalsAny(proxy.getDeviceInstance().getDevice().getType(), DeviceTypeEnum.ROBOT.name())) {
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("DEV_INSTANCE");
                alertLog.setAlertSourceId(proxy.getDeviceInstance().getId());
                alertLog.setAlertLevel(AlertLog.AlertLevelEnum.FATAL.name());
                alertLog.setMessage("机器人设备初始化失败");
                alertLog.setAlertCode("ROBOT_INIT_FAILED");
                alertLog.setAlertName("机器人设备初始化失败");

                if (proxy.getDeviceInstance().isInitialized()) {
                    alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
                    alertLog.setFixedBy("System");
                    alertLogService.fixLog(alertLog);
                } else {
                    alertLogService.createOrUpdate(alertLog);
                }
            } else if (StringUtils.equalsAny(proxy.getDeviceInstance().getDevice().getType(), DeviceTypeEnum.CONTROL.name())) {
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("DEV_INSTANCE");
                alertLog.setAlertSourceId(proxy.getDeviceInstance().getId());
                alertLog.setAlertLevel(AlertLog.AlertLevelEnum.FATAL.name());
                alertLog.setMessage("PLC控制器初始化失败");
                alertLog.setAlertCode("PLC_INIT_FAILED");
                alertLog.setAlertName("PLC控制器初始化失败");

                if (proxy.getDeviceInstance().isInitialized()) {
                    alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
                    alertLog.setFixedBy("System");
                    alertLogService.fixLog(alertLog);
                } else {
                    alertLogService.createOrUpdate(alertLog);
                }
            }

        }
    }

    /**
     * 检测在线运行的任务状态
     */
    private void checkOnlineTasks() {
        List<AlertLogDto> initedAlertLogs = alertLogService.findLatestAlertLogInInitStatus();
        for (AlertLogDto alertLogDto : initedAlertLogs){
            if (StringUtils.equals(alertLogDto.getAlertCategory(),"TASK_CMD")){
                TaskAction taskAction = taskActionService.findTaskActionByTaskCommandId(alertLogDto.getAlertSourceId());
                StationQueue onlineStationQue = actionScheduler.getStationQueueByStationId(taskAction.getStation().getId());
                if (onlineStationQue == null
                        || !onlineStationQue.getTaskActionsQueue().contains(taskAction)){
                    AlertLog alertLog = alertLogMapper.toEntity(alertLogDto);
                    alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
                    alertLog.setFixedBy("System");
                    alertLog.setFixedRemark("Fixed as TaskAction is offline.");
                    alertLogService.fixLog(alertLog);
                }

            }
        }
    }

}
