/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class TaskCommandDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 任务ID */
    private Long taskId;

    /**
     * 准备任务ID
     */
    private Long prepareTaskId;

    /** 关联命令 */
    private Long actionCommandId;
    /**
     * 配置的动作ID
     */
    private Long actionId;

    private Long taskMethodId;

    private Long taskStepId;

    /** 关联ACTION */
    private Long taskActionId;

    private Long deviceInstanceId;

    private String parameter;

    private Long selectedDevicePosId;

    /**
     * 命令名称
     */
    private String name;

    /** 命令备注描述 */
    private String comment;

    /** 运行失败后的处理方式:CONTINUE/RETURN_ACTION/RETURN_STEP/RETURN_METHOD/RETURN_PROCEDURE */
    private String failedThen;

    /** 状态: READY,RUNNING,SUCCESS,FAILED */
    private String status;

    /** 执行FAILED状态时的详情 */
    private String message;

    /**
     * 是否可选: Y/N
     */
    private String isOption;
    /**
     * 可选项编码
     */
    private String optionCode;

    private String failureFixAs;
    private String failureFixAsTemp;
    private String failureFixedBy;
    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}