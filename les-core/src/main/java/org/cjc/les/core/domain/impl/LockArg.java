/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain.impl;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Parameter;

/**
 * {"id":null,"name":"货架烧杯点位","description":null,"deviceInstanceName":"容器货架设备","deviceInstanceId":33,"lockMode":"BLOCK","lockObjectType":"DEV_INSTANCE","status":null,"deleteFlag":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"lockId":"","timeout":"","posNames":["货架培养皿点位","货架烧杯点位"]}
 */
@Data
public class LockArg {

    private String name;

    private String deviceInstanceName;

    private Long deviceInstanceId;

    private String lockMode;

    private String lockObjectType;

    @Parameter("锁超时时长")
    private long timeout = -1;

    public String getLockId() {
        String lockKey = lockObjectType;
        lockKey += "_" + deviceInstanceId;
        if (StringUtils.equalsAny(lockObjectType, "DEV_INSTANCE_POS")) {
            lockKey += "_" + name;
        }

        return lockKey;
    }
}
