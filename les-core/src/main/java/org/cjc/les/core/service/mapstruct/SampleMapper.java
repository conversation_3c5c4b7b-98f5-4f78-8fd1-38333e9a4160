/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.service.dto.SampleDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SampleMapper extends BaseMapper<SampleDto, Sample> {

    @Mapping(source = "category.name", target = "categoryName")
    @Mapping(source = "customer.name", target = "customerName")
    @Override
    SampleDto toDto(Sample entity);
}