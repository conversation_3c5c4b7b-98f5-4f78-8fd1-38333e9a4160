/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.DeviceInstanceCmd;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.helper.CommandExecuteHook;
import org.cjc.les.core.repository.DeviceInstanceCmdRepository;
import org.cjc.les.core.service.DeviceInstanceCmdService;
import org.cjc.les.core.service.dto.DeviceInstanceCmdDto;
import org.cjc.les.core.service.dto.DeviceInstanceCmdQueryCriteria;
import org.cjc.les.core.service.mapstruct.DeviceInstanceCmdMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.exception.ConfigApplicationException;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.vo.CommandReturn;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-07-19
**/
@Log4j2
@Service
@RequiredArgsConstructor
public class DeviceInstanceCmdServiceImpl implements DeviceInstanceCmdService {

    private final DeviceInstanceCmdRepository deviceInstanceCmdRepository;
    private final DeviceInstanceCmdMapper deviceInstanceCmdMapper;

    @Override
    public Map<String,Object> queryAll(DeviceInstanceCmdQueryCriteria criteria, Pageable pageable){
        Page<DeviceInstanceCmd> page = deviceInstanceCmdRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(deviceInstanceCmdMapper::toDto));
    }

    @Override
    public List<DeviceInstanceCmdDto> queryAll(DeviceInstanceCmdQueryCriteria criteria){
        return deviceInstanceCmdMapper.toDto(deviceInstanceCmdRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public DeviceInstanceCmdDto findById(Long id) {
        DeviceInstanceCmd deviceInstanceCmd = deviceInstanceCmdRepository.findById(id).orElseGet(DeviceInstanceCmd::new);
        ValidationUtil.isNull(deviceInstanceCmd.getId(),"DeviceInstanceCmd","id",id);
        return deviceInstanceCmdMapper.toDto(deviceInstanceCmd);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceInstanceCmdDto create(DeviceInstanceCmd resources) {
        return deviceInstanceCmdMapper.toDto(deviceInstanceCmdRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceInstanceCmd resources) {
        DeviceInstanceCmd deviceInstanceCmd = deviceInstanceCmdRepository.findById(resources.getId()).orElseGet(DeviceInstanceCmd::new);
        ValidationUtil.isNull( deviceInstanceCmd.getId(),"DeviceInstanceCmd","id",resources.getId());
        deviceInstanceCmd.copy(resources);
        deviceInstanceCmdRepository.save(deviceInstanceCmd);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            deviceInstanceCmdRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DeviceInstanceCmdDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DeviceInstanceCmdDto deviceInstanceCmd : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("设备实例命令名称", deviceInstanceCmd.getName());
            map.put("设备实例命令描述", deviceInstanceCmd.getDescription());
            map.put("设备实例ID", deviceInstanceCmd.getDeviceInstanceId());
            map.put("该设备本身定义的命令ID", deviceInstanceCmd.getCommandId());
            map.put("代理设备实例的命令ID,外键引用les_device_instance_cmd.id(该被引用的实例命令类型应该是原始设备命令)", deviceInstanceCmd.getProxyInstanceCmdId());
            map.put("当设置为PROXY代理命令时，可以通过不同参数自定义命令行为", deviceInstanceCmd.getParameter());
            map.put("命令类型, RAW原始命令, PROXY代理命令", deviceInstanceCmd.getCommandType());
            map.put("是否已被删除,Y/N", deviceInstanceCmd.getDeleteFlag());
            map.put("创建人", deviceInstanceCmd.getCreateBy());
            map.put("创建时间", deviceInstanceCmd.getCreateTime());
            map.put("更新人", deviceInstanceCmd.getUpdateBy());
            map.put("更新时间", deviceInstanceCmd.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateTestParameter(Long id, String testParameter) {
        deviceInstanceCmdRepository.updateTestParameter(id, testParameter);
    }

    @Override
    public DeviceInstanceCmdDto executeCommand(DeviceInstanceCmdDto resources) {

        return executeCommand(resources, null);
    }

    @Override
    public DeviceInstanceCmdDto executeCommand(DeviceInstanceCmdDto resources, CommandExecuteHook hook) {
        DeviceInstanceCmd deviceInstanceCmd = deviceInstanceCmdRepository.findById(resources.getId()).orElseGet(DeviceInstanceCmd::new);
        ValidationUtil.isNull(deviceInstanceCmd.getId(), "DeviceInstanceCmd", "id", resources.getId());
        DeviceInstance deviceInstance = deviceInstanceCmd.getRealDeviceInstance();

        deviceInstanceCmd.setParameter(resources.getTestParameter());
        try {
            if (TaskExecutorContext.getTask() == null) {
                TaskExecutorContext.setVar("TASK_ID", 0);
            }
            CommandReturn cmdRet = deviceInstance.getProxy().executeCommand(deviceInstanceCmd, hook);
            if (ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode())) {
                resources.setStatus(CommandStatusEnum.SUCCESS.name());
            } else {
                resources.setStatus(CommandStatusEnum.FAILED.name());
                resources.setMessage(cmdRet.getErrorMsg());
            }
        } catch (Throwable e) {
            resources.setStatus(CommandStatusEnum.FAILED.name());
            resources.setMessage(e.getMessage());
        }
        return resources;
    }

    @Override
    public DeviceInstanceCmdDto executeCommandByControlCode(DeviceInstanceCmdDto resources) {
        Optional<DeviceInstanceCmd> instanceCmdOpt = deviceInstanceCmdRepository.findDeviceInstanceCmdByBindControlCode(resources.getBindControlCode());
        if (!instanceCmdOpt.isPresent()) {
            throw new TaskRunningException("Could not found instance command by bindControlCode:"+resources.getBindControlCode());
        }
        DeviceInstanceCmd deviceInstanceCmd = instanceCmdOpt.get();
        DeviceInstance deviceInstance = deviceInstanceCmd.getRealDeviceInstance();

        deviceInstanceCmd.setParameter(deviceInstanceCmd.getParameter());
        try {
            CommandReturn cmdRet = deviceInstance.getProxy().executeCommand(deviceInstanceCmd);
            if (ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode())) {
                resources.setStatus(CommandStatusEnum.SUCCESS.name());
            } else {
                resources.setStatus(CommandStatusEnum.FAILED.name());
                resources.setMessage(cmdRet.getErrorMsg());
            }
        } catch (Throwable e) {
            resources.setStatus(CommandStatusEnum.FAILED.name());
            resources.setMessage(e.getMessage());
            log.error("Unknown error: {}", e.getMessage(), e);
        }
        return resources;
    }
}