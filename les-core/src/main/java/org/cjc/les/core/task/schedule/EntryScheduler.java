/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskMethodExecutor;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Log4j2
@RequiredArgsConstructor
@Component
public class EntryScheduler {

    private final int POOL_SIZE = 16;

    private LinkedBlockingQueue<TaskMethod> methodsQueue = new LinkedBlockingQueue<>();
    public LinkedBlockingQueue<TaskMethod> getMethodsQueue(){
        return methodsQueue;
    }

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        executorService = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new TaskThreadFactory("entry-method"));
    }


    private void submit(Runnable runnable){
        executorService.submit(runnable);
    }

    public void submit(TaskMethod entryMethod) {
        entryMethod.setStatus(RunStatusEnum.IN_SCHEDULE_QUE.name());
        entryMethod.setMessage("进入调度队列");
        TaskExecuteLogUtil.logMethod(entryMethod);
        final TaskExecutorContext context = TaskExecutorContext.getContext();
        methodsQueue.offer(entryMethod);
        submit(new Runnable() {
            @Override
            public void run() {
                TaskExecutorContext.copy(context, entryMethod);
                try {
                    new TaskMethodExecutor(ExecutorConfig.defaultConfig).execute(entryMethod);
                } catch(Throwable e){
                    log.error("EntryScheduler run occurs unknown error: {}", e.getMessage(), e);
                }finally {
                    TaskExecutorContext.clear();
                    methodsQueue.remove(entryMethod);
                }

            }
        });
    }

}
