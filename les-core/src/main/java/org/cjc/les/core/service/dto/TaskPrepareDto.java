/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.Sample;

import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-10
**/
@Data
public class TaskPrepareDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 任务需运行的流程ID */
    private Long procedureId;

    private ProcedureSmallDto procedure;


    /** 任务需选择的样本ID */
    private Long sampleId;

    private SampleDto sample;


    private Long taskId;

    /** 准备阶段完成后（PUSHED）运行的任务ID */
    private String taskNumber;


    /**
     * DRAFT: 初始化状态
     * WAIT_TAG_INPUT: 等待TAG输入
     * READY: 待推送到任务队列
     * WAIT_CONVEYOR_INPUT: 等待样品进入传送带
     * PUSHED: 已推送到任务队列
     * ERROR: 错误
     * CANCELLED: 已撤销
     */
    private String status;

    private boolean waitingForTagInput;

    private boolean waitingForConveyorInput;

    /** 状态的消息详情 */
    private String message;

    /** 进样模式: TRIAL, FACTORY */
    private String mode;

    private String rfTag;

    private Integer posIndex;

    private String parallel;

    /**
     * 同样品中存在多预处理任务时，选择哪一个
     */
    boolean selected;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}