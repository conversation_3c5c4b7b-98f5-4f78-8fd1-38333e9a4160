/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Result;
import org.cjc.les.core.service.dto.ResultDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-25
**/
@Mapper(componentModel = "spring",uses = {ResultItemMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ResultMapper extends BaseMapper<ResultDto, Result> {


    @Mapping(source = "task.id", target = "taskId")
    @Mapping(source = "task.taskNumber", target = "taskNumber")
    @Mapping(source = "sample.name", target = "sampleName")
    @Mapping(source = "sample.number", target = "sampleNumber")
    @Mapping(source = "sample.category.name", target = "sampleCategory")
    @Mapping(source = "sample.customer.name", target = "sampleCustomer")
    @Mapping(source = "task.tag", target = "sampleTag")
    @Override
    ResultDto toDto(Result entity);

}