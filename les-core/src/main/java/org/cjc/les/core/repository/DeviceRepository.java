/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

/**
 * 设备ORM接口层
 */
public interface DeviceRepository extends JpaRepository<Device, Long>, JpaSpecificationExecutor<Device> {

    Optional<Device> findDeviceByJavaClassNameEquals(String javaClassName);

    @Query(value="select count(1) from les_device_instance ins  where ins.delete_flag='N' and ins.device_id in :deviceIds", nativeQuery = true)
    long countDeviceInstances(@Param("deviceIds") Long[] deviceIds);
}