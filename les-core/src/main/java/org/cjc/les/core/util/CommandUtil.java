/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.util;

import org.cjc.les.core.schedule.CommandListenerPool;
import org.cjc.utils.SpringContextHolder;

public class CommandUtil {
    public static void submitToCommandListenerPool(Runnable runnable){
        CommandListenerPool pool = SpringContextHolder.getBean("commandListenerPool");
        pool.submit(runnable);
    }

}
