/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.service.dto.ResultFeatureDto;
import org.cjc.les.core.service.dto.ResultFeatureQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-25
**/
public interface ResultFeatureService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ResultFeatureQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ResultFeatureDto>
    */
    List<ResultFeatureDto> queryAll(ResultFeatureQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return ResultFeatureDto
     */
    ResultFeatureDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return ResultFeatureDto
    */
    ResultFeatureDto create(ResultFeature resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ResultFeature resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ResultFeatureDto> all, HttpServletResponse response) throws IOException;
}