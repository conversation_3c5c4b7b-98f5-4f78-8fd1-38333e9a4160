/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import org.cjc.les.core.service.dto.TaskGlobalInfoDto;

/**
 * 任务全局配置单例实现类
 *
 * <AUTHOR>
 * @date 2024-12-24
 **/
public class TaskGlobalConfig {

    private TaskGlobalInfoDto infoDto;

    private TaskGlobalConfig() {
        infoDto = new TaskGlobalInfoDto();
    }

    private static class TaskGlobalConfigHolder {
        private static TaskGlobalConfig inst = new TaskGlobalConfig();
    }

    public static TaskGlobalConfig getInstance() {
        return TaskGlobalConfigHolder.inst;
    }

    public TaskGlobalInfoDto getInfoDto() {
        return infoDto;
    }

    synchronized public void setStarted(boolean bStart) {
        infoDto.setStarted(bStart);
    }
}
