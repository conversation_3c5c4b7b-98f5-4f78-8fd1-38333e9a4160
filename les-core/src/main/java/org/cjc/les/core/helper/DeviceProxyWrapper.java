/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.service.DeviceInstanceService;
import org.cjc.les.core.service.dto.DevInstStatusDto;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.util.AlertUtil;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备代理封装类，
 * 所有设备实例的指令执行，应该都使用该类访问具体的方法
 */
@Data
@Log4j2
public class DeviceProxyWrapper {

    /**
     * 设备指令代理执行对象
     */
    private Object deviceProxy;

    /**
     * 设备实例数据配置对象
     */
    private DeviceInstance deviceInstance;

    public DeviceProxyWrapper(DeviceInstance deviceInst) {
        this.deviceInstance = deviceInst;
    }


    /**
     * 设备初始化
     *
     * @return true/false
     */
    public boolean init() {
        boolean ret = executeSuperInitMethod();
        if (ret) {
            CommandReturn cmdRet = executeSelfInitMethod();
            if (ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode())){
                ret =true;
                // 清除异常告警
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("DEVICE_INST_INIT");
                alertLog.setAlertSourceId(deviceInstance.getId());
                alertLog.setAlertCode("DEVICE_INST_INIT_ERROR");
                alertLog.setFixedBy("System");
                alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
                AlertUtil.clear(alertLog);
            }else{
                ret = false;
                // 记录异常告警
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("DEVICE_INST_INIT");
                alertLog.setAlertSourceId(deviceInstance.getId());
                alertLog.setAlertLevel(AlertLog.AlertLevelEnum.FATAL.name());
                alertLog.setMessage(deviceInstance.getName()+":"+cmdRet.getErrorMsg());
                alertLog.setAlertCode("DEVICE_INST_INIT_ERROR");
                alertLog.setAlertName(cmdRet.getErrorMsg());
                AlertUtil.createOrUpdate(alertLog);
            }
        }
        if (ret) {
            this.deviceInstance.setInitialized(true);
            sendDeviceInstanceInitChangeMsg(null);
        } else {
            this.deviceInstance.setInitialized(false);
            sendDeviceInstanceInitChangeMsg("FAILED","初始化失败");
        }
        return ret;
    }
    /**
     * 检查是否初始化
     */
    public boolean checkInitialized() {
        return deviceInstance.isInitialized() && checkProxyInitialized();
    }

    /**
     * 检查代理实现类是否初始化
     */
    public boolean checkProxyInitialized() {
        Method checkInitializedMethod = getCheckInitializedMethod();
        if (checkInitializedMethod == null) {
            return true; // 未配置初始化检测的实现类，默认为true
        } else {
            try {
                Object ret = checkInitializedMethod.invoke(this.deviceProxy);
                if (ret instanceof Boolean) {
                    return (Boolean) ret;
                }
            } catch (IllegalAccessException e) {
                log.error("finalize IllegalAccessException exception: {}", e.getMessage(), e);
            } catch (InvocationTargetException e) {
                log.error("finalize InvocationTargetException exception: {}", e.getMessage(), e);
            }
            // 出现异常，则认为未初始化
            return false;
        }
    }


    private boolean executeSuperInitMethod() {
        boolean ret = false;

        // 重新刷新设备实例最新配置
        SpringContextHolder.getBean(DeviceInstanceService.class).reload(this.deviceInstance);

        String configJavaClassName = this.deviceInstance.getDevice().getConfigJavaClassName();

        if (StringUtils.isEmpty(configJavaClassName)) {
            Method initMth = getInitMethod();
            if (initMth == null) {
                ret = false;
            } else {
                try {
                    ret = (Boolean) initMth.invoke(this.deviceProxy);
                } catch (IllegalAccessException e) {
                    log.error("IllegalAccessException exception: {}", e.getMessage(), e);
                } catch (InvocationTargetException e) {
                    log.error("InvocationTargetException exception: {}", e.getMessage(), e);
                }
            }

            return ret;
        }

        try {
            Class<?> clz = this.deviceProxy.getClass();
            Class<?> configClz = clz.getClassLoader().loadClass(configJavaClassName);
            Method initMethod = getInitMethod();
            if (initMethod == null) {
                return false;
            }
            String configStr = (this.deviceInstance.isEnableMock()) ? this.deviceInstance.getMockConfig()
                    : this.deviceInstance.getConfig();
            Object configObj = null;
            if (configClz.equals(String.class)) {
                configObj = configStr;
            } else {
                configObj = JSON.parseObject(configStr, configClz);
            }
            Class<?> returnType = initMethod.getReturnType();
            if (!StringUtils.equalsIgnoreCase(returnType.getName(), "boolean")) {
                log.error("doInit method shoule return type: Boolean");
                return false;
            }
            log.info("Initializing Device:{},{}， config={} ...", this.deviceInstance.getName(), clz.getName(), configObj);
            ret = (Boolean) initMethod.invoke(deviceProxy, configObj);
            log.info("Initializing Device:{},{} is done, ret=***** {} *****", this.deviceInstance.getName(), clz.getName(), ret);
        } catch (IllegalAccessException e) {
            log.error("IllegalAccessException exception: {}", e.getMessage(), e);
            ret = false;
        } catch (InvocationTargetException e) {
            log.error("InvocationTargetException exception: {}", e.getMessage(), e);
            ret = false;
        } catch (ClassNotFoundException e) {
            log.error("ClassNotFoundException exception: {}", e.getMessage(), e);
            ret = false;
        }

        return ret;
    }

    private CommandReturn executeSelfInitMethod() {
        boolean ret = true;
        CommandReturn cmdRet = new CommandReturn();
        cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);

        // 重新刷新设备实例最新配置
        SpringContextHolder.getBean(DeviceInstanceService.class).reload(this.deviceInstance);

        List<DeviceInstanceCmd> cmdList = this.deviceInstance.getCommands();
        for (DeviceInstanceCmd cmd : cmdList) {
            if (StringUtils.equals(cmd.getType(), "INIT") && StringUtils.equals(cmd.getCommandType(), "PROXY")) {
                executeCommandInternal(cmd, cmdRet);
                if (!ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode())) {
                    ret = false;
                    sendDeviceInstanceInitChangeMsg(cmdRet.getStatus().name(), cmdRet.getErrorMsg());
                    break;
                }
            }
        }
        return cmdRet;
    }

    private Method getInitMethod() {

        String configJavaClassName = this.deviceInstance.getDevice().getConfigJavaClassName();

        Class<?> clz = this.deviceProxy.getClass();

        Method initMethod = null;

        if (StringUtils.isEmpty(configJavaClassName)) {
            try {
                initMethod = clz.getDeclaredMethod("doInit");
            } catch (NoSuchMethodException e) {
                initMethod = getMethodByType(clz, CommandTypeEnum.INIT);
            }
        } else {
            Class<?> configClz = null;
            try {
                configClz = clz.getClassLoader().loadClass(configJavaClassName);
            } catch (ClassNotFoundException e) {
                log.error("ClassNotFoundException exception: {}", e.getMessage(), e);
                return null;
            }
            try {
                initMethod = clz.getDeclaredMethod("doInit", configClz);
            } catch (NoSuchMethodException e) {
                initMethod = getMethodByType(clz, CommandTypeEnum.INIT);
            }
        }
        if (initMethod != null) {
            Class<?> returnType = initMethod.getReturnType();
            if (!StringUtils.equalsIgnoreCase(returnType.getName(), "boolean")) {
                log.error("Init method should return type: Boolean");
                return null;
            }
        }
        return initMethod;
    }

    private Method getMethodByType(Class<?> clz, CommandTypeEnum type) {
        for (Method mth : clz.getDeclaredMethods()) {
            Command cmd = mth.getAnnotation(Command.class);
            if (cmd != null && type.equals(cmd.type())) {
                return mth;
            }
        }
        return null;
    }

    public Object getInitParameterObject() {

        String configJavaClassName = this.deviceInstance.getDevice().getConfigJavaClassName();

        String configStr = (this.deviceInstance.isEnableMock()) ? this.deviceInstance.getMockConfig()
                : this.deviceInstance.getConfig();
        if (configStr == null) {
            return null;
        }

        Class<?> clz = this.deviceProxy.getClass();
        try {
            Class<?> configClz = clz.getClassLoader().loadClass(configJavaClassName);

            Object configObj = null;
            if (configClz.equals(String.class)) {
                configObj = configStr;
            } else {
                configObj = JSON.parseObject(configStr, configClz);
            }
            return configObj;
        } catch (ClassNotFoundException e) {
            log.error("ClassNot found : error={}", e.getMessage(), e);
            return null;
        }

    }

    /**
     * 确保初始化完成，在任务执行过程中，每个实例指令执行之前应该调用该方法
     *
     * @return
     */
    public boolean makeSureInitialized() {
        if (!this.deviceInstance.isInitialized()) {
            return init();
        }
        return true;
    }

    /**
     * 设备终止方法
     */
    public void finalize() {
        Method finalizeMethod = getFinalizeMethod();
        log.info("{}, finalizeMethod={}", this.deviceInstance.getName(), finalizeMethod);
        if (finalizeMethod == null) {
            return;
        } else {
            try {
                finalizeMethod.invoke(this.deviceProxy);
                this.deviceInstance.setInitialized(false);
                sendDeviceInstanceInitChangeMsg(null);
            } catch (IllegalAccessException e) {
                log.error("finalize IllegalAccessException exception: {}", e.getMessage(), e);
            } catch (InvocationTargetException e) {
                log.error("finalize InvocationTargetException exception: {}", e.getMessage(), e);
            }
        }
    }


    private Method getFinalizeMethod() {

        Class<?> clz = this.deviceProxy.getClass();

        Method finalizeMethod = null;

        try {
            finalizeMethod = clz.getDeclaredMethod("doFinalize");
        } catch (NoSuchMethodException e) {
            finalizeMethod = getMethodByType(clz, CommandTypeEnum.FINALIZE);
        }

        return finalizeMethod;
    }

    private Method getCheckInitializedMethod() {

        Class<?> clz = this.deviceProxy.getClass();

        Method checkInitMethod = null;

        try {
            checkInitMethod = clz.getDeclaredMethod("checkInitialized");
        } catch (NoSuchMethodException e) {
            checkInitMethod = getMethodByType(clz, CommandTypeEnum.CHECK_INIT);
        }

        return checkInitMethod;
    }

    /**
     * 带有Hook的执行指令
     * @param instanceCmd
     * @param hook
     * @return
     */
    public CommandReturn executeCommand(DeviceInstanceCmd instanceCmd, CommandExecuteHook hook) {
        CommandReturn cmdRet = new CommandReturn();
        cmdRet.setStatus(RunStatusEnum.SUCCESS);
        cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);
        if (hook != null) {
             hook.beforeInvokeMethod(instanceCmd, cmdRet);
        }
        if (cmdRet.getErrorCode().equals(ErrorCodeEnum.SUCCESS)) {
            cmdRet = executeCommand(instanceCmd, cmdRet);
        }
        if (hook != null) {
            cmdRet = hook.afterInvokeMethod(instanceCmd, cmdRet);
        }

        return cmdRet;
    }

    /**
     * 执行设备命令
     *
     * @param instanceCmd
     * @return
     */
    public CommandReturn executeCommand(DeviceInstanceCmd instanceCmd) {
        CommandReturn cmdRet = new CommandReturn();
        cmdRet.setStatus(RunStatusEnum.SUCCESS);
        cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);
        executeCommand(instanceCmd, cmdRet);
        return cmdRet;
    }
    /**
     * 执行设备命令
     *
     * @param instanceCmd
     * @return
     */
    public CommandReturn executeCommand(DeviceInstanceCmd instanceCmd, CommandReturn cmdRet) {
        DeviceInstance deviceInstance = instanceCmd.getRealDeviceInstance();
        org.cjc.les.core.domain.Command command = instanceCmd.getRealCommand();
        DeviceProxyWrapper deviceProxyWrapper = DeviceProxyHelper.getInstance().getDeviceProxy(deviceInstance);

        if (CommandTypeEnum.INIT.equals(command.getType())) {
            boolean ret = this.makeSureInitialized();
            if (!ret) {
                cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                cmdRet.setErrorMsg("Device:" + deviceInstance.getName() + " initialize failed.");
            }
            return cmdRet;
        }
        if (CommandTypeEnum.FINALIZE.equals(command.getType())) {
            finalize();
            cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);
            return cmdRet;
        }

        if (deviceProxyWrapper == null || !deviceProxyWrapper.getDeviceInstance().isInitialized()) {
            cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
            cmdRet.setErrorMsg("Device:" + deviceInstance.getName() + " do not initialized.");
            return cmdRet;
        }

        return executeCommandInternal(instanceCmd, cmdRet);
    }

    public CommandReturn executeCommandInternal(DeviceInstanceCmd instanceCmd, CommandReturn cmdRet) {
        DeviceInstance deviceInstance = instanceCmd.getRealDeviceInstance();
        org.cjc.les.core.domain.Command command = instanceCmd.getRealCommand();

        Object device = deviceInstance.getProxy().getDeviceProxy();
        String javaMethod = command.getJavaMethodName();
        String parameter = instanceCmd.getParameter();
        if (StringUtils.isEmpty(parameter)) {
            parameter = instanceCmd.getParameter();
        }

        String parameterType = command.getParameterType();
        try {
            boolean ret = true;
            cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);

            log.debug("ExecuteCommand: {}({})...", command.getName(), parameter);

            if (StringUtils.isEmpty(parameterType)) {
                Method mth = device.getClass().getDeclaredMethod(javaMethod);
                Class<?> returnType = mth.getReturnType();
                if (StringUtils.equals(returnType.getName(), "void")) {
                    invokeMethod(mth, device);
                } else if (StringUtils.equals(returnType.getName(), "boolean")) {
                    ret = (boolean) invokeMethod(mth, device);
                    if (!ret) {
                        cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                        cmdRet.setErrorMsg("The Command return false");
                    }
                } else if (StringUtils.equals(returnType.getName(), "org.cjc.les.vo.CommandReturn")) {
                    CommandReturn invokeRet = (CommandReturn) invokeMethod(mth, device);
                    cmdRet.copy(invokeRet);
                } else {
                    log.error("Do not support another return type, except: void, boolean,CommandReturn");
                    cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
                    cmdRet.setErrorMsg("Do not support another return type, except: void, boolean,CommandReturn");
                }
            } else {
                Object parameterValue = convertActualClass(parameter, instanceCmd);
                Class clz = this.getClass().getClassLoader().loadClass(parameterType);
                Method mth = device.getClass().getDeclaredMethod(javaMethod, clz);
                Class<?> returnType = mth.getReturnType();
                if (StringUtils.equals(returnType.getName(), "void")) {
                    invokeMethod(mth, device, parameterValue);
                } else if (StringUtils.equals(returnType.getName(), "boolean")) {
                    ret = (boolean) invokeMethod(mth, device, parameterValue);
                    if (!ret) {
                        cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                        cmdRet.setErrorMsg("The Command return false");
                    }
                } else if (StringUtils.equals(returnType.getName(), "org.cjc.les.vo.CommandReturn")) {
                    CommandReturn invokeRet = (CommandReturn) invokeMethod(mth, device, parameterValue);
                    cmdRet.copy(invokeRet);
                } else {
                    log.error("Do not support another return type, except: void, boolean,CommandReturn");
                    cmdRet.setStatus(RunStatusEnum.FAILED);
                    cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
                    cmdRet.setErrorMsg("Do not support another return type, except: void, boolean,CommandReturn");
                }

            }
            log.debug("ExecuteCommand: {} is done. ret={}", command.getName(), cmdRet);
            // CommandContext.clear();
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("Could not executeCommand: {}, Method access error, Exception:{}", command, e.getMessage(), e);
            cmdRet.setStatus(RunStatusEnum.FAILED);
            cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
            cmdRet.setErrorMsg("Method access error");
        } catch (TaskRunningException e) {
            log.error("Could not executeCommand: {}, Exception:{}", command.getName(), e.getMessage(), e);
            cmdRet.setStatus(RunStatusEnum.FAILED);
            cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
            cmdRet.setErrorMsg(e.getMessage());
        } catch (Throwable e) {
            log.error("Could not executeCommand: {}, Exception:{}", command.getName(), e.getMessage(), e);
            cmdRet.setStatus(RunStatusEnum.FAILED);
            cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
            cmdRet.setErrorMsg("Method access unknown error");
        }

        // 执行指令完成后处理脚本
        if (ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode()) && StringUtils.isNotEmpty(instanceCmd.getPostExecution())){

            Map<String, Object> out = FormulaExecutionHelper.getInstance().execute(instanceCmd.getPostExecution(), cmdRet.getResult());
            String retStatus = out.get("status") == null ? "" : out.get("status").toString();
            String retFailedThen = out.get("failedThen") == null ? "" : out.get("failedThen").toString();
            if (StringUtils.equalsAny(RunStatusEnum.SUCCESS.name(), retStatus)) {
                cmdRet.setStatus(RunStatusEnum.SUCCESS);
            } else if (StringUtils.equalsAny(RunStatusEnum.FAILED.name(), retStatus)) {
                cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                cmdRet.setStatus(RunStatusEnum.FAILED);
                if (StringUtils.equalsAny(FailedThenEnum.RETRY.name(), retFailedThen)) {
                    cmdRet.setFailedThen(FailedThenEnum.RETRY);
                }
            } else{
                if (cmdRet.getResult() instanceof Map ) {
                    Map rs = (Map)cmdRet.getResult();
                    rs.putAll(out);
                }
            }
        }

        return cmdRet;
    }

    private Object convertActualClass(String parameter, DeviceInstanceCmd instanceCmd ) throws ClassNotFoundException {
        org.cjc.les.core.domain.Command command = instanceCmd.getRealCommand();
        if (StringUtils.isEmpty(parameter)){
            parameter = instanceCmd.getParameter();
        }
        return new ActualParameterConverter(instanceCmd, parameter).convert();
    }
    private Object invokeMethod(Method mth, Object deviceProxy, Object parameterValue, boolean withParam) throws Exception {
        recordDebugLog(parameterValue);
        Object ret = (withParam)? mth.invoke(deviceProxy, parameterValue):mth.invoke(deviceProxy);
        recordDebugLog(ret);
        return ret;
    }
    private Object invokeMethod(Method mth, Object deviceProxy) throws Exception {
        return invokeMethod(mth, deviceProxy, null, false);
    }
    private Object invokeMethod(Method mth, Object deviceProxy, Object parameterValue) throws Exception {
        return invokeMethod(mth, deviceProxy, parameterValue, true);
    }

    private void recordDebugLog(Object objectValue) {
        ExecutorConfig executorConfig = TaskExecutorContext.getContext().getExecutorConfig();
        if (executorConfig != null && !StringUtils.equalsAny(executorConfig.getDebugMode().name(), ExecutorConfig.DebugModeEnum.NONE.name())) {
            TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
            if (taskCommand != null) {
                if (objectValue != null) {
                    taskCommand.setMessage(objectValue.toString());
                }
                TaskExecuteLogUtil.logCommand(taskCommand, TaskLogLevelEnum.DEBUG);
                taskCommand.setMessage(null);
            }
        }
    }

    /**
     * 执行方法，无参
     *
     * @param javaMethodName
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     */
    public Object invokeMethod(String javaMethodName) throws TaskRunningException {
        try {
            Method mth = this.deviceProxy.getClass().getDeclaredMethod(javaMethodName);

            return mth.invoke(deviceProxy);

        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            log.error("invokeMethod Error: {}", e.getMessage(), e);
            throw new TaskRunningException("Invoke method error: " + e.getMessage());
        }
    }

    /**
     * 执行方法，带一个参数
     *
     * @param javaMethodName
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     */
    public Object invokeMethod(String javaMethodName, Object parameter) throws TaskRunningException {
        try {
            Method mth = this.deviceProxy.getClass().getDeclaredMethod(javaMethodName, parameter.getClass());

            return mth.invoke(deviceProxy, parameter);

        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            log.error("invokeMethod Error: {}", e.getMessage(), e);
            throw new TaskRunningException("Invoke method error: " + e.getMessage());
        }
    }
    public void sendDeviceInstanceInitChangeMsg() {
        sendDeviceInstanceInitChangeMsg(null, null);
    }
    public void sendDeviceInstanceInitChangeMsg(String status) {
        sendDeviceInstanceInitChangeMsg(status, null);
    }
    public void sendDeviceInstanceInitChangeMsg(String status, String message) {

        DevInstStatusDto dto = new DevInstStatusDto();
        dto.setOperation(DevInstStatusDto.OperationEnum.DEV_INST_STATUS_CHANGE);
        dto.setId(this.deviceInstance.getId());
        dto.setName(this.deviceInstance.getName());
        dto.setType(this.deviceInstance.getType());
        dto.setInitialized(this.deviceInstance.isInitialized());
        dto.setStatus(status);
        dto.setMessage(message);

        SocketMsg msg = new SocketMsg(JSON.toJSONString(dto), MsgType.INFO);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"positionStateChange"}, true);
        } catch (IOException e) {
            log.error("sendDeviceInstanceInitChangeMsg error, message={}", e.getMessage(), e);
        }

    }

}
