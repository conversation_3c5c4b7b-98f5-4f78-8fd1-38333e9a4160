/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.schedule;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.Command;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.vo.CommandReturn;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Scanner;

@Log4j2
public class CommandExecutor {

    private int retries = 0;

    public boolean executeCommand(Command command, Procedure procedure) {
        Device device = command.getDevice();
        if (device == null){
            log.error("Not found device for this command: {}",command.getName());
            return false;
        }
        if (!device.isInitialized()) {
            log.error("Device:{} is not initialized for this command: {}",device.getName(),command.getName());
            return false;
        }

        CommandContext.setProcedure(procedure);

        CommandReturn cmdRet = new CommandReturn();
        cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);
        do {
            cmdRet = execute(command);
            if (StringUtils.equalsAny(cmdRet.getErrorCode().name(), ErrorCodeEnum.ERROR.name(), ErrorCodeEnum.FATAL.name())) {
                if (FailedThenEnum.PAUSE.equals(command.getFailedThen())) {
                    FailedThenEnum then = waitToManualOperation(command, cmdRet);
                    if (StringUtils.equalsIgnoreCase(then.name(), FailedThenEnum.RETRY.name())) {
                        retries ++;
                        continue;
                    }
                }
            }
            break;
        }while(true);

        if (cmdRet.getErrorCode().equals(ErrorCodeEnum.SUCCESS)){
            command.setStatus(CommandStatusEnum.SUCCESS);
            return true;
        }else{
            return false;
        }

    }

    private CommandReturn execute(Command command) {
        Device device = command.getDevice();
        String javaMethod = command.getJavaMethodName();
        Object parameterValue = command.getParameter();

        String parameterType = command.getParameterType();
        try {
            boolean ret = true;
            CommandReturn cmdRet = new CommandReturn();
            cmdRet.setErrorCode(ErrorCodeEnum.SUCCESS);

            log.info("ExecuteCommand: {}({})...", command.getName(),parameterValue);
            if (StringUtils.isEmpty(parameterType) ){
                Method mth = device.getClass().getDeclaredMethod(javaMethod);
                Class<?> returnType = mth.getReturnType();
                if (StringUtils.equals(returnType.getName(),"void")) {
                    mth.invoke(device);
                }else if (StringUtils.equals(returnType.getName(),"boolean")){
                    ret = (boolean)mth.invoke(device);
                    if (!ret){
                        cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                        cmdRet.setErrorMsg("The Command return false");
                    }
                }
                else if (StringUtils.equals(returnType.getName(),"org.cjc.les.vo.CommandReturn")){
                    return (CommandReturn)mth.invoke(device);
                }
                else{
                    log.error("Do not support another return type, except: void, boolean,CommandReturn");
                    cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
                    cmdRet.setErrorMsg("Do not support another return type, except: void, boolean,CommandReturn");
                }
            }else {
                parameterValue = convertActualClass(parameterValue, parameterType);
                Class clz = this.getClass().getClassLoader().loadClass(parameterType);
                Method mth = device.getClass().getDeclaredMethod(javaMethod, clz);
                Class<?> returnType = mth.getReturnType();
                if (StringUtils.equals(returnType.getName(),"void")){
                    mth.invoke(device, parameterValue);
                }else if (StringUtils.equals(returnType.getName(),"boolean")){
                    ret = (boolean)mth.invoke(device, parameterValue);
                    if (!ret){
                        cmdRet.setErrorCode(ErrorCodeEnum.ERROR);
                        cmdRet.setErrorMsg("The Command return false");
                    }
                }
                else if (StringUtils.equals(returnType.getName(),"org.cjc.les.vo.CommandReturn")){
                    return (CommandReturn)mth.invoke(device, parameterValue);
                }
                else{
                    log.error("Do not support another return type, except: void, boolean,CommandReturn");
                    cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
                    cmdRet.setErrorMsg("Do not support another return type, except: void, boolean,CommandReturn");
                }

            }
            log.info("ExecuteCommand: {} is done. ret={}", command.getName(),cmdRet);
            // CommandContext.clear();
            return cmdRet;
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("Could not executeCommand: {}, Method access error, Exception:{}", command, e.getMessage(), e);

            CommandReturn cmdRet = new CommandReturn();
            cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
            cmdRet.setErrorMsg("Method access error");

            return cmdRet;
        }catch(Throwable e){
            log.error("Could not executeCommand: {}, Exception:{}", command, e.getMessage(), e);

            CommandReturn cmdRet = new CommandReturn();
            cmdRet.setErrorCode(ErrorCodeEnum.FATAL);
            cmdRet.setErrorMsg("Method access unknown error");
            waitToManualOperation(command, cmdRet);

            return cmdRet;
        }
    }

    private Object convertActualClass(Object obj, String clzName){
        if (StringUtils.equalsIgnoreCase("java.lang.long", clzName) && !(obj instanceof Long)){
            return Long.parseLong(obj.toString());
        }
        return obj;
    }

    private FailedThenEnum waitToManualOperation(Command command, CommandReturn cmdRet){

        Scanner scanner = new Scanner(System.in);
        log.error("Please choice the operation when the command was executed failed: {}, command: {}", cmdRet.getErrorMsg(), command);
        FailedThenEnum choiced = FailedThenEnum.PAUSE;
        do {
            log.error("[PAUSE,CONTINUE,RETRY,RETURN_ACTION,RETURN_STEP,RETURN_METHOD,RETURN_PROCEDURE], please choice ==> ");
            String line = scanner.nextLine();
            if (StringUtils.equalsIgnoreCase(FailedThenEnum.RETRY.name(), line)) {
                choiced = FailedThenEnum.RETRY;
                break;
            }
        }while(true);
        scanner.close();

        return choiced;
    }
}
