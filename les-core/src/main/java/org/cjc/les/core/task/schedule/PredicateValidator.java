/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.FormulaExecutionHelper;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.vo.ConditionItemVo;
import org.cjc.les.core.vo.LockRequestVo;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.les.core.vo.ScriptRequestVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.RedisUtils;
import org.cjc.utils.SpringContextHolder;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


/**
 * @website https://el-admin.vip
 * @description /
 * <AUTHOR>
 * @date 2024-11-02
 **/
@Log4j2
@RequiredArgsConstructor
@Component
public class PredicateValidator {

    @Data
    public static class ValidateResult {
        private boolean valid;
        private String unmatchedThen;
        public ValidateResult(boolean valid, String unmatchedThen) {
            this.valid = valid;
            this.unmatchedThen = unmatchedThen;
        }
        public ValidateResult(boolean valid) {
            this.valid = valid;
        }
    }

    private final PositionService positionService;

    /**
     * 任务动作执行若有锁等待，则使用非阻塞模式，从而保证不影响该动作所在站点的其他动作运行调度
     * @param taskAction
     * @return
     */
    public ValidateResult validate(TaskAction taskAction) {
        return validate(taskAction.getPredicates(), taskAction.getTaskId(), taskAction.getTaskMethodId());
    }

    public ValidateResult validate(TaskStep taskStep) {
        return validate(taskStep.getPredicates(), taskStep.getTaskId(), taskStep.getTaskMethod().getId());
    }

    public ValidateResult validate(String predicate, Long taskId, Long taskMethodId) {
        if (StringUtils.isEmpty(predicate)) {
            return new ValidateResult(true);
        }
        List<ConditionItemVo> predicatesList = JSON.parseArray(predicate, ConditionItemVo.class);
        boolean ret = true;
        for (ConditionItemVo item : predicatesList) {
            boolean tmpRet = executePredicate(item, taskId, taskMethodId);
            if (!tmpRet && StringUtils.equalsAny(item.getUnmatchedThen(), "WAIT", "SKIP")) {
                return new ValidateResult(false, item.getUnmatchedThen());
            }
            if ("and".equalsIgnoreCase(item.getJoinOperator())) {
                ret = ret && tmpRet;
                if (!ret) {
                    return new ValidateResult(false);
                }
            } else if ("or".equalsIgnoreCase(item.getJoinOperator())) {
                ret = ret || tmpRet;
                if (ret) {
                    return new ValidateResult(true);
                }
            }
        }
        return new ValidateResult(ret);
    }

    public boolean unlock(String predicate) {
        if (StringUtils.isEmpty(predicate)) {
            return true;
        }
        List<ConditionItemVo> predicatesList = JSON.parseArray(predicate, ConditionItemVo.class);
        boolean ret = true;
        for (ConditionItemVo item : predicatesList) {
            if ("lock-select".equalsIgnoreCase(item.getSourceType())) {
                executeUnLockSelectPredicate(item);
            }
        }
        return true;
    }


    private boolean executePredicate(ConditionItemVo item, Long taskId, Long taskMethodId) {
        if ("position-select".equalsIgnoreCase(item.getSourceType())) {
            return executePositionSelectPredicate(item, taskId, taskMethodId);
        }else if ("lock-select".equalsIgnoreCase(item.getSourceType())) {
            return executeLockSelectPredicate(item);
        }else if ("script-select".equalsIgnoreCase(item.getSourceType())) {
            return executeScriptSelectPredicate(item);
        } else if ("command".equalsIgnoreCase(item.getSourceType())) {
            return executeCommandPredicate(item);
        }

        return false;
    }

    private boolean executePositionSelectPredicate(ConditionItemVo item, Long taskId, Long taskMethodId) {
        PositionRequestVo posReq = JSON.parseObject(item.getSourceParameter(), PositionRequestVo.class);
        posReq.setTaskId(taskId);
        posReq.setTaskMethodId(taskMethodId);
        posReq.setStatus(item.getTargetValue());
        Optional<Position> retPos = positionService.findMatchedPosition(posReq);
        if (ConditionItemVo.CompareOperatorEnum.eq.name().equalsIgnoreCase(item.getCompareOperator())) {
            if (retPos.isPresent()) {
                return true;
            } else {
                return false;
            }
        } else if (ConditionItemVo.CompareOperatorEnum.ne.name().equalsIgnoreCase(item.getCompareOperator())) {
            if (!retPos.isPresent()) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }

    private boolean executeLockSelectPredicate(ConditionItemVo item) {
        RedisUtils redisUtils = SpringContextHolder.getBean(RedisUtils.class);

        String lockValue = String.valueOf(Thread.currentThread().getId());
        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            lockValue = task.getId() + "." + lockValue;
        }

        LockRequestVo lockReq = JSON.parseObject(item.getSourceParameter(), LockRequestVo.class);
        String lockKey = lockReq.getLockObjectType();
        lockKey += "_" + lockReq.getDeviceInstanceId();
        if (StringUtils.equalsAny(lockReq.getLockObjectType(), "DEV_INSTANCE_POS")) {
            lockKey += "_" + lockReq.getName();
        }

        // 非阻塞模式
        if (StringUtils.equalsIgnoreCase(lockReq.getLockMode(), "UNBLOCK")) {
            return redisUtils.tryLock("lock." + lockKey, lockValue, -1, TimeUnit.MILLISECONDS);
        }
        // 阻塞模式
        boolean ret = false;
        while (!(ret = redisUtils.tryLock("lock." + lockKey, lockValue, -1, TimeUnit.MILLISECONDS))) {
            waitTime(1000L);
        }
        return true;
    }

    private boolean executeUnLockSelectPredicate(ConditionItemVo item) {
        RedisUtils redisUtils = SpringContextHolder.getBean(RedisUtils.class);

        String lockValue = String.valueOf(Thread.currentThread().getId());
        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            lockValue = task.getId() + "." + lockValue;
        }
        LockRequestVo lockReq = JSON.parseObject(item.getSourceParameter(), LockRequestVo.class);
        String lockKey = lockReq.getLockObjectType();
        lockKey += "_" + lockReq.getDeviceInstanceId();
        if (StringUtils.equalsAny(lockReq.getLockObjectType(), "DEV_INSTANCE_POS")) {
            lockKey += "_" + lockReq.getName();
        }
        return redisUtils.unlock("lock." + lockKey, lockValue);
    }

    private boolean executeScriptSelectPredicate(ConditionItemVo item) {

        FormulaExecutionHelper formulaExecutionHelper = FormulaExecutionHelper.getInstance();
        Map<String, Object> paramMap = new HashMap<>();

        ScriptRequestVo scriptReq = JSON.parseObject(item.getSourceParameter(), ScriptRequestVo.class);
        paramMap.put("deviceInstanceId", scriptReq.getDeviceInstanceId());
        paramMap.put("positionName", scriptReq.getPositionName());

        boolean ret = formulaExecutionHelper.executeBooleanScript(item.getName(), paramMap);
        if (ConditionItemVo.CompareOperatorEnum.eq.name().equalsIgnoreCase(item.getCompareOperator())) {
            return ret;
        } else if (ConditionItemVo.CompareOperatorEnum.ne.name().equalsIgnoreCase(item.getCompareOperator())) {
            return !ret;
        }
        return ret;
    }

    private boolean executeCommandPredicate(ConditionItemVo item) {
        return true;
    }

    private void waitTime(Long milliSec) {
        try {
            Thread.sleep(milliSec);
        } catch (InterruptedException e) {
            log.error("Sleep exception:{}", e.getMessage(), e);
            throw new TaskRunningException("InterruptedException occurred");
        }
    }

}
