/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.Function;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.domain.Variable;
import org.cjc.les.core.repository.FunctionRepository;
import org.cjc.les.core.service.FunctionService;
import org.cjc.les.core.service.dto.FunctionDto;
import org.cjc.les.core.service.dto.FunctionQueryCriteria;
import org.cjc.les.core.service.mapstruct.FunctionMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-17
**/
@Service
@RequiredArgsConstructor
public class FunctionServiceImpl implements FunctionService {

    private final FunctionRepository functionRepository;
    private final FunctionMapper functionMapper;

    @Override
    public Map<String,Object> queryAll(FunctionQueryCriteria criteria, Pageable pageable){
        Page<Function> page = functionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(functionMapper::toDto));
    }

    @Override
    public List<FunctionDto> queryAll(FunctionQueryCriteria criteria){
        return functionMapper.toDto(functionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FunctionDto findById(Long id) {
        Function function = functionRepository.findById(id).orElseGet(Function::new);
        ValidationUtil.isNull(function.getId(),"Function","id",id);
        return functionMapper.toDto(function);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FunctionDto create(Function resources) {
        if(functionRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Function.class,"name",resources.getName());
        }
        return functionMapper.toDto(functionRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Function resources) {
        Function function = functionRepository.findById(resources.getId()).orElseGet(Function::new);
        ValidationUtil.isNull( function.getId(),"Function","id",resources.getId());
        Function function1 = null;
        function1 = functionRepository.findByName(resources.getName());
        if(function1 != null && !function1.getId().equals(function.getId())){
            throw new EntityExistException(Function.class,"name",resources.getName());
        }
        function.copy(resources);
        functionRepository.save(function);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(Function resources) {
        Function functionInDb = functionRepository.findByName(resources.getName());
        if (functionInDb == null){
            functionInDb = resources;
        }else{
            functionInDb.copy(resources);
        }
        functionRepository.save(functionInDb);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            functionRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FunctionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FunctionDto function : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("函数名称", function.getName());
            map.put("函数显示提示信息", function.getTip());
            map.put("函数描述", function.getDescription());
            map.put("参数定义", function.getParameters());
            map.put("返回类型", function.getReturnType());
            map.put("是否已被删除,Y/N", function.getDeleteFlag());
            map.put("创建人", function.getCreateBy());
            map.put("创建时间", function.getCreateTime());
            map.put("更新人", function.getUpdateBy());
            map.put("更新时间", function.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}