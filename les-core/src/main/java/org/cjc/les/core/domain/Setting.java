/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.cjc.base.LesConfigBaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-05-19
**/
@Entity
@Data
@Table(name="les_setting")
public class Setting extends LesConfigBaseEntity {

    @Column(name = "name",unique = true)
    @Schema(description = "设置名称")
    private String name;

    @Column(name = "global_settings")
    @Schema(description = "全局设置内容")
    private String globalSettings;

    @Column(name = "description")
    @Schema(description = "描述")
    private String description;

    public void copy(Setting source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}