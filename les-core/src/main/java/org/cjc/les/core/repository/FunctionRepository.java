/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Function;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-17
**/
public interface FunctionRepository extends JpaRepository<Function, Long>, JpaSpecificationExecutor<Function> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Function findByName(String name);
}