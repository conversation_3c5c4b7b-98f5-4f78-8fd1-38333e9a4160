/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.Action;
import org.cjc.les.core.domain.DeviceInstance;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-07-14
**/
@Data
public class StationDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工作站名称 */
    private String name;

    /** 工作站描述 */
    private String description;

    /** 工作站类型 */
    private String type;

    /**
     * DAG图
     */
    private String dagJson;

    /** 工作站状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    private List<Action> actions = new ArrayList<>();

    private List<DeviceInstance> devices = new ArrayList<>();

    /**
     * 最大线程数量，默认为1
     */
    private Integer maxThreadSize = 1;

    /**
     * 动作执行队列缓存数量
     */
    private Integer actionQueueSize = 0;
}