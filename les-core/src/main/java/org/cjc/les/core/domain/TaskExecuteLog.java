/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-08-23
**/
@Entity
@Data
@Table(name="les_task_execute_log")
public class TaskExecuteLog extends LesBaseEntity {

    @Column(name = "task_id")
    @Schema(description = "任务lD")
    private Long taskId;

    @Column(name = "task_method_id")
    @Schema(description = "任务方法ID")
    private Long taskMethodId;

    @Column(name = "task_step_id")
    @Schema(description = "任务步骤ID")
    private Long taskStepId;

    @Column(name = "task_action_id")
    @Schema(description = "任务动作ID")
    private Long taskActionId;

    @Column(name = "task_command_id")
    @Schema(description = "任务命令ID")
    private Long taskCommandId;

    @Column(name = "name")
    @Schema(description = "名称，根据日志类型分别表示对应的日志对象名称")
    private String name;

    @Column(name = "status")
    @Schema(description = "状态: READY,RUNNING,SUCCESS,FAILED")
    private String status;

    @Column(name = "message")
    @Schema(description = "执行详情")
    private String message;

    @Column(name = "log_type")
    @Schema(description = "日志类型:TASK,METHOD,STEP,ACTION,COMMAND")
    private String logType;

    @Column(name = "log_level")
    @Schema(description = "日志级别:DEBUG,INFO,WARN,ERROR")
    private String logLevel;

    public void copy(TaskExecuteLog source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}