/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Action;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
public interface ActionRepository extends JpaRepository<Action, Long>, JpaSpecificationExecutor<Action> {

    @Query(value = "select act.* from les_action act where act.name=?2 " +
            " and act.station_id in (select sdev.station_id from les_station_deviceinst sdev where sdev.device_instance_id = ?1) limit 1", nativeQuery = true)
    Optional<Action> findPickPlaceActionByDevInstId(Long deviceInstanceId, String actionName);

    @Query(value = "select act from Action as act where act.deleteFlag='N' order by act.station.id asc, act.name asc, act.createTime asc")
    List<Action> findActionsOrderByStationAndCreateTime();

    @Query(value = "select act.* from les_action act where act.delete_flag='N' and not exists (select 1 from les_step_action sact join les_step stp on stp.id=sact.step_id and stp.delete_flag='N' where sact.action_id=act.id and sact.delete_flag='N')", nativeQuery = true)
    List<Action> findUnRefActions();
}