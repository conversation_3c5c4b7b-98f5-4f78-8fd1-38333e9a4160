/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.core.service.dto.TaskCommandQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import java.util.Optional;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskCommandService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskCommandQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskCommandDto>
    */
    List<TaskCommandDto> queryAll(TaskCommandQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskCommandDto
     */
    TaskCommandDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return TaskCommandDto
    */
    TaskCommandDto create(TaskCommand resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskCommand resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskCommandDto> all, HttpServletResponse response) throws IOException;

    /**
     * 等待失败问题修复，知道人工决策，(通过设置failure_fix_as字段)
     * @param taskCommand
     */
    void waitUntilFailureHandledByManually(TaskCommand taskCommand);

    /**
     * 更新异常处理信息
     * @param dto
     */
    void updateFailureFixInfo(TaskCommandDto dto);

    /**
     * 更新点位状态信息
     * @param pos
     */
    void updatePositionStatus(TaskCommand cmd,  Position pos);

    Optional<TaskCommand> findTaskCommandById(Long id);

    /**
     * 通过板节点ID，获取该板中的所有点位列表
     * @param nodeId
     * @return
     */
    List<Position> findPositionsByBoardNodeId(String nodeId);
}