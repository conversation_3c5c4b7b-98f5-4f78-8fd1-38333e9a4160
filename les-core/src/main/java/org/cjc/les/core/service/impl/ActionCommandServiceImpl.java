/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.ActionCommand;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ActionCommandRepository;
import org.cjc.les.core.service.ActionCommandService;
import org.cjc.les.core.service.dto.ActionCommandDto;
import org.cjc.les.core.service.dto.ActionCommandQueryCriteria;
import org.cjc.les.core.service.mapstruct.ActionCommandMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.cjc.exception.EntityExistException;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-08
**/
@Service
@RequiredArgsConstructor
public class ActionCommandServiceImpl implements ActionCommandService {

    private final ActionCommandRepository actionCommandRepository;
    private final ActionCommandMapper actionCommandMapper;

    @Override
    public Map<String,Object> queryAll(ActionCommandQueryCriteria criteria, Pageable pageable){
        Page<ActionCommand> page = actionCommandRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(actionCommandMapper::toDto));
    }

    @Override
    public List<ActionCommandDto> queryAll(ActionCommandQueryCriteria criteria){
        return actionCommandMapper.toDto(actionCommandRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public ActionCommandDto findById(Long id) {
        ActionCommand actionCommand = actionCommandRepository.findById(id).orElseGet(ActionCommand::new);
        ValidationUtil.isNull(actionCommand.getId(),"ActionCommand","id",id);
        return actionCommandMapper.toDto(actionCommand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActionCommandDto create(ActionCommand resources) {
        return actionCommandMapper.toDto(actionCommandRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ActionCommand resources) {
        ActionCommand actionCommand = actionCommandRepository.findById(resources.getId()).orElseGet(ActionCommand::new);
        ValidationUtil.isNull( actionCommand.getId(),"ActionCommand","id",resources.getId());
        actionCommand.copy(resources);
        actionCommandRepository.save(actionCommand);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            actionCommandRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ActionCommandDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ActionCommandDto actionCommand : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联ACTION", actionCommand.getActionId());
            map.put("设备实例ID", actionCommand.getDeviceInstanceId());
            map.put("关联命令", actionCommand.getCommandId());
            map.put(" parameter",  actionCommand.getParameter());
            map.put("命令备注描述", actionCommand.getComment());
            map.put("运行失败后的处理方式:CONTINUE/RETURN_ACTION/RETURN_STEP/RETURN_METHOD/RETURN_PROCEDURE", actionCommand.getFailedThen());
            map.put("状态", actionCommand.getStatus());
            map.put("是否已被删除,Y/N", actionCommand.getDeleteFlag());
            map.put("创建人", actionCommand.getCreateBy());
            map.put("创建时间", actionCommand.getCreateTime());
            map.put("更新人", actionCommand.getUpdateBy());
            map.put("更新时间", actionCommand.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}