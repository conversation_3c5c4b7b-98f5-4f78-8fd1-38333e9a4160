/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.SampleCategory;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.SampleCategoryRepository;
import org.cjc.les.core.service.SampleCategoryService;
import org.cjc.les.core.service.dto.SampleCategoryDto;
import org.cjc.les.core.service.dto.SampleCategoryQueryCriteria;
import org.cjc.les.core.service.mapstruct.SampleCategoryMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-22
**/
@Service
@RequiredArgsConstructor
public class SampleCategoryServiceImpl implements SampleCategoryService {

    private final SampleCategoryRepository sampleCategoryRepository;
    private final SampleCategoryMapper sampleCategoryMapper;

    @Override
    public Map<String,Object> queryAll(SampleCategoryQueryCriteria criteria, Pageable pageable){
        Page<SampleCategory> page = sampleCategoryRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sampleCategoryMapper::toDto));
    }

    @Override
    public List<SampleCategoryDto> queryAll(SampleCategoryQueryCriteria criteria){
        return sampleCategoryMapper.toDto(sampleCategoryRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public SampleCategoryDto findById(Long id) {
        SampleCategory sampleCategory = sampleCategoryRepository.findById(id).orElseGet(SampleCategory::new);
        ValidationUtil.isNull(sampleCategory.getId(),"SampleCategory","id",id);
        return sampleCategoryMapper.toDto(sampleCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleCategoryDto create(SampleCategory resources) {
        if(sampleCategoryRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(SampleCategory.class,"name",resources.getName());
        }
        return sampleCategoryMapper.toDto(sampleCategoryRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SampleCategory resources) {
        SampleCategory sampleCategory = sampleCategoryRepository.findById(resources.getId()).orElseGet(SampleCategory::new);
        ValidationUtil.isNull( sampleCategory.getId(),"SampleCategory","id",resources.getId());
        Optional<SampleCategory> sampleCategoryOpt = sampleCategoryRepository.findByName(resources.getName());
        if(sampleCategoryOpt.isPresent() && !sampleCategoryOpt.get().getId().equals(sampleCategory.getId())){
            throw new EntityExistException(SampleCategory.class,"name",resources.getName());
        }
        sampleCategory.copy(resources);
        sampleCategoryRepository.save(sampleCategory);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            sampleCategoryRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SampleCategoryDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SampleCategoryDto sampleCategory : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("样本分类名称", sampleCategory.getName());
            map.put("样本描述", sampleCategory.getDescription());
            map.put("是否已被删除,Y/N", sampleCategory.getDeleteFlag());
            map.put("创建人", sampleCategory.getCreateBy());
            map.put("创建时间", sampleCategory.getCreateTime());
            map.put("更新人", sampleCategory.getUpdateBy());
            map.put("更新时间", sampleCategory.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}