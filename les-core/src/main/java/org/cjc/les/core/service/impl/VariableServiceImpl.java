/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.Variable;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.VariableRepository;
import org.cjc.les.core.service.ProcedureVariableService;
import org.cjc.les.core.service.VariableService;
import org.cjc.les.core.service.dto.ProcedureDto;
import org.cjc.les.core.service.dto.ProcedureVariableDto;
import org.cjc.les.core.service.dto.VariableDto;
import org.cjc.les.core.service.dto.VariableQueryCriteria;
import org.cjc.les.core.service.mapstruct.VariableMapper;
import org.cjc.les.core.task.execute.VariablePlaceholderResolver;
import org.cjc.les.core.vo.ConfigItemVo;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-17
**/
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "variableCache")
public class VariableServiceImpl implements VariableService {

    /**
     * 匹配字符串形如: SET_VAR("AAA","ee");  ADD_VAR("BBB","V"); SET_VAR("CCC",99); SET_VAR("DD_EE",99);
     */
    private final static Pattern VAR_SET_PATTERNS = Pattern.compile("(?:SET_VAR|ADD_VAR)\\s*\\(\\s*\"([A-Za-z_][A-Za-z0-9_]*)\"\\s*,\\s*([^)]+)\\s*\\)\\s*;?");
    private final VariableRepository variableRepository;
    private final VariableMapper variableMapper;

    private final ProcedureVariableService procedureVariableService;

    @Override
    public Map<String,Object> queryAll(VariableQueryCriteria criteria, Pageable pageable){
        Page<Variable> page = variableRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(variableMapper::toDto));
    }

    @Override
    public List<VariableDto> queryAll(VariableQueryCriteria criteria){
        return variableMapper.toDto(variableRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public VariableDto findById(Long id) {
        Variable variable = variableRepository.findById(id).orElseGet(Variable::new);
        ValidationUtil.isNull(variable.getId(),"Variable","id",id);
        return variableMapper.toDto(variable);
    }

    @Override
    @Cacheable(key = "'findVariableByName:' + #p0")
    public VariableDto findByName(String name) {
        Variable variable = variableRepository.findByName(name);
        if (variable == null) {
            return null;
        }
        return variableMapper.toDto(variable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VariableDto create(Variable resources) {
        if(variableRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Variable.class,"name",resources.getName());
        }
        return variableMapper.toDto(variableRepository.save(resources));
    }

    @Override
    @CacheEvict(key = "'findVariableByName:' + #p0.name")
    @Transactional(rollbackFor = Exception.class)
    public void update(Variable resources) {
        Variable variable = variableRepository.findById(resources.getId()).orElseGet(Variable::new);
        ValidationUtil.isNull( variable.getId(),"Variable","id",resources.getId());
        Variable variable1 = null;
        variable1 = variableRepository.findByName(resources.getName());
        if(variable1 != null && !variable1.getId().equals(variable.getId())){
            throw new EntityExistException(Variable.class,"name",resources.getName());
        }
        variable.copy(resources);
        variableRepository.save(variable);
    }

    @Override
    @CacheEvict(key = "'findVariableByName:' + #p0.name")
    @Transactional(rollbackFor = Exception.class)
    public void save(Variable resources) {
        Variable variableInDb = variableRepository.findByName(resources.getName());
        if (variableInDb == null){
            variableInDb = resources;
        }else{
            variableInDb.copy(resources);
        }
        variableRepository.save(variableInDb);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            variableRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<VariableDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (VariableDto variable : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("变量名称", variable.getName());
            map.put("变量显示名", variable.getViewName());
            map.put("变量描述", variable.getDescription());
            map.put("变量类型: NUMBER,STRING,ARRAY", variable.getType());
            map.put("是否已被删除,Y/N", variable.getDeleteFlag());
            map.put("创建人", variable.getCreateBy());
            map.put("创建时间", variable.getCreateTime());
            map.put("更新人", variable.getUpdateBy());
            map.put("更新时间", variable.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<VariableDto> loadFromProcedureConfig() {

        List<VariableDto> dtoList =new ArrayList<>();
        List<String> strFromParameter = variableRepository.findAllVarStrFromCmdParameters("%${%");
        for (String str : strFromParameter) {
            List<ConfigItemVo> voList = new ArrayList<>();
            if (VariablePlaceholderResolver.find(str, voList) ){
                for (ConfigItemVo vo : voList) {
                    if (vo.getKey().indexOf("(")>=0){
                        continue; // 变量名不允许出现括弧
                    }
                    Optional<VariableDto> dtoOpt = dtoList.stream().filter(dto-> StringUtils.equalsAny(dto.getName(), vo.getKey())).findFirst();
                    if (!dtoOpt.isPresent()) {
                        VariableDto dto = new VariableDto();
                        dto.setName(vo.getKey());
                        dto.setScope("USER");
                        dto.setSource("STATION_CONFIG");
                        dto.setDefaultValue(vo.getDefaultValue());
                        dtoList.add(dto);

                        List<Long> procIds = variableRepository.findBindProcedureIdsByVarNamePattern("%${"+vo.getKey()+"%");
                        if (CollectionUtils.isNotEmpty(procIds)){
                            List<ProcedureVariableDto> procedureVariableDtos = new ArrayList<>();
                            for (Long procId : procIds) {
                                ProcedureVariableDto procedureVariableDto = new ProcedureVariableDto();
                                procedureVariableDto.setProcedureId(procId);
                                procedureVariableDtos.add(procedureVariableDto);
                            }
                            dto.setProcedureVariableDtos(procedureVariableDtos);
                        }

                    }
                }
            }
        }

        List<String> strFromFormula = variableRepository.findAllVarStrFromFormulaContent();
        for (String str : strFromFormula) {
            List<ConfigItemVo> voList = new ArrayList<>();
            if (findVariableConfigsFromFormulaContent(str, voList) ){
                for (ConfigItemVo vo : voList) {
                    if (vo.getKey().indexOf("(")>=0){
                        continue; // 变量名不允许出现括弧
                    }
                    Optional<VariableDto> dtoOpt = dtoList.stream().filter(dto-> StringUtils.equalsAny(dto.getName(), vo.getKey())).findFirst();
                    if (!dtoOpt.isPresent()) {
                        VariableDto dto = new VariableDto();
                        dto.setName(vo.getKey());
                        dto.setScope("USER");
                        dto.setSource("SCRIPT");
                        dto.setDefaultValue(vo.getDefaultValue());
                        dtoList.add(dto);


                        List<Long> procIds = variableRepository.findBindProcedureIdsByVarNameInFormula("%VAR(\""+vo.getKey()+"\"%");
                        if (CollectionUtils.isNotEmpty(procIds)){
                            List<ProcedureVariableDto> procedureVariableDtos = new ArrayList<>();
                            for (Long procId : procIds) {
                                ProcedureVariableDto procedureVariableDto = new ProcedureVariableDto();
                                procedureVariableDto.setProcedureId(procId);
                                procedureVariableDtos.add(procedureVariableDto);
                            }
                            dto.setProcedureVariableDtos(procedureVariableDtos);
                        }

                    }
                }
            }
        }

        // 对比数据库已有变量列表，若不存在则添加至数据库，已存在保持不变
        List<Variable> newAddList = new ArrayList<>();
        List<VariableDto> existList = queryAll(new VariableQueryCriteria());
        for (VariableDto dto : dtoList) {
            Optional<VariableDto> existDtoOpt = existList.stream().filter(existDto-> StringUtils.equalsAny(existDto.getName(), dto.getName())).findFirst();
            if (!existDtoOpt.isPresent()) {
                newAddList.add(variableMapper.toEntity(dto));
            }
        }
        for (Variable entity : newAddList) {
            variableRepository.save(entity);
        }

        updateProcedureVariables(dtoList);

        return dtoList;
    }

    /**
     * 更新流程绑定变量列表,
     * @param variableDtoList
     */
    private void updateProcedureVariables(List<VariableDto> variableDtoList) {
        // 按照流程分组
        Map<Long, List<ProcedureVariableDto>> procVarMap = new HashMap<>();
        for (VariableDto dto : variableDtoList) {
            Variable entity = variableRepository.findByName(dto.getName());
            if (entity!= null && CollectionUtils.isNotEmpty(dto.getProcedureVariableDtos())) {
                for (ProcedureVariableDto procVarDto : dto.getProcedureVariableDtos()) {
                    procVarDto.setVariableId(entity.getId());
                    procVarDto.setDefaultValue(dto.getDefaultValue());
                    procVarMap.computeIfAbsent(procVarDto.getProcedureId(), k -> new ArrayList<>()).add(procVarDto);
                }
            }
        }
        for (Long procId : procVarMap.keySet()) {
            procedureVariableService.updateDefaultValueByProcedureId(procId, procVarMap.get(procId));
        }
    }

    private boolean findVariableConfigsFromFormulaContent(String input, List<ConfigItemVo> outItemList) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        if (outItemList == null) {
            throw new IllegalArgumentException("outItemList is null");
        }
        Matcher matcher = VAR_SET_PATTERNS.matcher(input);

        while (matcher.find()) {
            String propertyName = matcher.group(1); // 提取属性名
            String defaultValue = matcher.group(2); // 提取默认值
            if (defaultValue!=null && defaultValue.startsWith(":")){
                defaultValue = defaultValue.substring(1);
            }
            ConfigItemVo outItem = new ConfigItemVo();
            outItem.setKey(propertyName);
            outItem.setDefaultValue(defaultValue);
            outItemList.add(outItem);
        }
        return outItemList.size() > 0;
    }
}