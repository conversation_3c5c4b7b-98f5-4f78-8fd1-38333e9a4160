/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.ResultItem;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ResultItemRepository;
import org.cjc.les.core.service.ResultItemService;
import org.cjc.les.core.service.dto.ResultItemDto;
import org.cjc.les.core.service.dto.ResultItemQueryCriteria;
import org.cjc.les.core.service.mapstruct.ResultItemMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-25
**/
@Service
@RequiredArgsConstructor
public class ResultItemServiceImpl implements ResultItemService {

    private final ResultItemRepository resultItemRepository;
    private final ResultItemMapper resultItemMapper;

    @Override
    public Map<String,Object> queryAll(ResultItemQueryCriteria criteria, Pageable pageable){
        Page<ResultItem> page = resultItemRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(resultItemMapper::toDto));
    }

    @Override
    public List<ResultItemDto> queryAll(ResultItemQueryCriteria criteria){
        return resultItemMapper.toDto(resultItemRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public ResultItemDto findById(Long id) {
        ResultItem resultItem = resultItemRepository.findById(id).orElseGet(ResultItem::new);
        ValidationUtil.isNull(resultItem.getId(),"ResultItem","id",id);
        return resultItemMapper.toDto(resultItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultItemDto create(ResultItem resources) {
        return resultItemMapper.toDto(resultItemRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ResultItem resources) {
        ResultItem resultItem = resultItemRepository.findById(resources.getId()).orElseGet(ResultItem::new);
        ValidationUtil.isNull( resultItem.getId(),"ResultItem","id",resources.getId());
        resultItem.copy(resources);
        resultItemRepository.save(resultItem);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            resultItemRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ResultItemDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ResultItemDto resultItem : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联产品ID", resultItem.getResultId());
            map.put("产品项名称", resultItem.getName());
            map.put("产品项描述", resultItem.getDescription());
            map.put("生成该产品项的方法ID", resultItem.getTaskMethodId());
            map.put("是否已被删除,Y/N", resultItem.getDeleteFlag());
            map.put("创建人", resultItem.getCreateBy());
            map.put("创建时间", resultItem.getCreateTime());
            map.put("更新人", resultItem.getUpdateBy());
            map.put("更新时间", resultItem.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}