/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class TaskActionDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 命令名称 */
    private String name;

    /** 命令描述 */
    private String description;

    /** 关联设备ID */
    private Long stationId;

    /** 动作运行状态:READY,RUNNING,SUCCESS,FAILED */
    private String status;

    /** 任务ID */
    private Long taskId;

    private String taskNumber;

    private String taskName;

    /** 任务方法ID */
    private Long taskMethodId;

    private String taskMethodName;

    /** 任务步骤ID */
    private Long taskStepId;

    private String taskStepName;

    /** 配置的动作ID */
    private Long actionId;

    /**
     * 进入队列时间戳
     */
    private Timestamp enterQueueTime;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    private List<TaskCommandDto> commands;
}