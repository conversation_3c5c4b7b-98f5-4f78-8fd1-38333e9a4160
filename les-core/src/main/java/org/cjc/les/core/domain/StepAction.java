/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
 * 进程
 */
@Entity
@Data
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_step_action")
@SQLDelete(sql = "update les_step_action set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class StepAction extends LesConfigBaseEntity {

    private String dagNodeId;

    /**
     * 异步模式，默认同步模式运行
     */
    private String asyncMode;

    /**
     * 环境变量
     */
    private String variables;

    /**
     * 条件谓词表达式
     */
    private String predicates;
    /**
     * 当预置条件不满足时处理方式：WAIT,SKIP
     */
    private String unmatchedThen;

    @JSONField(serialize = false)
    @ManyToOne
    @JoinColumn(name = "step_id", referencedColumnName = "id")
    private Step step;

    @ManyToOne
    @JoinColumn(name = "action_id", referencedColumnName = "id")
    private Action action;

    private Long evaluateExecutingStart;
    private Long evaluateExecutingDuration;
    private Timestamp evaluatedTime;

    private String status = CommandStatusEnum.VALID.name();

    public void bind(Step parentObj) {
        this.setStep(parentObj);
        if (this.action == null){
            return;
        }
        this.action.bind(this);
    }

    public void copy(StepAction source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("step","action"));

        this.variables = source.variables;
        this.predicates = source.predicates;

        if (this.getAction()==null){
            this.setAction(source.getAction());
        }else{
            this.action.copy(source.getAction());
        }

    }

}
