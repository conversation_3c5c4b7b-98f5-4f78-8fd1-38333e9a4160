/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 工作站，对多个设备的组合, 是在用户界面对某些设备的自定义描述，
 * 如培养皿清洗器工作台，通常由PLC控制启停,设备本身执行清洗动作, 在用户界面来看，如果执行清洗动作，就只有培养皿工作台工作一个操作即可
 */
@Entity
@Data
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_station")
@SQLDelete(sql = "update les_station set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class Station extends LesConfigBaseEntity {

    /**
     * 工作站名
     */
    private String name;

    private String description;
    /**
     * 工作站类型,
     * SYS: 系统软件内部虚拟的设备,通常用来执行系统等待，数据存储，调度协调等指令;
     * ROBOT: 机器人设备，用来搬运物料,
     * CONTROL: 控制设备如PLC等, 用来控制外设的启停,通常不具有点位属性;
     * PERIPHERAL:外设如各种检测仪器, 台架等，一般具有点位属性,是控制设备操作的对象
     * STORAGE: 存储设备，用来存储物料，如烧杯架，样品架等.
     */
    @Column(name = "type")
    @Schema(description = "工作站类型:SYS,ROBOT,CONTROL,PERIPHERAL,STORAGE")
    private String type;

    /**
     * DAG图
     */
    private String dagJson;
    /**
     * DAG图中的节点ID
     */
    private String dagNodeId;

    private String status;

    /**
     * 最大线程数量，默认为1
     */
    private Integer maxThreadSize = 1;

    @OneToMany(mappedBy = "station", targetEntity = Action.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    private List<Action> actions = new ArrayList<>();

    /**
     * 包含的设备列表
     */
    @ManyToMany
    @JoinTable(name = "les_station_deviceinst",
            joinColumns = {@JoinColumn(name = "station_id",referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "device_instance_id",referencedColumnName = "id")})
    private List<DeviceInstance> devices = new ArrayList<>();

    public void copy(Station source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("actions","devices"));
        if (this.getDevices()==null){
            this.setDevices(new ArrayList<>());
        }
        this.getDevices().clear();
        this.getDevices().addAll(source.getDevices());

        if (this.getActions()==null){
            this.setActions(new ArrayList<>());
        }

        List<Action> deleteActionList = new ArrayList<>();

        for (Action act : this.getActions()) {
            act.setStation(this);
            Optional<Action> optOther = source.getActions().stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),act.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                act.copy(optOther.get());
            }else{
                deleteActionList.add(act);
            }
        }

        source.getActions().forEach(act->{
            if (act.getId() == null){
                this.getActions().add(act);
                act.setStation(this);
                act.getCommands().forEach(actCmd->{
                    actCmd.setAction(act);
                });
            }
        });

        if (CollectionUtils.isNotEmpty(deleteActionList)){
            this.getActions().removeAll(deleteActionList);
        }

    }

}
