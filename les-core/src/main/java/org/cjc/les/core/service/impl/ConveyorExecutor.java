/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.constants.TaskPrepareStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.*;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.SampleService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.mapstruct.TaskPrepareMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskStepHook;
import org.cjc.les.core.task.schedule.EntryScheduler;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * 样品传送执行器
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class ConveyorExecutor {

    /**
     * 任务预处理队列
     */
    private LinkedBlockingQueue<List<TaskPrepare>> taskPrepareQueue = new LinkedBlockingQueue<>();

    /**
     * 样品进样器
     */
    private ExecutorService conveyorExecutorService = Executors.newSingleThreadExecutor();

    private final ProcedureRepository procedureRepository;

    private final TaskService taskService;
    private final TaskRepository taskRepository;

    private final PositionRepository positionRepository;
    private final PositionService positionService;

    private final TaskPrepareRepository taskPrepareRepository;
    private final TaskPrepareMapper taskPrepareMapper;

    private final TaskScheduler taskScheduler;

    /**
     * 批量提交任务预处理
     * 返回false, 已有正在等候处理的样品待传送，处理策略： 1.替换原有任务， 2.排队等候，3.拒绝提交，提醒已有任务处理中.
     *
     * @param taskPrepareList 任务预处理列表
     * @return
     */
    synchronized public boolean submit(List<TaskPrepare> taskPrepareList) {
        if (!taskPrepareQueue.isEmpty()) {
            log.warn("There is another TaskPrepare list in the queue");
            return false;
        }

        taskPrepareQueue.offer(taskPrepareList);
        conveyorExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    List<TaskPrepare> taskPrepareList = taskPrepareQueue.peek();
                    execute(taskPrepareList);
                } catch (Throwable e) {
                    log.error("Exception in conveyorExecutorService, error: {}", e.getMessage(), e);
                } finally {
                    taskPrepareQueue.poll();
                }
            }


        });

        return true;
    }

    public void execute(List<TaskPrepare> taskPrepareList) {
        Set<Long> taskPrepareIds = taskPrepareList.stream().map(TaskPrepare::getId).collect(Collectors.toSet());
        while (true) {
            List<TaskPrepare> toCheckList = taskPrepareRepository.findByStatusInIds(TaskPrepareStatusEnum.READY.name(), taskPrepareIds);
            if (CollectionUtils.isEmpty(toCheckList)) {
                break;
            }

            Iterator<TaskPrepare> itr = toCheckList.iterator();
            while (itr.hasNext()) {
                TaskPrepare taskPrepareInDb = itr.next();
                TaskPrepare taskPrepare = taskPrepareList.stream().filter(e->{
                    return e.getId().equals(taskPrepareInDb.getId());
                }).findFirst().get();

                Task task = taskService.findByTaskNumber(taskPrepare.getTaskNumber());
                submitEntryConveyorStep(task, taskPrepare);
                RunStatusEnum entryMethodRet = waitForEntryMethodFinished(task, taskPrepareInDb, toCheckList);
                if (entryMethodRet.equals(RunStatusEnum.CANCELLED)){
                    taskPrepareIds.remove(taskPrepare.getId());
                }
                break;
            }

            // 无READY状态的数据，等一秒
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
    }


    private void submitEntryConveyorStep(Task task, TaskPrepare taskPrepare) {

        Optional<TaskMethod> entryMethodOpt = task.getEntryMethod();
        if (!entryMethodOpt.isPresent()) {
            log.warn("EntryMethod is not present.");
            return;
        }
        TaskMethod entryMethod = entryMethodOpt.get();
        if (entryMethod.getSteps().size() < 2) {
            log.error("Could not found second step.");
            return;
        }

        TaskStep secondStep = entryMethod.getSteps().get(1);

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);
        setScheduleEntryTime(task);

        TaskExecutorContext.setTaskStepHook(secondStep, new TaskStepHook() {
            @Override
            public CommandReturn<TaskStep> executeBefore(TaskStep step) {
                taskPrepare.setStatus("WAIT_CONVEYOR_INPUT");
                return null;
            }

            @Override
            public CommandReturn<TaskStep> executeAfter(TaskStep step) {
                taskPrepare.setStatus("CONVEYOR_INPUT_DONE");
                return null;
            }
        });

        StepScheduler stepScheduler = SpringContextHolder.getBean(StepScheduler.class);
        List<TaskStep> existedSteps = stepScheduler.getExistedSameSteps(secondStep);
        if (CollectionUtils.isNotEmpty(existedSteps)) {
            log.error("existedSteps={}", existedSteps);
            cancelExistedEntryMethods(existedSteps);
        }
        EntryScheduler entryScheduler = SpringContextHolder.getBean(EntryScheduler.class);
        entryScheduler.submit(entryMethod);

    }

    private void setScheduleEntryTime(Task task) {
        if (task.getScheduleEntryTime() == null) {
            task.setScheduleEntryTime(System.currentTimeMillis());
        }
        for (TaskMethod mth : task.getMethodList()) {
            for (TaskStep step : mth.getSteps()) {
                if (step.getScheduleEntryTime() == null) {
                    step.setScheduleEntryTime(task.getScheduleEntryTime());
                }
            }
        }
    }

    private RunStatusEnum waitForEntryMethodFinished(Task task, TaskPrepare taskPrepare, List<TaskPrepare> toCheckList) {
        Optional<TaskMethod> entryMethodOpt = task.getEntryMethod();
        if (!entryMethodOpt.isPresent()) {
            log.warn("EntryMethod is not present.");
            return RunStatusEnum.CANCELLED;
        }
        TaskMethod entryMethod = entryMethodOpt.get();

        // 等待进样方法完成
        while (!StringUtils.equalsAny(entryMethod.getStatus(),
                RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        if (StringUtils.equalsAny(entryMethod.getStatus(), RunStatusEnum.CANCELLED.name(), RunStatusEnum.FAILED.name())) {
            log.warn("entryMethod(id={}) status={}, Do not do doEntryMethodFinished.", entryMethod.getId(), entryMethod.getStatus());
            return RunStatusEnum.valueOf(entryMethod.getStatus());
        }

        ConveyorExecutor conveyorExecutor = SpringContextHolder.getBean(ConveyorExecutor.class);
        conveyorExecutor.doEntryMethodFinished(task, taskPrepare, toCheckList);
        return RunStatusEnum.valueOf(entryMethod.getStatus());
    }

    @Transactional
    public void doEntryMethodFinished(Task paramTask, TaskPrepare taskPrepare, List<TaskPrepare> toCheckList) {
        Optional<Task> taskOpt = taskRepository.findById(paramTask.getId());
        Task task = taskOpt.get();
        task.setContext(paramTask.getContext());
        Optional<TaskMethod> entryMethodOpt = task.getEntryMethod();
        if (!entryMethodOpt.isPresent()) {
            log.warn("EntryMethod is not present.");
            return;
        }
        TaskMethod entryMethod = entryMethodOpt.get();
        //Position selectedPos = getSelectedSampleRackPos(entryMethod);
        Optional<Position> selectedPosOpt = positionRepository.findLastPositionByMethodId(entryMethod.getId());

        String confirmRfTag = (String) task.getContext().getVarObj("CONFIRM_RF_TAG");
        Set<Long> tpIdSet = toCheckList.stream().map(TaskPrepare::getId).collect(Collectors.toSet());
        List<TaskPrepare> matchedTaskPrepares = taskPrepareRepository.findByRfTagAndStatus(confirmRfTag, "READY", tpIdSet);

        if (CollectionUtils.isNotEmpty(matchedTaskPrepares)) {

            if (matchedTaskPrepares.stream().filter(tp -> {
                return tp.getId().equals(taskPrepare.getId());
            }).count() > 0) {

                addOthersMethods(task, taskPrepare, false);
            } else {
                // 替换为其他Task
            }

            // 变更点位状态为HOLD
            if (selectedPosOpt.isPresent()) {
                Position selectedPos = selectedPosOpt.get();
                positionService.updatePositionStatus(selectedPos.getId(), Position.StatusEnum.HOLD,
                        selectedPos.getHoldByTaskId(), selectedPos.getHoldByTaskMethodId(), selectedPos.getHoldByTaskStepId(), selectedPos.getHoldByTaskActionId());
                sendPositionStateChangeMsg(selectedPos);
            }

        } else {

            addOthersMethods(task, taskPrepare, true);
            // 变更点位状态为DONE
            if (selectedPosOpt.isPresent()) {
                Position selectedPos = selectedPosOpt.get();
                positionService.updatePositionStatus(selectedPos.getId(), Position.StatusEnum.DONE,
                        selectedPos.getHoldByTaskId(), selectedPos.getHoldByTaskMethodId(), selectedPos.getHoldByTaskStepId(), selectedPos.getHoldByTaskActionId());
                sendPositionStateChangeMsg(selectedPos);
                log.info("Position is changed to DONE, task_id={}, taskPrepareId={},selectedPos.id={}, selectedPos.holdByTaskId={}",
                        task.getId(), taskPrepare.getId(), selectedPos.getId(), selectedPos.getHoldByTaskId());
            }
        }
        taskPrepare.setStatus("PUSHED");
        taskPrepareRepository.save(taskPrepare);

        // 提交剩余执行方法的任务
        Task schTask = taskService.findByTaskNumber(task.getTaskNumber());
        taskScheduler.schedule(schTask);

    }

    private void sendPositionStateChangeMsg(Position position) {

        SocketMsg msg = new SocketMsg(JSON.toJSONString(position), MsgType.INFO);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"positionStateChange"}, true);
        } catch (IOException e) {
            log.error("sendPositionStateChangeMsg error, message={}", e.getMessage(), e);
        }

    }

    private Position getSelectedSampleRackPos(TaskMethod entryMethod) {

        List<TaskCommand> cmdList = new ArrayList<>();
        for (TaskStep step : entryMethod.getSteps()) {
            for (TaskAction action : step.getActions()) {
                for (TaskCommand cmd : action.getCommands()) {
                    cmdList.add(cmd);
                }
            }
        }

        cmdList.sort((a, b) -> {
            return (int) ( b.getId().longValue() - a.getId().longValue());
        });

        for (TaskCommand cmd : cmdList) {
            Position pos = cmd.getSelectedDevicePos();
            if (pos != null) {
                return pos;
            }
        }
/*
        TaskStep secondStep = entryMethod.getSteps().get(1);
        for (TaskAction action : secondStep.getActions()) {
            for (TaskCommand cmd : action.getCommands()) {
                Position pos = cmd.getSelectedDevicePos();
                if (pos != null) {
                    return pos;
                }
            }
        }*/

        return null;
    }

    private void addOthersMethods(Task task, TaskPrepare taskPrepare, boolean exitMethodOnly) {
        taskService.addOthersMethods(task, taskPrepare, exitMethodOnly);
    }

    private void cancelExistedEntryMethods(List<TaskStep> existedSteps) {
        StepScheduler stepScheduler = SpringContextHolder.getBean(StepScheduler.class);
        for (TaskStep step : existedSteps) {
            TaskMethod method = step.getTaskMethod();
            for (int i = method.getSteps().size() - 1; i >= 0; i--) {
                TaskStep s = method.getSteps().get(i);
                stepScheduler.cancel(s);
            }
        }
    }

    public boolean isWaitingForConveyorInput(TaskPrepareDto dto) {
        List<TaskPrepare> tpList = taskPrepareQueue.peek();
        if (tpList == null) {
            return false;
        }
        Optional<TaskPrepare> tpOpt = tpList.stream().filter(e -> {
            return e.getId().equals(dto.getId());
        }).findFirst();
        if (!tpOpt.isPresent()) {
            return false;
        }

        Optional<TaskPrepare> waitingTagTpOpt = tpList.stream().filter(e -> {
            return StringUtils.equalsAny(e.getStatus(), "WAIT_CONVEYOR_INPUT");
        }).findFirst();

        if (!waitingTagTpOpt.isPresent()) {
            return false;
        }

        dto.setStatus(tpOpt.get().getStatus());
        return StringUtils.equalsAny(tpOpt.get().getStatus(), "READY", "WAIT_CONVEYOR_INPUT");

    }

}
