/*
 *  Copyright 2024-2024 <PERSON>han Annis Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Station;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-07-14
**/
public interface StationRepository extends JpaRepository<Station, Long>, JpaSpecificationExecutor<Station> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Station findByName(String name);

    @Query(value = "select distinct proc.name as procedureName, mth.name as methodName, stp.name as stepName, act.name as actionName \n" +
            "from les_action act join les_step_action sact on sact.action_id=act.id and sact.delete_flag='N'\n" +
            "join les_step stp on stp.id=sact.step_id and stp.delete_flag = 'N'\n" +
            "left join les_method_step mstp on  mstp.step_id = stp.id and mstp.delete_flag='N'\n" +
            "left join les_method mth on mth.id = mstp.method_id and mth.delete_flag='N'\n" +
            "left join les_procedure_method pmth on pmth.method_id=mth.id and pmth.delete_flag='N'\n" +
            "left join les_procedure proc on proc.id = pmth.procedure_id and proc.delete_flag='N'\n" +
            " where act.station_id = ?1 and act.delete_flag='N'\n" +
            "order by procedureName, methodName, stepName, actionName", nativeQuery = true)
    List<Map> findUsedProcActionsByStationId(Long stationId);

    @Query(value = "select distinct proc.name as procedureName, mth.name as methodName, stp.name as stepName, act.name as actionName \n" +
            "from les_action act join les_step_action sact on sact.action_id=act.id and sact.delete_flag='N'\n" +
            "join les_step stp on stp.id=sact.step_id and stp.delete_flag = 'N'\n" +
            "left join les_method_step mstp on  mstp.step_id = stp.id and mstp.delete_flag='N'\n" +
            "left join les_method mth on mth.id = mstp.method_id and mth.delete_flag='N'\n" +
            "left join les_procedure_method pmth on pmth.method_id=mth.id and pmth.delete_flag='N'\n" +
            "left join les_procedure proc on proc.id = pmth.procedure_id and proc.delete_flag='N'\n" +
            " where act.id in :actionIds and act.delete_flag='N'\n" +
            "order by procedureName, methodName, stepName, actionName", nativeQuery = true)
    List<Map> findUsedProcActionsByActionIds(@Param("actionIds")Long[] actionIds);
}