/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-04-08
**/
@Entity
@Data
@Table(name="les_board_pos")
@SQLDelete(sql = "update les_board_pos set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class BoardPos  extends LesConfigBaseEntity {

    @Column(name = "name")
    @Schema(description = "命令名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "命令描述")
    private String description;

    /**
     * 引用设备实例
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "device_instance_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = DeviceInstance.class)
    private DeviceInstance deviceInstance;

    @Column(name = "robot_pos_code")
    @Schema(description = "机器人定义的位置编号，用于机器人点位唯一识别")
    private String robotPosCode;

    @Column(name = "xpos")
    @Schema(description = "xpos")
    private BigDecimal xpos;

    @Column(name = "ypos")
    @Schema(description = "ypos")
    private BigDecimal ypos;

    @Column(name = "zpos")
    @Schema(description = "zpos")
    private BigDecimal zpos;

    @Column(name = "status")
    @Schema(description = "点位状态, IDLE, HOLD,LEAVE")
    private String status;

    @Column(name = "init_status")
    @Schema(description = "初始点位状态,点位还原时的参考")
    private String initStatus;

    @Column(name = "hold_by_task_id")
    @Schema(description = "所占用的任务")
    private Long holdByTaskId;

    @Column(name = "hold_by_task_action_id")
    @Schema(description = "所占用的动作")
    private Long holdByTaskActionId;

    public void copy(BoardPos source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}