/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.Setting;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.SettingRepository;
import org.cjc.les.core.service.SettingService;
import org.cjc.les.core.service.dto.SettingDto;
import org.cjc.les.core.service.dto.SettingQueryCriteria;
import org.cjc.les.core.service.mapstruct.SettingMapper;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-05-19
**/
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "settingCache")
public class SettingServiceImpl implements SettingService {

    private final SettingRepository settingRepository;
    private final SettingMapper settingMapper;

    @Override
    public Map<String,Object> queryAll(SettingQueryCriteria criteria, Pageable pageable){
        Page<Setting> page = settingRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(settingMapper::toDto));
    }

    @Override
    public List<SettingDto> queryAll(SettingQueryCriteria criteria){
        return settingMapper.toDto(settingRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public SettingDto findById(Long id) {
        Setting setting = settingRepository.findById(id).orElseGet(Setting::new);
        ValidationUtil.isNull(setting.getId(),"Setting","id",id);
        return settingMapper.toDto(setting);
    }

    @Override
    @Cacheable(key = "'queryLatestOne'")
    public SettingDto queryLatestOne() {
        Optional<Setting> settingOpt = settingRepository.findLatestOne();
        if (!settingOpt.isPresent()){
            return new SettingDto();
        }
        return settingMapper.toDto(settingOpt.get());
    }

    @Override
    @CacheEvict(key = "'queryLatestOne'")
    @Transactional(rollbackFor = Exception.class)
    public SettingDto create(Setting resources) {
        if(settingRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Setting.class,"name",resources.getName());
        }
        return settingMapper.toDto(settingRepository.save(resources));
    }

    @Override
    @CacheEvict(key = "'queryLatestOne'")
    @Transactional(rollbackFor = Exception.class)
    public void update(Setting resources) {
        Setting setting = settingRepository.findById(resources.getId()).orElseGet(Setting::new);
        ValidationUtil.isNull( setting.getId(),"Setting","id",resources.getId());
        Setting setting1 = null;
        setting1 = settingRepository.findByName(resources.getName());
        if(setting1 != null && !setting1.getId().equals(setting.getId())){
            throw new EntityExistException(Setting.class,"name",resources.getName());
        }
        setting.copy(resources);
        settingRepository.save(setting);
    }

    @Override
    @CacheEvict(key = "'queryLatestOne'")
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            settingRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SettingDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SettingDto setting : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("设置名称", setting.getName());
            map.put("全局设置内容", setting.getGlobalSettings());
            map.put("描述", setting.getDescription());
            map.put("是否已被删除,Y/N", setting.getDeleteFlag());
            map.put("创建人", setting.getCreateBy());
            map.put("创建时间", setting.getCreateTime());
            map.put("更新人", setting.getUpdateBy());
            map.put("更新时间", setting.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}