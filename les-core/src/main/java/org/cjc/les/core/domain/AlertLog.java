/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.cjc.base.LesBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-10-14
**/
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_alert_log")
public class AlertLog extends LesBaseEntity {

    public enum AlertLevelEnum {
        FATAL,
        ERROR,
        WARN
    }

    public enum FixStatusEnum {
        INIT,
        FIXED
    }

    public enum FixTypeEnum {
        AUTO,
        MANUAL
    }

    @Column(name = "alert_code",nullable = false)
    @NotBlank
    @Schema(description = "告警编码")
    private String alertCode;

    @Column(name = "alert_name")
    @Schema(description = "告警，名称")
    private String alertName;

    @Column(name = "alert_level")
    @Schema(description = "告警级别：FATAL, ERROR, WARN")
    private String alertLevel;

    @Column(name = "message")
    @Schema(description = "告警信息")
    private String message;

    @Column(name = "alert_category")
    @Schema(description = "告警类别：DEV_INSTANCE, TASK,SYS")
    private String alertCategory;

    @Column(name = "alert_source_id")
    @Schema(description = "告警来源ID, deviceInstanceId, taskId")
    private Long alertSourceId;

    @Column(name = "fix_status",nullable = false)
    @NotBlank
    @Schema(description = "告警处理状态, INIT, FIXED")
    private String fixStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "fix_type")
    @Schema(description = "告警修复类型: AUTO,MANUAL")
    private FixTypeEnum fixType;

    @Column(name = "fixed_by")
    @Schema(description = "告警修复人")
    private String fixedBy;

    @Column(name = "fixed_time")
    @Schema(description = "告警修复时间")
    private Timestamp fixedTime;

    /** 告警修复备注 */
    @Column(name = "fixed_remark")
    private String fixedRemark;

    @Column(name = "delete_flag")
    @Schema(description = "是否已被删除,Y/N")
    private String deleteFlag;


    public void copy(AlertLog source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}