/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.BoardPos;
import org.cjc.les.core.service.dto.BoardPosDto;
import org.cjc.les.core.service.dto.BoardPosQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-04-08
**/
public interface BoardPosService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(BoardPosQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<BoardPosDto>
    */
    List<BoardPosDto> queryAll(BoardPosQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return BoardPosDto
     */
    BoardPosDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return BoardPosDto
    */
    BoardPosDto create(BoardPos resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(BoardPos resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<BoardPosDto> all, HttpServletResponse response) throws IOException;
}