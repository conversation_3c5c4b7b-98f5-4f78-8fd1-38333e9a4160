/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-17
**/
@Data
public class VariableDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 变量名称 */
    private String name;

    /** 变量显示提示信息 */
    private String viewName;

    /** 变量描述 */
    private String description;

    /** 变量类型: NUMBER,STRING,ARRAY */
    private String type;

    private String defaultValue;

    /** 变量访问范围: USER,SYS */
    private String scope;

    /**
     * MANUAL, STATION_CONFIG, SCRIPT, SYS
     */
    private String source;

    /**
     *  变量值可被用户设置,Y/N
     */
    private String valueSetByUser;

    /**
     * 绑定的流程列表
     */
    private List<ProcedureVariableDto> procedureVariableDtos;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}