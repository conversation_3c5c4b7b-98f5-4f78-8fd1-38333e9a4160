package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.base.BaseDTO;
import org.cjc.les.core.domain.Command;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class DeviceDto extends BaseDTO {


    /** 主键ID */
    private Long id;

    /** 设备名称 */
    private String name;

    /** 设备描述 */
    private String description;

    /** 设备类型:SYS,ROBOT,CONTROL,PERIPHERAL,STORAGE */
    private String type;

    /**
     * 制造厂商
     */
    private String manufacturer;

    /**
     * 设备型号
     */
    private String model;

    /** 配置信息，JSON串，或者普通的字符串 */
    private String configTemplate;

    /** 该设备自定义的Java类名 */
    private String javaClassName;

    /** 该设备自定义的Java配置类名 */
    private String configJavaClassName;

    /** 设备布局中的显示图片地址 */
    private String layoutImage;

    /** 设备图片宽度 */
    private Integer layoutWidth;

    /** 设备图片高度 */
    private Integer layoutHeight;

    /** 设备图片点位配置 */
    private String positionConfig;

    /** 设置指令列表 */
    private List<CommandDto> commands = new ArrayList<>();


    /** 是否已被删除,Y/N */
    private String deleteFlag;

}
