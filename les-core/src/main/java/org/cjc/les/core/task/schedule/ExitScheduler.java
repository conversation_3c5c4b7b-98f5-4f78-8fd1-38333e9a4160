/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskMethodExecutor;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.*;

@Log4j2
@RequiredArgsConstructor
@Component
public class ExitScheduler {

    private final int POOL_SIZE = 16;

    private LinkedBlockingQueue<TaskMethod> methodsQueue = new LinkedBlockingQueue<>();
    public LinkedBlockingQueue<TaskMethod> getMethodsQueue(){
        return methodsQueue;
    }

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        executorService = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new TaskThreadFactory("exit-method"));
    }

    private void submit(Runnable runnable){
        executorService.submit(runnable);
    }

    public void submit(TaskMethod exitMethod) {
        exitMethod.setStatus(RunStatusEnum.IN_SCHEDULE_QUE.name());
        exitMethod.setMessage("进入调度队列");
        TaskExecuteLogUtil.logMethod(exitMethod);
        final TaskExecutorContext context = TaskExecutorContext.getContext();
        methodsQueue.offer(exitMethod);
        submit(new Runnable() {
            @Override
            public void run() {
                TaskExecutorContext.copy(context, exitMethod);
                try {
                    new TaskMethodExecutor(ExecutorConfig.defaultConfig).execute(exitMethod);
                }finally {
                    TaskExecutorContext.clear();
                    methodsQueue.remove(exitMethod);
                }
            }
        });
    }
}
