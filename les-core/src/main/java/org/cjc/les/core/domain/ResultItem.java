/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.base.LesBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 产品检测项
 */
@Entity
@Data
@Table(name = "les_result_item")
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)

public class ResultItem extends LesBaseEntity {

    /**
     * 所属的产品
     */

    @JSONField(serialize = false)
    @JoinColumn(name = "result_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER, targetEntity = Result.class)
    @Schema(description = "任务", hidden = true)
    private Result result;

    /**
     * 该检测项对应的方法
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "task_method_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER, targetEntity = TaskMethod.class)
    @Schema(description = "任务方法", hidden = true)
    private TaskMethod taskMethod;

    /**
     * 该产品项需要检测的特性列表
     */
    @OneToMany(mappedBy = "resultItem", targetEntity = ResultFeature.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = false)
    private List<ResultFeature> resultFeatures;


    public void copy(ResultItem source, boolean isDeep) {
        if (!isDeep) {
            copy(source);
            return;
        }
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("resultFeatures"));

        if (this.getResultFeatures() == null) {
            this.setResultFeatures(new ArrayList<>());
        }

        List<ResultFeature> deleteItemList = new ArrayList<>();

        for (ResultFeature item : this.getResultFeatures()) {
            item.setResultItem(this);
            Optional<ResultFeature> optOther = source.getResultFeatures().stream().filter(other -> {
                boolean b = Objects.equals(other.getId(), item.getId()) || StringUtils.equals(other.getCode(), item.getCode());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                item.copy(optOther.get());
            } else {
                deleteItemList.add(item);
            }
        }

        source.getResultFeatures().forEach(act -> {
            if (act.getId() == null) {
                this.getResultFeatures().add(act);
                act.setResultItem(this);
            }
        });

        if (CollectionUtils.isNotEmpty(deleteItemList)) {
            this.getResultFeatures().removeAll(deleteItemList);
        }
    }
}
