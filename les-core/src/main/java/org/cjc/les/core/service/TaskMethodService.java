/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.service.dto.TaskMethodDto;
import org.cjc.les.core.service.dto.TaskMethodQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskMethodService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskMethodQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskMethodDto>
    */
    List<TaskMethodDto> queryAll(TaskMethodQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskMethodDto
     */
    TaskMethodDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return TaskMethodDto
    */
    TaskMethodDto create(TaskMethod resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskMethod resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskMethodDto> all, HttpServletResponse response) throws IOException;
}