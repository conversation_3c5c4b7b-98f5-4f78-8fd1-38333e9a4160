/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-06-11
 **/
@Data
public class TaskToCheckSamplesReqDto implements Serializable {

    @Data
    public static class SampleItem {

        private Long taskId;

        private Integer posIndex;
    }


    /**
     * 任务批次ID
     */
    private Long batchId;

    private Long taskId;

    /**
     * 最后一次运行状态
     */
    private String tag;

    /**
     * 消息详情
     */
    private int sampleCount;

    /**
     * 任务执行样品列表
     */
    private List<SampleItem> sampleList = new ArrayList<>();
}