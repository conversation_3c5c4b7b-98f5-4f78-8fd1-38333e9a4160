/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.constants.TaskPrepareStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.*;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ProcedureRepository;
import org.cjc.les.core.repository.SampleRepository;
import org.cjc.les.core.repository.TaskPrepareRepository;
import org.cjc.les.core.service.*;
import org.cjc.les.core.service.dto.BoardDto;
import org.cjc.les.core.service.dto.SampleEntryStepStatusDto;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.dto.TaskPrepareQueryCriteria;
import org.cjc.les.core.service.mapstruct.TaskPrepareMapper;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskStepExecutor;
import org.cjc.les.core.task.execute.TaskStepHook;
import org.cjc.les.core.task.schedule.EntryScheduler;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.SerialNumberGenerator;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.*;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.*;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-06-10
 **/
@Log4j2
@Service
@RequiredArgsConstructor
public class TaskPrepareServiceImpl implements TaskPrepareService {

    private final TaskPrepareRepository taskPrepareRepository;
    private final TaskPrepareMapper taskPrepareMapper;

    private final ProcedureRepository procedureRepository;

    private final TaskScheduler taskScheduler;

    private final TaskService taskService;

    private final SampleService sampleService;

    private final SampleRepository sampleRepository;

    private final PositionService positionService;

    private final ConveyorExecutor conveyorExecutor;

    /**
     * RFReader执行器
     */
    private ExecutorService rfExecutorService = Executors.newSingleThreadExecutor();

    @Override
    public Map<String, Object> queryAll(TaskPrepareQueryCriteria criteria, Pageable pageable) {
        Page<TaskPrepare> page = taskPrepareRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(taskPrepareMapper::toDto));
    }

    @Override
    public List<TaskPrepareDto> queryAll(TaskPrepareQueryCriteria criteria) {
        return taskPrepareMapper.toDto(taskPrepareRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    public List<BoardDto> queryTagsToEdit(TaskPrepareQueryCriteria criteria) {
        List<String> allTags = taskPrepareRepository.findRfTagsToEdit();
        List<String> outTags = allTags.stream().distinct().collect(Collectors.toList());
        List<BoardDto> outList = new ArrayList<>();
        for (String tag : outTags) {
            BoardDto dto = new BoardDto();
            dto.setTag(tag);
            List<Integer> posIndexes = taskPrepareRepository.findPosIndexByRfTag(tag);
            dto.setPositions(posIndexes);
            outList.add(dto);
        }
        return outList;
    }

    @Override
    @Transactional
    public TaskPrepareDto findById(Long id) {
        TaskPrepare taskPrepare = taskPrepareRepository.findById(id).orElseGet(TaskPrepare::new);
        ValidationUtil.isNull(taskPrepare.getId(), "TaskPrepare", "id", id);
        return taskPrepareMapper.toDto(taskPrepare);
    }

    @Override
    public TaskPrepareDto findLatestTaskPrepare() {
        Optional<TaskPrepare> tpOpt = taskPrepareRepository.findLatestTaskPrepare();
        if (!tpOpt.isPresent()){
            return new TaskPrepareDto();
        }
        return taskPrepareMapper.toDto(tpOpt.get());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskPrepareDto create(TaskPrepare resources) {
        //if (StringUtils.isEmpty(resources.getTaskNumber())){
        resources.setTaskNumber(SerialNumberGenerator.generateTaskSerialNumber());
        // }
        if (resources.getId() != null) {
            resources.setId(null);
        }
        Sample sample = resources.getSample();
        Optional<Sample> optSample = Optional.empty();
        if (sample.getId() != null) {
            optSample = sampleRepository.findById(sample.getId());
        } else if (StringUtils.isNotEmpty(sample.getName())) {
            optSample = sampleRepository.findSampleByName(sample.getName());
        }
        if (optSample.isPresent()) {
            sample = optSample.get();
        }
        if (CollectionUtils.isEmpty(sample.getTaskPrepares())) {
            sample.setTaskPrepares(new ArrayList<>());
        }
        resources.setSample(sample);
        sample.getTaskPrepares().add(resources);

        Sample savedSample = sampleRepository.save(sample);
        TaskPrepare lastTaskPrepare = savedSample.getTaskPrepares().get(savedSample.getTaskPrepares().size() - 1);

        return taskPrepareMapper.toDto(lastTaskPrepare);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskPrepare resources) {
        TaskPrepare taskPrepare = taskPrepareRepository.findById(resources.getId()).orElseGet(TaskPrepare::new);
        ValidationUtil.isNull(taskPrepare.getId(), "TaskPrepare", "id", resources.getId());
        taskPrepare.copy(resources);
        taskPrepareRepository.save(taskPrepare);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskPrepare save(TaskPrepare resources) {
        if (resources.getId() == null) {
            return taskPrepareRepository.save(resources);
        } else {
            Optional<TaskPrepare> tpOpt = taskPrepareRepository.findById(resources.getId());
            if (!tpOpt.isPresent()) {
                throw new TaskRunningException("Could not found TaskPrepare by Id:" + resources.getId());
            }
            TaskPrepare tp = tpOpt.get();
            tp.copy(resources);
            return taskPrepareRepository.save(tp);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(TaskPrepareDto taskPrepareDto) {
        if (taskPrepareDto.getId() == null) {
            log.error("Could not cancelled by null id");
            return;
        }
        TaskPrepare taskPrepare = taskPrepareRepository.findById(taskPrepareDto.getId()).orElseGet(TaskPrepare::new);
        ValidationUtil.isNull(taskPrepareDto.getId(), "TaskPrepare", "id", taskPrepareDto.getId());
        if (!StringUtils.equals(taskPrepare.getStatus(), "DRAFT")) {
            log.warn("The TaskPrepare(id:{}) is not DRAFT, could not be cancelled.", taskPrepareDto.getId());
            return;
        }
        taskPrepare.setStatus("CANCELLED");
        taskPrepareRepository.save(taskPrepare);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            taskPrepareRepository.updateStatus(id, TaskPrepareStatusEnum.CANCELLED.name());
        }
    }

    @Override
    public void download(List<TaskPrepareDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskPrepareDto taskPrepare : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("任务需运行的流程ID", taskPrepare.getProcedureId());
            map.put("任务需选择的样本ID", taskPrepare.getSampleId());
            map.put("任务编号", taskPrepare.getTaskNumber());
            map.put("状态, DRAFT,READY,PUSHED,CANCELLED", taskPrepare.getStatus());
            map.put("状态的消息详情", taskPrepare.getMessage());
            map.put("进样模式: TRIAL, FACTORY", taskPrepare.getMode());
            map.put("是否已被删除,Y/N", taskPrepare.getDeleteFlag());
            map.put("创建人", taskPrepare.getCreateBy());
            map.put("创建时间", taskPrepare.getCreateTime());
            map.put("更新人", taskPrepare.getUpdateBy());
            map.put("更新时间", taskPrepare.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional
    public void readRfTag(TaskPrepareDto taskPrepareDto) {

        TaskPrepare taskPrepare = taskPrepareRepository.findById(taskPrepareDto.getId()).orElseGet(TaskPrepare::new);
        Optional<Procedure> procedureOpt = procedureRepository.findById(taskPrepare.getProcedure().getId());
        if (!procedureOpt.isPresent()) {
            log.error("Could not found procedure by id:{}", taskPrepare.getProcedure().getId());
            throw new IllegalArgumentException("Could not found procedure by id:" + taskPrepare.getProcedure().getId());
        }
        Procedure procedure = procedureOpt.get();
        //   ProcedureMethod entryMethod = procedure.getEntryMethod().get();

        //   TaskMethod taskMethod = new TaskMethod();
        //   taskMethod.copy(entryMethod, true);

        //   Long id = taskMethod.getId();

        Task task = new Task();
        task.copy(procedure, true);
        task.setPrepareTaskId(taskPrepare.getId());
        task.setStatus(CommandStatusEnum.READY.name());
        task.setTaskNumber(taskPrepare.getTaskNumber());
        task.setTaskName(taskPrepare.getSample().getName() + ":" + procedure.getName());
        task.setTag(taskPrepare.getRfTag());
        task.setSample(taskPrepare.getSample());

        //   if (!task.getEntryMethod().isPresent() || task.getEntryMethod().get().getSteps().size()==0){
        //       log.error("Could not found first step of entry methos.");
        //       return;
        //   }
        //1. 保存任务状态
        taskService.create(task);

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);

        task.setMessage("任务创建成功，等待调度执行");
        TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.INFO);

        taskScheduler.schedule(task);

        taskPrepareDto.setTaskId(task.getId());
        taskPrepareDto.setTaskNumber(task.getTaskNumber());
        /*
        TaskStep firstStep = task.getEntryMethod().get().getSteps().get(0);
        ExecutorConfig executorConfig = new ExecutorConfig();
        executorConfig.setExecuteMode(ExecutorConfig.ExecuteModeEnum.DIRECT);
        CommandReturn ret = new TaskStepExecutor(executorConfig).execute(firstStep);

        log.info("firstStep result: {}", ret);
        if (ErrorCodeEnum.SUCCESS == ret.getErrorCode()) {
            // 试验模式，自动保存样品编码
            String rfTag = TaskExecutorContext.getVarAsString("RF_TAG");
            Sample sample = taskPrepare.getSample();
            sample.setNumber(rfTag);
            sampleService.update(sample);

            taskScheduler.schedule(task);
        }else{
            task.setMessage("等待扫描失败，任务异常退出");
            task.setStatus(RunStatusEnum.FAILED.name());
            TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.ERROR);
        }
*/
    }

    @Override
    public void readRfTag2(TaskPrepareDto taskPrepareDto) {
        if (taskPrepareDto.getId() == null) {
            throw new IllegalArgumentException("Parameter should be a existed entity with id null");
        }

        TaskPrepare taskPrepare = taskPrepareRepository.findById(taskPrepareDto.getId()).orElseGet(TaskPrepare::new);
        Optional<Procedure> procedureOpt = procedureRepository.findById(taskPrepare.getProcedure().getId());
        if (!procedureOpt.isPresent()) {
            log.error("Could not found procedure by id:{}", taskPrepare.getProcedure().getId());
            throw new IllegalArgumentException("Could not found procedure by id:" + taskPrepare.getProcedure().getId());
        }
        // 创建只包含进样方法的Task
        Procedure procedure = procedureOpt.get();
        List<ProcedureMethod> methods = procedure.getMethods();
        List<ProcedureMethod> entryMethods = methods.stream().filter(mth -> {
            return StringUtils.equals(mth.getMethod().getType(), "ENTRY");
        })
                .collect(Collectors.toList());
        procedure.setMethods(entryMethods);

        Task task = new Task();
        task.copy(procedure, true);
        task.setPrepareTaskId(taskPrepare.getId());
        task.setStatus(CommandStatusEnum.DRAFT.name());
        task.setTaskNumber(taskPrepare.getTaskNumber());
        task.setTaskName(taskPrepare.getSample().getName() + ":" + procedure.getName());
        task.setTag(taskPrepare.getRfTag());
        task.setSample(taskPrepare.getSample());

        taskService.create(task);

        // 同步方式执行第一步

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);

        TaskStep firstStep = task.getEntryMethod().get().getSteps().get(0);
        TaskExecutorContext.setTaskStepHook(firstStep, new TaskStepHook() {
            @Override
            public CommandReturn<TaskStep> executeBefore(TaskStep step) {
                return null;
            }

            @Override
            public CommandReturn<TaskStep> executeAfter(TaskStep step) {
                responseRfTag(task, firstStep);
                CommandReturn ret = new CommandReturn();
                ret.setResult(step);
                ret.setErrorCode(ErrorCodeEnum.ERROR);
                ret.setFailedThen(FailedThenEnum.THROW);
                return ret;
            }
        });
        ExecutorConfig executorConfig = new ExecutorConfig();

        StepScheduler stepScheduler = SpringContextHolder.getBean(StepScheduler.class);
        List<TaskStep> existedSteps = stepScheduler.getExistedSameSteps(firstStep);
        if (CollectionUtils.isNotEmpty(existedSteps)) {
            log.error("existedSteps={}", existedSteps);
            cancelExistedEntryMethods(existedSteps);
        }
        EntryScheduler entryScheduler = SpringContextHolder.getBean(EntryScheduler.class);
        entryScheduler.submit(task.getEntryMethod().get());
        // stepScheduler.executeImmediately(firstStep, executorConfig,true);

    }

    private void cancelExistedEntryMethods(List<TaskStep> existedSteps) {
        StepScheduler stepScheduler = SpringContextHolder.getBean(StepScheduler.class);
        for (TaskStep step : existedSteps) {
            TaskMethod method = step.getTaskMethod();
            for (int i = method.getSteps().size() - 1; i >= 0; i--) {
                TaskStep s = method.getSteps().get(i);
                stepScheduler.cancel(s);
            }
        }
    }

    private void responseRfTag(Task task, TaskStep firstStep) {
        Long prepareId = task.getPrepareTaskId();
        Optional<TaskPrepare> prepareOpt = taskPrepareRepository.findById(prepareId);
        if (!prepareOpt.isPresent()) {
            log.error("Could not found TaskPrepare by Id: {}", prepareId);
            return;
        }
        TaskPrepare taskPrepare = prepareOpt.get();
        Object rfId = task.getContext().getVarObj("RF_ID");
        taskPrepare.setRfTag(rfId.toString());
        taskPrepareRepository.save(taskPrepare);
        TaskPrepareDto dto = taskPrepareMapper.toDto(taskPrepare);

        SocketMsg msg = new SocketMsg(JSON.toJSONString(dto), MsgType.INFO);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"responseRfTag"}, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 从进样传送带接收样品
     *
     * @param taskPrepareDtos
     */
    public void acceptSampleToEntryConveyor(List<TaskPrepareDto> taskPrepareDtos) {
        List<TaskPrepare> taskPrepares = taskPrepareMapper.toEntity(taskPrepareDtos);
        conveyorExecutor.submit(taskPrepares);
    }

    @Override
    public SampleEntryStepStatusDto querySampleEntryStepStatus(SampleEntryStepStatusDto dto) {
        Long taskId = dto.getTaskId();

        Optional<Task> taskOpt = taskScheduler.getTaskById(taskId);
        if (!taskOpt.isPresent()) {
            dto.setStatus("FAILED");
            dto.setMessage("队列中不存在该任务");
            return dto;
        }

        TaskStep firstStep = null;
        boolean bLiquidEntry = false;
        Task task = taskOpt.get();
        if (!task.getEntryMethod().isPresent() || task.getEntryMethod().get().getSteps().size() == 0) {
            if (task.getMainMethodList().size() == 0 || task.getMainMethodList().get(0).getSteps().size() == 0) {
                log.error("Could not found first step of entry methos.");
                dto.setStatus("FAILED");
                dto.setMessage("队列中不存在该任务的步骤");
                return dto;
            }
            firstStep = task.getMainMethodList().get(0).getSteps().get(0);
            bLiquidEntry = true;
        } else {
            firstStep = task.getEntryMethod().get().getSteps().get(0);
        }
        if (StringUtils.equalsAny(firstStep.getStatus(), "RUNNING")) {
            if (isAnyFailedInStep(firstStep)) {
                dto.setStatus("FAILED");
                dto.setMessage("任务运行异常，请检查告警详情并修复后重试");
            } else {
                dto.setStatus("RUNNING");
                if (bLiquidEntry) {
                    dto.setMessage("请注入5ml样品溶液后推入样品烧杯");
                } else {
                    dto.setMessage("请扫描RF标签");
                }
            }
            return dto;
        }
        if (StringUtils.equalsAny(firstStep.getStatus(), "SUCCESS")) {
            dto.setStatus("SUCCESS");
            if (bLiquidEntry) {
                dto.setMessage("推入样品烧杯成功");
            } else {
                dto.setMessage("扫描成功RF标签: " + task.getContext().getVarObj("RF_TAG"));
            }
            return dto;
        }

        return dto;
    }

    private boolean isAnyFailedInStep(TaskStep step) {
        for (TaskAction action : step.getActions()) {
            if (StringUtils.equalsAny(action.getStatus(), "FAILED")) {
                return true;
            }
            for (TaskCommand command : action.getCommands()) {
                if (StringUtils.equalsAny(command.getStatus(), "FAILED")) {
                    return true;
                }
            }
        }
        return false;
    }

    @Transactional
    @Override
    public boolean checkIfEntryMethodPresent(TaskPrepare paramTp) {
        TaskPrepare taskPrepare = taskPrepareRepository.findById(paramTp.getId()).orElseGet(TaskPrepare::new);
        Procedure procedure = procedureRepository.findById(taskPrepare.getProcedure().getId()).orElseGet(Procedure::new);
        List<ProcedureMethod> methods = procedure.getMethods();
        List<ProcedureMethod> entryMethods = methods.stream().filter(mth -> {
            return StringUtils.equals(mth.getMethod().getType(), "ENTRY");
        })
                .collect(Collectors.toList());
        return !entryMethods.isEmpty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskPrepareDto> batchSaveTaskPrepares(List<TaskPrepareDto> resources) {
        List<TaskPrepareDto> toUpdateList = resources.stream().filter(dto->{
            return dto.getId()!=null && dto.getId().longValue()>0;
        }).collect(Collectors.toList());

        List<TaskPrepareDto> toAddList = resources.stream().filter(dto->{
            return dto.getId() == null;
        }).collect(Collectors.toList());

        List<TaskPrepare> toSaveList = new ArrayList<>();
        List<Long> toUpdateIds = toUpdateList.stream().map(TaskPrepareDto::getId).collect(Collectors.toList());
        List<TaskPrepare> toUpdateListInDb = taskPrepareRepository.findAllById(toUpdateIds);
        for (TaskPrepare db : toUpdateListInDb) {
            Optional<TaskPrepareDto> dtoI18n = toUpdateList.stream().filter(dto->{
                return db.getId().equals(dto.getId());
            }).findFirst();
            if (dtoI18n.isPresent()) {
                db.copy(taskPrepareMapper.toEntity(dtoI18n.get()));
            }
        }
        toSaveList.addAll(toUpdateListInDb);

        for (TaskPrepareDto newDto : toAddList) {
            newDto.setId(null);
            // 重新生成新的任务编号
            newDto.setTaskNumber(SerialNumberGenerator.generateTaskSerialNumber());
            toSaveList.add(taskPrepareMapper.toEntity(newDto));
        }

        for (TaskPrepare tp : toSaveList) {
            Sample sample = tp.getSample();
            if (sample!= null && sample.getId() == null) {
                Sample savedSample = sampleRepository.save(sample);
                tp.setSample(savedSample);
            }
        }

        List<TaskPrepare> savedList = taskPrepareRepository.saveAll(toSaveList);
        return taskPrepareMapper.toDto(savedList);
    }

}