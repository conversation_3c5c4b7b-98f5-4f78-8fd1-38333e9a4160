/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-27
**/
@Data
public class ProcedureVariableDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 关联流程ID */
    private Long procedureId;

    /** 关联变量ID */
    private Long variableId;

    /** 变量名称 */
    private String name;

    /** 变量显示提示信息 */
    private String viewName;

    /** 变量描述 */
    private String description;

    /** 变量类型: NUMBER,STRING,ARRAY */
    private String type;

    /** 默认值 */
    private String defaultValue;

    /** 当前值 */
    private String value;

    /** 列表顺序下标 */
    private Integer orderIndex;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}