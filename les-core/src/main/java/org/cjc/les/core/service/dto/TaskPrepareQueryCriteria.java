/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.cjc.annotation.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-10
**/
@Data
public class TaskPrepareQueryCriteria{
    /**
     * 预处理任务状态
     */
    @Query( type = Query.Type.IN)
    private Set<String> status = new HashSet<>();

    @Query( type = Query.Type.EQUAL)
    private String rfTag;
}