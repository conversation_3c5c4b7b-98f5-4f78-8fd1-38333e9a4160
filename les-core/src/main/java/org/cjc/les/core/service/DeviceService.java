package org.cjc.les.core.service;

import org.cjc.exception.ApplicationException;
import org.cjc.les.core.domain.Command;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.service.dto.DeviceDto;
import org.cjc.les.core.service.dto.DeviceQueryCriteria;
import org.springframework.data.domain.Pageable;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface DeviceService {

    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryAll(DeviceQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     * @param criteria 条件参数
     * @return List<DeviceDto>
     */
    List<DeviceDto> queryAll(DeviceQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return DeviceDto
     */
    DeviceDto findById(Long id);

    /**
     * 创建
     * @param resources /
     * @return ShopInfoDto
     */
    Device create(Device resources);

    /**
     * 编辑
     * @param resources /
     */
    void update(Device resources);

    /**
     * 保存
     * @param resources 设备对象
     */
    void save(Device resources);

    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(Long[] ids) throws ApplicationException;

    /**
     * 导出数据
     * @param all 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<DeviceDto> all, HttpServletResponse response) throws IOException;

    /**
     * 保存命令列表详情
     * @param commands 命令列表详情
     */
    void saveCommands(List<Command> commands);

    /**
     * 创建设备实例
     * @param deviceInstance 设备实例
     * @return
     */
    DeviceInstance createDeviceInstance(DeviceInstance deviceInstance);

    /**
     * 查找所有有效的设备定义
     * @return
     */
    List<DeviceDto> findAllValidDevices();

    /**
     * 通过Java类名查找设备定义
     * @param javaClassName
     * @return
     */
    Optional<Device> findDeviceByJavaClassName(String javaClassName);
}
