/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-22
**/
@Entity
@Data
@Table(name="les_sample_customer")
public class SampleCustomer extends LesConfigBaseEntity {

    @Column(name = "name",unique = true)
    @Schema(description = "客户名称")
    private String name;

    @Column(name = "contacts")
    @Schema(description = "客户联系人")
    private String contacts;

    @Column(name = "telephone")
    @Schema(description = "客户联系电话")
    private String telephone;

    @Column(name = "customer_address")
    @Schema(description = "客户联系地址")
    private String customerAddress;

    @Column(name = "description")
    @Schema(description = "客户描述")
    private String description;

    public void copy(SampleCustomer source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}