/*
 *  Copyright 2024-2025 <PERSON>han Annis Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.dto.PositionIoMapDto;
import org.cjc.les.core.service.dto.PositionSelectDto;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.VariablePlaceholderResolver;
import org.cjc.les.core.vo.ConfigItemVo;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 正式参数转换计算实现类
 */
public class ActualParameterConverter {

    // 原始参数
    private String parameter;

    // 待执行的实例设备指令
    private DeviceInstanceCmd instanceCmd;

    // 待执行的任务指令对象
    private TaskCommand taskCommand;

    public ActualParameterConverter(DeviceInstanceCmd insCmd, String param) {
        this.instanceCmd = insCmd;
        this.parameter = param;

        if (TaskExecutorContext.getTaskCommand() != null){
            taskCommand = TaskExecutorContext.getTaskCommand();
        }else{
            taskCommand = new TaskCommand();
            taskCommand.setCommand(instanceCmd);
            taskCommand.setParameter(parameter);
        }
    }

    /**
     * 转换执行入口
     * @return
     */
    public Object convert() throws ClassNotFoundException {
        return convertActualClass(this.taskCommand);
    }


    private Object convertActualClass(TaskCommand taskCommand) throws ClassNotFoundException {
        DeviceInstanceCmd instanceCmd = taskCommand.getCommand();
        Command command = instanceCmd.getRealCommand();
        String parameter = taskCommand.getParameter();
        if (StringUtils.isEmpty(parameter)) {
            parameter = instanceCmd.getParameter();
        }
        String objStr = parameter;
        String clzName = command.getParameterType();
        String templateStr = command.getParameterTemplate();

        if (StringUtils.equalsIgnoreCase("java.lang.String", clzName)) {
            objStr = convertAsPositionValueMap(parameter, templateStr, clzName, taskCommand);
            return VariablePlaceholderResolver.resolve(objStr);
        } else if (StringUtils.equalsIgnoreCase("java.lang.Long", clzName)) {
            objStr = convertAsPositionValueMap(parameter, templateStr, clzName, taskCommand);
            return Long.parseLong(VariablePlaceholderResolver.resolve(objStr));
        } else if (StringUtils.equalsIgnoreCase("java.lang.Integer", clzName)) {
            objStr = convertAsPositionValueMap(parameter, templateStr, clzName, taskCommand);
            return Integer.parseInt(VariablePlaceholderResolver.resolve(objStr));
        } else if (StringUtils.equalsIgnoreCase("java.lang.Double", clzName)) {
            objStr = convertAsPositionValueMap(parameter, templateStr, clzName, taskCommand);
            return Double.parseDouble(VariablePlaceholderResolver.resolve(objStr));
        } else if (StringUtils.equalsIgnoreCase("java.lang.Boolean", clzName)) {
            objStr = convertAsPositionValueMap(parameter, templateStr, clzName, taskCommand);
            return Boolean.parseBoolean(VariablePlaceholderResolver.resolve(objStr));
        }
        // 扩展其他基础类型的转换
        else {
            ConfigItemVo rootItem = JSON.parseObject(templateStr, ConfigItemVo.class);
            if (StringUtils.equals(rootItem.getInputComponent(), "position-select")) {
                return convertAsPositionSelect(parameter, clzName, taskCommand);
            } else if (StringUtils.equals(rootItem.getInputComponent(), "position-input")) {
                return convertAsPositionInput(parameter, clzName, taskCommand);
            } else if (StringUtils.equals(rootItem.getInputComponent(), "position-io-map")) {
                return convertAsPositionIoMap(parameter, clzName, taskCommand);
            }
            // 当作JSON串处理
            Class<?> clz = this.getClass().getClassLoader().loadClass(clzName);
            JSONObject jsonObj = JSON.parseObject(objStr);
            VariablePlaceholderResolver.deepTraverseAndConvert(jsonObj);
            return jsonObj.toJavaObject(clz);
        }
    }

    private String convertAsPositionValueMap(String parameter, String templateStr, String clzName, TaskCommand taskCommand) throws ClassNotFoundException {
        if (StringUtils.isEmpty(parameter)) {
            return parameter;
        }
        if (StringUtils.isEmpty(templateStr)) {
            return parameter;
        }

        ConfigItemVo rootItem = JSON.parseObject(templateStr, ConfigItemVo.class);
        if (!StringUtils.equals(rootItem.getInputComponent(), "position-value-map")) {
            return parameter;
        }

        // posId, value
        Map<Long, String> valuesMap = new HashMap<>();
        String[] valueArr = parameter.split(",");
        for (String valuePair : valueArr) {
            String[] valuePairArr = valuePair.split(":");
            if (valuePairArr.length == 1) {
                valuesMap.put(0L, valuePairArr[0]);
            } else if (valuePairArr.length == 2) {
                valuesMap.put(Long.parseLong(valuePairArr[1]), valuePairArr[0]);
            }
        }

        if (valuesMap.keySet().size() == 0) {
            throw new TaskRunningException("parse parameter error, param: " + parameter);
        }
        String outStr = null;
        if (valuesMap.keySet().size() == 1) {
            outStr = valuesMap.values().stream().findFirst().get();
        } else {
            Set<Long> posIdSet = valuesMap.keySet();
            Long taskId = TaskExecutorContext.getTask().getId();

            PositionService positionService = SpringContextHolder.getBean(PositionService.class);
            Optional<Position> posOpt = positionService.findMatchedPositionByIDsAnAndHoldByTaskId(posIdSet, taskId);
            if (!posOpt.isPresent()) {
                throw new TaskRunningException("Could not found position for taskId:" + taskId + ", posIdSet: " + posIdSet);
            }

            Position matchedPos = posOpt.get();
            outStr = valuesMap.get(matchedPos.getId());
        }

        return outStr;
    }

    private Object convertAsPositionIoMap(String parameter, String clzName, TaskCommand taskCommand) throws ClassNotFoundException {
        Class<?> clz = this.getClass().getClassLoader().loadClass(clzName);
        try {
            Object outObj = clz.newInstance();
            PositionIoMapDto positionIoMapDto = JSON.parseObject(parameter, PositionIoMapDto.class);

            String ioNumbers = positionIoMapDto.getBoolNumber();

            Map<Long, String> iosMap = new HashMap<>();
            String[] ioPairsArr = ioNumbers.split(",");
            for (String ioPair : ioPairsArr) {
                String[] ioPairArr = ioPair.split(":");
                if (ioPairArr.length == 1) {
                    iosMap.put(0L, ioPairArr[0]);
                } else if (ioPairArr.length == 2) {
                    iosMap.put(Long.parseLong(ioPairArr[1]), ioPairArr[0]);
                }
            }

            if (iosMap.keySet().size() == 0) {
                throw new TaskRunningException("parse parameter error, param: " + parameter);
            }
            String ioNumberStr = null;
            Long posId = iosMap.keySet().stream().findFirst().get();
            if (iosMap.keySet().size() == 1 && posId.longValue() == 0L) {
                ioNumberStr = iosMap.values().stream().findFirst().get();
            } else {
                Set<Long> posIdSet = iosMap.keySet();
                Long taskId = TaskExecutorContext.getTask().getId();


                PositionService positionService = SpringContextHolder.getBean(PositionService.class);
                Optional<Position> posOpt = positionService.findMatchedPositionByIDsAnAndHoldByTaskId(posIdSet, taskId);
                if (!posOpt.isPresent()) {
                    throw new TaskRunningException("Could not found position for taskId:" + taskId + ", posIdSet: " + posIdSet);
                }

                Position matchedPos = posOpt.get();

                taskCommand.setSelectedDevicePos(matchedPos);

                ioNumberStr = iosMap.get(matchedPos.getId());
            }

            positionIoMapDto.setBoolNumber(ioNumberStr);

            BeanUtils.copyProperties(outObj, positionIoMapDto);

            return outObj;
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    private Object convertAsPositionSelect(String parameter, String clzName, TaskCommand taskCommand) throws ClassNotFoundException {
        Class<?> clz = this.getClass().getClassLoader().loadClass(clzName);
        try {
            Object outObj = clz.newInstance();
            PositionSelectDto positionSelectDto = JSON.parseObject(parameter, PositionSelectDto.class);

            PositionRequestVo posVo = new PositionRequestVo();
            posVo.setDeviceInstanceId(positionSelectDto.getDeviceInstanceId());
            posVo.setName(positionSelectDto.getName());

            String status = positionSelectDto.getStatus();
            if (StringUtils.isNotEmpty(status)) {
                String[] statusArr = status.split(":");
                if (statusArr.length >= 2) {
                    status = statusArr[0];
                }
                posVo.setStatus(status);
                posVo.setTaskId(taskCommand.getTaskId());
                posVo.setTaskMethodId(taskCommand.getTaskMethodId());
            }

            PositionService positionService = SpringContextHolder.getBean(PositionService.class);
            Optional<Position> posOpt = positionService.findMatchedPosition(posVo);

            if (!posOpt.isPresent()) {
                throw new TaskRunningException("Could not found matched status positions, param: " + posVo);
            }

            Position matchedPos = posOpt.get();
            DeviceInstance curDevInst = taskCommand.getDeviceInstance() != null ? taskCommand.getDeviceInstance() : taskCommand.getCommand().getDeviceInstance();
            Object insConfig = getDeviceInstanceConfig(curDevInst);
            if (insConfig != null) {
                BeanUtils.copyProperties(outObj, insConfig);
            }
            BeanUtils.copyProperties(outObj, matchedPos);

            taskCommand.setSelectedDevicePos(matchedPos);

            return outObj;
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    private Object convertAsPositionInput(String parameter, String clzName, TaskCommand taskCommand) throws ClassNotFoundException {
        Class<?> clz = this.getClass().getClassLoader().loadClass(clzName);
        try {
            Object outObj = JSON.parseObject(parameter, clz);

            DeviceInstance curDevInst = taskCommand.getDeviceInstance() != null ? taskCommand.getDeviceInstance() : taskCommand.getCommand().getDeviceInstance();
            Object insConfig = getDeviceInstanceConfig(curDevInst);
            if (insConfig != null) {
                BeanUtils.copyProperties(outObj, insConfig);
            }
            return outObj;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }


    private Object getDeviceInstanceConfig(DeviceInstance devInst) throws ClassNotFoundException {
        String config = devInst.isEnableMock() ? devInst.getMockConfig() : devInst.getConfig();
        String clzName = devInst.getDevice().getConfigJavaClassName();
        if (StringUtils.isAnyEmpty(config, clzName)) {
            return null;
        }
        Class<?> clz = this.getClass().getClassLoader().loadClass(clzName);
        return JSON.parseObject(config, clz);
    }

}
