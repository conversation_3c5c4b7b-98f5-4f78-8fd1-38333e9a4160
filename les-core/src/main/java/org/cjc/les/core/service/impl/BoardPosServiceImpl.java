/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.BoardPos;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.BoardPosRepository;
import org.cjc.les.core.service.BoardPosService;
import org.cjc.les.core.service.dto.BoardPosDto;
import org.cjc.les.core.service.dto.BoardPosQueryCriteria;
import org.cjc.les.core.service.mapstruct.BoardPosMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-04-08
**/
@Service
@RequiredArgsConstructor
public class BoardPosServiceImpl implements BoardPosService {

    private final BoardPosRepository boardPosRepository;
    private final BoardPosMapper boardPosMapper;

    @Override
    public Map<String,Object> queryAll(BoardPosQueryCriteria criteria, Pageable pageable){
        Page<BoardPos> page = boardPosRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(boardPosMapper::toDto));
    }

    @Override
    public List<BoardPosDto> queryAll(BoardPosQueryCriteria criteria){
        return boardPosMapper.toDto(boardPosRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public BoardPosDto findById(Long id) {
        BoardPos boardPos = boardPosRepository.findById(id).orElseGet(BoardPos::new);
        ValidationUtil.isNull(boardPos.getId(),"BoardPos","id",id);
        return boardPosMapper.toDto(boardPos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BoardPosDto create(BoardPos resources) {
        return boardPosMapper.toDto(boardPosRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BoardPos resources) {
        BoardPos boardPos = boardPosRepository.findById(resources.getId()).orElseGet(BoardPos::new);
        ValidationUtil.isNull( boardPos.getId(),"BoardPos","id",resources.getId());
        boardPos.copy(resources);
        boardPosRepository.save(boardPos);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            boardPosRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<BoardPosDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (BoardPosDto boardPos : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("命令名称", boardPos.getName());
            map.put("命令描述", boardPos.getDescription());
            map.put("关联设备实例ID", boardPos.getDeviceInstanceId());
            map.put("在该设备实例中的索引位置", boardPos.getDevicePosIndex());
            map.put("机器人定义的位置编号，用于机器人点位唯一识别", boardPos.getRobotPosCode());
            map.put(" xpos",  boardPos.getXpos());
            map.put(" ypos",  boardPos.getYpos());
            map.put(" zpos",  boardPos.getZpos());
            map.put("点位状态, IDLE, HOLD,LEAVE", boardPos.getStatus());
            map.put("初始点位状态,点位还原时的参考", boardPos.getInitStatus());
            map.put("所占用的任务", boardPos.getHoldByTaskId());
            map.put("所占用的动作", boardPos.getHoldByTaskActionId());
            map.put("是否已被删除,Y/N", boardPos.getDeleteFlag());
            map.put("创建人", boardPos.getCreateBy());
            map.put("创建时间", boardPos.getCreateTime());
            map.put("更新人", boardPos.getUpdateBy());
            map.put("更新时间", boardPos.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}