/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.constants.TaskLogTypeEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.DeviceProxyHelper;
import org.cjc.les.core.service.TaskExecuteLogService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.core.service.dto.TaskExecuteLogDto;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.utils.SpringContextHolder;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;

import java.io.IOException;
import java.sql.Timestamp;

@Log4j2
public class TaskExecuteLogUtil {

    private static class TaskExecuteLogServiceHolder {
        private static TaskExecuteLogService instance = SpringContextHolder.getBean(TaskExecuteLogService.class);
        private static TaskService taskService = SpringContextHolder.getBean(TaskService.class);
    }

    private static TaskExecuteLogService getLogService() {
        TaskExecuteLogService inst = TaskExecuteLogServiceHolder.instance;
        if (inst == null) {
            throw new IllegalArgumentException("Could not initialize TaskExecuteLogService.");
        }
        return inst;
    }

    private static TaskService getTaskService() {
        TaskService inst = TaskExecuteLogServiceHolder.taskService;
        if (inst == null) {
            throw new IllegalArgumentException("Could not initialize TaskService.");
        }
        return inst;
    }

    private static TaskLogLevelEnum getLogLevelByStatus(String status) {
        if (StringUtils.equalsAny(status, "ERROR","FATAL")){
            return TaskLogLevelEnum.ERROR;
        }else{
            return TaskLogLevelEnum.INFO;
        }
    }

    public static void setScheduleEntryTime(Task task) {
        getTaskService().updateScheduleEntryTime(task);
    }

    public static void createLog(TaskExecuteLog taskExecuteLog) {
        TaskExecuteLogDto logDto = getLogService().create(taskExecuteLog);
        sendMsg(logDto);
    }

    public static void log(TaskBaseEntity taskBaseEntity) {
        if (taskBaseEntity instanceof Task) {
            logTask((Task) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskMethod) {
            logMethod((TaskMethod) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskStep) {
            logStep((TaskStep) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskAction) {
            logAction((TaskAction) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskCommand) {
            logCommand((TaskCommand) taskBaseEntity);
        }
    }

    public static void logTask(Task task) {
        logTask(task, getLogLevelByStatus(task.getStatus()));
    }
    public static void logTask(Task task, TaskLogLevelEnum logLevel) {
        TaskExecuteLog log = new TaskExecuteLog();
        log.setTaskId(task.getId());
        log.setLogType(TaskLogTypeEnum.TASK.name());
        log.setLogLevel(logLevel.name());
        log.setName(task.getTaskName());
        log.setStatus(task.getStatus());
        log.setMessage(task.getMessage());
        createLog(log);
    }

    public static void logMethod(TaskMethod taskMethod) {
        logMethod(taskMethod, getLogLevelByStatus(taskMethod.getStatus()));
    }
    public static void logMethod(TaskMethod taskMethod, TaskLogLevelEnum logLevel) {
        TaskExecuteLog log = new TaskExecuteLog();

        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            log.setTaskId(task.getId());
        }
        log.setTaskMethodId(taskMethod.getId());
        log.setLogType(TaskLogTypeEnum.METHOD.name());
        log.setLogLevel(logLevel.name());
        log.setName(taskMethod.getName());
        log.setStatus(taskMethod.getStatus());
        log.setMessage(taskMethod.getMessage());
        createLog(log);
    }

    public static void logStep(TaskStep taskStep) {
        logStep(taskStep,getLogLevelByStatus(taskStep.getStatus()) );
    }
    public static void logStep(TaskStep taskStep, TaskLogLevelEnum logLevel) {
        TaskExecuteLog log = new TaskExecuteLog();

        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            log.setTaskId(task.getId());
        }
        TaskMethod taskMethod = TaskExecutorContext.getTaskMethod();
        if (taskMethod != null) {
            log.setTaskMethodId(taskMethod.getId());
        }
        log.setTaskStepId(taskStep.getId());
        log.setLogType(TaskLogTypeEnum.STEP.name());
        log.setLogLevel(logLevel.name());
        log.setName(taskStep.getName());
        log.setStatus(taskStep.getStatus());
        log.setMessage(taskStep.getMessage());
        createLog(log);
    }

    public static void logAction(TaskAction taskAction) {
        logAction(taskAction,getLogLevelByStatus(taskAction.getStatus()));
    }
    public static void logAction(TaskAction taskAction, TaskLogLevelEnum logLevel) {
        TaskExecuteLog log = new TaskExecuteLog();

        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            log.setTaskId(task.getId());
        }
        TaskMethod taskMethod = TaskExecutorContext.getTaskMethod();
        if (taskMethod != null) {
            log.setTaskMethodId(taskMethod.getId());
        }
        TaskStep taskStep = TaskExecutorContext.getTaskStep();
        if (taskStep != null) {
            log.setTaskStepId(taskStep.getId());
        }
        log.setTaskActionId(taskAction.getId());
        log.setLogType(TaskLogTypeEnum.ACTION.name());
        log.setLogLevel(logLevel.name());
        log.setName(taskAction.getName());
        log.setStatus(taskAction.getStatus());
        log.setMessage(taskAction.getMessage());
        createLog(log);
    }

    public static void logCommand(TaskCommand taskCommand) {
        logCommand(taskCommand, getLogLevelByStatus(taskCommand.getStatus()));
    }
    public static void logCommand(TaskCommand taskCommand, TaskLogLevelEnum logLevel) {
        TaskExecuteLog log = new TaskExecuteLog();

        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            log.setTaskId(task.getId());
        }
        TaskMethod taskMethod = TaskExecutorContext.getTaskMethod();
        if (taskMethod != null) {
            log.setTaskMethodId(taskMethod.getId());
        }
        TaskStep taskStep = TaskExecutorContext.getTaskStep();
        if (taskStep != null) {
            log.setTaskStepId(taskStep.getId());
        }
        TaskAction taskAction = TaskExecutorContext.getTaskAction();
        if (taskAction != null) {
            log.setTaskActionId(taskAction.getId());
        }
        if (taskCommand.getTaskAction()!=null){
            log.setTaskActionId(taskCommand.getTaskAction().getId());
        }
        log.setTaskCommandId(taskCommand.getId());
        log.setLogType(TaskLogTypeEnum.COMMAND.name());
        log.setLogLevel(logLevel.name());
        log.setName(taskCommand.getName());
        log.setStatus(taskCommand.getStatus());
        log.setMessage(taskCommand.getMessage());
        createLog(log);
    }

    private static void sendMsg(TaskExecuteLogDto taskExecuteLog) {
        MsgType msgType = MsgType.INFO;
        if (StringUtils.equalsAny(taskExecuteLog.getLogLevel(), TaskLogLevelEnum.INFO.name(), TaskLogLevelEnum.ERROR.name())) {
            msgType = MsgType.valueOf(taskExecuteLog.getLogLevel());
        }
        SocketMsg msg = new SocketMsg(JSON.toJSONString(taskExecuteLog), msgType);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"taskMonitor", "taskMonitorLog"}, true);
        } catch (Exception e) {
            log.error("sendMsg error, message={}, taskExecuteLog={}", e.getMessage(), taskExecuteLog, e);
        }
    }


}

