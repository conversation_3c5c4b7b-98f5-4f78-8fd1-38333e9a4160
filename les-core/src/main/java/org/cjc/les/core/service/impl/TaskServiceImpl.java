/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.*;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.SampleService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.*;
import org.cjc.les.core.service.mapstruct.TaskMapper;
import org.cjc.les.core.service.mapstruct.TaskSmallMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.utils.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-11
**/
@Service
@RequiredArgsConstructor
@Log4j2
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final TaskMapper taskMapper;
    private final TaskSmallMapper taskSmallMapper;

    private final TaskExecuteLogRepository taskExecuteLogRepository;

    private final StepActionRepository stepActionRepository;
    private final MethodStepRepository methodStepRepository;

    private final ProcedureRepository procedureRepository;

    private final TaskCommandRepository taskCommandRepository;

    private final TaskPrepareRepository taskPrepareRepository;

    private final SampleService sampleService;

    @Override
    public Map<String,Object> queryAll(TaskQueryCriteria criteria, Pageable pageable){
        Page<Task> page = taskRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskMapper::toDto));
    }

    @Override
    public Map<String, Object> queryLatestTasks(TaskQueryCriteria criteria, Pageable pageable) {

        //  PageRequest pageable = PageRequest.of(0, 10, Sort.by(new Sort.Order(Sort.Direction.DESC,"id")));

        Page<Task> page = taskRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Map<String, Object> out = PageUtil.toPage(page.map(taskMapper::toDto));

        Collection<TaskDto> taskList = (Collection<TaskDto>) out.get("content");
        for (TaskDto dto : taskList) {
            if (StringUtils.equalsAny(dto.getStatus(),
                    RunStatusEnum.CANCELLED.name(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name())) {
                continue;
            }
            Optional<TaskExecuteLog> logOpt = taskExecuteLogRepository.findLatestLogByTaskId(dto.getId());
            if (logOpt.isPresent()) {
                TaskExecuteLog log = logOpt.get();
                dto.setLastStatus(log.getStatus());
                dto.setMessage(log.getName() + (StringUtils.isEmpty(log.getMessage()) ? "" : ":" + log.getMessage()));
                dto.setLastExecutedTaskCommandId(log.getTaskCommandId());
                dto.setUpdateTime(log.getUpdateTime());
            }
        }
        return out;
    }

    @Override
    public List<TaskDto> queryAll(TaskQueryCriteria criteria){
        return taskMapper.toDto(taskRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<TaskSmallDto> queryForSelection(TaskQueryCriteria criteria) {
        if (StringUtils.isEmpty(criteria.getTaskName())){
            return taskSmallMapper.toDto(taskRepository.queryForSelection(criteria.getProcedureId()));
        }
        return taskSmallMapper.toDto(taskRepository.queryForSelection(criteria.getProcedureId(), criteria.getTaskName()));
    }

    @Override
    @Transactional
    public TaskDto findById(Long id) {
        Task task = taskRepository.findById(id).orElseGet(Task::new);
        ValidationUtil.isNull(task.getId(),"Task","id",id);
        return taskMapper.toDto(task);
    }

    @Override
    @Transactional
    public Task findByTaskNumber(String taskNumber) {
        Task task = taskRepository.findByTaskNumber(taskNumber);
        // 加载所有对象
        Optional<TaskMethod> entryMethodOpt = task.getEntryMethod();
        if (entryMethodOpt.isPresent()){
            TaskMethod mth = entryMethodOpt.get();
            List<TaskStep> steps = mth.getSteps();
            for (TaskStep step : steps) {
                List<TaskAction> actions = step.getActions();
                for (TaskAction action : actions) {
                    List<TaskCommand> cmds = action.getCommands();
                    for (TaskCommand cmd : cmds){
                        // Do nothing.
                    }
                }
            }
        }
        return task;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public TaskDto create(Task resources) {
        Task ret = taskRepository.save(resources);
        updateRefFields(resources);
        return taskMapper.toDto(ret);
    }

    private void updateRefFields(Task task) {
        for (TaskMethod mth : task.getMethodList()) {
            for (TaskStep step : mth.getSteps()) {
                step.setTaskId(task.getId());
                for (TaskAction action : step.getActions()) {
                    action.setTaskId(task.getId());
                    action.setTaskMethodId(mth.getId());
                    for (TaskCommand command : action.getCommands()) {
                        command.setTaskId(task.getId());
                        command.setTaskMethodId(mth.getId());
                        command.setTaskStepId(step.getId());
                    }
                }
            }
        }
        taskRepository.save(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Task createTaskWithEntryMethod(TaskPrepare paramTaskPrepare) {
        TaskPrepare taskPrepare = taskPrepareRepository.findById(paramTaskPrepare.getId()).orElseGet(TaskPrepare::new);
        Optional<Procedure> procedureOpt = procedureRepository.findById(taskPrepare.getProcedure().getId());
        if (!procedureOpt.isPresent()) {
            log.error("Could not found procedure by id:{}", taskPrepare.getProcedure().getId());
            throw new IllegalArgumentException("Could not found procedure by id:" + taskPrepare.getProcedure().getId());
        }
        // 创建只包含进样方法的Task
        Procedure procedure = procedureOpt.get();
        List<ProcedureMethod> methods = procedure.getMethods();
        List<ProcedureMethod> entryMethods = methods.stream().filter(mth -> {
            return StringUtils.equals(mth.getMethod().getType(), "ENTRY");
        })
                .collect(Collectors.toList());
        procedure.setMethods(entryMethods);

        Task task = new Task();
        task.copy(procedure, true);
        task.setPrepareTaskId(taskPrepare.getId());
        task.setStatus(CommandStatusEnum.DRAFT.name());
        task.setTaskNumber(taskPrepare.getTaskNumber());
        task.setTaskName(taskPrepare.getSample().getName() + ":" + procedure.getName());
        task.setTag(taskPrepare.getRfTag());
        task.setSample(taskPrepare.getSample());

        create(task);
        return task;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOthersMethods(Task task, TaskPrepare taskPrepare, boolean exitMethodOnly) {
        Optional<Procedure> procedureOpt = procedureRepository.findById(taskPrepare.getProcedure().getId());
        if (!procedureOpt.isPresent()) {
            log.error("Could not found procedure by id:{}", taskPrepare.getProcedure().getId());
            throw new IllegalArgumentException("Could not found procedure by id:" + taskPrepare.getProcedure().getId());
        }
        // 创建只包含进样方法的Task
        Procedure procedure = procedureOpt.get();
        List<ProcedureMethod> methods = procedure.getMethods();
        List<ProcedureMethod> otherMethods = new ArrayList<>();
        if (!exitMethodOnly) {
            otherMethods = methods.stream().filter(mth -> {
                return !StringUtils.equals(mth.getMethod().getType(), "ENTRY");
            })
                    .collect(Collectors.toList());
        } else {
            otherMethods = methods.stream().filter(mth -> {
                return StringUtils.equals(mth.getMethod().getType(), "EXIT");
            })
                    .collect(Collectors.toList());
        }
        for (ProcedureMethod procMethod : otherMethods) {
            TaskMethod taskMethod = new TaskMethod();
            taskMethod.copy(procMethod, true);
            taskMethod.setTask(task);
            task.getMethodList().add(taskMethod);
        }
        taskRepository.save(task);

        updateRefFields(task);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Task resources) {
        Task task = taskRepository.findById(resources.getId()).orElseGet(Task::new);
        ValidationUtil.isNull( task.getId(),"Task","id",resources.getId());
        task.copy(resources);
        taskRepository.save(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScheduleEntryTime(Task task) {
        taskRepository.updateScheduleEntryTime(task.getId(), task.getScheduleEntryTime());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            Task task = taskRepository.findById(id).orElseGet(Task::new);
            setTaskCancelledStatus(task);
            taskRepository.save(task);
        }
    }

    private void setTaskCancelledStatus(Task task) {

        task.setStatus(RunStatusEnum.CANCELLED.name());

        // 遍历方法
        for (int idxMth=task.getMethodList().size()-1; idxMth>=0; idxMth--){
            TaskMethod taskMethod = task.getMethodList().get(idxMth);
            if ( !StringUtils.equalsAny( taskMethod.getStatus(),
                    RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                taskMethod.setStatus(RunStatusEnum.CANCELLED.name());
            }
            // 遍历步骤
            for (int idxStep=taskMethod.getSteps().size()-1; idxStep>=0; idxStep--){
                TaskStep step = taskMethod.getSteps().get(idxStep);
                if ( !StringUtils.equalsAny( step.getStatus(),
                        RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                    step.setStatus(RunStatusEnum.CANCELLED.name());
                }
                // 遍历动作
                for (int idxAct=step.getActions().size()-1; idxAct>=0; idxAct--){
                    TaskAction action = step.getActions().get(idxAct);
                    if ( !StringUtils.equalsAny( action.getStatus(),
                            RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                        action.setStatus(RunStatusEnum.CANCELLED.name());
                    }

                    // 遍历命令
                    for (int idxCmd=action.getCommands().size()-1; idxCmd>=0; idxCmd--){
                        TaskCommand taskCommand = action.getCommands().get(idxCmd);
                        if ( !StringUtils.equalsAny( taskCommand.getStatus(),
                                RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                            taskCommand.setStatus(RunStatusEnum.CANCELLED.name());
                        }
                    }
                }
            }
        }
    }

    @Override
    public void download(List<TaskDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskDto task : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" procedureId",  task.getProcedureId());
            map.put("开启进程方法ID", task.getEntryMethodId());
            map.put("结束进程方法ID", task.getExitMethodId());
            map.put("进程状态, READY,RUNNING,SUCCESS,FAILED", task.getStatus());
            map.put("是否已被删除,Y/N", task.getDeleteFlag());
            map.put("创建人", task.getCreateBy());
            map.put("创建时间", task.getCreateTime());
            map.put("更新人", task.getUpdateBy());
            map.put("更新时间", task.getUpdateTime());
            map.put("任务编号，前端用户唯一识别的任务号: sampleNumber-datetime-seq", task.getTaskNumber());
            map.put("任务名称, 样品名-流程名", task.getTaskName());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public TaskScheduleChartDto querySchedulerGanttData() {
        TaskScheduleChartDto dto = new TaskScheduleChartDto();
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TaskDto setFavoriteScheduler(Task resources) {
        Task task = taskRepository.findById(resources.getId()).orElseGet(Task::new);
        ValidationUtil.isNull( task.getId(),"Task","id",resources.getId());

        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                Long methodStepId = step.getStepId();
                methodStepRepository.updateEvaluateTime(methodStepId, step.getExecutedStart(), step.getExecutedDuration());
                for (TaskAction action : step.getActions()) {
                    stepActionRepository.updateEvaluateTime(action.getActionId(), action.getExecutedStart(), action.getExecutedDuration());
                }
            }
        }
        return null;
    }


    @Override
    public List<TaskStatItemDto> getStatistics() {
        List<Object[]> statList = taskRepository.queryStatisticsByStatus();
        List<TaskStatItemDto> dtoList = new ArrayList<>();
        for (Object[] column : statList) {
            TaskStatItemDto dto = new TaskStatItemDto();
            dtoList.add(dto);
            String colName = String.valueOf(column[0]);
            String colValue = String.valueOf(column[1]);
            String statName = "";
            switch (colName){
                case "READY":
                    statName = "待进样任务:" + colValue;
                    break;
                case "IN_SCHEDULE_QUE":
                    statName = "运行中任务:" + colValue;
                    break;
                case "FAILED":
                    statName = "执行失败数:" + colValue;
                    break;
                case "SUCCESS":
                    statName = "执行成功数:" + colValue;
                    break;
            }
            dto.setName(statName);
            dto.setValue(String.valueOf(colValue));
        }
        return dtoList;
    }

    @Override
    public List<TaskStatItemDto> getRunningStatistics() {
        TaskScheduler taskScheduler = SpringContextHolder.getBean(TaskScheduler.class);
        Queue<Task> taskQue = taskScheduler.getTaskQueue();

        int runningCount = 0;
        int waitToSampleEntryCount = 0;
        int failedCount = 0;
        List<TaskStatItemDto> dtoList = new ArrayList<>();
        for (Task task : taskQue) {
            if (isTaskFailedStatus(task)){
                failedCount ++;
                continue;
            }

            // 运行状态
            if (task.getMainMethodList().stream().anyMatch(taskMethod -> {
                return StringUtils.equalsAny( taskMethod.getStatus(), RunStatusEnum.IN_SCHEDULE_QUE.name(),RunStatusEnum.RUNNING.name());
            })){
                runningCount ++;
            }else{
                waitToSampleEntryCount ++;
            }
        }

        TaskStatItemDto runningDto = new TaskStatItemDto();
        dtoList.add(runningDto);
        runningDto.setCode("RUNNING_COUNT");
        runningDto.setName("运行中任务:" + runningCount);
        runningDto.setValue(Integer.toString(runningCount));

        TaskStatItemDto waitToSampleEntryDto = new TaskStatItemDto();
        dtoList.add(waitToSampleEntryDto);
        waitToSampleEntryDto.setCode("WAIT_TO_SAMPLE_ENTRY_COUNT");
        waitToSampleEntryDto.setName("待进样任务:" + waitToSampleEntryCount);
        waitToSampleEntryDto.setValue(Integer.toString(waitToSampleEntryCount));

        TaskStatItemDto failedDto = new TaskStatItemDto();
        dtoList.add(failedDto);
        failedDto.setCode("FAILED_COUNT");
        failedDto.setName("失败任务:" + failedCount);
        failedDto.setValue(Integer.toString(failedCount));

        return dtoList;
    }

    private boolean isTaskFailedStatus(Task task) {
        if (StringUtils.equalsAny(task.getStatus(), RunStatusEnum.FAILED.name())){
            return true;
        }
        for (TaskMethod method : task.getMethodList()) {
            if (StringUtils.equalsAny(method.getStatus(), RunStatusEnum.FAILED.name())){
                return true;
            }
            for (TaskStep step : method.getSteps()) {
                if (StringUtils.equalsAny(step.getStatus(), RunStatusEnum.FAILED.name())){
                    return true;
                }
                for (TaskAction action : step.getActions()) {
                    if (StringUtils.equalsAny(action.getStatus(), RunStatusEnum.FAILED.name())){
                        return true;
                    }
                    for (TaskCommand cmd : action.getCommands()) {
                        if (StringUtils.equalsAny(cmd.getStatus(), RunStatusEnum.FAILED.name())){
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    @Override
    public TaskToCheckSamplesReqDto getTaskToCheckSamplesReqByBatchId(Long batchId) {
        TaskToCheckSamplesReqDto dto = new TaskToCheckSamplesReqDto();
        List<Task> taskList = taskRepository.findByBatchId(batchId);
        dto.setBatchId(batchId);
        for (Task task : taskList) {
            TaskPrepare tp = taskPrepareRepository.findTaskPrepareByTaskId(task.getId());
            if (tp != null) {
                dto.setTag(tp.getRfTag());
                TaskToCheckSamplesReqDto.SampleItem sampleItem = new TaskToCheckSamplesReqDto.SampleItem();
                sampleItem.setTaskId(task.getId());
                sampleItem.setPosIndex(tp.getPosIndex());
                dto.getSampleList().add(sampleItem);
            }
        }
        dto.setSampleCount(dto.getSampleList().size());
        return dto;
    }

    @Override
    public String validateTag(String tagValue) {
        String out = "SUCCESS";
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        Position position = taskCommand.getSelectedDevicePos();

        TaskScheduler taskScheduler = SpringContextHolder.getBean(TaskScheduler.class);
        Optional<Task> onlineTaskOpt = taskScheduler.getTaskByTag(tagValue);
        if (!onlineTaskOpt.isPresent()) {
            log.error("Validate error, could not found task in queue by this tag: {}", tagValue);
            return "FAILED";
        }

        Task onlineTask = onlineTaskOpt.get();
        // TAG与当前任务一致
        if (onlineTask.getId().equals(taskCommand.getTaskId())){
            return "SUCCESS";
        }

        PositionRequestVo posVo = new PositionRequestVo();
        posVo.setDeviceInstanceId(position.getDeviceInstance().getId());
        posVo.setName(position.getName());
        posVo.setStatus("HOLD");
        posVo.setTaskId(taskCommand.getTaskId());
        posVo.setTaskMethodId(taskCommand.getTaskMethodId());

        // 变更当前样品存储位为onlineTask
        PositionService positionService = SpringContextHolder.getBean(PositionService.class);
        Optional<Position> posOpt = positionService.findMatchedPosition(posVo);
        if (!posOpt.isPresent()){
            log.error("Validate error, Could not found sample position");
            return "FAILED";
        }

        Position pos = posOpt.get();
        positionService.updatePositionStatus(pos.getId(), Position.StatusEnum.HOLD, onlineTask.getId(), null );
        // 变更onlineTask的进样方法状态为SKIPPED
        TaskMethod onlineEntryMethod = onlineTask.getEntryMethod().get();
        onlineEntryMethod.setStatus(RunStatusEnum.SKIPPED.name());
        onlineEntryMethod.setMessage("Skipped by taskId:"+taskCommand.getTaskId());
        TaskExecuteLogUtil.logMethod(onlineEntryMethod);

        // 变更当前任务进样方法状态为SUSPEND
        TaskMethod curEntryMethod = TaskExecutorContext.getTask().getEntryMethod().get();
        curEntryMethod.setStatus(RunStatusEnum.SUSPEND.name());
        curEntryMethod.setMessage("Suspend as tag is not matched the current task.");
        TaskExecuteLogUtil.logMethod(curEntryMethod);

        return out;
    }
}