/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskRepository extends JpaRepository<Task, Long>, JpaSpecificationExecutor<Task> {

    Task findByTaskNumber(String taskNumber);

    @Modifying
    @Query(value="update Task set status=?2, message=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateStatus(Long id, String status, String message);

    @Modifying
    @Query(value="update Task set scheduleEntryTime=?2, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateScheduleEntryTime(Long id, Long scheduleEntryTime);


    //@Query(value="select Task set status=?2, message=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    @Query(value = "select t.* from les_task t where t.procedure_id=?1 and t.task_name like %?2% and t.status not in ('CANCELLED') order by t.id desc limit 50", nativeQuery = true)
    List<Task> queryForSelection(Long procedureId, String taskName);
    @Query(value = "select t.* from les_task t where t.procedure_id=?1 and t.status not in ('CANCELLED') order by t.id desc limit 50", nativeQuery = true)
    List<Task> queryForSelection(Long procedureId);

    @Query(value = "SELECT t.status, COUNT(1) FROM les_task t where t.status != 'CANCELLED' GROUP BY t.status", nativeQuery = true)
    List<Object[]> queryStatisticsByStatus();

    /**
     * 通过任务批次查询任务清单
     * @param batchId
     * @return
     */
    List<Task> findByBatchId(Long batchId);
}