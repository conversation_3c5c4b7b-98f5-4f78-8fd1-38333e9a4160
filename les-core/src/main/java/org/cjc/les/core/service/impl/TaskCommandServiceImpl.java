/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskCommand;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.PositionRepository;
import org.cjc.les.core.repository.TaskCommandRepository;
import org.cjc.les.core.service.TaskCommandService;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.core.service.dto.TaskCommandQueryCriteria;
import org.cjc.les.core.service.mapstruct.TaskCommandMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.exception.TaskRunningException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.cjc.exception.EntityExistException;

import java.util.*;
import java.io.IOException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-11
**/
@Service
@RequiredArgsConstructor
@Log4j2
public class TaskCommandServiceImpl implements TaskCommandService {

    @PersistenceContext
    private EntityManager entityManager;

    private final TaskCommandRepository taskCommandRepository;
    private final TaskCommandMapper taskCommandMapper;

    private final PositionRepository positionRepository;

    @Autowired
    private TaskCommandService self;

    @Override
    public Map<String,Object> queryAll(TaskCommandQueryCriteria criteria, Pageable pageable){
        Page<TaskCommand> page = taskCommandRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskCommandMapper::toDto));
    }

    @Override
    public List<TaskCommandDto> queryAll(TaskCommandQueryCriteria criteria){
        return taskCommandMapper.toDto(taskCommandRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskCommandDto findById(Long id) {
        TaskCommand taskCommand = taskCommandRepository.findById(id).orElseGet(TaskCommand::new);
        ValidationUtil.isNull(taskCommand.getId(),"TaskCommand","id",id);
        return taskCommandMapper.toDto(taskCommand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskCommandDto create(TaskCommand resources) {
        return taskCommandMapper.toDto(taskCommandRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskCommand resources) {
        TaskCommand taskCommand = taskCommandRepository.findById(resources.getId()).orElseGet(TaskCommand::new);
        ValidationUtil.isNull( taskCommand.getId(),"TaskCommand","id",resources.getId());
        taskCommand.copy(resources);
        if (resources.getSelectedDevicePos() != null && taskCommand.getSelectedDevicePos() != null){
            taskCommand.getSelectedDevicePos().copy(resources.getSelectedDevicePos());
        }else{
            taskCommand.setSelectedDevicePos(resources.getSelectedDevicePos());
        }
        taskCommandRepository.save(taskCommand);
        //Position pos = taskCommand.getSelectedDevicePos();
        //if (pos!=null) {
        //    taskCommandRepository.updatePositionStatus(pos.getId(),pos.getStatus(),pos.getHoldByTaskId(), pos.getHoldByTaskActionId());
        //}
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            taskCommandRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TaskCommandDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskCommandDto taskCommand : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("任务ID", taskCommand.getTaskId());
            map.put("关联命令", taskCommand.getActionCommandId());
            map.put(" taskMethodId",  taskCommand.getTaskMethodId());
            map.put(" taskStepId",  taskCommand.getTaskStepId());
            map.put("关联ACTION", taskCommand.getTaskActionId());
            map.put(" deviceInstanceId",  taskCommand.getDeviceInstanceId());
            map.put(" parameter",  taskCommand.getParameter());
            map.put(" selectedDevicePosId",  taskCommand.getSelectedDevicePosId());
            map.put("命令备注描述", taskCommand.getComment());
            map.put("运行失败后的处理方式:CONTINUE/RETURN_ACTION/RETURN_STEP/RETURN_METHOD/RETURN_PROCEDURE", taskCommand.getFailedThen());
            map.put("状态: READY,RUNNING,SUCCESS,FAILED", taskCommand.getStatus());
            map.put("执行FAILED状态时的详情", taskCommand.getMessage());
            map.put("创建人", taskCommand.getCreateBy());
            map.put("创建时间", taskCommand.getCreateTime());
            map.put("更新人", taskCommand.getUpdateBy());
            map.put("更新时间", taskCommand.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void waitUntilFailureHandledByManually(TaskCommand taskCommand) {
        Optional<TaskCommand> newCommandOpt = Optional.empty();
        newCommandOpt = self.findTaskCommandById(taskCommand.getId());
        if (!newCommandOpt.isPresent()) {
            log.error("Not found TaskCommand by Id:{}", taskCommand.getId());
            return;
        }

        TaskCommand newCommand = newCommandOpt.get();
        newCommand.setFailedThen(FailedThenEnum.PAUSE);
      //  taskCommandRepository.saveAndFlush(newCommand);

        while (!Thread.interrupted()) {
            // 已撤销，则直接退出
            if (StringUtils.equalsAny(taskCommand.getStatus(), RunStatusEnum.CANCELLED.name())){
                break;
            }
            newCommandOpt = self.findTaskCommandById(taskCommand.getId());

            newCommand = newCommandOpt.get();
            if (StringUtils.isEmpty(newCommand.getFailureFixAsTemp())) {
                try {
                    Task task = TaskExecutorContext.getTask();
                    log.error("Need to fix the failure for [{}] taskCommand: {}",(task==null?"":task.getTaskNumber()), taskCommand);
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    throw new TaskRunningException("InterruptedException occurred");
                }
                continue;
            }
            newCommand.setFailureFixAsTemp(null);
            taskCommandRepository.saveAndFlush(newCommand);
            taskCommand.setFailureFixAs(newCommand.getFailureFixAs());
            break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFailureFixInfo(TaskCommandDto dto) {
        TaskCommand taskCommand = taskCommandRepository.findById(dto.getId()).orElseGet(TaskCommand::new);
        ValidationUtil.isNull( taskCommand.getId(),"TaskCommand","id",dto.getId());
        taskCommand.setFailureFixAs(dto.getFailureFixAs());
        taskCommand.setFailureFixAsTemp(dto.getFailureFixAs());
        taskCommand.setFailureFixedBy(taskCommand.getUpdateBy());
        taskCommandRepository.save(taskCommand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updatePositionStatus(TaskCommand cmd, Position pos) {
        taskCommandRepository.updateSelectedPositionId(cmd.getId(), pos);
        positionRepository.updatePositionStatus(pos.getId(),pos.getStatus(),
                pos.getHoldByTaskId(), pos.getHoldByTaskMethodId(), pos.getHoldByTaskStepId(), pos.getHoldByTaskActionId());
        log.info("updatePositionStatus: TaskCommand.id={}, taskId={}, taskMethodId={}, taskStepId={}, taskActionId={}, posId={},posName={},posStatus={}",
                cmd.getId(), cmd.getTaskId(),cmd.getTaskMethodId(), cmd.getTaskStepId(), cmd.getTaskAction().getId(), pos.getId(),pos.getName(),pos.getStatus());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Optional<TaskCommand> findTaskCommandById(Long id) {
        entityManager.flush(); // 强制刷新持久化上下文中的更改到数据库
        entityManager.clear(); // 清空持久化上下文中的缓存
        TaskCommand out = entityManager.find(TaskCommand.class, id);
        if (out == null) {
            return Optional.empty();
        }
        return Optional.of(out); // 从数据库获取最新数据
        // return this.taskCommandRepository.findTaskCommandById(id);
    }

    @Override
    public List<Position> findPositionsByBoardNodeId(String nodeId) {
        return positionRepository.findPositionsByBoardNodeId(nodeId);
    }

}