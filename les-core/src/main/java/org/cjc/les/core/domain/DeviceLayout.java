/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-04
**/
@Entity
@Data
@Table(name="les_device_layout")
@SQLDelete(sql = "update les_device_layout set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class DeviceLayout  extends LesConfigBaseEntity {


    @Column(name = "description")
    @Schema(description = "工作站描述")
    private String description;

    @Column(name = "layout_svg")
    @Schema(description = "SVG格式的存储")
    private String layoutSvg;

    @Column(name = "style_setting")
    @Schema(description = "样式设置")
    private String styleSetting;

    @Column(name = "status",nullable = false)
    @NotBlank
    @Schema(description = "状态:DRAFT, DEPLOY")
    private String status = "DRAFT";

    @Column(name = "version")
    @Schema(description = "保存的更新版本")
    private Integer version;

    /* 变更为OneToMany
    @ManyToMany
    @JoinTable(name = "les_device_layout_rel",
            joinColumns = {@JoinColumn(name = "device_layout_id",referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "device_instance_id",referencedColumnName = "id")})
    */
    @OneToMany(mappedBy = "deviceLayout", targetEntity = DeviceInstance.class, cascade = {CascadeType.PERSIST,CascadeType.ALL}, orphanRemoval = true)
    private List<DeviceInstance> deviceInstanceList = new ArrayList<>();

    public void copy(DeviceLayout source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("deviceInstanceList"));
        if (this.getDeviceInstanceList()==null){
            this.setDeviceInstanceList(new ArrayList<>());
        }

        List<DeviceInstance> deleteList = new ArrayList<>();
        for (DeviceInstance deviceInstance : this.getDeviceInstanceList()) {
            Optional<DeviceInstance> optOther = source.getDeviceInstanceList().stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),deviceInstance.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                deviceInstance.copy(optOther.get());
                deviceInstance.setDeviceLayout(this);
            }else{
                deleteList.add(deviceInstance);
            }

        }

        source.getDeviceInstanceList().forEach(act->{
            if (act.getId() == null){
                this.getDeviceInstanceList().add(act);
                act.setDeviceLayout(this);
                for (Position pos : act.getPositions()){
                    pos.setDeviceInstance(act);
                    if (CollectionUtils.isEmpty(pos.getRobots())){
                        continue;
                    }
                    for (PositionRobot posRobot : pos.getRobots()){
                        posRobot.setPosition(pos);
                    }
                }
            }
        });

        if (CollectionUtils.isNotEmpty(deleteList)) {
            this.getDeviceInstanceList().removeAll(deleteList);
        }

    }
}