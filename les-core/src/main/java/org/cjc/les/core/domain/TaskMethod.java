/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_task_method")
public class TaskMethod extends TaskBaseEntity {

    @Column(name = "name",unique = true)
    @Schema(description = "任务创建时的方法名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "任务创建时的方法描述")
    private String description;
    /**
     * 方法类型:ENTRY,EXIT,MAIN
     */
    private String type;

    private String message;

    private Long deriveProcId;

    private String parallel;

    /**
     * 环境变量
     */
    private String variables;

    @Column(name = "method_id")
    @Schema(description = "引用配置的方法ID")
    private Long methodId;

    @Column(name = "delete_flag")
    @Schema(description = "是否已被删除,Y/N")
    private String deleteFlag;

    @JSONField(serialize = false)
    @JoinColumn(name = "task_id", referencedColumnName = "id")
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = Task.class)
    @Schema(description = "任务", hidden = true)
    private Task task;

    @OneToMany(mappedBy = "taskMethod", targetEntity = TaskStep.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<TaskStep> steps = new ArrayList<>();

    public void copy(TaskMethod source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    /**
     * 从方法定义中拷贝
     * @param method
     * @param isDeep 是否深拷贝，默认false
     */
    public void copy(ProcedureMethod method, boolean isDeep){
        BeanUtil.copyProperties(method,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","steps"));
        if (ObjectUtils.isNotEmpty(method.getMethod())){
            BeanUtil.copyProperties(method.getMethod(),this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","steps"));
        }
        this.setMethodId(method.getMethod().getId());

        if (isDeep) {
            steps.clear();
            for (MethodStep step : method.getMethod().getSteps()) {
                TaskStep taskStep = new TaskStep();
                taskStep.copy(step, true);
                taskStep.setTaskMethod(this);
                steps.add(taskStep);
            }
        }

    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.getId())
                .append("name", name)
                .append("description", description)
                .append("type", type)
                .append("status", this.getStatus())
                .append("methodId", methodId)
                .append("deleteFlag", deleteFlag)
                .toString();
    }
}