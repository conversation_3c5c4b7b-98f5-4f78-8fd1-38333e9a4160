/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.ProcedureVariable;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.service.dto.ProcedureVariableDto;
import org.cjc.les.core.service.dto.TaskMethodDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-27
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProcedureVariableMapper extends BaseMapper<ProcedureVariableDto, ProcedureVariable> {

    @Mapping(source = "variable.name", target = "name")
    @Mapping(source = "variable.description", target = "description")
    @Mapping(source = "variable.viewName", target = "viewName")
    @Mapping(source = "variable.type", target = "type")
    @Override
    ProcedureVariableDto toDto(ProcedureVariable entity);

    @Mapping(source = "variableId", target = "variable.id")
    @Mapping(source = "procedureId", target = "procedure.id")
    @Override
    ProcedureVariable toEntity(ProcedureVariableDto dto);
}