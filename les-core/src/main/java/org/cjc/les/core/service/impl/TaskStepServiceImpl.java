/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.TaskStep;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.TaskStepRepository;
import org.cjc.les.core.service.TaskStepService;
import org.cjc.les.core.service.dto.TaskStepDto;
import org.cjc.les.core.service.dto.TaskStepQueryCriteria;
import org.cjc.les.core.service.mapstruct.TaskStepMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.cjc.exception.EntityExistException;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-11
**/
@Service
@RequiredArgsConstructor
public class TaskStepServiceImpl implements TaskStepService {

    private final TaskStepRepository taskStepRepository;
    private final TaskStepMapper taskStepMapper;

    @Override
    public Map<String,Object> queryAll(TaskStepQueryCriteria criteria, Pageable pageable){
        Page<TaskStep> page = taskStepRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskStepMapper::toDto));
    }

    @Override
    public List<TaskStepDto> queryAll(TaskStepQueryCriteria criteria){
        return taskStepMapper.toDto(taskStepRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskStepDto findById(Long id) {
        TaskStep taskStep = taskStepRepository.findById(id).orElseGet(TaskStep::new);
        ValidationUtil.isNull(taskStep.getId(),"TaskStep","id",id);
        return taskStepMapper.toDto(taskStep);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskStepDto create(TaskStep resources) {
        if(taskStepRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(TaskStep.class,"name",resources.getName());
        }
        return taskStepMapper.toDto(taskStepRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskStep resources) {
        TaskStep taskStep = taskStepRepository.findById(resources.getId()).orElseGet(TaskStep::new);
        ValidationUtil.isNull( taskStep.getId(),"TaskStep","id",resources.getId());
        TaskStep taskStep1 = null;
        taskStep1 = taskStepRepository.findByName(resources.getName());
        if(taskStep1 != null && !taskStep1.getId().equals(taskStep.getId())){
            throw new EntityExistException(TaskStep.class,"name",resources.getName());
        }
        taskStep.copy(resources);
        taskStepRepository.save(taskStep);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateExecutedTime(TaskStep resources) {
        taskStepRepository.updateExecutedTime(resources.getId(), resources.getExecutedStart(), resources.getExecutedDuration());
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            taskStepRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TaskStepDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskStepDto taskStep : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("任务运行的步骤名称", taskStep.getName());
            map.put("任务中的步骤描述", taskStep.getDescription());
            map.put("运行状态: READY,RUNNING,SUCCESS,FAILED", taskStep.getStatus());
            map.put("任务ID", taskStep.getTaskId());
            map.put("任务的方法ID", taskStep.getTaskMethodId());
            map.put("引用配置的步骤ID", taskStep.getStepId());
            map.put("是否已被删除,Y/N", taskStep.getDeleteFlag());
            map.put("创建人", taskStep.getCreateBy());
            map.put("创建时间", taskStep.getCreateTime());
            map.put("更新人", taskStep.getUpdateBy());
            map.put("更新时间", taskStep.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}