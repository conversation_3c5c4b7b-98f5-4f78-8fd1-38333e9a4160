package org.cjc.les.core.task.execute;

import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;

@Data
public class ExecutorConfig {

    public enum ExecuteModeEnum{
        DIRECT, // 直接调用
        IN_SCHEDULE // 调度器管理
    }

    public enum DebugModeEnum {
        NONE,    // 非调试模式
        CONTINUE, // 连续调试模式，可打断点
        MONO // 单点调试模式, 每执行一次指令就暂停
    }

    /**
     * 是否记录执行日志, 默认记录;
     * 当仅用于临时检测或调试时，可以设置为false, 则不保存该执行的结果
     */
    private boolean recordLog = true;

    /**
     * 执行模式
     */
    private ExecuteModeEnum executeMode = ExecuteModeEnum.IN_SCHEDULE;


    private DebugModeEnum debugMode = DebugModeEnum.NONE;

    private AtomicBoolean monoBreakpointLock = new AtomicBoolean(false);

    public volatile Object breakPointMuteLock = new Object();

    public static ExecutorConfig defaultConfig = new ExecutorConfig();
}
