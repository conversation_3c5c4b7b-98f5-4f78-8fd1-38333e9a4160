/*
 *  Copyright 2024-2025 <PERSON><PERSON>is Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.ProcedureRepository;
import org.cjc.les.core.repository.TaskRepository;
import org.cjc.les.core.service.SampleService;
import org.cjc.les.core.service.TaskDebugService;
import org.cjc.les.core.service.TaskPrepareService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskDebugRequestDto;
import org.cjc.les.core.service.dto.TaskDto;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.SerialNumberGenerator;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;

/**
* 任务调试服务类实现
* @description 服务实现
* <AUTHOR>
* @date 2025-03-18
**/
@Service
@RequiredArgsConstructor
@Log4j2
public class TaskDebugServiceImpl implements TaskDebugService {

    private final TaskRepository taskRepository;

    private final ProcedureRepository procedureRepository;

    private final SampleService sampleService;

    private final TaskPrepareService taskPrepareService;


    @Override
    public Task createTaskDebug(TaskDebugRequestDto dto) {
        Optional<Procedure> procedureOpt = procedureRepository.findById( dto.getProcedureId() ) ;
        if (!procedureOpt.isPresent()){
            log.error("Could not found procedure by id:{}", dto.getProcedureId() );
            throw new IllegalArgumentException("Could not found procedure by id:" + dto.getProcedureId());
        }
        Procedure procedure = procedureOpt.get();
        if (procedure.getEntryMethod().isPresent()) {
            ProcedureMethod entryMethod = procedure.getEntryMethod().get();

            TaskMethod taskMethod = new TaskMethod();
            taskMethod.copy(entryMethod, true);

            Long id = taskMethod.getId();
        }
        Sample sample = new Sample();
        sample.setName("test");
        sample = sampleService.save(sample);


        TaskPrepare taskPrepare = new TaskPrepare();
        taskPrepare.setRfTag("test");
        taskPrepare.setPosIndex(0);
        taskPrepare.setProcedure(procedure);
        taskPrepare.setSample(sample);
        taskPrepare.setTaskNumber(SerialNumberGenerator.generateTaskSerialNumber());
        taskPrepare.setStatus("PUSHED");
        taskPrepareService.save(taskPrepare);

        Task task = new Task();
        task.copy(procedure, true);
        task.setPrepareTaskId(taskPrepare.getId());
        task.setStatus(CommandStatusEnum.READY.name());
        task.setTaskNumber(taskPrepare.getTaskNumber());
        task.setTaskName(sample.getName()+":"+procedure.getName() );
        task.setName(task.getTaskName());
        task.setTag(taskPrepare.getRfTag());
        task.setSample(sample);
        long curTm = System.currentTimeMillis();
        task.setBatchId(Long.valueOf(taskPrepare.getRfTag().hashCode() + curTm));
    //    if (!task.getEntryMethod().isPresent() || task.getEntryMethod().get().getSteps().size()==0){
    //        log.error("Could not found first step of entry methos.");
    //        return new Task();
    //    }
        //1. 保存任务状态
        TaskService taskService = SpringContextHolder.getBean(TaskService.class);
        TaskDto out = taskService.create(task);

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);
        TaskExecutorContext.getContext().getExecutorConfig().setDebugMode(ExecutorConfig.DebugModeEnum.valueOf(dto.getDebugMode()));
        task.setMessage("任务创建成功，等待调度执行");
        TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.INFO);

        setMonoMode(task, dto);
        TaskScheduler taskScheduler = SpringContextHolder.getBean(TaskScheduler.class);
        taskScheduler.schedule(task);

        fillLayoutImage(task);
        return task;
    }

    @Override
    public Task loadTaskDebug(TaskDebugRequestDto dto) {


        TaskScheduler taskScheduler = SpringContextHolder.getBean(TaskScheduler.class);
        Optional<Task> taskInSchedulerOpt = taskScheduler.getTaskById(dto.getTaskId());
        if (taskInSchedulerOpt.isPresent()){
            Task task = taskInSchedulerOpt.get();
            fillLayoutImage(task);
            return task;
        }

        Optional<Task> taskOpt = taskRepository.findById(dto.getTaskId());
        if (!taskOpt.isPresent()) {
            throw new IllegalArgumentException("Could not found task by id:" + dto.getTaskId());
        }

        Task task = taskOpt.get();
        if (StringUtils.equals(RunStatusEnum.CANCELLED.name(), task.getStatus())){
            throw new TaskRunningException("该任务已被撤销");
        }
        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);

        TaskExecutorContext.getContext().getExecutorConfig().setDebugMode(ExecutorConfig.DebugModeEnum.valueOf(dto.getDebugMode()));

        task.setMessage("任务创建成功，等待调度执行");
        TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.INFO);

        taskScheduler.schedule(task);
        fillLayoutImage(task);
        task.setName(task.getTaskName());
        return task;
    }

    private void fillLayoutImage(Task task) {
        for (TaskMethod mth : task.getMethodList()) {
            for (TaskStep step : mth.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    for (TaskCommand command : action.getCommands()) {
                        command.setLayoutImage(getDeviceLayoutImage(command.getDeviceInstance()));
                        command.getDeviceInstance().setCommands(null);
                    }
                }
            }
        }
    }

    private static Map<Long, String> deviceInstanceImageMap = new HashMap<>();

    private String getDeviceLayoutImage(DeviceInstance instance) {
        if (deviceInstanceImageMap.containsKey(instance.getId())) {
            return deviceInstanceImageMap.get(instance.getId());
        }
       // String layoutImage = taskCommandRepository.findLayoutImage(instance.getId());
        deviceInstanceImageMap.put(instance.getId(), instance.getDevice().getLayoutImage());
        return instance.getDevice().getLayoutImage();
    }


    @Override
    public void setMonoMode(Task task, TaskDebugRequestDto dto) {
        List<TaskBaseEntity> taskBaseEntities = getSortedTaskBaseEntities(task);

        String lastEntityStatus = RunStatusEnum.SUCCESS.name();
        for (int i = 0; i < taskBaseEntities.size(); i++) {
            TaskBaseEntity taskBaseEntity = taskBaseEntities.get(i);
            String curEntityStatus = taskBaseEntity.getStatus();
            if (StringUtils.equalsAny(curEntityStatus, RunStatusEnum.PAUSED.name())
                    && checkPrevNodeDoneInSameLevel(taskBaseEntities, taskBaseEntity)) {
                dto.setStatus("SUCCESS");
                dto.setMessage("");
                synchronized (task.getContext().getExecutorConfig().breakPointMuteLock) {
                    taskBaseEntity.setStatus(RunStatusEnum.READY.name());
                    TaskExecuteLogUtil.log(taskBaseEntity);
                    if (i < taskBaseEntities.size() - 1) {
                        TaskBaseEntity nextEntity = taskBaseEntities.get(i + 1);
                        nextEntity.setStatus(RunStatusEnum.PAUSED.name());
                        TaskExecuteLogUtil.log(nextEntity);
                    }
                }
                return;
            }

            lastEntityStatus = taskBaseEntity.getStatus();
        }

        boolean bExistPaused = taskBaseEntities.stream().anyMatch(taskBaseEntity ->
                StringUtils.equalsAny(taskBaseEntity.getStatus(), RunStatusEnum.PAUSED.name()));
        if (!bExistPaused) {
            for (int i = 0; i < taskBaseEntities.size(); i++) {
                TaskBaseEntity taskBaseEntity = taskBaseEntities.get(i);
                String curEntityStatus = taskBaseEntity.getStatus();
                if (StringUtils.equalsAny(curEntityStatus, RunStatusEnum.READY.name(), RunStatusEnum.BREAK.name())) {
                    dto.setStatus("SUCCESS");
                    dto.setMessage("");
                    synchronized (task.getContext().getExecutorConfig().breakPointMuteLock) {
                        taskBaseEntity.setStatus(RunStatusEnum.PAUSED.name());
                        TaskExecuteLogUtil.log(taskBaseEntity);
                    }
                    return;
                }
            }
        }
    }

    @Override
    public void setContinueMode(Task task, TaskDebugRequestDto dto) {
        List<TaskBaseEntity> taskBaseEntities = getSortedTaskBaseEntities(task);

        for (int i = 0; i < taskBaseEntities.size(); i++) {
            TaskBaseEntity taskBaseEntity = taskBaseEntities.get(i);
            String curEntityStatus = taskBaseEntity.getStatus();
            if (StringUtils.equalsAny(curEntityStatus, RunStatusEnum.PAUSED.name(), RunStatusEnum.BREAK.name())
            ) {
                dto.setStatus("SUCCESS");
                dto.setMessage("");
                synchronized (task.getContext().getExecutorConfig().breakPointMuteLock) {
                    taskBaseEntity.setStatus(RunStatusEnum.READY.name());
                    TaskExecuteLogUtil.log(taskBaseEntity);
                }
                return;
            }

        }
    }

    private List<TaskBaseEntity> getSortedTaskBaseEntities(Task task) {
        List<TaskBaseEntity> taskBaseEntities = new ArrayList<>();
        for (TaskMethod method : task.getMethodList()) {
            taskBaseEntities.add(method);
            for (TaskStep step : method.getSteps()) {
                taskBaseEntities.add(step);
                for (TaskAction action : step.getActions()) {
                    taskBaseEntities.add(action);
                    for (TaskCommand command : action.getCommands()) {
                        taskBaseEntities.add(command);
                    }
                }
            }
        }
        return taskBaseEntities;
    }

    /**
     * 检查通级别节点的前一个兄弟节点是否已完成
     * @param taskBaseEntities
     * @param curEntity
     * @return
     */
    private boolean checkPrevNodeDoneInSameLevel(List<TaskBaseEntity> taskBaseEntities, TaskBaseEntity curEntity) {
        int curIdx = -1;
        for (int i = taskBaseEntities.size()-1; i >=0; i-- ) {
            TaskBaseEntity tmpEntity = taskBaseEntities.get(i);
            if (tmpEntity.getId().equals(curEntity.getId())){
                curIdx = i;
            }
        }
        if (curIdx==-1){
            return false;
        }
        for (int i = curIdx-1; i >=0; i-- ) {
            TaskBaseEntity tmpEntity = taskBaseEntities.get(i);
            if (StringUtils.equals(tmpEntity.getClass().getName(),curEntity.getClass().getName()) ){
                if (!StringUtils.equalsAny(tmpEntity.getStatus(),
                        RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(),
                        RunStatusEnum.CANCELLED.name())) {
                    return false;
                }
            }
        }

        return true;
    }
}