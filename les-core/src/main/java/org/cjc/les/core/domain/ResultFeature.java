/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.cjc.base.LesBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;

@Entity
@Data
@Table(name="les_result_feature")
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class ResultFeature extends LesBaseEntity {

    /**
     * 所属检测结果
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "result_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = Result.class)
    @Schema(description = "任务", hidden = true)
    private Result result;
    /**
     * 产品检测项
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "result_item_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = ResultItem.class)
    @Schema(description = "任务", hidden = true)
    private ResultItem resultItem;

    /**
     * 结果特性编码
     */
    private String code;

    /**
     * 数值单位
     */
    private String unit;

    /**
     * 特性名称
     */
    private String name;

    /**
     * 特性值
     */
    private String value;

    /**
     * 特性描述
     */
    private String description;

    /**
     * 第一次检测结果
     */
    private String firstValue;

    /**
     * 第二次检测结果
     */
    private String secondValue;

    /**
     * 原始检测结果
     */
    private String rawValue;

    /**
     * 结论判定规则
     */
    private String checkRules;

    /**
     * 结论
     */
    private String conclusion;

    /**
     * 异常原因
     */
    private String reason;
    /**
     * 配置ID
     */
    private Long featureConfigId;
}
