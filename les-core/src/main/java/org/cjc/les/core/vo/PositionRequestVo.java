/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PositionRequestVo {
    private String name;
    private Long deviceInstanceId;
    private Long taskId;
    private Long taskMethodId;
    private String status;
    private String type = "DEFAULT";
    /**
     * 排序规则: serviceCode,asc;updateTime,asc
     */
    private String orderBy;
    /**
     * 对应的板卡节点ID
     */
    private String boardNodeId;

    public static PositionRequestVo parseFromStatusFormattedStr(String formattedStr) {
        PositionRequestVo vo = new PositionRequestVo();
        if (StringUtils.isEmpty(formattedStr)){
            return vo;
        }
        String[] arr1 = formattedStr.split("@");
        if (arr1.length==2){
            vo.setStatus(arr1[0]);
            String type1 = arr1[1];
            String[] typeArr = type1.split(":");
            if (typeArr.length == 2) {
                vo.setName(typeArr[0]);
                vo.setType(typeArr[1]);
            }else{
                vo.setName(typeArr[0]);
            }
            return vo;
        }

        String[] arr2 = formattedStr.split(":");
        if (arr2.length==2){
            vo.setStatus(arr2[0]);
            vo.setType(arr2[1]);
        }else if (arr2.length==1){
            vo.setStatus(arr2[0]);
        }

        return vo;
    }
}
