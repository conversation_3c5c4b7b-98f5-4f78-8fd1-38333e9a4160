/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.cjc.annotation.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class TaskQueryCriteria{
    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> updateTime;

    /**
     * 任务编号查询
     */
    @Query(type = Query.Type.EQUAL)
    private String taskNumber;

    @Query(type = Query.Type.INNER_LIKE )
    private String taskName;

  //  @Query(type = Query.Type.EQUAL)
  //  private String status;

    @Query( type = Query.Type.IN)
    private Set<String> status = new HashSet<>();

    @Query(type = Query.Type.EQUAL)
    private Long procedureId;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime;
}