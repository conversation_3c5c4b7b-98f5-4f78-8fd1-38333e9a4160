/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.DeviceLayout;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.dto.DeviceLayoutDto;
import org.cjc.les.core.service.dto.DeviceLayoutQueryCriteria;
import org.cjc.les.exception.ConfigApplicationException;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-04
**/
public interface DeviceLayoutService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(DeviceLayoutQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<DeviceLayoutDto>
    */
    List<DeviceLayoutDto> queryAll(DeviceLayoutQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return DeviceLayoutDto
     */
    DeviceLayoutDto findById(Long id);

    /**
     * 查找最新的布局设置
     * @param status ALL/DRAFT/DEPLOY
     * @return
     */
    DeviceLayoutDto findLatest(String status);

    DeviceLayoutDto getLatestLayoutStyle(String status);

    /**
    * 创建
    * @param resources /
    * @return DeviceLayoutDto
    */
    DeviceLayoutDto create(DeviceLayout resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(DeviceLayout resources) throws ConfigApplicationException;

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<DeviceLayoutDto> all, HttpServletResponse response) throws IOException;

    /**
     * 重置点位为初始状态
     * @param resources
     * @return 有变更的设备实例列表
     */
    List<DeviceInstance> resetAllPositions(DeviceLayout resources);

    /**
     * 查找最近一次布局的所有设备实例列表
     * @return
     */
    List<DeviceInstance> findAllDevicesInLatestLayout();
}