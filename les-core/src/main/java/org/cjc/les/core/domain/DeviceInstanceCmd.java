/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-07-19
**/
@Entity
@Data
@Table(name="les_device_instance_cmd")
@SQLDelete(sql = "update les_device_instance_cmd set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class DeviceInstanceCmd extends LesConfigBaseEntity {

    @Column(name = "name")
    @Schema(description = "设备实例命令名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "设备实例命令描述")
    private String description;

    @JSONField(serialize = false)
    @JoinColumn(name = "device_instance_id", referencedColumnName = "id", nullable = false)
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = DeviceInstance.class)
    private DeviceInstance deviceInstance;
//
//    @Column(name = "command_id")
//    @Schema(description = "该设备本身定义的命令ID")
//    private Long commandId;
//
    /**
     * 引用实际物理设备命令
     */
    @OneToOne
    @JoinColumn(name = "command_id", referencedColumnName = "id", nullable = true)
    private Command command;

//    @Column(name = "proxy_instance_cmd_id")
//    @Schema(description = "代理设备实例的命令ID,外键引用les_device_instance_cmd.id(该被引用的实例命令类型应该是原始设备命令)")
//    private Long proxyInstanceCmdId;
//
 //   @JSONField(serialize = false)
    @OneToOne
    @JoinColumn(name = "proxy_instance_cmd_id", referencedColumnName = "id", nullable = true)
    private DeviceInstanceCmd proxyInstanceCmd;



    @Column(name = "parameter")
    @Schema(description = "当设置为PROXY代理命令时，可以通过不同参数自定义命令行为")
    private String parameter;

    @Column(name = "test_parameter")
    private String testParameter;

    @Column(name = "command_type")
    @Schema(description = "命令类型, RAW原始命令, PROXY代理命令")
    private String commandType;

    @Column(name = "type")
    private String type;

    @Column(name = "post_execution")
    private String postExecution;

    @Column(name = "bind_control_code")
    private String bindControlCode;

    /**
     * 获取真实的设备命令
     * @return
     */
    @JSONField(serialize = false)
    public Command getRealCommand(){
        if (this.command != null) {
            return this.command;
        }else if (proxyInstanceCmd != null){
            return this.proxyInstanceCmd.getRealCommand();
        }
        return null;
    }

    /**
     * 获取真实的设备实例
     * @return
     */
    @JSONField(serialize = false)
    public DeviceInstance getRealDeviceInstance() {
        if (this.command != null) {
            return this.deviceInstance;
        }else if (proxyInstanceCmd != null){
            return this.proxyInstanceCmd.getRealDeviceInstance();
        }
        return null;
    }

    public void copy(DeviceInstanceCmd source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("deviceInstance"));
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("name", name)
                .append("description", description)
                .append("parameter", parameter)
                .append("commandType", commandType)
                .toString();
    }
}