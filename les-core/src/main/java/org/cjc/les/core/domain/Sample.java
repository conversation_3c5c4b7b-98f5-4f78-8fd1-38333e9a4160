/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.cjc.base.BaseEntity;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.sql.Timestamp;
import java.util.List;

/**
 * 样品对象
 */
@Entity
@Data
@Table(name="les_sample")
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class Sample extends LesConfigBaseEntity {

    /**
     * 样品名称
     */
    @Column(name = "name")
    @NotBlank
    @Schema(description = "样品名称")
    private String name;

    /**
     * 样品编号,与RFTAG一致
     */
    @Column(name = "number")
    @Schema(description = "样品编码")
    private String number;

    /**
     * 样品描述
     */
    @Column(name = "description")
    @Schema(description = "样品描述")
    private String description;

    /**
     * 样品分类
     */
    @JoinColumn(name = "category_id", referencedColumnName = "id", nullable = true)
    @ManyToOne(fetch=FetchType.EAGER, cascade = CascadeType.ALL, targetEntity = SampleCategory.class)
    @Schema(description = "样品分类", hidden = true)
    private SampleCategory category;

    /**
     * 样品客户
     */
    @JoinColumn(name = "customer_id", referencedColumnName = "id", nullable = true)
    @ManyToOne(fetch=FetchType.EAGER,cascade = CascadeType.ALL,  targetEntity = SampleCustomer.class)
    @Schema(description = "样品客户", hidden = true)
    private SampleCustomer customer;

    /**
     * 样品采样地
     */
    @Column(name = "collection_address")
    @Schema(description = "样品采样地")
    private String collectionAddress;

    @Column(name = "acceptation_date")
    @Schema(description = "样品接收时间")
    private Timestamp acceptationDate;

    @OneToMany(mappedBy = "sample", targetEntity = TaskPrepare.class, cascade = {CascadeType.PERSIST,CascadeType.ALL},
            fetch = FetchType.EAGER, orphanRemoval = false)
    private List<TaskPrepare> taskPrepares;
}
