/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.Command;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.CommandRepository;
import org.cjc.les.core.service.CommandService;
import org.cjc.les.core.service.dto.CommandDto;
import org.cjc.les.core.service.dto.CommandQueryCriteria;
import org.cjc.les.core.service.mapstruct.CommandMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-08
**/
@Service
@RequiredArgsConstructor
public class CommandServiceImpl implements CommandService {

    private final CommandRepository commandRepository;
    private final CommandMapper commandMapper;

    @Override
    public Map<String,Object> queryAll(CommandQueryCriteria criteria, Pageable pageable){
        Page<Command> page = commandRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(commandMapper::toDto));
    }

    @Override
    public List<CommandDto> queryAll(CommandQueryCriteria criteria){
        return commandMapper.toDto(commandRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public CommandDto findById(Long id) {
        Command command = commandRepository.findById(id).orElseGet(Command::new);
        ValidationUtil.isNull(command.getId(),"Command","id",id);
        return commandMapper.toDto(command);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommandDto create(Command resources) {
        return commandMapper.toDto(commandRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Command resources) {
        Command command = commandRepository.findById(resources.getId()).orElseGet(Command::new);
        ValidationUtil.isNull( command.getId(),"Command","id",resources.getId());
        command.copy(resources);
        commandRepository.save(command);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            commandRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<CommandDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (CommandDto command : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("命令名称", command.getName());
            map.put("命令描述", command.getDescription());
            map.put("关联设备ID", command.getDeviceId());
            map.put("该命令对应的Java方法名", command.getJavaMethodName());
            map.put("命令参数配置，JSON串，或者普通的字符串", command.getParameterTemplate());
            map.put("命令参数的java类", command.getParameterType());
            map.put("是否已被删除,Y/N", command.getDeleteFlag());
            map.put("创建人", command.getCreateBy());
            map.put("创建时间", command.getCreateTime());
            map.put("更新人", command.getUpdateBy());
            map.put("更新时间", command.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}