/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Material;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-03-26
**/
public interface MaterialRepository extends JpaRepository<Material, Long>, JpaSpecificationExecutor<Material> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Material findByName(String name);

    /**
     * 根据状态排序查询最新物料列表
     * @return
     */
    @Query(value = "SELECT t.*, " +
            "       (CASE t.status" +
            "   WHEN 'ERROR' THEN" +
            "                3" +
            "   WHEN 'WARN' THEN" +
            "                2" +
            "   WHEN 'NORMAL' THEN" +
            "                1" +
            "    ELSE" +
            "                0" +
            "        END) ord" +
            "  FROM les_material t" +
            " ORDER BY ord DESC, t.update_time DESC" +
            " LIMIT ?1", nativeQuery = true)
    List<Material> queryTopByStatus(Long topCount);

    Optional<Material> findByNameAndDeviceInstanceId(String name, Long deviceInstanceId);

    @Modifying
    @Query(value = "update les_material m join les_position pos on pos.device_instance_id=m.device_instance_id and pos.object_name=m.name " +
            "set m.remain_value=m.total_value, m.status='NORMAL', m.update_time=CURRENT_TIMESTAMP where pos.id=?1", nativeQuery = true)
    void resetRemainValueByPosId(Long posId);

    @Query(value = "select m.* from les_material m join les_position pos on pos.device_instance_id=m.device_instance_id and pos.object_name=m.name where pos.id= ?1", nativeQuery = true)
    Optional<Material> findMaterialByPosId(Long posId);
}