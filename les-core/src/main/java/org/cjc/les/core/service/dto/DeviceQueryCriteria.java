/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.annotation.Query;
import org.cjc.les.constants.DeviceTypeEnum;

import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-07
**/
@Data
public class DeviceQueryCriteria {

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String name;

    /** 类型 */
    @Query(type = Query.Type.EQUAL)
    private DeviceTypeEnum type;

    /** 创建时间 */
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime;
}