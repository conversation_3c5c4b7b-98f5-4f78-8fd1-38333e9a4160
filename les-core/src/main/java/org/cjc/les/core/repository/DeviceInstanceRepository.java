package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import jakarta.transaction.Transactional;
import java.util.Optional;

/**
 * 设备ORM接口层
 */
public interface DeviceInstanceRepository extends JpaRepository<DeviceInstance, Long>, JpaSpecificationExecutor<DeviceInstance> {

    @Modifying
    @Query(value="update DeviceInstance set config=?2, mockConfig=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateConfig(Long id, String config, String mockConfig);
}