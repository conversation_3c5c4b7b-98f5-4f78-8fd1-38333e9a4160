/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Data
public class ActionCommandDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 关联ACTION */
    private Long actionId;

    /** 设备实例ID */
    private Long deviceInstanceId;

    /** 关联命令 */
    private Long commandId;

    private String parameter;

    /** 命令备注描述 */
    private String comment;

    /** 运行失败后的处理方式:CONTINUE/RETURN_ACTION/RETURN_STEP/RETURN_METHOD/RETURN_PROCEDURE */
    private String failedThen;

    /**
     * 物料消耗量
     */
    private String materialConsumption;

    /**
     * 是否可选: Y/N
     */
    private String isOption;
    /**
     * 可选项编码
     */
    private String optionCode;

    /** 状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}