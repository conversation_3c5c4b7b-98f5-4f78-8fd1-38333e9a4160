/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.service.impl.DeviceExecuteServiceImpl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Log4j2
public class DeviceProxyHelper {

    /**
     * 缓存当前注册的设备代理列表
     */
    private ConcurrentHashMap<Long, DeviceProxyWrapper> deviceHashMap = new ConcurrentHashMap<Long, DeviceProxyWrapper>();

    private static class DeviceProxyHelperHolder {
        private static DeviceProxyHelper instance = new DeviceProxyHelper();
    }

    private DeviceProxyHelper(){

    }

    public static DeviceProxyHelper getInstance(){
        return DeviceProxyHelperHolder.instance;
    }


    public DeviceProxyWrapper getOrCreateDeviceProxy(DeviceInstance deviceInstance) {
        Long deviceId = deviceInstance.getId();
        DeviceProxyWrapper dev = deviceHashMap.get(deviceId);
        if (dev != null) {
            return dev;
        }
        synchronized (DeviceExecuteServiceImpl.class) {
            if ((dev = deviceHashMap.get(deviceId)) != null) {
                return dev;
            }
            DeviceProxyWrapper createdDev = createProxy(deviceInstance);
            deviceHashMap.put(deviceId, createdDev);
            return createdDev;
        }

    }

    public DeviceProxyWrapper getDeviceProxy(DeviceInstance deviceInstance) {
        Long deviceId = deviceInstance.getId();
        return deviceHashMap.get(deviceId);
    }

    private DeviceProxyWrapper createProxy(DeviceInstance deviceInstance) {
        String javaClassName = deviceInstance.getDevice().getJavaClassName();
        DeviceProxyWrapper deviceProxyWrapper = new DeviceProxyWrapper(deviceInstance);

        try {
            Class clz = Class.forName(javaClassName);
            Object obj = clz.newInstance();
            deviceProxyWrapper.setDeviceProxy(obj);
         //   BeanUtil.copyProperties(deviceInstance.getDevice(), obj, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id"));
            BeanUtil.copyProperties(deviceInstance,obj, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("status","commands"));
        } catch (InstantiationException e) {
            log.error("InstantiationException exception: {}", e.getMessage(), e);
        } catch (IllegalAccessException e) {
            log.error("IllegalAccessException exception: {}", e.getMessage(), e);
        } catch (ClassNotFoundException e) {
            log.error("ClassNotFoundException exception: {}", e.getMessage(), e);
        }
        return deviceProxyWrapper;
    }

    public Collection<DeviceProxyWrapper> getAllDeviceProxies() {
        return deviceHashMap.values();
    }

    public List<DeviceProxyWrapper> getRobotDeviceProxies() {
        return deviceHashMap.values().stream().filter(deviceProxyWrapper -> {
            return "ROBOT".equalsIgnoreCase(deviceProxyWrapper.getDeviceInstance().getType());
        }).collect(Collectors.toList());
    }

}
