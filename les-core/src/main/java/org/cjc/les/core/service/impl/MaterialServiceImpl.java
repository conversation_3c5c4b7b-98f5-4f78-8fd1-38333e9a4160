/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.exception.EntityExistException;
import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.domain.Material;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.helper.RepoManagementHelper;
import org.cjc.les.core.repository.MaterialRepository;
import org.cjc.les.core.repository.PositionRepository;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.MaterialService;
import org.cjc.les.core.service.dto.MaterialDto;
import org.cjc.les.core.service.dto.MaterialQueryCriteria;
import org.cjc.les.core.service.mapstruct.MaterialMapper;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-03-26
**/
@Service
@RequiredArgsConstructor
public class MaterialServiceImpl implements MaterialService {

    private final MaterialRepository materialRepository;
    private final MaterialMapper materialMapper;

    private final AlertLogService alertLogService;

    private final RepoManagementHelper repoManagementHelper;

    private final PositionRepository positionRepository;

    @Override
    public Map<String,Object> queryAll(MaterialQueryCriteria criteria, Pageable pageable){
        Page<Material> page = materialRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);

        Map<String,Object>  out = PageUtil.toPage(page.map(materialMapper::toDto));
        Object content = out.get("content");
        if (content instanceof List){
            List<MaterialDto> outDtoList = (List<MaterialDto>)content;
            TaskScheduler taskScheduler = SpringContextHolder.getBean(TaskScheduler.class);
            Map<Long, BigDecimal> consumptionMap = taskScheduler.calculateMaterialToUseValue();
            for (MaterialDto dto : outDtoList) {
                BigDecimal val = consumptionMap.get(dto.getId());
                if (val != null) {
                    dto.setToUseValue(val);
                }
            }
        }
        return out;
    }

    @Override
    public List<MaterialDto> queryAll(MaterialQueryCriteria criteria){
        return materialMapper.toDto(materialRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<MaterialDto> queryTopByStatus(MaterialQueryCriteria criteria) {
        Long topCount = criteria.getTopCount();
        if (topCount == null) {
            topCount = 6L;
        }
        return materialMapper.toDto(materialRepository.queryTopByStatus(topCount));
    }

    @Override
    @Transactional
    public MaterialDto findById(Long id) {
        Material material = materialRepository.findById(id).orElseGet(Material::new);
        ValidationUtil.isNull(material.getId(),"Material","id",id);
        return materialMapper.toDto(material);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialDto create(Material resources) {
        if(materialRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Material.class,"name",resources.getName());
        }
        return materialMapper.toDto(materialRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Material resources) {
        Material material = materialRepository.findById(resources.getId()).orElseGet(Material::new);
        ValidationUtil.isNull( material.getId(),"Material","id",resources.getId());
        Material material1 = materialRepository.findByName(resources.getName());
        if(material1 != null && !material1.getId().equals(material.getId())){
            throw new EntityExistException(Material.class,"name",resources.getName());
        }
        material.copy(resources);
        materialRepository.save(material);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            materialRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<MaterialDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MaterialDto material : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("物料编号", material.getNumber());
            map.put("物料名称", material.getName());
            map.put("物料描述", material.getDescription());
            map.put("是否已被删除", material.getDeleteFlag());
            map.put("创建人", material.getCreateBy());
            map.put("创建时间", material.getCreateTime());
            map.put("更新人", material.getUpdateBy());
            map.put("更新时间", material.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public Material deductRemainValue(Long id, BigDecimal toDeductValue) {
        Optional<Material> materialOptional = materialRepository.findById(id);
        if (!materialOptional.isPresent()) {
            throw new TaskRunningException("Could not found material by id:"+id);
        }
        Material material = materialOptional.get();
        material.setRemainValue(material.getRemainValue().subtract(toDeductValue));
        Material rs = materialRepository.save(material);
        // 检测物料告警
        checkAlert(material);

        return rs;
    }

    private void checkAlert(Material material) {
        AlertLog alertLog = new AlertLog();
        alertLog.setAlertCategory("MATERIAL");
        alertLog.setAlertSourceId(material.getId());
        alertLog.setAlertLevel(AlertLog.AlertLevelEnum.WARN.name());
        alertLog.setMessage(material.getName() +"物料不足,请及时补充");
        alertLog.setAlertCode("MATERIAL_NOT_ENOUGH");
        alertLog.setAlertName(material.getName() +"物料不足");

        if (material.getRemainValue().subtract(material.getWarnValue()).longValue()>0L ){
            alertLog.setFixedBy("System");
            alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
            alertLogService.fixLog(alertLog);
            return;
        }

        alertLogService.createOrUpdate(alertLog);
    }

    @Override
    public Optional<Position> queryIndicatorPosition(Material material) {
        Optional<Position> posOpt = positionRepository.findPositionByDeviceInstanceIdAndNameAndType(material.getDeviceInstanceId(), material.getName(), "INDICATOR");
        if (posOpt.isPresent()){
            Position pos = posOpt.get();
            pos.setMaterialTotalValue(material.getTotalValue());
            pos.setMaterialRemainValue(material.getRemainValue());
            pos.setMaterialUnit(material.getUnit());
            return posOpt;
        }
        return Optional.empty();
    }

    @Override
    public void reset(Material resources) {
        resources.setRemainValue(resources.getTotalValue());
        update(resources);
        if (resources.getDeviceInstanceId() != null) {
            repoManagementHelper.resetAllPositionsByDeviceInstanceId(resources.getDeviceInstanceId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetRemainValueByPosId(Long posId) {
        Optional<Material> materialOptional = materialRepository.findMaterialByPosId(posId);
        if (!materialOptional.isPresent()) {
            return;
        }
        Material material = materialOptional.get();
        material.setRemainValue(material.getTotalValue());
        material.setStatus(Material.StatusEnum.NORMAL);
        Material rs = materialRepository.save(material);
        // 检测物料告警
        checkAlert(material);

    }
}