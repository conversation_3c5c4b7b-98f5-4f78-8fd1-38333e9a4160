/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.domain.DeviceLayout;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-10-14
**/
public interface AlertLogRepository extends JpaRepository<AlertLog, Long>, JpaSpecificationExecutor<AlertLog> {

    @Query(value = "select log.* from les_alert_log log where log.fix_status = 'INIT' order by log.update_time desc limit 100", nativeQuery = true)
    List<AlertLog> findLatestAlertLogInInitStatus();

    @Query(value = "select log.alert_level, count(1) from les_alert_log log where log.fix_status = 'INIT' group by log.alert_level", nativeQuery = true)
    List<Object[]> findAlertLogStatsLevelInInitStatus();

    List<AlertLog> findByAlertCategoryAndAlertSourceIdAndFixStatus(String alertCategory, Long alertSourceId, String fixStatus);
}