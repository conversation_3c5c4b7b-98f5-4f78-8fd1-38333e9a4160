/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.util;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

public class SerialNumberGenerator {
    private static final AtomicInteger counter = new AtomicInteger(1);
    private static final int MAX_SEQ = 9999;

    public static String generateTaskSerialNumber() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedDate = now.format(formatter);

        String sequence = getNextSequence();

        return "T" + formattedDate + sequence;
    }

    private static String getNextSequence() {
        int seq = counter.getAndIncrement();
        if (seq > MAX_SEQ) {
            counter.set(1);
            seq = counter.getAndIncrement();
        }
        return String.format("%04d", seq);
    }
}
