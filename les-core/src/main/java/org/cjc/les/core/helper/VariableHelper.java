/*
 *  Copyright 2024-2024 <PERSON>han Annis Robot Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.service.ProcedureService;
import org.cjc.les.core.service.ProcedureVariableService;
import org.cjc.les.core.service.dto.ProcedureVariableDto;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.VariablePlaceholderResolver;
import org.cjc.les.core.task.execute.VariableValueWrapper;
import org.cjc.les.core.vo.ConfigItemVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 变量帮助类
 */
public class VariableHelper {

    private static final Object setVarLock = new Object();

    /**
     * 在TaskExecutorContext中获取变量封装对象
     *
     * @param varName
     * @return
     */
    public static VariableValueWrapper getVar(String varName) {
        TaskExecutorContext context = TaskExecutorContext.getContext();
        if (context == null) {
            return null;
        }
        return context.getVariableMap().get(varName);
    }

    /**
     * 在TaskExecutorContext中获取变量原始值
     * 当获取值类型为List时，获取最后一个元素
     *
     * @param varName
     * @return
     */
    public static Object getVarAsObject(String varName) {
        VariableValueWrapper v = getVar(varName);
        if (v == null) {
            return null;
        }

        Object value = v.getValue();
        if (value instanceof List) {
            List objValueList = (List) value;
            if (CollectionUtils.isEmpty(objValueList)) {
                return null;
            }
            Object firstObj = objValueList.get(0);

            if (!(firstObj instanceof VariableValueWrapper)) {
                return value; // 不是VariableValueWrapper类型，直接返回
            }

            List<VariableValueWrapper> valueList = (List<VariableValueWrapper>) value;

            TaskMethod taskMethod = TaskExecutorContext.getCurTaskMethod();
            if (taskMethod == null) {
                return null;
            }
            List<Object> oList = valueList.stream().filter(wrapper -> {
                        return taskMethod.getId().equals(wrapper.getTaskMethodId());
                    })
                    .map(VariableValueWrapper::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(oList)) {
                return null;
            }
            value = oList.get(oList.size() - 1);
        }
        return value;
    }

    /**
     * 获取变量值并转换为List
     *
     * @param varName
     * @return
     */
    public static List getVarAsList(String varName) {
        VariableValueWrapper v = getVar(varName);
        if (v == null) {
            return null;
        }

        Object value = v.getValue();
        if (value instanceof List) {
            List objValueList = (List) value;
            if (CollectionUtils.isEmpty(objValueList)) {
                return null;
            }
            Object firstObj = objValueList.get(0);

            if (!(firstObj instanceof VariableValueWrapper)) {
                return objValueList; // 不是VariableValueWrapper类型，直接返回
            }

            List<VariableValueWrapper> valueList = (List<VariableValueWrapper>) value;

            TaskMethod taskMethod = TaskExecutorContext.getCurTaskMethod();
            if (taskMethod == null) {
                return null;
            }
            List<Object> oList = valueList.stream().filter(wrapper -> {
                        return taskMethod.getId().equals(wrapper.getTaskMethodId());
                    })
                    .map(VariableValueWrapper::getValue).collect(Collectors.toList());
            return oList;
        }
        List outList = new ArrayList();
        outList.add(value);
        return outList;
    }

    /**
     * 在TaskExecutorContext中获取变量值并转换为String
     * 当获取值类型为List时，获取最后一个元素，否则直接toString转换
     *
     * @param varName
     * @return
     */
    public static String getVarAsString(String varName) {
        Object value = getVarAsObject(varName);
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    public static void addVar(String varName, Object value) {
        TaskExecutorContext context = TaskExecutorContext.getContext();
        if (context == null) {
            return;
        }
        addVar(varName, value, TaskExecutorContext.getContext().getVariableMap());
    }

    public static void addVar(String varName, Object value, ConcurrentHashMap<String, VariableValueWrapper> varMap) {

        VariableValueWrapper varWrapper = varMap.get(varName);
        if (varWrapper == null) {
            varWrapper = new VariableValueWrapper();
            varWrapper.setValue(new ArrayList<VariableValueWrapper>());
            varMap.put(varName, varWrapper);
        }

        List<VariableValueWrapper> listValues = null;
        if (!(varWrapper.getValue() instanceof List)) {
            listValues = new ArrayList<>();
            listValues.add(varWrapper);
            varWrapper = new VariableValueWrapper();
            varWrapper.setValue(listValues);
            varMap.put(varName, varWrapper);
        }else{
            listValues = (List<VariableValueWrapper>) varWrapper.getValue();
        }


        if (listValues == null) {
            throw new TaskRunningException("Value is null of " + varName + ", toAdd value=" + value);
        }
        VariableValueWrapper varItemWrapper = new VariableValueWrapper();
        varItemWrapper.setValue(value);
        TaskBaseEntity taskBaseEntity = getCurTaskBaseEntity();
        if (taskBaseEntity != null) {
            fillVarWrapper(varItemWrapper, taskBaseEntity);
        }
        listValues.add(varItemWrapper);
    }

    /**
     * 设置变量
     *
     * @param varName      变量名
     * @param value        变量值
     * @param context      上下文
     */
    public static void setVar(String varName, Object value, TaskExecutorContext context) {
        if (StringUtils.isEmpty(varName)) {
            return;
        }
        VariableValueWrapper varWrapper = new VariableValueWrapper();
        varWrapper.setValue(value);
        TaskBaseEntity taskBaseEntity = getCurTaskBaseEntity();
        if (taskBaseEntity != null) {
            fillVarWrapper(varWrapper, taskBaseEntity );
        }

        setVarInner(varName, varWrapper, context);
    }

    private static TaskBaseEntity getCurTaskBaseEntity() {
        TaskCommand taskCommand = TaskExecutorContext.getCurTaskCommand();
        if (taskCommand != null) {
            return taskCommand;
        }
        TaskAction taskAction = TaskExecutorContext.getCurTaskAction();
        if (taskAction != null) {
            return taskAction;
        }
        TaskStep taskStep = TaskExecutorContext.getCurTaskStep();
        if (taskStep != null) {
            return taskStep;
        }
        TaskMethod taskMethod = TaskExecutorContext.getCurTaskMethod();
        if (taskMethod != null) {
            return taskMethod;
        }
        Task task = TaskExecutorContext.getCurTask();
        if (task != null) {
            return task;
        }
        return null;
    }

    /**
     * 设置变量
     *
     * @param varName 变量名
     * @param value   变量值
     */
    public static void setVar(String varName, Object value) {
        TaskExecutorContext context = TaskExecutorContext.getContext();
        setVar(varName, value, context);
    }
    /**
     * 设置变量
     *
     * @param varName        变量名
     * @param value          变量值
     * @param taskBaseEntity 任务节点
     */
    public static void setVar(String varName, Object value, TaskBaseEntity taskBaseEntity) {

        int settingLevel = getSettingLevel(taskBaseEntity);
        setVar(varName, value, taskBaseEntity, settingLevel);
    }
    /**
     * 设置变量
     *
     * @param varName        变量名
     * @param value          变量值
     * @param taskBaseEntity 任务节点
     */
    public static void setVar(String varName, Object value, TaskBaseEntity taskBaseEntity, int settingLevel) {
        if (StringUtils.isEmpty(varName)) {
            return;
        }

        VariableValueWrapper varWrapper = new VariableValueWrapper();
        varWrapper.setValue(value);

        fillVarWrapper(varWrapper,taskBaseEntity, settingLevel);

        TaskExecutorContext context = TaskExecutorContext.getContext();

        setVarInner(varName, varWrapper, context);
    }

    private static void fillVarWrapper(VariableValueWrapper varWrapper, TaskBaseEntity taskBaseEntity) {
        fillVarWrapper(varWrapper, taskBaseEntity, getSettingLevel(taskBaseEntity));
    }

    private static void fillVarWrapper(VariableValueWrapper varWrapper, TaskBaseEntity taskBaseEntity, int settingLevel) {

        varWrapper.setSettingLevel(settingLevel);

        if (taskBaseEntity instanceof TaskCommand) {
            TaskCommand taskCommand = (TaskCommand) taskBaseEntity;
            varWrapper.setTaskId(taskCommand.getTaskId());
            varWrapper.setTaskMethodId(taskCommand.getTaskMethodId());
            varWrapper.setTaskStepId(taskCommand.getTaskStepId());
            varWrapper.setTaskActionId(taskCommand.getTaskAction().getId());
            varWrapper.setTaskCommandId(taskBaseEntity.getId());
        } else if (taskBaseEntity instanceof TaskAction) {
            TaskAction taskAction = (TaskAction) taskBaseEntity;
            varWrapper.setTaskId(taskAction.getTaskId());
            varWrapper.setTaskMethodId(taskAction.getTaskMethodId());
            varWrapper.setTaskStepId(taskAction.getTaskStep().getId());
            varWrapper.setTaskActionId(taskBaseEntity.getId());
        } else if (taskBaseEntity instanceof TaskStep) {
            TaskStep taskStep = (TaskStep) taskBaseEntity;
            varWrapper.setTaskId(taskStep.getTaskId());
            varWrapper.setTaskMethodId(taskStep.getTaskMethod().getId());
            varWrapper.setTaskStepId(taskBaseEntity.getId());
        } else if (taskBaseEntity instanceof TaskMethod) {
            TaskMethod taskMethod = (TaskMethod) taskBaseEntity;
            varWrapper.setTaskId(taskMethod.getTask().getId());
            varWrapper.setTaskMethodId(taskBaseEntity.getId());
        } else if (taskBaseEntity instanceof Task) {
            Task task = (Task) taskBaseEntity;
            varWrapper.setTaskId(taskBaseEntity.getId());
        }
    }

    private static void setVarInner(String varName, VariableValueWrapper varWrapper, TaskExecutorContext context) {
        synchronized (setVarLock) {

            VariableValueWrapper existVarWrapper = context.getVariableMap().get(varName);
            if (existVarWrapper == null || varWrapper.getTaskMethodId() == 0L) {
                context.getVariableMap().put(varName, varWrapper);
                return;
            }

            // 相同TaskMethodId直接替换
            if (existVarWrapper.getTaskMethodId() == varWrapper.getTaskMethodId()) {
                context.getVariableMap().put(varName, varWrapper);
                return;
            }
            // 不相同TaskMethodId时追加列表

            Object existValue = existVarWrapper.getValue();
            if (isVarWrapperList(existValue)) {
                List<VariableValueWrapper> valueList = (List<VariableValueWrapper>) existValue;
                for (int i = 0; i < valueList.size(); i++) {
                    VariableValueWrapper wrapper = valueList.get(i);
                    if (wrapper.getTaskMethodId() == varWrapper.getTaskMethodId()) {
                        valueList.set(i, varWrapper);
                        return;
                    }
                }
                valueList.add(varWrapper);
                return;
            }

            VariableValueWrapper newWrapper = new VariableValueWrapper();
            List<VariableValueWrapper> newList = new ArrayList<>();
            newWrapper.setValue(newList);
            newList.add(existVarWrapper);
            newList.add(varWrapper);
            context.getVariableMap().put(varName, newWrapper);
        }
    }

    private static boolean isVarWrapperList(Object valueObj) {
        if (valueObj instanceof List) {
            List objValueList = (List) valueObj;
            if (CollectionUtils.isEmpty(objValueList)) {
                return false;
            }
            Object firstObj = objValueList.get(0);

            if (firstObj instanceof VariableValueWrapper) {
                return true;
            }
        }
        return false;
    }

    /**
     * 设置节点变量, settingLevel>=已存在的变量设置时，可以被覆盖，否则不覆盖
     *
     * @param variables
     * @param taskBaseEntity 0:Command, 1:Action, 2:Step, 3:Method, 4:Task
     */
    public static void setNodeVariables(String variables, TaskBaseEntity taskBaseEntity) {
        if (StringUtils.isEmpty(variables)) {
            return;
        }
        int settingLevel = getSettingLevel(taskBaseEntity);
        JSONObject jsonObj = JSON.parseObject(variables);
        for (String key : jsonObj.keySet()) {
            Object value = jsonObj.get(key);
            VariableValueWrapper varWrapper = getVar(key);
            if (varWrapper != null && varWrapper.getSettingLevel() > settingLevel) {
                continue;
            }
            if (value == null) {
                continue;
            }
            Optional<ConfigItemVo> opt = VariablePlaceholderResolver.find(value.toString());
            if (opt.isPresent()) {
                ConfigItemVo item = opt.get();
                VariableValueWrapper itemVarWrapper = getVar(item.getKey());
                if (itemVarWrapper != null && itemVarWrapper.getSettingLevel() > settingLevel) {
                    setVar(key, itemVarWrapper.getValue(), taskBaseEntity);
                    continue;
                }

                setVar(item.getKey(), item.getDefaultValue(), taskBaseEntity);
                setVar(key, item.getDefaultValue(), taskBaseEntity);

            } else {
                setVar(key, value, taskBaseEntity);
            }

        }
    }

    /**
     * 初始化任务变量
     * @param task
     */
    public static void initTaskVariables(Task task) {
        Procedure proc = task.getProcedure();
        if (proc == null) {
            ProcedureService procedureService = SpringContextHolder.getBean(ProcedureService.class);
            proc = procedureService.findProcedureById(task.getProcedureId());
            task.setProcedure(proc);
        }
        if (StringUtils.equalsAny(proc.getType(),"DERIVE")){
            ProcedureVariableService procedureVariableService = SpringContextHolder.getBean(ProcedureVariableService.class);
            for (TaskMethod taskMethod : task.getMainMethodList()) {
                Long rawProcId = taskMethod.getDeriveProcId();
                List<ProcedureVariableDto> varDtoList = procedureVariableService.queryAllUserSettingsBYProcId(rawProcId);
                varDtoList.forEach(vo->{
                    setVar(vo.getName(), vo.getValue(), taskMethod, 4);
                });
            }

        }else {
            task.getProcedure().getVariables().forEach(vo -> {
                if (StringUtils.equalsAny("Y", vo.getVariable().getValueSetByUser())) {
                    setVar(vo.getVariable().getName(), vo.getValue(), task);
                }
            });
        }
    }

    private static int getSettingLevel(TaskBaseEntity taskBaseEntity) {
        if (taskBaseEntity instanceof TaskCommand) {
            return 0;
        } else if (taskBaseEntity instanceof TaskAction) {
            return 1;
        } else if (taskBaseEntity instanceof TaskStep) {
            return 2;
        } else if (taskBaseEntity instanceof TaskMethod) {
            return 3;
        } else if (taskBaseEntity instanceof Task) {
            return 4;
        } else {
            return 0;
        }
    }

}
