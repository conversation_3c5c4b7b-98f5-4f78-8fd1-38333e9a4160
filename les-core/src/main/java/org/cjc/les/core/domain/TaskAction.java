/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_task_action")
public class TaskAction extends TaskBaseEntity {

    @Column(name = "name")
    @Schema(description = "命令名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "命令描述")
    private String description;
    /**
     * 该动作所属的工作站
     */
    @JSONField(serialize = false)
    @OneToOne
    @JoinColumn(name = "station_id")
    @Schema(description = "工作站")
    private Station station;

    private String message;

    /**
     * 异步模式，默认同步模式运行
     */
    private String asyncMode;

    /**
     * 环境变量
     */
    private String variables;

    /**
     * 谓词表达式
     */
    private String predicates;
    /**
     * 当预置条件不满足时处理方式：WAIT,SKIP
     */
    private String unmatchedThen;

    @Column(name = "task_id")
    @Schema(description = "任务ID")
    private Long taskId;

    @Column(name = "task_method_id")
    @Schema(description = "任务方法ID")
    private Long taskMethodId;

    @Column(name = "action_id")
    @Schema(description = "配置的动作ID")
    private Long actionId;

    private String dagNodeImage;


    private Long evaluateExecutingStart = 0L;
    private Long evaluateExecutingDuration = 0L;
    private Timestamp evaluatedTime;

    private Long executedStart;
    private Long executedDuration;

    /**
     * 进入队列的时间
     */
    transient  private Timestamp enterQueueTime;

    transient  private ExecutorConfig executorConfig;

    transient private Thread thread;

    @Column(name = "delete_flag")
    @Schema(description = "是否已被删除,Y/N")
    private String deleteFlag;

    @JSONField(serialize = false)
    @JoinColumn(name = "task_step_id", referencedColumnName = "id")
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = TaskStep.class)
    @Schema(description = "任务步骤", hidden = true)
    private TaskStep taskStep;

    @OneToMany(mappedBy = "taskAction", targetEntity = TaskCommand.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<TaskCommand> commands = new ArrayList<>();

    public void copy(TaskAction source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
    /**
     * 从方法定义中拷贝
     * @param action
     * @param isDeep 是否深拷贝，默认false
     */
    public void copy(StepAction action, boolean isDeep){
        BeanUtil.copyProperties(action,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","commands"));
        if (ObjectUtils.isNotEmpty(action.getAction())) {
            BeanUtil.copyProperties(action.getAction(),this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","commands"));
        }
        this.setActionId(action.getAction().getId());

        this.setStation(action.getAction().getStation());

        if (isDeep) {
            commands.clear();
            for (ActionCommand command : action.getAction().getCommands()) {
                TaskCommand taskCommand = new TaskCommand();
                taskCommand.copy(command);
                taskCommand.setTaskAction(this);
                commands.add(taskCommand);
            }
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.getId())
                .append("name", name)
                .append("description", description)
                .append("stationId", this.getStation().getId())
                .append("status", this.getStatus())
                .append("taskId", taskId)
                .append("taskMethodId", taskMethodId)
                .append("actionId", actionId)
                .append("deleteFlag", deleteFlag)
                .toString();
    }
}