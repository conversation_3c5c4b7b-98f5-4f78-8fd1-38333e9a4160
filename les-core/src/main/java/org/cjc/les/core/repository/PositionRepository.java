/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.Position;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface PositionRepository extends JpaRepository<Position, Long>, JpaSpecificationExecutor<Position> {

    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.holdByTaskId=?2 and pos.status=?3 and pos.type in ('DEFAULT','BOARD')")
    List<Position> findMatchedPositions(DeviceInstance deviceInstance, Long holdTaskId, Position.StatusEnum status);

    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.status='IDLE' and pos.type in ('DEFAULT','BOARD') order by pos.id asc ")
    List<Position> findMatchedPositionsIdle(DeviceInstance deviceInstance, String positionName);
    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.boardNodeId=?3 and pos.status='IDLE' and pos.type in ('DEFAULT','BOARD') order by pos.id asc ")
    List<Position> findMatchedPositionsIdle(DeviceInstance deviceInstance, String positionName, String boardNodeId);

    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.status='READY' and pos.type in ('DEFAULT','BOARD') order by pos.updateTime asc")
    List<Position> findMatchedPositionsReady(DeviceInstance deviceInstance, String positionName);
    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.boardNodeId=?3 and pos.status='READY' and pos.type in ('DEFAULT','BOARD') order by pos.updateTime asc")
    List<Position> findMatchedPositionsReady(DeviceInstance deviceInstance, String positionName, String boardNodeId);

    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.holdByTaskId=?3  and pos.status=?4  and pos.type in ('DEFAULT','BOARD') and pos.holdLevel='TASK' order by pos.id asc ")
    List<Position> findMatchedPositionsInTask(DeviceInstance deviceInstance, String positionName, Long holdTaskId, Position.StatusEnum status);
    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.holdByTaskId=?3  and pos.status=?4 and pos.boardNodeId=?5  and pos.holdLevel='TASK' and pos.type in ('DEFAULT','BOARD') order by pos.id asc ")
    List<Position> findMatchedPositionsInTask(DeviceInstance deviceInstance, String positionName, Long holdTaskId, Position.StatusEnum status, String boardNodeId);

    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.holdByTaskMethodId=?3  and pos.status=?4  and (pos.holdLevel='METHOD' or pos.holdLevel is null) and pos.type in ('DEFAULT','BOARD') order by pos.id asc ")
    List<Position> findMatchedPositionsInMethod(DeviceInstance deviceInstance, String positionName, Long holdTaskMethodId, Position.StatusEnum status);
    @Query(value="select pos from Position as pos where pos.deviceInstance=?1 and pos.name=?2 and pos.holdByTaskMethodId=?3  and pos.status=?4 and pos.boardNodeId=?5  and (pos.holdLevel='METHOD' or pos.holdLevel is null) and pos.type in ('DEFAULT','BOARD') order by pos.id asc ")
    List<Position> findMatchedPositionsInMethod(DeviceInstance deviceInstance, String positionName, Long holdTaskMethodId, Position.StatusEnum status, String boardNodeId);

    /**
     * 在给定的点位中，查找绑定的任务点位
     * @param posIds
     * @param holdTaskId
     * @return
     */
    @Query(value="select pos from Position as pos where pos.id in ?1 and pos.holdByTaskId=?2 ")
    Optional<Position> findMatchedPositionByIDsAnAndHoldByTaskId(Set<Long> posIds, Long holdTaskId);

    @Query(value = "select pos.* from les_task_command cmd join les_position pos on pos.id=cmd.selected_device_pos_id where cmd.task_method_id = ?1 order by cmd.id desc limit 1", nativeQuery = true)
    Optional<Position> findLastPositionByMethodId(Long mthId);

    @Modifying
    @Query(value="update Position set status=?2, holdByTaskId=?3, holdByTaskMethodId=?4, holdByTaskStepId=?5, holdByTaskActionId=?6, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updatePositionStatus(Long positionId, Position.StatusEnum status, Long taskId, Long taskMethodId, Long taskStepId, Long taskActionId);

    @Modifying
    @Query(value="update Position set status= initStatus, updateTime=CURRENT_TIMESTAMP where id=?1")
    void resetPositionStatus(Long positionId);

    @Modifying
    @Query(value="update les_position pos set pos.status = pos.init_status, update_time=CURRENT_TIMESTAMP where pos.name =?1 and pos.delete_flag='N'", nativeQuery = true)
    void resetPositionStatusWithAssociations(String positionName);

    @Modifying
    @Query(value="update les_position pos set pos.status = 'TO_CONFIRM', update_time=CURRENT_TIMESTAMP where pos.type in ('INDICATOR','CONTROLLER') and pos.delete_flag='N'", nativeQuery = true)
    void resetAllIndicatorsToConfirm();

    @Query(value="select pos from Position as pos where pos.status = ?1 and pos.deleteFlag='N' ")
    List<Position> findAllByStatus(Position.StatusEnum status);

    @Modifying
    @Query(value="update les_position pos set pos.status = pos.init_status, update_time=CURRENT_TIMESTAMP where pos.type in ('INDICATOR','CONTROLLER') and pos.delete_flag='N'", nativeQuery = true)
    void resetAllIndicatorsToInit();


    @Query(value = "select pos.* from les_position pos where pos.board_node_id=?1 and pos.type='DEFAULT' order by pos.device_pos_index asc ", nativeQuery = true)
    List<Position> findPositionsByBoardNodeId(String nodeId);

    @Query(value = "select pos.* from les_position pos where pos.board_node_id=?1 order by pos.device_pos_index asc ", nativeQuery = true)
    List<Position> findAllPositionsByBoardNodeId(String nodeId);

    @Query(value = "select pos.* from les_position pos where pos.device_instance_id=?1 order by pos.device_pos_index asc ", nativeQuery = true)
    List<Position> findAllPositionsByDeviceInstanceId(Long deviceInstanceId);

    Optional<Position> findPositionByName(String name);

    List<Position> findAllByName(String name);

    @Query(value = "select pos.status from les_position pos where pos.device_instance_id=?1 and pos.name=?2  and pos.delete_flag='N' limit 1 ", nativeQuery = true)
    String findPositionStatus(Long deviceInstanceId, String posName);

    @Query(value = "select pos.* from les_position pos where pos.device_instance_id=?1 and pos.name=?2 and pos.delete_flag='N' limit 1 ", nativeQuery = true)
    Optional<Position> findFirstPosition(Long deviceInstanceId, String posName);

    @Query(value = "select distinct pos.name, pos.type  from les_position pos where pos.delete_flag='N' and pos.type in ('DEFAULT','BOARD') order by pos.name asc", nativeQuery = true)
    List<Map> findPositionNamesForSelection();

    @Query(value = "select pos.* from les_position pos where pos.device_instance_id=?1 and pos.object_name=?2 and pos.type=?3 and pos.delete_flag='N' limit 1 ", nativeQuery = true)
    Optional<Position> findPositionByDeviceInstanceIdAndNameAndType(Long deviceInstanceId, String objectName, String type);
}
