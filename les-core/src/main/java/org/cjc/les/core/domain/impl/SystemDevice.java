/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.schedule.SimpleMainScheduler;
import org.cjc.les.core.schedule.SampleExitScheduler;
import org.cjc.les.core.service.DeviceExecuteService;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.RedisUtils;
import org.cjc.utils.SpringContextHolder;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 系统设备,虚拟设备，用来保存该软件本身固有的功能特性
 */
@Data
@Log4j2
@org.cjc.les.annotation.Device(value = "系统", type = DeviceTypeEnum.SYS)
public class SystemDevice extends Device {

    private boolean sampleExistAndUnusable = false;

    @org.cjc.les.annotation.Variable(name = "TASK_ID", type = "String", scope = "SYS", tip = "任务执行ID")
    @Override
    public boolean doInit() {
        RedisUtils redisUtils = SpringContextHolder.getBean(RedisUtils.class);
        redisUtils.delKeysByPattern("lock.*");
        return true;
    }

    @Override
    public void doFinalize() {
        // Do nothing
    }

    public boolean checkConnection(){

        return true;
    }

    /**
     * 系统等待，
     *
     * @param milliSec 毫秒数
     */
    @Command(value = "等待", description = "等待时间，单位毫秒")
    public void waitTime(@Parameter("等待时长(ms)") Long milliSec) {
        try {
            Thread.sleep(milliSec);
        } catch (InterruptedException e) {
            log.error("Sleep exeception:{}", e.getMessage(), e);
            throw new TaskRunningException("InterruptedException occurred");
        }
    }

    /**
     * 系统选择点位, 该指令本身不执行任务动作，可通过该指令配合辅助参数变更点位状态等操作
     * @param arg
     */
    @Command(value = "系统选择点位", description = "选取设备点位, 可通过该指令配合辅助参数变更点位状态等操作")
    public void selectPosition(@Parameter(name = "选择点位", inputComponent = "position-select") Position arg){
        // Do nothing
    }

    @Command(value = "通过RF标签创建进程", description = "通过RF标签创建进程")
    public void createProcedureByRfTag() {
        String rfTag = TaskExecutorContext.getVarAsString("RF_TAG");

        // 样品对象，实际实现先查数据库
        Sample sample = new Sample();
        sample.setNumber(rfTag);
        sample.setName("测试样品");

        // 创建产品
        Result result = new Result();
        result.setName(sample.getName());
        Date now = new Date();
        // String strNow = new SimpleDateFormat("YYYYMMDDhh24miss").format(now);
        result.setNumber(sample.getNumber()+"-"+0);
        result.setSample(sample);
        Procedure procedure = parseProcedureFromConfigFile();
        procedure.setResult(result);
    }


    private Procedure parseProcedureFromConfigFile(){
        File configFile = new File("test_moisture_checking_procedure.json");
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(configFile);
            Procedure proc = JSON.parseObject(fis, Procedure.class);
            appendDeviceForAllCommands(proc);
            return proc;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }

        return null;
    }
    private void appendDeviceForAllCommands(Procedure proc) throws ClassNotFoundException {
        DeviceExecuteService deviceExecuteService = SpringContextHolder.getBean(DeviceExecuteService.class);
        List<ProcedureMethod> methods = new ArrayList<>();
        methods.add(proc.getEntryMethod().get());
        methods.addAll(proc.getMethods());
        methods.add(proc.getExitMethod().get());

        for (ProcedureMethod method : methods) {
            for (MethodStep step : method.getMethod().getSteps()) {
                for (StepAction action : step.getStep().getActions()) {
                    for (ActionCommand actionCommand : action.getAction().getCommands()) {
                        org.cjc.les.core.domain.Command command = null;// actionCommand.getCommand();
                        if (command.getDeviceId() ==null){
                            continue;
                        }
                        command.setDevice(deviceExecuteService.getDevice(command.getDeviceId()));

                        Object objParameter = command.getParameter();
                        if (objParameter instanceof JSONObject && StringUtils.isNotEmpty(command.getParameterType())){
                            Class<?> clz = this.getClass().getClassLoader().loadClass( command.getParameterType());
                            objParameter = JSON.parseObject( JSON.toJSONString(objParameter) , clz);
                            command.setParameter(objParameter);
                        }

                    }
                }
            }
        }
        //

    }

    /**
     * 把当前进程加入到排程队列, 并进行数据持久化（通常为数据库记录，用户可以在界面实时观察调度进展）
     * 进程状态为TO_SCHEDULE(等待调度)
     */
/*    @Command(value = "加入到排程队列", description = "把当前进程加入到排程队列")
    public void addToScheduleQueue() {
        // TODO 当前执行的Procedure增加到数据表
        sampleExistAndUnusable = true;
        waitTime(2000L);
        // 推送该进程至主体方法线程池运行调度
        SimpleMainScheduler scheduler = SpringContextHolder.getBean(SimpleMainScheduler.class);
        scheduler.push(CommandContext.getProcedure());

        waitTime(2000L);

        SampleExitScheduler exitScheduler = SpringContextHolder.getBean(SampleExitScheduler.class);
        exitScheduler.push(CommandContext.getProcedure());
    }*/

    /**
     * 检查样品架所在位置已存在样品且不再被该进程使用， 当已放样，且不再被进程使用时，返回true，否则false
     * @return
     */
    @Command("检查样品架所在位置已存在样品且不再被该进程使用")
    public boolean checkSampleUnusable(){

        Task task = TaskExecutorContext.getTask();
        if (task == null){
            return false;
        }
        List<TaskMethod> taskMethods = task.getMainMethodList();
        for (TaskMethod taskMethod : taskMethods) {
            if (!StringUtils.equals(taskMethod.getStatus(), CommandStatusEnum.SUCCESS.name())){
                return false;
            }
        }
        log.info("checkSampleUnusable OK, Exit process continue. success={}",true);
        return true;
    }

    /**
     * 检查当前任务在代理设备实例点位中的状态，Position.StatusEnum
     * @param status
     * @return
     */
    @Command(name = "检测点位状态", description = "该命令通常用于给存储设备代理使用")
    public boolean checkCurPositionStatus(@Parameter("点位状态") String status) {
        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        Task task = TaskExecutorContext.getTask();
        if (taskCommand == null || task == null){
            return false;
        }
        DeviceInstanceCmd insCmd = taskCommand.getCommand();
        DeviceInstance deviceInstance = insCmd.getDeviceInstance();

        PositionService positionService = SpringContextHolder.getBean(PositionService.class);

        List<Position> positions = positionService.findMatchedPositions(deviceInstance, task.getId(), Position.StatusEnum.valueOf(status));

        if (CollectionUtils.isNotEmpty(positions)){
            return true;
        }else{
            return false;
        }

    }

    @Command(name = "系统加锁", description = "系统加锁")
    public boolean lock(@Parameter(value = "锁参数", inputComponent = "lock-select") LockArg lockArg) {
        RedisUtils redisUtils = SpringContextHolder.getBean(RedisUtils.class);

        String lockValue = String.valueOf(Thread.currentThread().getId());
        Task task = TaskExecutorContext.getTask();
        if (task!=null){
            lockValue = task.getId().toString();
        }

        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand!=null){
            lockValue += "." + taskCommand.getTaskMethodId().toString();
        }
        boolean ret = false;
        while (!(ret = redisUtils.tryLock("lock." + lockArg.getLockId(), lockValue, lockArg.getTimeout(), TimeUnit.MILLISECONDS))) {
            waitTime(1000L);
        }

        return ret;
    }

    @Command(name = "系统解锁", description = "系统解锁")
    public boolean unlock(@Parameter(value = "锁参数", inputComponent = "lock-select") LockArg lockArg) {
        String lockId = lockArg.getLockId();

        RedisUtils redisUtils = SpringContextHolder.getBean(RedisUtils.class);

        String lockValue = String.valueOf(Thread.currentThread().getId());
        Task task = TaskExecutorContext.getTask();
        if (task!=null){
            lockValue = task.getId().toString();
        }

        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand!=null){
            lockValue += "." + taskCommand.getTaskMethodId().toString();
        }
        return redisUtils.unlock("lock." + lockId, lockValue);
    }

}
