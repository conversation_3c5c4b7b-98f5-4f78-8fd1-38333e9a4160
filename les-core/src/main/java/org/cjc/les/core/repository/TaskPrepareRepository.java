/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.domain.TaskPrepare;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-10
**/
public interface TaskPrepareRepository extends JpaRepository<TaskPrepare, Long>, JpaSpecificationExecutor<TaskPrepare> {
    /**
     * 通过RFTGAG和状态查询任务预处理列表
     *
     * @param rfTag
     * @param status
     * @return
     */
    @Query(value="select tp from TaskPrepare as tp where tp.id in ?3 and tp.rfTag=?1 and tp.status=?2 ")
    List<TaskPrepare> findByRfTagAndStatus(String rfTag, String status, Set<Long> ids);

    @Query(value="select tp from TaskPrepare as tp where tp.id in ?2 and tp.status=?1 ")
    List<TaskPrepare> findByStatusInIds(String status,  Set<Long> ids);

    @Modifying
    @Query(value = "update TaskPrepare set status=?2, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateStatus(Long id, String status);

    @Query(value="select * from les_task_prepare tp where tp.task_number in (select task_number from les_task t where t.id=?1) ", nativeQuery = true)
    TaskPrepare findTaskPrepareByTaskId(Long taskId);

    @Query(value = "SELECT  tp.rf_tag FROM les_task_prepare tp where tp.status in ('DRAFT','READY') order by tp.update_time asc", nativeQuery = true)
    List<String> findRfTagsToEdit();

    @Query(value = "select pos_index from les_task_prepare tp where tp.rf_tag = ?1 and tp.status in ('DRAFT','READY') order by tp.pos_index", nativeQuery = true)
    List<Integer> findPosIndexByRfTag(String tag);

    /**
     * 查找最近一次更新的预处理任务
     *
     * @return
     */
    @Query(value = "select * from les_task_prepare tp where tp.delete_flag='N' order by tp.create_time desc limit 1", nativeQuery = true)
    Optional<TaskPrepare> findLatestTaskPrepare();
}