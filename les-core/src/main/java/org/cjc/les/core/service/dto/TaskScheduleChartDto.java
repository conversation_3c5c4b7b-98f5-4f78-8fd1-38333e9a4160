package org.cjc.les.core.service.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务排程图数据对象
 */
@Data
public class TaskScheduleChartDto {
    /**
     * 任务定义，用来展示YAxis
     * "dimensions": [
     *             "id", // ID
     *             "name", // 名称
     *             "type", // TASK,METHOD,STEP
     *             "progress" // 当前进度,0-100
     *         ],
     *         "data": [
     *             [
     *                 1,
     *                 "**任务",
     *                 "TASK",
     *                 0
     *             ]
     *         ]
     */
    private Map<String, Object> taskdefs = new HashMap<>();
    /**
     * 任务数据项，用来展示数据
     * "dimensions": [
     *             "parentId", // 父项ID
     *             "id", //当前项ID
     *             "type", // METHOD,STEP,ACTION
     *             "name", // 当前项名称
     *             "status", // 当前项运行状态
     *             "progress", // 当前项进度,0-100
     *             "startTime", // 开始时间点
     *             "endTime", // 结束时间点
     *         ],
     *         "data": [
     *             [
     *                 "**任务",
     *                 "TASK",
     *                 0
     *             ]
     *         ]
     *
     */
    private Map<String, Object> taskItems = new HashMap<>();

}
