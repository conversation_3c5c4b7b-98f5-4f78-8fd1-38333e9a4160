package org.cjc.les.core.util;

import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.helper.FormulaExecutionHelper;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.utils.SpringContextHolder;

public class AlertUtil {

    private static AlertLogService alertLogService;
    static {
        alertLogService = SpringContextHolder.getBean(AlertLogService.class);
    }

    public static void createOrUpdate(AlertLog alertLog) {
        alertLogService.createOrUpdate(alertLog);
    }

    public static void clear(AlertLog alertLog) {
        alertLogService.fixLog(alertLog);
    }

}
