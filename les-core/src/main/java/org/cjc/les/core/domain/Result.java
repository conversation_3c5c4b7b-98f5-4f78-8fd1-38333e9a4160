/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 检测结果
 */
@Entity
@Data
@Table(name="les_result")
public class Result extends LesBaseEntity {

    /**
     * 结果名称
     */
    @Column(name = "name")
    @Schema(description = "检测结果名称")
    private String name;

    /**
     * 检测结果编码
     */
    @Column(name = "number")
    @Schema(description = "检测结果编码")
    private String number;

    /**
     * 检测的样品
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "sample_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = Sample.class)
    @Schema(description = "样品", hidden = true)
    private Sample sample;

    /**
     * 检测任务
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "task_id", referencedColumnName = "id")
    @OneToOne
    @Schema(description = "任务", hidden = true)
    private Task task;

    /**
     * 检测项列表
     */
    @OneToMany(mappedBy = "result", targetEntity = ResultItem.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = false)
    private List<ResultItem> items;

    public void copy(Result source, boolean isDeep) {
        if (!isDeep) {
            copy(source);
            return;
        }
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("items"));


        if (this.getItems() == null) {
            this.setItems(new ArrayList<>());
        }

        List<ResultItem> deleteItemList = new ArrayList<>();

        for (ResultItem item : this.getItems()) {
            item.setResult(this);
            Optional<ResultItem> optOther = source.getItems().stream().filter(other -> {
                boolean b = Objects.equals(other.getId(), item.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                item.copy(optOther.get(), true);
            } else {
                deleteItemList.add(item);
            }
        }

        source.getItems().forEach(act -> {
            if (act.getId() == null) {
                this.getItems().add(act);
                act.setResult(this);
                act.getResultFeatures().forEach(feature -> {
                    feature.setResultItem(act);
                });
            }
        });

        if (CollectionUtils.isNotEmpty(deleteItemList)) {
            this.getItems().removeAll(deleteItemList);
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("name", name)
                .append("number", number)
                .append("sample", sample)
                .toString();
    }
}
