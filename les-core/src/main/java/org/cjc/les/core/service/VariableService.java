/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Variable;
import org.cjc.les.core.service.dto.VariableDto;
import org.cjc.les.core.service.dto.VariableQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-11-17
**/
public interface VariableService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(VariableQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<VariableDto>
    */
    List<VariableDto> queryAll(VariableQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return VariableDto
     */
    VariableDto findById(Long id);

    /**
     * 根据name查询
     * @param name 变量名
     * @return VariableDto
     */
    VariableDto findByName(String name);

    /**
    * 创建
    * @param resources /
    * @return VariableDto
    */
    VariableDto create(Variable resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Variable resources);

    void save(Variable resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<VariableDto> all, HttpServletResponse response) throws IOException;

    /**
     * 从流程配置中加载环境变量
     * @return
     */
    List<VariableDto> loadFromProcedureConfig();
}