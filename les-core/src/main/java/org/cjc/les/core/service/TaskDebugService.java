/*
 *  Copyright 2024-2025 <PERSON>han Annis Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.service.dto.*;
import org.springframework.data.domain.Pageable;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 任务调试服务类
 *
 * <AUTHOR>
 * @description 服务接口
 * @date 2025-03-18
 **/
public interface TaskDebugService {

    /**
     * 创建调试任务
     *
     * @param dto
     * @return
     */
    Task createTaskDebug(TaskDebugRequestDto dto);

    /**
     * 创建调试任务
     *
     * @param dto
     * @return
     */
    Task loadTaskDebug(TaskDebugRequestDto dto);

    /**
     * 设置单点调试模式
     *
     * @param task
     * @param dto
     */
    void setMonoMode(Task task, TaskDebugRequestDto dto);

    /**
     * 设置连续调试模式
     *
     * @param task
     * @param dto
     */
    void setContinueMode(Task task, TaskDebugRequestDto dto);
}