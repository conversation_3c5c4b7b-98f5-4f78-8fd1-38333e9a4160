/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Function;
import org.cjc.les.core.service.dto.FunctionDto;
import org.cjc.les.core.service.dto.FunctionQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-11-17
**/
public interface FunctionService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(FunctionQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<FunctionDto>
    */
    List<FunctionDto> queryAll(FunctionQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return FunctionDto
     */
    FunctionDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return FunctionDto
    */
    FunctionDto create(Function resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Function resources);

    void save(Function resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<FunctionDto> all, HttpServletResponse response) throws IOException;
}