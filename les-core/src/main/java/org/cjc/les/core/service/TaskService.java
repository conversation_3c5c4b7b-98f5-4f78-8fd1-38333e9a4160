/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.service.dto.*;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskQueryCriteria criteria, Pageable pageable);

    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String,Object> queryLatestTasks(TaskQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskDto>
    */
    List<TaskDto> queryAll(TaskQueryCriteria criteria);

    /**
     *
     * @param criteria
     * @return
     */
    List<TaskSmallDto> queryForSelection(TaskQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskDto
     */
    TaskDto findById(Long id);

    /**
     * 根据ID查询
     * @param taskNumber taskNumber
     * @return TaskDto
     */
    Task findByTaskNumber(String taskNumber);

    /**
    * 创建
    * @param resources /
    * @return TaskDto
    */
    TaskDto create(Task resources);

    /**
     * 通过任务预处理，只带有进样方法
     * @param taskPrepare
     * @return
     */
    Task createTaskWithEntryMethod(TaskPrepare taskPrepare);

    /**
     * 增加除进样之外的所有方法
     * @param task
     * @param taskPrepare
     * @param exitMethodOnly
     */
    void addOthersMethods(Task task, TaskPrepare taskPrepare, boolean exitMethodOnly);

    /**
    * 编辑
    * @param resources /
    */
    void update(Task resources);

    /**
     * 设置任务调度进入时间
     * @param task
     */
    void updateScheduleEntryTime(Task task);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查询排程图
     * @return
     */
    TaskScheduleChartDto querySchedulerGanttData();

    /**
     * 设置为参考时长
     * @param resources
     * @return
     */
    TaskDto setFavoriteScheduler(Task resources);

    /**
     * 获取任务执行统计
     * @return
     */
    List<TaskStatItemDto> getStatistics();

    /**
     * 获取任务执行统计
     * @return
     */
    List<TaskStatItemDto> getRunningStatistics();

    /**
     * 获取待检测样品清单
     * @param batchId 任务批次ID
     * @return
     */
    TaskToCheckSamplesReqDto getTaskToCheckSamplesReqByBatchId(Long batchId);

    /**
     * 校验TAG值
     * @param tagValue
     * @return
     */
    String validateTag(String tagValue);
}