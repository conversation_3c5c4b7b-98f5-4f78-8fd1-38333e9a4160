/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;

/**
* @website https://www.ximei.com
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class MotionInfoDto implements Serializable {

    /**
     * 机器人设备名
     */
    private String name;

    /**
     * 机器人运动状态
     */
    private String status = "IDLE";
    private String errorCode = "0";
    private String errorMsg;
    /**
     * 运动速度
     */
    private double speed = 0.00;

    private double speedFactor = 0.00;

    /**
     * 当前X轴坐标
     */
    private double xpos = 0.00;
    /**
     * 当前Y轴坐标
     */
    private double ypos = 0.00;
    /**
     * 当前Z轴坐标
     */
    private double zpos = 0.00;

    /**
     * 示教步长mm
     */
    private double stepSize = 0.01;

    /**
     * 门锁状态： -1,连接异常，
     *         3: 门打开且锁已打开
     *         2: 门打开但已上锁 (异常状态)
     *         1: 门关闭但锁已打开
     *         0: 门关闭且已上锁
     *         DOOR_BIT/LOCK_BIT: 11 DoorOpened and UnLocked, 10 DoorOpened and locked: Should NOT BE in this statues, 01 DoorClosed and Unlocked, 00 DoorClosed and locked
     */
    private int doorStatus = -1;

    /**
     * 设置是否打开
     */
    private boolean toOpenDoor;
}