/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.DeviceInstance;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-04
**/
@Data
public class DeviceLayoutDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工作站描述 */
    private String description;

    /** SVG格式的存储 */
    private String layoutSvg;

    private String styleSetting;

    /**
     * 设备实例列表
     */
    private List<DeviceInstance> deviceInstanceList;

    /** 状态:DRAFT, DEPLOY */
    private String status;

    /** 保存的更新版本 */
    private Integer version;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}