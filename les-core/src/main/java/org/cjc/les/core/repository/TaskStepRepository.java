/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.TaskStep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskStepRepository extends JpaRepository<TaskStep, Long>, JpaSpecificationExecutor<TaskStep> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    TaskStep findByName(String name);

    @Modifying
    @Query(value="update TaskStep set status=?2, message=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateStatus(Long id, String status, String message);

    @Modifying
    @Query(value="update TaskStep set executedStart=?2, executedDuration=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateExecutedTime(Long id, Long startTime, Long durationTime);
}