/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PositionMoveDto implements Serializable {
    /**
     * 机器人设备实例ID
     */
    private Long robotDevInstanceId;
    /**
     * 源点位ID
     */
    private Long sourcePositionId;
    /**
     * 目标点位ID
     */
    private Long targetPositionId;

    /**
     * 移动结果状态
     */
    private String status;
    /**
     * 结果状态描述消息
     */
    private String message;
}
