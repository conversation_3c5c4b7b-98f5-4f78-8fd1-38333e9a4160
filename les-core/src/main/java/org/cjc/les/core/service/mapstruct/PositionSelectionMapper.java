/*
*  Copyright 2024-2024 <PERSON>han Annis Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.service.dto.PositionDto;
import org.cjc.les.core.service.dto.PositionSelectDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
*
* <AUTHOR>
* @date 2025-06-06
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PositionSelectionMapper extends BaseMapper<PositionSelectDto, Position> {

}