/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.FeatureConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-16
**/
public interface FeatureConfigRepository extends JpaRepository<FeatureConfig, Long>, JpaSpecificationExecutor<FeatureConfig> {

    Optional<FeatureConfig> findByMethodIdAndInstrumentColumnName(Long methodId, String instrumentColumnName);
}