/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

/**
 * 进程
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_procedure_method")
@SQLDelete(sql = "update les_procedure_method set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class ProcedureMethod extends LesConfigBaseEntity {

    private String dagNodeId;

    @JSONField(serialize = false)
    @ManyToOne
    @JoinColumn(name = "procedure_id", referencedColumnName = "id")
    private Procedure procedure;

    @ManyToOne(cascade = {CascadeType.PERSIST,CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH})
    @JoinColumn(name = "method_id", referencedColumnName = "id")
    private Method method;

    private String status = CommandStatusEnum.VALID.name();

    private Long deriveProcId;

    private String parallel;

    /**
     * 环境变量
     */
    private String variables;

    public void bind(Procedure parentObj) {
        this.setProcedure(parentObj);
        if (this.method == null ){
            return;
        }
        method.bind();
    }

    public void copy(ProcedureMethod source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("procedure","method"));

        this.variables = source.variables;

        if (this.getMethod()==null){
            this.setMethod(source.getMethod());
        }else{
            this.method.copy(source.getMethod());
        }

    }
}
