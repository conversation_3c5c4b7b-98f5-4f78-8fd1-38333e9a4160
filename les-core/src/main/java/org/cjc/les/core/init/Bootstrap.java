/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.init;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.impl.SystemDevice;
import org.cjc.les.core.helper.FormulaExecutionHelper;
import org.cjc.les.core.schedule.SimpleMainScheduler;
import org.cjc.les.core.schedule.SampleEntryScheduler;
import org.cjc.les.core.schedule.SampleExitScheduler;
import org.cjc.les.core.service.DeviceExecuteService;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Profile("!test")
@Component
@Log4j2
@RequiredArgsConstructor
public class Bootstrap implements ApplicationRunner, DisposableBean {

    private final DeviceExecuteService deviceExecuteService;

    private final SampleEntryScheduler sampleEntryScheduler;
    private final SimpleMainScheduler mainScheduler;
    private final SampleExitScheduler sampleExitScheduler;

    private final DeviceInitializer deviceInitializer;

    private final FormulaInitializer formulaInitializer;

    private final TaskScheduler taskScheduler;

    private final StepScheduler stepScheduler;

    private final ControlDeviceMonitor controlDeviceMonitor;

    private final AlertMonitor alertMonitor;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("Bootstrap...");
        // 初始化设备定义及控制设备实例
        deviceInitializer.init();

        // 初始化公式定义
        formulaInitializer.init();

        // 开启任务调度监听
        taskScheduler.start();

        // 开启步骤调度监听
        stepScheduler.start();

        // 告警监控
        alertMonitor.init();

        // 常用设备初始并监听状态
        controlDeviceMonitor.initDevices();


    }

    private void initDevicesTest() {
        // 是否启用模拟测试
        boolean enableMock = false;
        // 0. 初始化系统设备
        Device sysDev = new SystemDevice();
        sysDev.setId(0L);
        sysDev.init();
        deviceExecuteService.addDevice(sysDev);

        // 1. 初始化Robot
        Device robotMain = new Device();
        robotMain.setId(1L);
        robotMain.setType("CONTROL");
        robotMain.setConfig("192.168.0.250");
        robotMain.setMockConfig("127.0.0.1");
        robotMain.setEnableMock(enableMock);
        robotMain.setJavaClassName("org.cjc.les.device.TrioMotionDevice");
        robotMain.setConfigJavaClassName("java.lang.String");
        robotMain = robotMain.createActualInstance();

        boolean robotMainRet = robotMain.init();
        if (!robotMainRet){
            log.error("Initialize Robot error, Please check the configuration.");
            return;
        }
        deviceExecuteService.addDevice(robotMain);

        // 2. 初始化PLC
        Device plc = new Device();
        plc.setId(2L);
        robotMain.setType("CONTROL");
        plc.setConfig("JETIP:*************:4001:4000");
        plc.setMockConfig("JETIP:127.0.0.1:50012:50010");
        plc.setEnableMock(enableMock);
        plc.setJavaClassName("org.cjc.les.device.JetterPlcDevice");
        plc.setConfigJavaClassName("java.lang.String");
        plc = plc.createActualInstance();

        boolean plcRet = plc.init();
        if (!plcRet){
            log.error("Initialize PLC error, Please check the configuration.");
            return;
        }
        deviceExecuteService.addDevice(plc);

        // 3. 初始化水分检测仪
        Device moisture = new Device();
        moisture.setId(3L);
        moisture.setConfig("{\"portName\":\"COM3\",\"baudRate\":9600,\"parity\":0,\"dataBit\":8,\"stopBit\":1}");
        moisture.setMockConfig("{\"portName\":\"COM1\",\"baudRate\":9600,\"parity\":0,\"dataBit\":8,\"stopBit\":1}");
        moisture.setEnableMock(enableMock);
        moisture.setJavaClassName("org.cjc.les.device.MoistureBalanceDevice");
        moisture.setConfigJavaClassName("org.cjc.les.driver.serialport.SerialPortConfig");
        moisture = moisture.createActualInstance();

        moisture.init();
        deviceExecuteService.addDevice(moisture);

        // 4.初始化SDU
        Device sduDevice = new Device();
        sduDevice.setId(4L);
        sduDevice.setConfig("{\"ipAddress\":\"*************\",\"port\":47}");
        sduDevice.setMockConfig("{\"ipAddress\":\"127.0.0.1\",\"port\":4701}");
        sduDevice.setEnableMock(enableMock);
        sduDevice.setJavaClassName("org.cjc.les.device.SduDevice");
        sduDevice.setConfigJavaClassName("org.cjc.les.device.IpConfigArg");
        sduDevice = sduDevice.createActualInstance();

        sduDevice.init();
        deviceExecuteService.addDevice(sduDevice);

        // 5. 初始化RFID Confirm Reader2
        Device rfidConfirmReaderDevice = new Device();
        rfidConfirmReaderDevice.setId(5L);
        rfidConfirmReaderDevice.setConfig("{\"ipAddress\":\"*************\",\"port\":47}");
        rfidConfirmReaderDevice.setMockConfig("{\"ipAddress\":\"127.0.0.1\",\"port\":4702}");
        rfidConfirmReaderDevice.setEnableMock(enableMock);
        rfidConfirmReaderDevice.setJavaClassName("org.cjc.les.device.RfidReaderDevice");
        rfidConfirmReaderDevice.setConfigJavaClassName("org.cjc.les.device.IpConfigArg");
        rfidConfirmReaderDevice = rfidConfirmReaderDevice.createActualInstance();

        rfidConfirmReaderDevice.init();
        deviceExecuteService.addDevice(rfidConfirmReaderDevice);

        // 6. 初始化RFID Reader1
        Device rfidReaderDevice = new Device();
        rfidReaderDevice.setId(6L);
        rfidReaderDevice.setConfig("{\"ipAddress\":\"*************\",\"port\":47}");
        rfidReaderDevice.setMockConfig("{\"ipAddress\":\"127.0.0.1\",\"port\":4703}");
        rfidReaderDevice.setEnableMock(enableMock);
        rfidReaderDevice.setJavaClassName("org.cjc.les.device.RfidReaderDevice");
        rfidReaderDevice.setConfigJavaClassName("org.cjc.les.device.IpConfigArg");
        rfidReaderDevice = rfidReaderDevice.createActualInstance();

        rfidReaderDevice.init();
        deviceExecuteService.addDevice(rfidReaderDevice);

        // 1001 固体进样器
        Device devSampleEntry = new Device();
        devSampleEntry.setId(1001L);
        devSampleEntry.setName("固体进样器");
        Position pos = new Position();
        pos.setXpos(1656.27);
        pos.setYpos(1.34);
        pos.setZpos(423.02);
      //  devSampleEntry.getPositions().add(pos);
        deviceExecuteService.addDevice(devSampleEntry);

        // 1002 固体出样器
        Device devSampleExit = new Device();
        devSampleExit.setId(1002L);
        devSampleExit.setName("固体出样器");
        pos = new Position();
        pos.setXpos(1386.67);
        pos.setYpos(17.02);
        pos.setZpos(422.00);
    //    devSampleExit.getPositions().add(pos);
        deviceExecuteService.addDevice(devSampleExit);

        // 1003 固体样品架
        Device devSolidRack = new Device();
        devSolidRack.setId(1003L);
        devSolidRack.setName("固体样品架");
        pos = new Position();
        pos.setXpos(1301.86);
        pos.setYpos(784.57);
        pos.setZpos(429.11);
   //     devSolidRack.getPositions().add(pos);
        deviceExecuteService.addDevice(devSolidRack);

        // 1004 玻璃容器架
        Device devGlassRack = new Device();
        devGlassRack.setId(1004L);
        devGlassRack.setName("玻璃容器架");
        pos = new Position();
        pos.setXpos(1068.34);
        pos.setYpos(733.26);
        pos.setZpos(412.50);
    //    pos.setDevicePosIndex(0);
        pos.setName("培养皿空点位");
     //   devGlassRack.getPositions().add(pos);
        pos = new Position();
        pos.setXpos(963.30);
        pos.setYpos(780.27);
        pos.setZpos(412.52);
 //       pos.setDevicePosIndex(1);
        pos.setName("培养皿点位1");
    //    devGlassRack.getPositions().add(pos);
        deviceExecuteService.addDevice(devGlassRack);

        // 1101 培养皿摇床
        Device devPetriShaker = new Device();
        devPetriShaker.setId(1101L);
        devPetriShaker.setName("培养皿摇床");
        pos = new Position();
        pos.setXpos(1100.76);
        pos.setYpos(12.14);
        pos.setZpos(403.05);
   //     devPetriShaker.getPositions().add(pos);
        deviceExecuteService.addDevice(devPetriShaker);

        // 1102 培养皿清洗器
        Device devPetriClean = new Device();
        devPetriClean.setId(1102L);
        devPetriClean.setName("培养皿清洗器");
        pos = new Position();
        pos.setXpos(835.81);
        pos.setYpos(6.49);
        pos.setZpos(418.73);
    //    devPetriClean.getPositions().add(pos);
        deviceExecuteService.addDevice(devPetriClean);


    }

    @Override
    public void destroy() throws Exception {
        log.info("destroy devices...");
        controlDeviceMonitor.stopMonitor();
        /*
        Collection<Device> devices = deviceExecuteService.getAllDevices();
        devices.forEach((device) ->{
            device.finalize();
            log.info("Destroy {} is done.", device.getId());
        } );
         */
    }
}
