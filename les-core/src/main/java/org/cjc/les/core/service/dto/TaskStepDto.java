/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.MethodStep;

import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class TaskStepDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 任务运行的步骤名称 */
    private String name;

    /** 任务中的步骤描述 */
    private String description;

    /** 运行状态: READY,RUNNING,SUCCESS,FAILED */
    private String status;

    private String message;

    /** 任务ID */
    private Long taskId;

    /** 任务的方法ID */
    private Long taskMethodId;

    /** 引用配置的步骤ID */
    private Long stepId;

    /**
     * BLOCK_ALL,BLOCK_SELF,UNBLOCK
     */
    private String scheduleMode;

    /**
     * 环境变量
     */
    private String variables;

    /**
     * 条件谓词表达式
     */
    private String predicates;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}