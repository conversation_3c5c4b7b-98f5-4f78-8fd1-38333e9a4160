/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Result;
import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.service.dto.FeatureConfigQueryCriteria;
import org.cjc.les.core.service.dto.ResultDto;
import org.cjc.les.core.service.dto.ResultQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-25
**/
public interface ResultService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ResultQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ResultDto>
    */
    List<ResultDto> queryAll(ResultQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return ResultDto
     */
    ResultDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return ResultDto
    */
    ResultDto create(Result resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Result resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ResultDto> all, HttpServletResponse response) throws IOException;

    void fillFeatureConfig(ResultFeature feature, FeatureConfigQueryCriteria criteria);
}