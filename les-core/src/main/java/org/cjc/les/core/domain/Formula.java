/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-15
**/
@Entity
@Data
@Table(name="les_formula")
@SQLDelete(sql = "UPDATE les_formula SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class Formula extends LesConfigBaseEntity {

    @Column(name = "type")
    @Schema(description = "脚本类型")
    private String type;

    @Column(name = "name",unique = true)
    @Schema(description = "公式名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "公式描述")
    private String description;

    @Column(name = "content")
    @Schema(description = "公式内容")
    private String content;

    public void copy(Formula source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}