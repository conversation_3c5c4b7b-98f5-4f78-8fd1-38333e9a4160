/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.ProcedureVariable;
import org.cjc.les.core.service.dto.ProcedureVariableDto;
import org.cjc.les.core.service.dto.ProcedureVariableQueryCriteria;
import org.cjc.les.core.service.dto.ProcedureWithVariablesDto;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-07-27
**/
public interface ProcedureVariableService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ProcedureVariableQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ProcedureVariableDto>
    */
    List<ProcedureVariableDto> queryAll(ProcedureVariableQueryCriteria criteria);

    /**
     * 通过流程ID 查询用户设置变量值
     * @param procId
     * @return
     */
    List<ProcedureVariableDto> queryAllUserSettingsBYProcId(Long procId);
    /**
     * 根据ID查询
     * @param id ID
     * @return ProcedureVariableDto
     */
    ProcedureVariableDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return ProcedureVariableDto
    */
    ProcedureVariableDto create(ProcedureVariable resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ProcedureVariable resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ProcedureVariableDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查找原始流程列表
     *
     * @return
     */
    List<ProcedureWithVariablesDto> queryAllRawProceduresWithVariables();

    /**
     * 更新流程绑定变量列表
     * @param procedureDtos
     */
    void updateProcedureVariablesValue(List<ProcedureWithVariablesDto> procedureDtos);

    /**
     * 更新流程绑定变量列表
     * @param procedureId
     * @param variableDtos
     */
    void updateDefaultValueByProcedureId(Long procedureId, List<ProcedureVariableDto> variableDtos);
}