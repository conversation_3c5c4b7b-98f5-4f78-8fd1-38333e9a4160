/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 流程程
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_procedure")
@SQLDelete(sql = "update les_procedure set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class Procedure extends LesConfigBaseEntity {

    /**
     * 流程名
     */
    private String name;
    /**
     * 描述
     */
    private String description;

    /**
     * 流程类型,
     * RAW: 原始类型,最多只存在一个主体方法
     * DERIVE: 派生类型, 从一个或多个RAW原始类型进程中派生出来，该类型进程通常由用户在前端界面选择生成，
     *         其选择的多个流程应该具有相同的进样方法,
     *         该类型进程不允许直接编辑其关联的方法及其下的子节点
     */
    private String type;
    /**
     * 当type=DERIVE时，该字段存储对应被派生的流程ID列表，存在多个被派生流程ID时，以英文逗号分割
     */
    private String deriveProcIds;

    /**
     * 进程编号
     */
    transient private String number;
    transient private Result result;

    /**
     * DAG图
     */
    private String dagJson;

    /**
     * DAG节点ID
     */
    private String dagNodeId;

    /**
     * 流程状态: DRAFT, DEPLOY
     */
    private String status;

    /**
     * 进程方法，顺序执行
     */
    @OneToMany(mappedBy = "procedure", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<ProcedureMethod> methods = new ArrayList<>();

    /**
     * 进程绑定的变量
     */
    @OneToMany(mappedBy = "procedure", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<ProcedureVariable> variables = new ArrayList<>();

    /**
     * 进样方法
     */
    public Optional<ProcedureMethod> getEntryMethod(){
        for (ProcedureMethod mth : this.getMethods()) {
            if (StringUtils.equalsIgnoreCase(mth.getMethod().getType(),"ENTRY")){
                return Optional.of(mth);
            }
        }
        return Optional.empty();
    }

    /**
     * 主体业务方法列表
     * @return
     */
    public List<ProcedureMethod> getMainMethods() {
        return methods.stream().filter(mth->{ return StringUtils.equalsIgnoreCase(mth.getMethod().getType(),"MAIN");})
                .collect(Collectors.toList());
    }

    /**
     * 出样方法
     */
    public Optional<ProcedureMethod> getExitMethod(){
        for (ProcedureMethod mth : methods) {
            if (StringUtils.equalsIgnoreCase(mth.getMethod().getType(),"EXIT")){
                return Optional.of(mth);
            }
        }
        return Optional.empty();
    }

    public void bind() {
        for (ProcedureMethod mth : methods) {
            mth.bind(this);
        }
    }

    public void copy(Procedure source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("methods","variables"));

        if (this.getMethods()==null){
            this.setMethods(new ArrayList<>());
        }
        List<ProcedureMethod> bakObjs = new ArrayList<>(this.getMethods());

        this.getMethods().clear();
        for (ProcedureMethod srcObj : source.getMethods()) {
            Optional<ProcedureMethod> optOther = bakObjs.stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),srcObj.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                ProcedureMethod other = optOther.get();
                other.copy(srcObj);
                this.getMethods().add(other);
            }else{
                this.getMethods().add(srcObj);
            }
        }

        this.bind();

    }
}
