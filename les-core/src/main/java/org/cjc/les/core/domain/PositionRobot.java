/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-04-16
**/
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Entity
@Data
@Table(name="les_position_robot")
@SQLDelete(sql = "update les_position_robot set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class PositionRobot extends LesConfigBaseEntity {

    /**
     * 引用设备点位
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "position_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = Position.class)
    private Position position;

    @Column(name = "robot_dev_instance_id",nullable = false)
    @NotNull
    @Schema(description = "关联ROBOT设备实例ID")
    private Long robotDevInstanceId;

    @Column(name = "robot_pos_code")
    @Schema(description = "机器人定义的位置编号，用于机器人点位唯一识别")
    private String robotPosCode;

    @Column(name = "xpos")
    @Schema(description = "xpos")
    private BigDecimal xpos;

    @Column(name = "ypos")
    @Schema(description = "ypos")
    private BigDecimal ypos;

    @Column(name = "zpos")
    @Schema(description = "zpos")
    private BigDecimal zpos;

    @Column(name = "status")
    @Schema(description = "保留")
    private String status;

    @Column(name = "upos")
    @Schema(description = "upos")
    private BigDecimal upos;

    @Column(name = "vpos")
    @Schema(description = "vpos")
    private BigDecimal vpos;

    @Column(name = "wpos")
    @Schema(description = "wpos")
    private BigDecimal wpos;

    public void copy(PositionRobot source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}