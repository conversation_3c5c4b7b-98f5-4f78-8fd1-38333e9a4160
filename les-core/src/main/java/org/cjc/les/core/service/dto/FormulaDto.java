/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-15
**/
@Data
public class FormulaDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 脚本类型 */
    private String type;

    /** 公式名称 */
    private String name;

    /** 公式描述 */
    private String description;

    /** 公式内容 */
    private String content;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}