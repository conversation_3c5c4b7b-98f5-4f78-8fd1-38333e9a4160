/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.*;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ProcedureRepository;
import org.cjc.les.core.repository.SampleCategoryRepository;
import org.cjc.les.core.repository.SampleCustomerRepository;
import org.cjc.les.core.repository.SampleRepository;
import org.cjc.les.core.service.ProcedureService;
import org.cjc.les.core.service.SampleService;
import org.cjc.les.core.service.dto.ProcedureSmallDto;
import org.cjc.les.core.service.dto.SampleDto;
import org.cjc.les.core.service.dto.SampleQueryCriteria;
import org.cjc.les.core.service.dto.TaskPrepareNestedDto;
import org.cjc.les.core.service.mapstruct.ProcedureSmallMapper;
import org.cjc.les.core.service.mapstruct.SampleMapper;
import org.cjc.les.core.util.SerialNumberGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.cjc.exception.EntityExistException;

import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-11
**/
@Service
@RequiredArgsConstructor
public class SampleServiceImpl implements SampleService {

    private final SampleRepository sampleRepository;
    private final SampleCategoryRepository sampleCategoryRepository;
    private final SampleCustomerRepository sampleCustomerRepository;
    private final SampleMapper sampleMapper;

    private final ProcedureRepository procedureRepository;
    private final ProcedureSmallMapper procedureSmallMapper;

    private final ProcedureService procedureService;

    @Override
    public Map<String,Object> queryAll(SampleQueryCriteria criteria, Pageable pageable){
        Page<Sample> page = sampleRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sampleMapper::toDto));
    }

    @Override
    public List<SampleDto> queryAll(SampleQueryCriteria criteria){
        return sampleMapper.toDto(sampleRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public SampleDto checkSampleName(Sample resource) {
        Optional<Sample> sampleOpt = sampleRepository.findSampleByName(resource.getName());
        if (!sampleOpt.isPresent()) {
            return null;
        } else {
            return sampleMapper.toDto(sampleOpt.get());
        }
    }

    @Override
    public SampleDto queryLatestSample() {
        Optional<Sample> sampleOpt = sampleRepository.findLatestSample();
        if (!sampleOpt.isPresent()) {
            return null;
        } else {
            SampleDto sampleDto = sampleMapper.toDto(sampleOpt.get());
            convertDeriveProcToRawProcOptions(sampleDto);
            return sampleDto;
        }
    }

    private void convertDeriveProcToRawProcOptions(SampleDto sampleDto) {
        if (CollectionUtils.isEmpty(sampleDto.getTaskPrepares())) {
            return;
        }
        for (TaskPrepareNestedDto taskPrepareNestedDto : sampleDto.getTaskPrepares()) {
            ProcedureSmallDto procDto = taskPrepareNestedDto.getProcedure();
            if (StringUtils.equalsAny(procDto.getType(), "DERIVE")) {
                String rawProcIds = procDto.getDeriveProcIds();
                String[] ids = rawProcIds.split(",");
                List<Long> idList = new ArrayList<>();
                Map<Long, String> parallelMap = new HashMap<>();
                for (String idStr : ids) {
                    String[] idArr = idStr.split(":");
                    if (idArr.length==1) {
                        idList.add(Long.parseLong(idStr));
                    }else{
                        Long id = Long.parseLong(idArr[0]);
                        idList.add(id);
                        parallelMap.put(id, idArr[1]);
                    }
                }
                List<Procedure> procList = procedureRepository.findAllById(idList);
                List<ProcedureSmallDto> procedureSmallDtos = procedureSmallMapper.toDto(procList);
                for (ProcedureSmallDto dto : procedureSmallDtos) {
                    dto.setParallel(parallelMap.get(dto.getId()));
                }
                taskPrepareNestedDto.getProcedureOptions().addAll(procedureSmallDtos);
            } else {
                taskPrepareNestedDto.getProcedureOptions().add(procDto);
            }
        }
    }

    @Override
    @Transactional
    public SampleDto findById(Long id) {
        Sample sample = sampleRepository.findById(id).orElseGet(Sample::new);
        ValidationUtil.isNull(sample.getId(),"Sample","id",id);
        return sampleMapper.toDto(sample);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleDto create(SampleDto resources) {
        Optional<Sample> optSample = sampleRepository.findSampleByName(resources.getName());
        if( optSample.isPresent()){
            throw new EntityExistException(Sample.class,"name",resources.getNumber());
        }

        bindDeriveTp(resources);

        Sample toCreateRes = sampleMapper.toEntity(resources);

        // 重新检查Sample入参，并补充必要信息
        checkSampleResources(toCreateRes);

        if (CollectionUtils.isNotEmpty(toCreateRes.getTaskPrepares())){
            for (TaskPrepare taskPrepare : toCreateRes.getTaskPrepares()) {
                taskPrepare.setSample(toCreateRes);
                taskPrepare.setTaskNumber(SerialNumberGenerator.generateTaskSerialNumber());
            }
        }
        return sampleMapper.toDto(sampleRepository.save(toCreateRes));
    }

    /**
     * 若存在多个RAW类型TP对象，则创建或绑定派生对象
     * @param resources
     */
    private void bindDeriveTp(SampleDto resources) {
        if (CollectionUtils.isEmpty(resources.getTaskPrepares())) {
            return;
        }
        for (TaskPrepareNestedDto tpDto : resources.getTaskPrepares()) {
            List<ProcedureSmallDto> procDtos = tpDto.getProcedureOptions();
            if (CollectionUtils.isEmpty(procDtos)){
                continue;
            }
            if (CollectionUtils.size(procDtos)==1 && !StringUtils.equals(procDtos.get(0).getParallel(),"Y") ){
                tpDto.setProcedure(procDtos.get(0));
                continue;
            }

            procDtos.sort(Comparator.comparing(ProcedureSmallDto::getId));

            Procedure deriveProc = procedureService.queryOrCreateDeriveProc(procDtos);
            tpDto.setProcedure( procedureSmallMapper.toDto(deriveProc) );
        }
    }


    private void checkSampleResources(Sample resources) {
        if (resources.getCategory() != null && resources.getCategory().getName() == null) {
            resources.setCategory(null);
        }
        if (resources.getCategory() != null && resources.getCategory().getName() != null) {
            Optional<SampleCategory> optCategory = sampleCategoryRepository.findByName(resources.getCategory().getName() );
            if (optCategory.isPresent()) {
                resources.setCategory(optCategory.get());
            }
        }
        if (resources.getCustomer() != null && resources.getCustomer().getName() == null){
            resources.setCustomer(null);
        }
        if (resources.getCustomer() != null && resources.getCustomer().getName() != null) {
            Optional<SampleCustomer> optCustomer = sampleCustomerRepository.findByName(resources.getCustomer().getName());
            if (optCustomer.isPresent()) {
                resources.setCustomer(optCustomer.get());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Sample resources) {
        Sample sample = sampleRepository.findById(resources.getId()).orElseGet(Sample::new);
        ValidationUtil.isNull( sample.getId(),"Sample","id",resources.getId());
        Sample sample1 = null;
     //   sample1 = sampleRepository.findByNumber(resources.getNumber());
        if(sample1 != null && !sample1.getId().equals(sample.getId())){
            throw new EntityExistException(Sample.class,"number",resources.getNumber());
        }
        if (CollectionUtils.isNotEmpty(resources.getTaskPrepares())){
            for (TaskPrepare taskPrepare : resources.getTaskPrepares()) {
                taskPrepare.setSample(resources);
                if (StringUtils.isEmpty(taskPrepare.getTaskNumber())) {
                    taskPrepare.setTaskNumber(SerialNumberGenerator.generateTaskSerialNumber());
                }
            }
        }

        // 重新检查Sample入参，并补充必要信息
        checkSampleResources(resources);

        sample.copy(resources);
        sampleRepository.save(sample);
        resources.copy(sample);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            sampleRepository.deleteById(id);
        }
    }

    @Override
    public Sample save(Sample resources) {
        Optional<Sample> sampleOpt = sampleRepository.findSampleByName(resources.getName());
        if( sampleOpt.isPresent()){
            Sample sample = sampleOpt.get();
            sample.copy(resources);
            return sampleRepository.save(sample);
        //    throw new EntityExistException(Sample.class,"name",resources.getName());
        }else {
            return sampleRepository.save(resources);
        }
    }

    @Override
    public void download(List<SampleDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SampleDto sample : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("样本编号", sample.getNumber());
            map.put("样本名称", sample.getName());
            map.put("样本描述", sample.getDescription());
            map.put("是否已被删除,Y/N", sample.getDeleteFlag());
            map.put("创建人", sample.getCreateBy());
            map.put("创建时间", sample.getCreateTime());
            map.put("更新人", sample.getUpdateBy());
            map.put("更新时间", sample.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}