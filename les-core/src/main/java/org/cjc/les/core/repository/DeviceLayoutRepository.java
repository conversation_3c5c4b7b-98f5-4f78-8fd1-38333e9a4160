/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.DeviceLayout;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-04
**/
public interface DeviceLayoutRepository extends JpaRepository<DeviceLayout, Long>, JpaSpecificationExecutor<DeviceLayout> {

    @Query(value = "select layout.* from les_device_layout layout where layout.status =?1 order by layout.version desc limit 1", nativeQuery = true)
    Optional<DeviceLayout> findLatestByStatus(String status);
    @Query(value = "select layout.* from les_device_layout layout order by layout.version desc limit 1", nativeQuery = true)
    Optional<DeviceLayout> findLatest();

    @Query(value="select devInst.name as devInstName, stat.name as stationName from les_device_instance devInst join les_station_deviceinst stDev on stDev.device_instance_id=devInst.id\n" +
            "join les_station stat on stat.id=stDev.station_id and stat.delete_flag='N'\n" +
            " where devInst.id in :devInstIds", nativeQuery = true)
    List<Map> findUsedDevInstancesByIds(@Param("devInstIds")Long[] devInstIds);
}