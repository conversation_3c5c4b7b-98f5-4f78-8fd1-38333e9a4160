/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.core.domain.Position;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-07-30
**/
@Data
public class DeviceInstanceDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 设备名称 */
    private String name;

    /** 设备描述 */
    private String description;

    /** 设备定义ID */
    private Long deviceId;

    private String type;
    /** 配置信息，JSON串，或者普通的字符串 */
    private String config;

    /** 模拟配置,  */
    private String mockConfig;

    /** 1:mock_config配置生效，0:config配置生效 */
    private boolean enableMock;

    /**
     * 设备实例点位
     */
    private List<Position> positions;

    private String positionConfig;
    private String positionDetailConfig;

    /**
     * 设备定义对象
     */
    private Device device;

    /** 设备布局ID */
    private Long layoutId;

    /** 设备布局节点ID */
    private String layoutNodeId;

    private List<DeviceInstanceCmd> commands = new ArrayList<>();

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}