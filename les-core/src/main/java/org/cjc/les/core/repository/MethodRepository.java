/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Method;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
public interface MethodRepository extends JpaRepository<Method, Long>, JpaSpecificationExecutor<Method> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Method findByName(String name);

    /**
     * 查询未引用的流程节点
     * @return
     */
    @Query(value = "select mth.* from les_method mth where mth.delete_flag='N' and not exists (select 1 from les_procedure_method pm join les_procedure proc on proc.id=pm.procedure_id and proc.delete_flag='N' where pm.method_id=mth.id and pm.delete_flag='N')", nativeQuery = true)
    List<Method> findUnRefMethods();

    @Query(value="select m.name from les_method m where m.delete_flag='N' and m.type='MAIN'", nativeQuery = true)
    List<String> queryMainMethodNames();
}