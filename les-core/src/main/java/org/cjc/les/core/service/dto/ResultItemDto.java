/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.TaskMethod;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-25
**/
@Data
public class ResultItemDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 关联产品ID */
    private Long resultId;

    /** 产品项名称 */
    private String name;

    /** 产品项描述 */
    private String description;

    /** 生成该产品项的方法ID */
    private Long taskMethodId;

    /**
     * 检测方法名称
     */
    private String taskMethodName;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    private TaskMethodDto taskMethod;

    List<ResultFeatureDto> resultFeatures = new ArrayList<>();

}