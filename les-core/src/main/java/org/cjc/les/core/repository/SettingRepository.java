/*
*  Copyright 2024-2024 <PERSON>han Annis Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Setting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-05-19
**/
public interface SettingRepository extends JpaRepository<Setting, Long>, JpaSpecificationExecutor<Setting> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Setting findByName(String name);

    /**
     * @return
     */
    @Query(value = "select * from les_setting order by create_time desc limit 1", nativeQuery = true)
    Optional<Setting> findLatestOne();

}