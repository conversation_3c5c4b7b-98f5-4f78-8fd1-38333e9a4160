/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class TaskMethodDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 任务创建时的方法名称 */
    private String name;

    /** 任务创建时的方法描述 */
    private String description;

    private String type;

    /** 方法运行状态：READY,RUNNING,SUCCESS,FAILED */
    private String status;

    /** 任务ID */
    private Long taskId;

    /** 引用配置的方法ID */
    private Long methodId;

    private String parallel;
    /**
     * 环境变量
     */
    private String variables;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    /**
     * 该任务方法的执行步骤列表
     */
    private List<TaskStepDto> steps;
}