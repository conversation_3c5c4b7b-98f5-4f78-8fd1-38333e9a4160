/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.AlertLog;

import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-10-14
**/
@Data
public class AlertLogDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 告警编码 */
    private String alertCode;

    /** 告警，名称 */
    private String alertName;

    /** 告警级别：FATAL, ERROR, WARN */
    private String alertLevel;

    /** 告警信息 */
    private String message;

    /** 告警类别：DEV_INSTANCE, TASK,SYS */
    private String alertCategory;

    /** 告警来源ID, deviceInstanceId, taskId */
    private Long alertSourceId;

    /** 告警处理状态, INIT, FIXED */
    private String fixStatus;

    /** 告警修复类型: AUTO,MANUAL */
    private AlertLog.FixTypeEnum fixType;

    /** 告警修复人 */
    private String fixedBy;

    /** 告警修复备注 */
    private String fixedRemark;

    /** 告警修复时间 */
    private Timestamp fixedTime;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}