/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.cjc.base.BaseEntity;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 检测方法
 */
@Entity
@Data
@Table(name="les_method")
@SQLDelete(sql = "update les_method set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class Method extends LesConfigBaseEntity {

    /**
     * 方法名称
     */
    private String name;

    /**
     * 方法描述
     */
    private String description;

    /**
     * 方法类型
     */
    private String type;

    /**
     * 创建该方法的流程ID
     */
    private Long createByProcedureId;

    /**
     * 步骤列表
     */
    @OneToMany(mappedBy = "method", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<MethodStep> steps = new ArrayList<>();

    @JSONField(serialize = false)
    @OneToMany(mappedBy = "method", cascade = CascadeType.ALL)
    private List<ProcedureMethod> procedureMethods;

    private String status = CommandStatusEnum.VALID.name();

    public void bind() {
        for (MethodStep step : steps) {
            step.bind(this);
        }
    }


    public void copy(Method source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("steps","procedureMethods"));

        if (this.getSteps()==null){
            this.setSteps(new ArrayList<>());
        }

        List<MethodStep> bakObjs = new ArrayList<>(this.getSteps());

        this.getSteps().clear();
        for (MethodStep srcObj : source.getSteps()) {
            Optional<MethodStep> optOther = bakObjs.stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),srcObj.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                MethodStep other = optOther.get();
                other.copy(srcObj);
                this.getSteps().add(other);
            }else{
                this.getSteps().add(srcObj);
            }
        }

        this.bind();
    }
}
