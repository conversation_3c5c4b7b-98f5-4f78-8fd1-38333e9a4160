/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.schedule;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.domain.Procedure;

import java.util.HashMap;

/**
 * 命令执行上下文对象，存储当前命令执行所需的环境信息
 */
@Data
@Log4j2
public class CommandContext {

    private final static ThreadLocal<CommandContext> current = new ThreadLocal<>();

    // 当前执行处理中的进程
    private Procedure procedure;

    /**
     * 保存当前线程中用到的变量
     */
    private HashMap<String, Object> variableMap = new HashMap<>();

    public static Procedure getProcedure(){
        CommandContext context = current.get();
        if (context==null){
            log.error("Context is null, please setProduct at first.");
            return null;
        }
        return context.procedure;
    }

    public static void setProcedure(Procedure procedure){
        CommandContext context = getContext();
        context.procedure = procedure;
    }

    public static void setVar(String varName, Object value){
        CommandContext context = getContext();
        context.variableMap.put(varName, value);
    }

    public static String getVarAsString(String varName){
        CommandContext context = getContext();
        Object v = context.variableMap.get(varName);
        if (v instanceof String){
            return (String)v;
        }
        return null;
    }

    public static CommandContext getContext() {
        CommandContext context = current.get();
        if (context==null){
            context = new CommandContext();
            current.set(context);
        }
        return context;
    }

    public static  void clear(){
        CommandContext context = current.get();
        if (context==null){
            return;
        }
        current.remove();
    }

}
