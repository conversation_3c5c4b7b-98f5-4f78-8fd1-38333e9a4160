/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.core.helper.CommandExecuteHook;
import org.cjc.les.core.service.dto.DeviceInstanceCmdDto;
import org.cjc.les.core.service.dto.DeviceInstanceCmdQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-07-19
**/
public interface DeviceInstanceCmdService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(DeviceInstanceCmdQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<DeviceInstanceCmdDto>
    */
    List<DeviceInstanceCmdDto> queryAll(DeviceInstanceCmdQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return DeviceInstanceCmdDto
     */
    DeviceInstanceCmdDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return DeviceInstanceCmdDto
    */
    DeviceInstanceCmdDto create(DeviceInstanceCmd resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(DeviceInstanceCmd resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<DeviceInstanceCmdDto> all, HttpServletResponse response) throws IOException;

    void updateTestParameter(Long id, String testParameter);

    /**
     * 执行设备实例命令
     * @param resources
     * @return
     */
    DeviceInstanceCmdDto executeCommand(DeviceInstanceCmdDto resources);
    /**
     * 执行设备实例命令
     * @param resources
     * @return
     */
    DeviceInstanceCmdDto executeCommandByControlCode(DeviceInstanceCmdDto resources);

    /**
     * 执行设备实例命令
     * @param resources
     * @param hook
     * @return
     */
    DeviceInstanceCmdDto executeCommand(DeviceInstanceCmdDto resources, CommandExecuteHook hook);

}