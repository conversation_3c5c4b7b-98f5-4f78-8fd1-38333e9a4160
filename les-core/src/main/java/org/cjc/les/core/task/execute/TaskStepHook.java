/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.vo.CommandReturn;

/**
 * 任务执行步骤钩子接口
 */
public interface TaskStepHook {

    /**
     * 步骤调用之前执行，errorCode=SUCCESS继续执行该步骤，否则依据failedThen决策是否下一步骤
     *
     * @param step 当前步骤对象
     * @return
     */
    CommandReturn<TaskStep> executeBefore(TaskStep step);

    /**
     * 步骤调用之后执行，errorCode=SUCCESS继续执行该步骤，否则依据failedThen决策是否下一步骤
     *
     * @param step 当前步骤对象
     * @return
     */
    CommandReturn<TaskStep> executeAfter(TaskStep step);

}
