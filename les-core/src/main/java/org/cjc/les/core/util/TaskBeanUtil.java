/*
 *  Copyright 2024-2024 <PERSON>han Annis Robot Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.les.core.domain.*;

/**
 * 任务对象工具类
 */
public class TaskBeanUtil {
    public static void copy(TaskBaseEntity source, TaskBaseEntity target){
        BeanUtil.copyProperties(source,target, CopyOptions.create().setIgnoreNullValue(true));
    }

    public static TaskMethod getTaskMethodById(Task task, Long taskMethodId) {
        for (TaskMethod method : task.getMethodList()) {
            if (method.getId().equals(taskMethodId)) {
                return method;
            }
        }
        return null;
    }
    public static TaskMethod getTaskMethodByMethodId(Task task, Long methodId) {
        for (TaskMethod method : task.getMethodList()) {
            if (method.getMethodId().equals(methodId)) {
                return method;
            }
        }
        return null;
    }

    public static TaskMethod getTaskMethodByTaskStepId(Task task, Long taskStepId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                if (step.getId().equals(taskStepId)) {
                    return method;
                }
            }
        }
        return null;
    }
    public static TaskMethod getTaskMethodByTaskActionId(Task task, Long taskActionId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    if (action.getId().equals(taskActionId)) {
                        return method;
                    }
                }
            }
        }
        return null;
    }
    public static TaskMethod getTaskMethodByTaskCommandId(Task task, Long taskCommandId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    for (TaskCommand command : action.getCommands()) {
                        if (command.getId().equals(taskCommandId)) {
                            return method;
                        }
                    }
                }
            }
        }
        return null;
    }
    public static TaskStep getTaskStepById(Task task, Long taskStepId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                if (step.getId().equals(taskStepId)) {
                    return step;
                }
            }
        }
        return null;
    }
    public static TaskStep getTaskStepByTaskActionId(Task task, Long taskActionId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    if (action.getId().equals(taskActionId)) {
                        return step;
                    }
                }
            }
        }
        return null;
    }
    public static TaskStep getTaskStepByTaskCommandId(Task task, Long taskCommandId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    for (TaskCommand command : action.getCommands()) {
                        if (command.getId().equals(taskCommandId)) {
                            return step;
                        }
                    }
                }
            }
        }
        return null;
    }
    public static TaskAction getTaskActionById(Task task, Long taskActionId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    if (action.getId().equals(taskActionId)) {
                        return action;
                    }
                }
            }
        }
        return null;
    }
    public static TaskAction getTaskActionByTaskCommandId(Task task, Long taskCommandId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    for (TaskCommand command : action.getCommands()) {
                        if (command.getId().equals(taskCommandId)) {
                            return action;
                        }
                    }
                }
            }
        }
        return null;
    }
    public static TaskCommand getTaskCommandById(Task task, Long taskCommandId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    for (TaskCommand command : action.getCommands()) {
                        if (command.getId().equals(taskCommandId)) {
                            return command;
                        }
                    }
                }
            }
        }
        return null;
    }
}
