/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-16
**/
@Data
public class FeatureConfigDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 关联les_method，检测方法标识 */
    private Long methodId;

    /** 特性编码 */
    private String code;

    /** 特性名称 */
    private String name;

    /** 仪器对应字段名 */
    private String instrumentColumnName;

    /** 结果特性描述 */
    private String description;

    /** 数值单位 */
    private String unit;

    /** 结论判定规则集 */
    private String checkRules;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}