/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.cjc.base.BaseEntity;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 步骤下属的动作
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_action")
@SQLDelete(sql = "UPDATE les_action SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class Action extends LesConfigBaseEntity {

    /**
     * 动作名称
     */
    private String name;

    private String description;
    /**
     * DAG图中的节点ID
     */
    private String dagNodeId;
    /**
     * DAG节点的图像显示
     */
    private String dagNodeImage;

    /**
     * 该动作所属的工作站
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "station_id")
    @ManyToOne(fetch=FetchType.LAZY, targetEntity = Station.class)
    @Schema(description = "工作站")
    private Station station;

    /**
     * 命令列表
     */
    @OneToMany(mappedBy = "action", targetEntity = ActionCommand.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<ActionCommand> commands = new ArrayList<>();

    private String status = CommandStatusEnum.VALID.name();

    public void bind(Station parentObj) {
        this.setStation(parentObj);
        for (ActionCommand actionCommand : this.commands) {
            actionCommand.bind(this);
        }
    }

    public void copy(Action source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("station","commands"));


        if (this.getCommands()==null){
            this.setCommands(new ArrayList<>());
        }

        // List<ActionCommand> deleteList = new ArrayList<>();
        List<ActionCommand> bakCommands = new ArrayList<>(this.getCommands());

        this.getCommands().clear();
        for (ActionCommand srcActCommand : source.getCommands()) {

            Optional<ActionCommand> optOther = bakCommands.stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),srcActCommand.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                ActionCommand other = optOther.get();
                other.copy(srcActCommand);
                other.setAction(this);
                this.getCommands().add(other);
            }else{
                srcActCommand.setAction(this);
                this.getCommands().add(srcActCommand);
            }
        }

        /*
        for (ActionCommand actCmd : this.getCommands()) {
            Optional<ActionCommand> optOther = source.getCommands().stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),actCmd.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                actCmd.copy(optOther.get());
                actCmd.setAction(this);
            }else{
                deleteList.add(actCmd);
            }
        }

        source.getCommands().forEach(act->{
            if (act.getId() == null){
                this.getCommands().add(act);
                act.setAction(this);
            }
        });

        if (CollectionUtils.isNotEmpty(deleteList)) {
            this.getCommands().removeAll(deleteList);
        }
*/
    }
}
