/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.schedule;

import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.*;

public class MethodExecutor {

    public boolean executeMethod(ProcedureMethod method, Procedure procedure) {
        boolean retProc = false;
        boolean retMethod = false;
        boolean retStep = false;

        for (MethodStep step : method.getMethod().getSteps()){
            for (StepAction action : step.getStep().getActions()) {
                for (ActionCommand actionCommand : action.getAction().getCommands()){
                    Command command = null; // actionCommand.getCommand();
                    boolean ret = new CommandExecutor().executeCommand(command, procedure);
                    if (!ret && StringUtils.equalsIgnoreCase("RETURN_ACTION",command.getFailedThen().name())){
                        break;
                    }
                    else if(!ret && StringUtils.equalsIgnoreCase("RETURN_STEP",command.getFailedThen().name())){
                        retStep = true;
                        break;
                    }else if(!ret && StringUtils.equalsIgnoreCase("RETURN_METHOD",command.getFailedThen().name())){
                        retStep = true;
                        retMethod = true;
                        break;
                    }else if(!ret && StringUtils.equalsIgnoreCase("RETURN_PROCEDURE",command.getFailedThen().name())){
                        retStep = true;
                        retMethod = true;
                        retProc = true;
                        break;
                    }

                    command.setStatus(CommandStatusEnum.SUCCESS);
                }
                if (retStep){
                    break;
                }

                action.setStatus(CommandStatusEnum.SUCCESS.name());
            }

            if (retMethod){
                break;
            }
            step.setStatus(CommandStatusEnum.SUCCESS.name());
        }

        if (retProc){
            return false;
        }else{
            setMethodStatus(method);
            return true;
        }
    }

    private void setMethodStatus(ProcedureMethod method){
        boolean allStepsSuccess = true;
        for (MethodStep step : method.getMethod().getSteps()){
            if (!CommandStatusEnum.SUCCESS.name().equals(step.getStatus())){
                allStepsSuccess = false;
                break;
            }
        }
        if (allStepsSuccess){
            method.setStatus(CommandStatusEnum.SUCCESS.name());
        }
    }

}
