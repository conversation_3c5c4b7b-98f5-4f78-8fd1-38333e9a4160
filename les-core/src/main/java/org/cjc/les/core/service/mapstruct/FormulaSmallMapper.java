/*
 *  Copyright 2024-2025 <PERSON>han <PERSON>is Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Formula;
import org.cjc.les.core.service.dto.FormulaSmallDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-03-24
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FormulaSmallMapper extends BaseMapper<FormulaSmallDto, Formula> {

}