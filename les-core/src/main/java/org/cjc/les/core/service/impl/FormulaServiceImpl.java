/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.exception.EntityNotFoundException;
import org.cjc.les.core.domain.Formula;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.FormulaRepository;
import org.cjc.les.core.service.FormulaService;
import org.cjc.les.core.service.dto.FormulaDto;
import org.cjc.les.core.service.dto.FormulaQueryCriteria;
import org.cjc.les.core.service.dto.FormulaSmallDto;
import org.cjc.les.core.service.mapstruct.FormulaMapper;
import org.cjc.les.core.service.mapstruct.FormulaSmallMapper;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-15
**/
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "formulaCache")
public class FormulaServiceImpl implements FormulaService {

    private final FormulaRepository formulaRepository;
    private final FormulaMapper formulaMapper;
    private final FormulaSmallMapper formulaSmallMapper;

    @Override
    public Map<String,Object> queryAll(FormulaQueryCriteria criteria, Pageable pageable){
        Page<Formula> page = formulaRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(formulaMapper::toDto));
    }

    @Override
    public List<FormulaDto> queryAll(FormulaQueryCriteria criteria){
        return formulaMapper.toDto(formulaRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public FormulaDto findById(Long id) {
        Formula formula = formulaRepository.findById(id).orElseGet(Formula::new);
        ValidationUtil.isNull(formula.getId(),"Formula","id",id);
        return formulaMapper.toDto(formula);
    }

    @Override
    public FormulaDto queryByName(String name) {
        Formula formula = formulaRepository.findByName(name);
        if (formula == null) {
            throw new EntityNotFoundException(Formula.class,"name", name);
        }

        return formulaMapper.toDto(formula);
    }

    @Override
    @Cacheable(key = "'findFormulaContentByName:' + #p0")
    public String findFormulaContentByName(String name) {
        Formula formula = formulaRepository.findByName(name);
        if (formula == null) {
            throw new EntityNotFoundException(Formula.class,"name", name);
        }
        return formula.getContent();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FormulaDto create(Formula resources) {
        if(formulaRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Formula.class,"name",resources.getName());
        }
        return formulaMapper.toDto(formulaRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(key = "'findFormulaContentByName:' + #p0.name")
    public void update(Formula resources) {
        Formula formula = formulaRepository.findById(resources.getId()).orElseGet(Formula::new);
        ValidationUtil.isNull( formula.getId(),"Formula","id",resources.getId());
        Formula formula1 = null;
        formula1 = formulaRepository.findByName(resources.getName());
        if(formula1 != null && !formula1.getId().equals(formula.getId())){
            throw new EntityExistException(Formula.class,"name",resources.getName());
        }
        formula.copy(resources);
        formulaRepository.save(formula);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            formulaRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<FormulaDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FormulaDto formula : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("公式名称", formula.getName());
            map.put("公式描述", formula.getDescription());
            map.put("公式内容", formula.getContent());
            map.put("是否已被删除,Y/N", formula.getDeleteFlag());
            map.put("创建人", formula.getCreateBy());
            map.put("创建时间", formula.getCreateTime());
            map.put("更新人", formula.getUpdateBy());
            map.put("更新时间", formula.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<FormulaSmallDto> queryForSelection(String type) {
        List<Formula> list = formulaRepository.findFormulasByType(type);
        return formulaSmallMapper.toDto(list);
    }
}