/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.service.dto.TaskActionDto;
import org.cjc.les.core.service.dto.TaskActionQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskActionService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskActionQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskActionDto>
    */
    List<TaskActionDto> queryAll(TaskActionQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskActionDto
     */
    TaskActionDto findById(Long id);

    /**
     * 通过任务指令ID获取任务动作对象
     * @param taskCommandId
     * @return
     */
    TaskAction findTaskActionByTaskCommandId(Long taskCommandId);

    /**
    * 创建
    * @param resources /
    * @return TaskActionDto
    */
    TaskActionDto create(TaskAction resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskAction resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskActionDto> all, HttpServletResponse response) throws IOException;

    /**
     * 更新执行时长
     * @param resources
     */
    void updateExecutedTime(TaskAction resources);
}