/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.schedule;


import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.Method;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.ProcedureMethod;
import org.springframework.stereotype.Component;

import java.util.concurrent.LinkedBlockingQueue;

@Log4j2
@RequiredArgsConstructor
@Component
public class SampleExitScheduler {

    private final CommandListenerPool commandListenerPool;

    private LinkedBlockingQueue<Procedure> procedureQueue = new LinkedBlockingQueue<>();

    public void push(Procedure procedure) {
        procedureQueue.offer(procedure);
    }

    public void start() {

        commandListenerPool.submit(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    Procedure proc = null;
                    try {
                        proc = procedureQueue.take();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (proc == null){
                        continue;
                    }
                    ProcedureMethod exitMethod = proc.getExitMethod().get();
                    new MethodExecutor().executeMethod(exitMethod,proc);
                    if (!CommandStatusEnum.SUCCESS.equals( exitMethod.getStatus())){
                        push(proc);

                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        });

    }
}
