/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.Command;

import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-07-19
**/
@Data
public class DeviceInstanceCmdDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 设备实例命令名称 */
    private String name;

    /** 设备实例命令描述 */
    private String description;

    /** 设备实例ID */
    private Long deviceInstanceId;

    /** 设备实例名称 */
    private String deviceInstanceName;

    /** 该设备本身定义的命令ID */
    private Long commandId;
    private Command command;

    /** 代理设备实例的命令ID,外键引用les_device_instance_cmd.id(该被引用的实例命令类型应该是原始设备命令) */
    private Long proxyInstanceCmdId;

    /** 当设置为PROXY代理命令时，可以通过不同参数自定义命令行为 */
    private String parameter;

    private String testParameter;

    /** 命令类型, RAW原始命令, PROXY代理命令 */
    private String commandType;

    private String type;

    private String postExecution;

    private String bindControlCode;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /**
     * 执行结果状态
     */
    private String status;

    /**
     * 执行结果详情
     */
    private String message;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}