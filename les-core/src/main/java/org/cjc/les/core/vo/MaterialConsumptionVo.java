/*
 *  Copyright 2024-2024 Wuhan Annis Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.vo;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.ActionCommand;
import org.cjc.les.core.domain.StepAction;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.VariablePlaceholderResolver;
import org.cjc.les.exception.TaskRunningException;

import java.math.BigDecimal;
import java.util.function.Function;

@Data
public class MaterialConsumptionVo {

    private Long materialId;

    private String materialName;

    private BigDecimal materialValue;

    private String materialUnit;

    /**
     * 默认在任务运行上下文环境解析数值
     * @param formattedStr
     * @return
     */
    public static MaterialConsumptionVo parse(String formattedStr) {

        return parse(formattedStr, (str)->{
            return VariablePlaceholderResolver.resolve(str);
        });
    }

    /**
     * 指定任务指令对象解析，在任务未执行环境下解析值
     * @param formattedStr
     * @param taskCommand
     * @return
     */
    public static MaterialConsumptionVo parse(String formattedStr, TaskCommand taskCommand) {

        String param = taskCommand.getParameter();

        return parse(formattedStr, (str)->{

            String propName = extractPropName(formattedStr);
            String value = getValueFromActionVariables(propName,taskCommand.getTaskAction().getVariables());
            if (value != null) {
                return value;
            }
            return extractDefaultValue(propName, param);
        });
    }

    /**
     * 指定配置动作指令对象解析, 在任务未创建时，使用进程配置对象解析
     * @param formattedStr
     * @param actionCommand
     * @return
     */
    public static MaterialConsumptionVo parse(String formattedStr, ActionCommand actionCommand, StepAction stepAction) {

        String param = actionCommand.getParameter();

        return parse(formattedStr, (str)->{

            String propName = extractPropName(formattedStr);
            String value = getValueFromActionVariables(propName,stepAction.getVariables());
            if (value != null) {
                return value;
            }
            return extractDefaultValue(propName, param);
        });
    }

    private static MaterialConsumptionVo parse(String formattedStr, Function<String, String> valueResolveFunc) {
        MaterialConsumptionVo vo = new MaterialConsumptionVo();
        if (StringUtils.isEmpty(formattedStr)) {
            return vo;
        }
        String[] arr = formattedStr.split(":");
        if (arr.length != 4) {
            throw new TaskRunningException("MaterialConsumption format is error.");
        }
        Long materialId = Long.parseLong(arr[0]);
        String materialName = arr[1];
        String materialValue = arr[2];
        String materialUnit = arr[3];
        if (materialValue.startsWith("${")) {
            materialValue = valueResolveFunc.apply(materialValue);// VariablePlaceholderResolver.resolve(materialValue);
        }

        if (StringUtils.isEmpty(materialValue)) {
            throw new TaskRunningException("MaterialConsumption materialValue is empty.");
        }

        BigDecimal decimalValue = new BigDecimal(materialValue);

        vo.setMaterialId(materialId);
        vo.setMaterialName(materialName);
        vo.setMaterialValue(decimalValue);
        vo.setMaterialUnit(materialUnit);

        return vo;
    }

    public static String extractDefaultValue(String attrName, String inputStr) {
        if (attrName == null || inputStr == null) {
            return null;
        }

        // 构造正则表达式，例如：\$\{TASK_ID:([^}]+)}
        String regex = "\\$\\{" + attrName + ":([^}]+)}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(inputStr);

        if (matcher.find()) {
            return matcher.group(1); // 获取默认值
        }

        return null; // 未找到匹配内容
    }
    private static String extractPropName(String inputStr) {
        if (inputStr == null) {
            return null;
        }

        // 构造正则表达式，例如：\$\{TASK_ID:([^}]+)}
        String regex = "\\$\\{([^}]+)}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(inputStr);

        if (matcher.find()) {
            return matcher.group(1); // 获取默认值
        }

        return null; // 未找到匹配内容
    }

    private static String getValueFromActionVariables(String attrName, String varStr) {
        if (StringUtils.isEmpty(varStr)){
            return null;
        }
        JSONObject jsonObj = JSON.parseObject(varStr);
        for (String key : jsonObj.keySet()) {
            if (StringUtils.equalsAny(key, attrName)) {
                return jsonObj.get(key).toString();
            }
        }
        return null;
    }
}
