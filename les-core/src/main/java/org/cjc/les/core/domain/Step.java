/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.cjc.base.BaseEntity;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Data
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_step")
@SQLDelete(sql = "update les_step set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class Step extends LesConfigBaseEntity {

    /**
     * 步骤名称
     */
    private String name;

    /**
     * 动作列表
     */
    @OneToMany(mappedBy = "step", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<StepAction> actions = new ArrayList<>();

    @JSONField(serialize = false)
    @OneToMany(mappedBy = "step", cascade = CascadeType.ALL)
    private List<MethodStep> methodSteps;

    private String status = CommandStatusEnum.VALID.name();

    public void bind() {
        for (StepAction action : actions) {
            action.bind(this);
        }
    }

    public void copy(Step source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("actions","methodSteps"));

        if (this.getActions()==null){
            this.setActions(new ArrayList<>());
        }

        List<StepAction> bakObjs = new ArrayList<>(this.getActions());

        this.getActions().clear();
        for (StepAction srcObj : source.getActions()) {
            Optional<StepAction> optOther = bakObjs.stream().filter(actOther->{
                boolean b= Objects.equals(actOther.getId(),srcObj.getId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                StepAction other = optOther.get();
                other.copy(srcObj);
                this.getActions().add(other);
            }else{
                this.getActions().add(srcObj);
            }
        }

        this.bind();

    }
}
