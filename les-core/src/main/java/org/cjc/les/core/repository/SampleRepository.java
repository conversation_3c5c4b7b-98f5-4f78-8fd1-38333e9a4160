/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Sample;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
public interface SampleRepository extends JpaRepository<Sample, Long>, JpaSpecificationExecutor<Sample> {
    /**
    * 根据 Number 查询
    * @param number /
    * @return /
    */
    Sample findByNumber(String number);

    /**
     * 通过样品名称查找对应样品
     * @param name
     * @return
     */
    Optional<Sample> findSampleByName(String name);

    /**
     * 查找最近一次更新的样品
     * @return
     */
    @Query(value = "select * from les_sample s where s.delete_flag='N' order by s.update_time desc limit 1", nativeQuery = true)
    Optional<Sample> findLatestSample();
}