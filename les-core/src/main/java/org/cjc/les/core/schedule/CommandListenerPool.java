/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.schedule;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Log4j2
@Component
public class CommandListenerPool {

    private final int POOL_SIZE = 16;

    private ExecutorService executorService = Executors.newFixedThreadPool(POOL_SIZE);

    public void submit(Runnable runnable){
        executorService.submit(runnable);
    }

}
