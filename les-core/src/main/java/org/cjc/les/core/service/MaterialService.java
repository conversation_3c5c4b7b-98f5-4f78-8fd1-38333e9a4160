/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Material;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.service.dto.MaterialDto;
import org.cjc.les.core.service.dto.MaterialQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import java.util.Optional;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-03-26
**/
public interface MaterialService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(MaterialQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<MaterialDto>
    */
    List<MaterialDto> queryAll(MaterialQueryCriteria criteria);

    /**
     * 查询最新的指定条数
     *
     * @param criteria 条件参数
     * @return List<MaterialDto>
     */
    List<MaterialDto> queryTopByStatus(MaterialQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return MaterialDto
     */
    MaterialDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return MaterialDto
    */
    MaterialDto create(Material resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Material resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<MaterialDto> all, HttpServletResponse response) throws IOException;

    /**
     * 扣减剩余量
     *
     * @param id
     * @param toDeductValue
     */
    Material deductRemainValue(Long id, BigDecimal toDeductValue);

    /**
     * 通过物料ID查询点位指示数据
     * @param material
     * @return
     */
    Optional<Position> queryIndicatorPosition(Material material);

    /**
     * 编辑
     * @param resources /
     */
    void reset(Material resources);

    void resetRemainValueByPosId(Long posId);
}