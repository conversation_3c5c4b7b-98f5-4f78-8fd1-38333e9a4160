/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.TaskCommand;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Map;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskCommandRepository extends JpaRepository<TaskCommand, Long>, JpaSpecificationExecutor<TaskCommand> {

    @Query("SELECT t FROM TaskCommand t WHERE t.id = :id")
    Optional<TaskCommand> findTaskCommandById(@Param("id")Long id);

    @Modifying
    @Query(value="update TaskCommand set status=?2, message=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateStatus(Long id, String status, String message);

    @Modifying
    @Query(value="update TaskCommand set selectedDevicePos=?2, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateSelectedPositionId(Long taskCommandId, Position pos );

    @Query(value="SELECT dev.layout_image as layoutImage FROM  les_task_command tc left join les_device_instance_cmd inscmd on inscmd.id=tc.command_id\n" +
            "left join les_device_instance ins on ins.id=inscmd.device_instance_id\n" +
            "left join les_device dev on dev.id=ins.device_id\n" +
            "where tc.id=?1", nativeQuery = true)
    String findLayoutImage(Long taskCommandId);
}