/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.extern.log4j.Log4j2;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimplePdfExporterConfiguration;
import org.cjc.les.core.domain.FeatureConfig;
import org.cjc.les.core.domain.Result;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.repository.FeatureConfigRepository;
import org.cjc.les.core.repository.ResultRepository;
import org.cjc.les.core.repository.TaskPrepareRepository;
import org.cjc.les.core.service.ResultService;
import org.cjc.les.core.service.dto.*;
import org.cjc.les.core.service.mapstruct.ResultMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.io.File;
import java.io.OutputStream;
import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-25
**/
@Log4j2
@Service
@RequiredArgsConstructor
public class ResultServiceImpl implements ResultService {

    private final TaskPrepareRepository taskPrepareRepository;
    private final ResultRepository resultRepository;
    private final ResultMapper resultMapper;

    private final FeatureConfigRepository featureConfigRepository;

    @Override
    public Map<String, Object> queryAll(ResultQueryCriteria criteria, Pageable pageable) {
        Page<Result> page = resultRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);

        Map<String, Object> out = PageUtil.toPage(page.map(resultMapper::toDto));
        for (ResultDto dto : (List<ResultDto>)out.get("content")){
            if (dto.getTaskId() == null) {
                continue;
            }
        }
        // 把content列表转换为List<ResultFeatureDetailDto>
        out.put("content", convertToDetailDtoList((List<ResultDto>)out.get("content")));

        return out;
    }

    private List<ResultFeatureDetailDto> convertToDetailDtoList(List<ResultDto> resultDtoList) {
        List<ResultFeatureDetailDto> outList = new ArrayList<>();
        for (ResultDto resultDto : resultDtoList) {
            for (ResultItemDto itemDto : resultDto.getItems()) {
                for (ResultFeatureDto featureDto : itemDto.getResultFeatures()) {
                    ResultFeatureDetailDto detailDto = new ResultFeatureDetailDto();
                    BeanUtil.copyProperties(featureDto, detailDto, CopyOptions.create().setIgnoreNullValue(true));

                    detailDto.setResultName(resultDto.getName());
                    detailDto.setResultNumber(resultDto.getNumber());
                    detailDto.setTaskMethodName(itemDto.getTaskMethodName());
                    detailDto.setSampleNumber(resultDto.getSampleNumber());
                    detailDto.setSampleName(resultDto.getSampleName());
                    detailDto.setSampleCategory(resultDto.getSampleCategory());
                    detailDto.setSampleCustomer(resultDto.getSampleCustomer());
                    detailDto.setSampleTag(resultDto.getSampleTag());
                    outList.add(detailDto);
                }
            }
        }
        return outList;
    }

    @Override
    public List<ResultDto> queryAll(ResultQueryCriteria criteria) {
        List<ResultDto> out = resultMapper.toDto(resultRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
        for (ResultDto dto : out) {
            if (dto.getTaskId() == null) {
                continue;
            }

        }
        return out;
    }

    @Override
    @Transactional
    public ResultDto findById(Long id) {
        Result result = resultRepository.findById(id).orElseGet(Result::new);
        ValidationUtil.isNull(result.getId(),"Result","id",id);
        return resultMapper.toDto(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultDto create(Result resources) {
        if(resultRepository.findByNumber(resources.getNumber()) != null){
            throw new EntityExistException(Result.class,"number",resources.getNumber());
        }
        return resultMapper.toDto(resultRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Result resources) {
        Result result = null;
        if (resources.getId() == null){
            result = resources;
        }else{
            result = resultRepository.findById(resources.getId()).orElseGet(Result::new);
            result.copy(resources, true);
        }

        Result rs = resultRepository.save(result);
        resources.copy(rs);

        log.debug("rs.id=" + rs.getId());

    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            resultRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ResultDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ResultDto result : all) {
            for (ResultItemDto item : result.getItems()) {
                for (ResultFeatureDto feature : item.getResultFeatures()) {
                    Map<String, Object> map = new LinkedHashMap<>();
                    map.put("检测编号", result.getNumber());
                    map.put("检测名称", result.getName());
                    map.put("样品标签", result.getSampleTag());
                    map.put("样品名称", result.getSampleName());
                    map.put("样品类别", result.getSampleCategory());
                    map.put("送样客户", result.getSampleCustomer());

                    map.put("检测方法", item.getTaskMethodName());
                    map.put("检测项", feature.getName());
                    map.put("检测值", feature.getValue());

                    map.put("创建人", result.getCreateBy());
                    map.put("创建时间", result.getCreateTime());
                    list.add(map);
                }
            }
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void fillFeatureConfig(ResultFeature feature, FeatureConfigQueryCriteria criteria) {
        Optional<FeatureConfig> opt = featureConfigRepository.findByMethodIdAndInstrumentColumnName(criteria.getMethodId(), criteria.getInstrumentColumnName());
        if (opt.isPresent()) {
            FeatureConfig config = opt.get();
            feature.setName(config.getName());
            feature.setDescription(config.getDescription());
            feature.setUnit(config.getUnit());
            feature.setCheckRules(config.getCheckRules());
            feature.setFeatureConfigId(config.getId());
            feature.setCode(config.getCode());
        }
    }
}