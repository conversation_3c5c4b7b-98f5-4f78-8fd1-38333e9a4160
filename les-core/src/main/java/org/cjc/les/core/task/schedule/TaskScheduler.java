/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.VariableHelper;
import org.cjc.les.core.schedule.CommandListenerPool;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskStepExecutor;
import org.cjc.les.core.util.TaskBreakPointUtil;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.core.vo.MaterialConsumptionVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.RedisUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;

@Log4j2
@RequiredArgsConstructor
@Component
public class TaskScheduler {


    private final int POOL_SIZE = 4;

    private ExecutorService executorService = executorService = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(),
            new TaskThreadFactory("TaskSchedule"));

    private final TaskService taskService;

    private final EntryScheduler entryScheduler;
    private final MainScheduler mainScheduler;
    private final ExitScheduler exitScheduler;

    private final StepScheduler stepScheduler;
    private final ActionScheduler actionScheduler;

    private final RedisUtils redisUtils;

    private LinkedBlockingQueue<Task> taskQueue = new LinkedBlockingQueue<>();

    public LinkedBlockingQueue<Task> getTaskQueue(){
        return taskQueue;
    }

    public Optional<Task> getTaskById(Long taskId){
        for (Task task : taskQueue){
            if (task.getId().equals(taskId)){
                return Optional.of(task);
            }
        }
        return Optional.empty();
    }

    /**
     * 通过标签查找在线的任务
     * @param tagValue
     * @return
     */
    public Optional<Task> getTaskByTag(String tagValue){
        for (Task task : taskQueue){
            if (StringUtils.equalsIgnoreCase(tagValue, task.getTag())){
                return Optional.of(task);
            }
        }
        return Optional.empty();
    }

    /**
     * 计算在想任务中的物料待使用量, 待扣减物料任务指令还未被执行
     * @return
     */
    public Map<Long, BigDecimal> calculateMaterialToUseValue(){
        Map<Long, BigDecimal> out = new HashMap<>();
        for (Task task : taskQueue){
            for (TaskMethod method : task.getMethodList()){
                for (TaskStep step : method.getSteps() ) {
                    for (TaskAction action : step.getActions()) {
                        for (TaskCommand cmd : action.getCommands()) {
                            if (StringUtils.isEmpty(cmd.getMaterialConsumption()) || StringUtils.equalsAny( step.getStatus(),
                                    RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ){
                                continue;
                            }
                            parseMaterialConsumption(cmd, out);
                        }
                    }
                }
            }
        }

        return out;
    }

    private void parseMaterialConsumption( TaskCommand cmd, Map<Long, BigDecimal> out) {
        String materialConsumption = cmd.getMaterialConsumption();
        MaterialConsumptionVo vo = MaterialConsumptionVo.parse(materialConsumption, cmd);
        BigDecimal value = out.get(vo.getMaterialId());
        if (value == null) {
            value = new BigDecimal(vo.getMaterialValue().toString());
        }else{
            value = value.add(vo.getMaterialValue());
        }
        out.put(vo.getMaterialId(), value);
    }


    private boolean existedInQueue(Task taskParam){
        for (Task task : taskQueue){
            if (task.getId().equals( taskParam.getId())){
                return true;
            }
        }

        return false;
    }

    public void schedule(Task task) {

        if (existedInQueue(task)){
            throw new TaskRunningException("The Task:"+task.getId()+" already been in taskQueue. ");
        }

        VariableHelper.setVar("TASK_ID", task.getId(), task.getContext());

        VariableHelper.initTaskVariables(task);

        task.setStatus(RunStatusEnum.IN_SCHEDULE_QUE.name());
        task.setMessage("进入调度队列");
        setScheduleEntryTime(task );
        TaskExecuteLogUtil.setScheduleEntryTime(task);
        TaskExecuteLogUtil.logTask(task);
        // 缓存到任务调度队列
        taskQueue.offer(task);
        //2. 进样方法推送到进样方法线程池
        if (task.getEntryMethod().isPresent()
                && StringUtils.equalsAny(task.getEntryMethod().get().getStatus(),"READY") ) {
            entryScheduler.submit(task.getEntryMethod().get());
        }
        //3. 出样方法自动检测满足条件后，推送到出样方法线程池
        //4. 主方法满足进样方法完成，且满足排程条件后，推送到主体方法线程池

    }

    private void setScheduleEntryTime(Task task) {
        task.setScheduleEntryTime(System.currentTimeMillis());
        for (TaskMethod mth : task.getMethodList()) {
            for (TaskStep step : mth.getSteps()) {
                step.setScheduleEntryTime(task.getScheduleEntryTime());
            }
        }
    }

    public void start() {
        startEntryScheduler();
        startExitScheduler();
        startMainScheduler();
        checkTaskDone();
    }

    public void cancel(Task task) {
        Optional<Task> taskInQueOpt = taskQueue.stream().filter(t->{return t.getId().equals(task.getId());}).findFirst();
        if (!taskInQueOpt.isPresent()) {
            redisUtils.delKeysByPatternAndValue("lock.*",task.getId().toString());
            log.warn("No need to remove from taskQueue.");
            return;
        }
        Task taskInQue = taskInQueOpt.get();
        taskQueue.remove(taskInQue);

        taskInQue.setStatus(RunStatusEnum.CANCELLED.name());

        // 遍历方法
        for (int idxMth=taskInQue.getMethodList().size()-1; idxMth>=0; idxMth--){
            TaskMethod taskMethod = taskInQue.getMethodList().get(idxMth);
            if ( !StringUtils.equalsAny( taskMethod.getStatus(),
                    RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                taskMethod.setStatus(RunStatusEnum.CANCELLED.name());
            }
            // 遍历步骤
            for (int idxStep=taskMethod.getSteps().size()-1; idxStep>=0; idxStep--){
                TaskStep step = taskMethod.getSteps().get(idxStep);
                if ( !StringUtils.equalsAny( step.getStatus(),
                        RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                    step.setStatus(RunStatusEnum.CANCELLED.name());
                }
                stepScheduler.cancel(step);

                // 遍历动作
                for (int idxAct=step.getActions().size()-1; idxAct>=0; idxAct--){
                    TaskAction action = step.getActions().get(idxAct);
                    if ( !StringUtils.equalsAny( action.getStatus(),
                            RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                        action.setStatus(RunStatusEnum.CANCELLED.name());
                    }

                    // 遍历命令
                    for (int idxCmd=action.getCommands().size()-1; idxCmd>=0; idxCmd--){
                        TaskCommand taskCommand = action.getCommands().get(idxCmd);
                        if ( !StringUtils.equalsAny( taskCommand.getStatus(),
                                RunStatusEnum.SUCCESS.name(),RunStatusEnum.FAILED.name(),RunStatusEnum.CANCELLED.name()) ) {
                            taskCommand.setStatus(RunStatusEnum.CANCELLED.name());
                        }
                    }

                    actionScheduler.cancel(action);
                }
            }
        }

        redisUtils.delKeysByPatternAndValue("lock.*",task.getId().toString());
    }

    private void startEntryScheduler() {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    Iterator<Task> taskIterator = taskQueue.iterator();
                    while (taskIterator.hasNext()) {
                        Task task = taskIterator.next();

                        Optional<TaskMethod> entryMethodOpt = task.getEntryMethod();
                        if (!entryMethodOpt.isPresent()) {
                            continue;
                        }

                        TaskMethod entryMethod = entryMethodOpt.get();
                        TaskExecutorContext.copy(task.getContext(), entryMethod);
                        TaskBreakPointUtil.checkBreakPoint(entryMethod);

                        if (!(StringUtils.equalsAny(entryMethod.getStatus(), CommandStatusEnum.READY.name()))) {
                            continue;
                        }

                        /**
                         * 检测出样方法的第一个步骤，若返回成功，执行出样方法
                         */
                        entryMethod.setStatus(CommandStatusEnum.IN_SCHEDULE_QUE.name());
                        entryScheduler.submit(entryMethod);

                    }

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    private void startExitScheduler() {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    Iterator<Task> taskIterator = taskQueue.iterator();
                    while (taskIterator.hasNext()) {
                        Task task = taskIterator.next();

                        Optional<TaskMethod> exitMethodOpt = task.getExitMethod();
                        if (!exitMethodOpt.isPresent()) {
                            continue;
                        }

                        TaskMethod exitMethod = exitMethodOpt.get();

                        if ( ! ( StringUtils.equalsAny(exitMethod.getStatus(), CommandStatusEnum.READY.name()) && exitMethod.getSteps().size()>0) ){
                            continue;
                        }

                        /**
                         * 检测出样方法的第一个步骤，若返回成功，执行出样方法
                         */
                        TaskExecutorContext.copy(task.getContext(), exitMethod);

                        TaskBreakPointUtil.checkBreakPoint(exitMethod);

                     //   TaskStep firstStep = exitMethod.getSteps().get(0);
                     //   ExecutorConfig executorConfig = new ExecutorConfig();
                      //  executorConfig.setRecordLog(false);
                     //   resetStepStatus(firstStep);
                        // 临时调试
                     //   new TaskStepExecutor(executorConfig).execute(firstStep);
                     //   if ( ! StringUtils.equalsAny( firstStep.getStatus() , RunStatusEnum.SUCCESS.name() ) ) {
                    //        continue;
                   //     }
                     //   resetStepStatus(firstStep);
                        exitMethod.setStatus(CommandStatusEnum.IN_SCHEDULE_QUE.name());
                        exitScheduler.submit(exitMethod);

/*                        boolean allMainMethodDone = task.getMethodList().stream().filter(mth->{
                            return StringUtils.equalsAnyIgnoreCase(mth.getType(),"ENTRY","MAIN");
                        }).allMatch(mth -> {
                            return StringUtils.equalsAny(mth.getStatus(), CommandStatusEnum.SUCCESS.name(), CommandStatusEnum.FAILED.name());
                        });

                        if (StringUtils.equalsAny(exitMethod.getStatus(), CommandStatusEnum.READY.name())
                                && allMainMethodDone) {
                            // 具备出样的条件,最简单算法，等待该任务所有的方法执行完成, 后续优化算法为只要该样品不再被使用即可, 检测该任务样品对应点位状态为DONE
                            exitMethod.setStatus(CommandStatusEnum.IN_SCHEDULE_QUE.name());
                            exitScheduler.submit(exitMethod);
                        }*/

                    }

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    private void startMainScheduler() {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    Iterator<Task> taskIterator = taskQueue.iterator();
                    while (taskIterator.hasNext()) {
                        Task task = taskIterator.next();
                        for (TaskMethod taskMethod : task.getMainMethodList()) {
                            TaskExecutorContext.copy(task.getContext(), taskMethod);
                            TaskBreakPointUtil.checkBreakPoint(taskMethod);

                            if (StringUtils.equalsAny(taskMethod.getStatus(), CommandStatusEnum.READY.name())
                                    &&(!task.getEntryMethod().isPresent()
                                    || StringUtils.equalsAny(task.getEntryMethod().get().getStatus(),
                                    RunStatusEnum.SUCCESS.name(),
                                    RunStatusEnum.SUCCESS_WITH_FORKED.name(),
                                    RunStatusEnum.SKIPPED.name() )) ) {
                                taskMethod.setStatus(CommandStatusEnum.IN_SCHEDULE_QUE.name());

                                mainScheduler.submit(taskMethod);
                            }
                        }
                    }

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        });


    }

    private void checkTaskDone() {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    Iterator<Task> taskIterator = taskQueue.iterator();
                    while (taskIterator.hasNext()) {
                        Task task = taskIterator.next();

                        Optional<TaskMethod> taskMethodOpt = task.getMethodList().stream().filter(t->{
                            return !StringUtils.equalsAny(t.getStatus(), "SUCCESS","FAILED","SKIPPED", "SUSPEND");
                        }).findFirst();
                        if (taskMethodOpt.isPresent()) {
                            // 存在未完成的方法，任务未完成
                            continue;
                        }

                        taskIterator.remove();

                        Optional<TaskMethod> failedTaskMethodOpt = task.getMethodList().stream().filter(t->{
                            return StringUtils.equalsAny(t.getStatus(), "FAILED");
                        }).findFirst();
                        if (failedTaskMethodOpt.isPresent()){
                            task.setStatus(RunStatusEnum.FAILED.name());
                            task.setMessage("任务执行失败");
                        }else{
                            task.setStatus(RunStatusEnum.SUCCESS.name());
                            task.setMessage("任务执行成功");
                        }
                        TaskExecuteLogUtil.logTask(task);
                    }

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        });

    }

    private void resetStepStatus(TaskStep step) {
        step.setStatus(RunStatusEnum.READY.name());
        for (TaskAction act : step.getActions()) {
            act.setStatus(RunStatusEnum.READY.name());
            for (TaskCommand cmd : act.getCommands()){
                cmd.setStatus(RunStatusEnum.READY.name());
            }
        }
    }

}
