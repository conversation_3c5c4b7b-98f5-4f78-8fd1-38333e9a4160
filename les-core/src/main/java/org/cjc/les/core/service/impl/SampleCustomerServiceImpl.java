/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.SampleCustomer;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.SampleCustomerRepository;
import org.cjc.les.core.service.SampleCustomerService;
import org.cjc.les.core.service.dto.SampleCustomerDto;
import org.cjc.les.core.service.dto.SampleCustomerQueryCriteria;
import org.cjc.les.core.service.mapstruct.SampleCustomerMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-22
**/
@Service
@RequiredArgsConstructor
public class SampleCustomerServiceImpl implements SampleCustomerService {

    private final SampleCustomerRepository sampleCustomerRepository;
    private final SampleCustomerMapper sampleCustomerMapper;

    @Override
    public Map<String,Object> queryAll(SampleCustomerQueryCriteria criteria, Pageable pageable){
        Page<SampleCustomer> page = sampleCustomerRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sampleCustomerMapper::toDto));
    }

    @Override
    public List<SampleCustomerDto> queryAll(SampleCustomerQueryCriteria criteria){
        return sampleCustomerMapper.toDto(sampleCustomerRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public SampleCustomerDto findById(Long id) {
        SampleCustomer sampleCustomer = sampleCustomerRepository.findById(id).orElseGet(SampleCustomer::new);
        ValidationUtil.isNull(sampleCustomer.getId(),"SampleCustomer","id",id);
        return sampleCustomerMapper.toDto(sampleCustomer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SampleCustomerDto create(SampleCustomer resources) {
        if(sampleCustomerRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(SampleCustomer.class,"name",resources.getName());
        }
        return sampleCustomerMapper.toDto(sampleCustomerRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SampleCustomer resources) {
        SampleCustomer sampleCustomer = sampleCustomerRepository.findById(resources.getId()).orElseGet(SampleCustomer::new);
        ValidationUtil.isNull( sampleCustomer.getId(),"SampleCustomer","id",resources.getId());
        Optional<SampleCustomer> sampleCustomerOpt = sampleCustomerRepository.findByName(resources.getName());
        if(sampleCustomerOpt.isPresent() && !sampleCustomerOpt.get().getId().equals(sampleCustomer.getId())){
            throw new EntityExistException(SampleCustomer.class,"name",resources.getName());
        }
        sampleCustomer.copy(resources);
        sampleCustomerRepository.save(sampleCustomer);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            sampleCustomerRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SampleCustomerDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SampleCustomerDto sampleCustomer : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("客户名称", sampleCustomer.getName());
            map.put("客户联系人", sampleCustomer.getContacts());
            map.put("客户联系电话", sampleCustomer.getTelephone());
            map.put("客户联系地址", sampleCustomer.getCustomerAddress());
            map.put("客户描述", sampleCustomer.getDescription());
            map.put("是否已被删除,Y/N", sampleCustomer.getDeleteFlag());
            map.put("创建人", sampleCustomer.getCreateBy());
            map.put("创建时间", sampleCustomer.getCreateTime());
            map.put("更新人", sampleCustomer.getUpdateBy());
            map.put("更新时间", sampleCustomer.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}