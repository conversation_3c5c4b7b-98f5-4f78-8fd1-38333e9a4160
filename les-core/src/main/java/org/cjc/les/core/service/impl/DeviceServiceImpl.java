package org.cjc.les.core.service.impl;

import lombok.RequiredArgsConstructor;
import org.cjc.exception.ApplicationException;
import org.cjc.les.core.domain.Command;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.repository.CommandRepository;
import org.cjc.les.core.repository.DeviceInstanceRepository;
import org.cjc.les.core.repository.DeviceRepository;
import org.cjc.les.core.service.DeviceService;
import org.cjc.les.core.service.dto.DeviceCriteria;
import org.cjc.les.core.service.dto.DeviceDto;
import org.cjc.les.core.service.dto.DeviceQueryCriteria;
import org.cjc.les.core.service.mapstruct.DeviceMapper;
import org.cjc.les.exception.ConfigApplicationException;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@Service
@RequiredArgsConstructor
public class DeviceServiceImpl implements DeviceService {

    private final DeviceRepository deviceRepository;

    private final DeviceInstanceRepository deviceInstanceRepository;

    private final CommandRepository commandRepository;

    private final DeviceMapper deviceMapper;

    @Override
    public Map<String,Object> queryAll(DeviceQueryCriteria criteria, Pageable pageable){
        Page<Device> page = deviceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(deviceMapper::toDto));
    }

    @Override
    public List<DeviceDto> queryAll(DeviceQueryCriteria criteria){
        return deviceMapper.toDto(deviceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public DeviceDto findById(Long id) {
        Device device = deviceRepository.findById(id).orElseGet(Device::new);
        ValidationUtil.isNull(device.getId(),"Device","id",id);
        return deviceMapper.toDto(device);
    }

    @Override
    public Device create(Device resources) {
        return deviceRepository.save(resources);
    }

    @Override
    public void update(Device resources) {
        Optional<Device> deviceInDbOpt = Optional.empty();
        if (resources.getId() != null){
            deviceInDbOpt = deviceRepository.findById(resources.getId());
        }else {
            deviceInDbOpt = this.findDeviceByJavaClassName(resources.getJavaClassName());
        }
        if (!deviceInDbOpt.isPresent()){
            // 不存在的设备定义，自动新增
            deviceRepository.save(resources);
            return;
            // throw new TaskRunningException("Could not found Device by id:"+resources.getId());
        }
        Device deviceInDb = deviceInDbOpt.get();

        deviceInDb.copy(resources);
        for (Command command: deviceInDb.getCommands()){
            command.setDevice(deviceInDb);
        }
        deviceRepository.save(deviceInDb);
        resources.setId(deviceInDb.getId());
    }

    @Override
    public void save(Device resources) {
        update(resources);
    }

    @Override
    public void deleteAll(Long[] ids) throws ApplicationException {
        long insCount = deviceRepository.countDeviceInstances(ids);
        if (insCount>0){
            throw new ConfigApplicationException("DEV_INSTANCE_EXISTED","There are "+insCount+" device instances, DO NOT permit to delete this device.");
        }
        for (Long id : ids) {
            deviceRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DeviceDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DeviceDto device : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("设备名称", device.getName());
            map.put("设备描述", device.getDescription());
            map.put("设备类型:SYS,ROBOT,CONTROL,PERIPHERAL,STORAGE", device.getType());
            map.put("配置信息，JSON串，或者普通的字符串", device.getConfigTemplate());
            map.put("该设备自定义的Java类名", device.getJavaClassName());
            map.put("该设备自定义的Java配置类名", device.getConfigJavaClassName());
            map.put("设备布局中的显示图片地址", device.getLayoutImage());
            map.put("设备图片宽度", device.getLayoutWidth());
            map.put("设备图片高度", device.getLayoutHeight());
            map.put("是否已被删除,Y/N", device.getDeleteFlag());
            map.put("创建人", device.getCreateBy());
            map.put("创建时间", device.getCreateTime());
            map.put("更新人", device.getUpdateBy());
            map.put("更新时间", device.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void saveCommands(List<Command> commands) {
        for (Command command : commands) {
          //  Command cmdInDb = commandRepository.findCommandByDeviceIdAndJavaMethodNameAndParameterType(command.getDeviceId(), command.getJavaMethodName(), command.getParameterType()).orElseGet(Command::new);
          //  cmdInDb.copy(command);
         //   commandRepository.save(cmdInDb);
        }
    }

    @Override
    public DeviceInstance createDeviceInstance(DeviceInstance deviceInstance) {
        DeviceInstance savedDevInstance = deviceInstanceRepository.save(deviceInstance);
        return savedDevInstance;
    }

    @Override
    public List<DeviceDto> findAllValidDevices() {
        DeviceCriteria criteria = new DeviceCriteria();
        return deviceMapper.toDto(deviceRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Transactional(readOnly = true)
    @Override
    public Optional<Device> findDeviceByJavaClassName(String javaClassName) {
        Optional<Device>  optDev = deviceRepository.findDeviceByJavaClassNameEquals(javaClassName);
        if (optDev.isPresent()) {
            optDev.get().getCommands();
        }
        return optDev;
    }

}
