/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.cjc.base.LesBaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.*;

/**
 * 设备点位
 */
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Entity
@Data
@Table(name="les_position")
@SQLDelete(sql = "update les_position set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class Position  extends LesConfigBaseEntity {

    public static enum StatusEnum{
        NONE, // 未设置，不可用
        IDLE, // 空闲状态
        READY, // 容器已可用
        HOLD, // 被占用状态
        RUNNING, // 运行中
        TO_ENTER, // 待进入状态
        LEAVE, // 暂时离开状态，还会还回来的.
        WAITING, // 系统等待
        DONE, // 该点位对应物料已使用完毕，该状态下可以出样，出样完成后就可以设置为IDLE状态，可以被重新使用
        TO_CONFIRM // 待用户确认状态
    };

    public static enum TypeEnum {
        DEFAULT,
        BOARD
    };
    /**
     * 坐标命名
     */
    private String name;

    /**
     * 所属设备ID
     */
 //   private Long deviceInstanceId;
    /**
     * 放置的物体名称
     */
    private String objectName;

    /**
     * X轴点位
     */
    private double xpos;

    /**
     * Y轴点位
     */
    private double ypos;

    /**
     * Z轴点位
     */
    private double zpos;

    /**
     * 该点位在设备中点位列表所处的索引
     */
  //  private int devicePosIndex = 0;

    @Column(name = "robot_pos_code")
    @Schema(description = "机器人定义的位置编号，用于机器人点位唯一识别")
    private String robotPosCode;

    /**
     * 服务码
     */
    @Column(name = "service_code")
    private String serviceCode;

    /**
     * 点位类型:BOARD, DEFAULT
     */
    private String type;

    private String nodeId;
    /**
     * 板位ID
     */
    private String boardNodeId;

    /**
     * 该设备拥有的点位列表
     */
    @OneToMany(mappedBy = "position", targetEntity = PositionRobot.class, fetch = FetchType.EAGER, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    private Set<PositionRobot> robots = new HashSet<>();

    /**
     * 是否可见: Y/N
     */
    private String visible;

    /**
     * 该点位状态
     */
    @Enumerated(EnumType.STRING)
    private StatusEnum status = StatusEnum.NONE;

    /**
     * 点位初始状态
     */
    @Enumerated(EnumType.STRING)
    private StatusEnum initStatus = StatusEnum.NONE;

    /**
     * 占用级别:TASK/METHOD/STEP/ACTION
     */
    private String holdLevel;

    /**
     * 被占用的任务ID
     */
    private Long holdByTaskId;

    @Transient
    private String holdByTaskNumber;
    @Transient
    private String holdByTaskName;

    private Long holdByTaskMethodId;

    private Long holdByTaskStepId;

    /**
     * 被占用的动作ID
     */
    private Long holdByTaskActionId;

    @Transient
    private String holdByTaskActionName;
    @Transient
    private Long runningStartTime;
    @Transient
    private Long runningEndTime;

    /**
     * 当与物料管理绑定时，表示物料总数
     */
    @Transient
    private BigDecimal materialTotalValue;
    /**
     * 当与物料管理绑定时，表示物料剩余数
     */
    @Transient
    private BigDecimal materialRemainValue;
    /**
     * 当与物料管理绑定时，表示物料单位
     */
    @Transient
    private String materialUnit;

    /**
     * 引用设备实例
     */
    @JSONField(serialize = false)
    @JoinColumn(name = "device_instance_id", referencedColumnName = "id")
    @ManyToOne(fetch= FetchType.EAGER, targetEntity = DeviceInstance.class)
    private DeviceInstance deviceInstance;


    public void copy(Position source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("robots","createTime"));

        // 点位列表
        if (this.getRobots() == null) {
            this.setRobots(new HashSet<>());
        }

        List<PositionRobot> deleteList = new ArrayList<>();
        for (PositionRobot positionRobot : this.getRobots()) {
            Optional<PositionRobot> optOther = source.getRobots().stream().filter(objOther -> {
                boolean b = positionRobot.getRobotDevInstanceId().equals(objOther.getRobotDevInstanceId());
                return b;
            }).findFirst();
            if (optOther.isPresent()) {
                positionRobot.copy(optOther.get());
                positionRobot.setPosition(this);
                optOther.get().setId(positionRobot.getId());
            } else {
                deleteList.add(positionRobot);
            }

        }

        source.getRobots().forEach(act -> {
            if (act.getId() == null) {
                this.getRobots().add(act);
                act.setPosition(this);
            }
        });

        if (CollectionUtils.isNotEmpty(deleteList)) {
            this.getRobots().removeAll(deleteList);
        }
    }


    @Override
    public String toString() {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("id=").append(this.getId())
                .append(",name=").append(this.getName())
                .append(",status=").append(this.getStatus());
        return stringBuffer.toString();
    }
}
