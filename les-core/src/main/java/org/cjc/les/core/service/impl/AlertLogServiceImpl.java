/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.AlertLog;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.AlertLogRepository;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.dto.AlertLogDto;
import org.cjc.les.core.service.dto.AlertLogQueryCriteria;
import org.cjc.les.core.service.dto.AlertStatItemDto;
import org.cjc.les.core.service.mapstruct.AlertLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-10-14
**/
@Service
@RequiredArgsConstructor
public class AlertLogServiceImpl implements AlertLogService {

    private final AlertLogRepository alertLogRepository;
    private final AlertLogMapper alertLogMapper;

    @Override
    public Map<String,Object> queryAll(AlertLogQueryCriteria criteria, Pageable pageable){
        Page<AlertLog> page = alertLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(alertLogMapper::toDto));
    }

    @Override
    public List<AlertLogDto> queryAll(AlertLogQueryCriteria criteria){
        return alertLogMapper.toDto(alertLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<AlertLogDto> findLatestAlertLogInInitStatus() {
        return alertLogMapper.toDto(alertLogRepository.findLatestAlertLogInInitStatus());
    }

    @Override
    public List<AlertStatItemDto> findAlertLogStatsLevelInInitStatus() {
        List<Object[]> stats = alertLogRepository.findAlertLogStatsLevelInInitStatus();
        List<AlertStatItemDto> itemDtoList = new ArrayList<>();
        for (Object[] stat : stats) {
            AlertStatItemDto dto = new AlertStatItemDto();
            itemDtoList.add(dto);
            dto.setName(String.valueOf(stat[0]));
            dto.setValue(String.valueOf(stat[1]));
        }
        return itemDtoList;
    }

    @Override
    @Transactional
    public AlertLogDto findById(Long id) {
        AlertLog alertLog = alertLogRepository.findById(id).orElseGet(AlertLog::new);
        ValidationUtil.isNull(alertLog.getId(),"AlertLog","id",id);
        return alertLogMapper.toDto(alertLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlertLogDto create(AlertLog resources) {
        return alertLogMapper.toDto(alertLogRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AlertLog resources) {
        AlertLog alertLog = alertLogRepository.findById(resources.getId()).orElseGet(AlertLog::new);
        ValidationUtil.isNull( alertLog.getId(),"AlertLog","id",resources.getId());
        alertLog.copy(resources);
        alertLogRepository.save(alertLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void createOrUpdate(AlertLog resources) {
        List<AlertLog> alertLogList = alertLogRepository.findByAlertCategoryAndAlertSourceIdAndFixStatus(
                resources.getAlertCategory(), resources.getAlertSourceId(), AlertLog.FixStatusEnum.INIT.name());
        if (alertLogList.size()>0){
            AlertLog dbLog = alertLogList.get(0);
            dbLog.setMessage(resources.getMessage());
            alertLogRepository.save(dbLog);
        }else{
            resources.setId(null);
            resources.setFixStatus(AlertLog.FixStatusEnum.INIT.name());
            alertLogRepository.save(resources);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void fixLog(AlertLog resources) {
        List<AlertLog> alertLogList = alertLogRepository.findByAlertCategoryAndAlertSourceIdAndFixStatus(
                resources.getAlertCategory(), resources.getAlertSourceId(), AlertLog.FixStatusEnum.INIT.name());
        if (alertLogList.size()>0){
            AlertLog dbLog = alertLogList.get(0);
           // dbLog.setMessage(resources.getMessage());
            dbLog.setFixedBy(resources.getFixedBy());
            dbLog.setFixStatus(AlertLog.FixStatusEnum.FIXED.name());
            dbLog.setFixType(resources.getFixType());
            dbLog.setFixedTime(Timestamp.valueOf(LocalDateTime.now()));
            alertLogRepository.save(dbLog);
        }
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            alertLogRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<AlertLogDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (AlertLogDto alertLog : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("告警编码", alertLog.getAlertCode());
            map.put("告警，名称", alertLog.getAlertName());
            map.put("告警级别：FATAL, ERROR, WARN", alertLog.getAlertLevel());
            map.put("告警信息", alertLog.getMessage());
            map.put("告警类别：DEV_INSTANCE, TASK,SYS", alertLog.getAlertCategory());
            map.put("告警来源ID, deviceInstanceId, taskId", alertLog.getAlertSourceId());
            map.put("告警处理状态, INIT, FIXED", alertLog.getFixStatus());
            map.put("告警修复类型: AUTO,MANUAL", alertLog.getFixType());
            map.put("告警修复人", alertLog.getFixedBy());
            map.put("告警修复时间", alertLog.getFixedTime());
            map.put("是否已被删除,Y/N", alertLog.getDeleteFlag());
            map.put("创建人", alertLog.getCreateBy());
            map.put("创建时间", alertLog.getCreateTime());
            map.put("更新人", alertLog.getUpdateBy());
            map.put("更新时间", alertLog.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}