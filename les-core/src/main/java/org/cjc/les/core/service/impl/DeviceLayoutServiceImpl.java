/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.constants.CommandTypeEnum;
import org.cjc.les.core.domain.*;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.*;
import org.cjc.les.core.service.DeviceLayoutService;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.dto.DeviceLayoutDto;
import org.cjc.les.core.service.dto.DeviceLayoutQueryCriteria;
import org.cjc.les.core.service.mapstruct.DeviceInstanceMapper;
import org.cjc.les.core.service.mapstruct.DeviceLayoutMapper;
import org.cjc.les.exception.ConfigApplicationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-04
**/
@Service
@RequiredArgsConstructor
@Log4j2
public class DeviceLayoutServiceImpl implements DeviceLayoutService {

    private final DeviceLayoutRepository deviceLayoutRepository;
    private final DeviceLayoutMapper deviceLayoutMapper;

    private final PositionRepository positionRepository;

    private final TaskRepository taskRepository;
    private final TaskActionRepository taskActionRepository;

    private final MaterialRepository materialRepository;

    private final DeviceInstanceMapper deviceInstanceMapper;

    @Override
    public Map<String,Object> queryAll(DeviceLayoutQueryCriteria criteria, Pageable pageable){
        Page<DeviceLayout> page = deviceLayoutRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(deviceLayoutMapper::toDto));
    }

    @Override
    public List<DeviceLayoutDto> queryAll(DeviceLayoutQueryCriteria criteria){
        return deviceLayoutMapper.toDto(deviceLayoutRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public DeviceLayoutDto findById(Long id) {
        DeviceLayout deviceLayout = deviceLayoutRepository.findById(id).orElseGet(DeviceLayout::new);
        ValidationUtil.isNull(deviceLayout.getId(),"DeviceLayout","id",id);
        return deviceLayoutMapper.toDto(deviceLayout);
    }

    @Override
    public DeviceLayoutDto findLatest(String status) {
        DeviceLayout deviceLayout = new DeviceLayout();
        if (StringUtils.isEmpty(status) || StringUtils.equalsIgnoreCase(status, "ALL")){
            deviceLayout =  deviceLayoutRepository.findLatest().orElseGet(DeviceLayout::new);
        }else{
            deviceLayout =  deviceLayoutRepository.findLatestByStatus(status).orElseGet(DeviceLayout::new);
        }

        if (deviceLayout.getId() != null) {
            for (DeviceInstance deviceInstance : deviceLayout.getDeviceInstanceList()) {
                if (deviceInstance.getProxy() != null) {
                    deviceInstance.setInitialized(deviceInstance.getProxy().getDeviceInstance().isInitialized());
                }
                // 刷新设备原有命令清单
                List<DeviceInstanceCmd> instanceCmdList = deviceInstance.getCommands();
                List<Command> devCmdList = deviceInstance.getDevice().getCommands();
                for (Command cmd : devCmdList) {
                    if(isContainsCommand(cmd, instanceCmdList)){
                        continue;
                    }
                    instanceCmdList.add(createNewInstanceCommand(deviceInstance,cmd));
                }
                // 更新点位详情
                for (Position pos : deviceInstance.getPositions()) {
                    // 更新物料信息
                    if (StringUtils.equalsAny(pos.getType(),"INDICATOR")) {
                        fillPositionMaterialValues(deviceInstance, pos);
                    }

                    // 更新任务执行信息
                    if (pos.getHoldByTaskId() != null &&
                            (!pos.getStatus().equals(Position.StatusEnum.IDLE)
                                    && !pos.getStatus().equals(Position.StatusEnum.NONE)
                            && !pos.getStatus().equals(Position.StatusEnum.READY) ) ) {
                        fillPositionTaskRunningInfo(pos);
                    }
                }
            }
        }

        return deviceLayoutMapper.toDto(deviceLayout);
    }

    private void fillPositionMaterialValues(DeviceInstance deviceInstance, Position position) {
        Optional<Material> materialOpt = materialRepository.findByNameAndDeviceInstanceId(position.getObjectName(), deviceInstance.getId());
        if (materialOpt.isPresent()){
            Material material = materialOpt.get();
            position.setMaterialUnit(material.getUnit());
            position.setMaterialRemainValue(material.getRemainValue());
            position.setMaterialTotalValue(material.getTotalValue());
        }
    }

    private void fillPositionTaskRunningInfo(Position pos) {
        Optional<Task> taskOpt = taskRepository.findById(pos.getHoldByTaskId());
        if (taskOpt.isPresent()) {
            Task task = taskOpt.get();
            pos.setHoldByTaskName(task.getTaskName());
            pos.setHoldByTaskNumber(task.getTaskNumber());
            if (pos.getHoldByTaskActionId() == null) {
                return;
            }
            Optional<TaskAction> taskActionOpt = taskActionRepository.findById(pos.getHoldByTaskActionId());
            if (taskActionOpt.isPresent()) {
                TaskAction taskAction = taskActionOpt.get();
                pos.setHoldByTaskActionName(taskAction.getName());
                if (task.getScheduleEntryTime() != null && taskAction.getExecutedStart() != null && taskAction.getEvaluateExecutingDuration() != null) {
                    pos.setRunningStartTime(task.getScheduleEntryTime() + taskAction.getExecutedStart());
                    pos.setRunningEndTime(pos.getRunningStartTime() + taskAction.getEvaluateExecutingDuration());
                }
            }
        }
    }

    @Override
    public DeviceLayoutDto getLatestLayoutStyle(String status) {
        DeviceLayout deviceLayout = new DeviceLayout();
        if (StringUtils.isEmpty(status) || StringUtils.equalsIgnoreCase(status, "ALL")){
            deviceLayout =  deviceLayoutRepository.findLatest().orElseGet(DeviceLayout::new);
        }else{
            deviceLayout =  deviceLayoutRepository.findLatestByStatus(status).orElseGet(DeviceLayout::new);
        }
        DeviceLayoutDto dto = new DeviceLayoutDto();
        dto.setId(deviceLayout.getId());
        dto.setStyleSetting(deviceLayout.getStyleSetting());
        return dto;
    }


    private boolean isContainsCommand(Command cmd, List<DeviceInstanceCmd> instanceCmdList) {
        if (CollectionUtils.isEmpty(instanceCmdList)){
            return false;
        }
        for (DeviceInstanceCmd instanceCmd : instanceCmdList) {
            if ( cmd.equals( instanceCmd.getCommand()) ) {
                return true;
            }
        }
        return false;
    }

    private DeviceInstanceCmd createNewInstanceCommand(DeviceInstance deviceInstance, Command cmd){
        DeviceInstanceCmd instanceCmd = new DeviceInstanceCmd();
        instanceCmd.setCommand(cmd);
        instanceCmd.setCommandType(CommandTypeEnum.RAW.name());
        instanceCmd.setDescription(cmd.getDescription());
        instanceCmd.setName(cmd.getName());
        instanceCmd.setDeviceInstance(deviceInstance);
        return instanceCmd;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceLayoutDto create(DeviceLayout resources) {
        return deviceLayoutMapper.toDto(deviceLayoutRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceLayout resources) throws ConfigApplicationException {
        DeviceLayout deviceLayout = deviceLayoutRepository.findById(resources.getId()).orElseGet(DeviceLayout::new);
        ValidationUtil.isNull( deviceLayout.getId(),"DeviceLayout","id",resources.getId());
        validateUsedDevInstances(deviceLayout, resources);
        deviceLayout.copy(resources);
        deviceLayoutRepository.save(deviceLayout);
    }

    private void validateUsedDevInstances(DeviceLayout rawDevLayout, DeviceLayout updatedDevLayout) throws ConfigApplicationException {
        List<DeviceInstance> toRemoveDevInstList = new ArrayList<>();
        for (DeviceInstance devInst : rawDevLayout.getDeviceInstanceList()) {
            if (!updatedDevLayout.getDeviceInstanceList().stream().anyMatch(dev->{
                return devInst.getId().equals( dev.getId());})){
                toRemoveDevInstList.add(devInst);
            }
        }
        if (CollectionUtils.isNotEmpty(toRemoveDevInstList)) {
            Long[] devInstIds = toRemoveDevInstList.stream().map(DeviceInstance::getId).toArray(Long[]::new);
            List<Map> validList = deviceLayoutRepository.findUsedDevInstancesByIds(devInstIds);
            if (CollectionUtils.isNotEmpty(validList)){
                ConfigApplicationException expt = new ConfigApplicationException("Existed station in used procedures.");
                expt.setData(validList);
                throw expt;
            }
        }
    }
    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            deviceLayoutRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DeviceLayoutDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DeviceLayoutDto deviceLayout : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("工作站描述", deviceLayout.getDescription());
            map.put("SVG格式的存储", deviceLayout.getLayoutSvg());
            map.put("状态:DRAFT, DEPLOY", deviceLayout.getStatus());
            map.put("保存的更新版本", deviceLayout.getVersion());
            map.put("是否已被删除,Y/N", deviceLayout.getDeleteFlag());
            map.put("创建人", deviceLayout.getCreateBy());
            map.put("创建时间", deviceLayout.getCreateTime());
            map.put("更新人", deviceLayout.getUpdateBy());
            map.put("更新时间", deviceLayout.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public List<DeviceInstance> resetAllPositions(DeviceLayout resources) {
        DeviceLayout deviceLayout = deviceLayoutRepository.findById(resources.getId()).orElseGet(DeviceLayout::new);
        ValidationUtil.isNull( deviceLayout.getId(),"DeviceLayout","id",resources.getId());
        List<Position> needToResetPostions = new ArrayList<>();
        List<DeviceInstance> outDevInstances = new ArrayList<>();
        for (DeviceInstance devInst : deviceLayout.getDeviceInstanceList()) {
            List<Position> positions = devInst.getPositions();
            if (CollectionUtils.isEmpty(positions)){
                continue;
            }
            List<Position> nPos = positions.stream().filter(pos->{
                return !pos.getStatus().equals(pos.getInitStatus());
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(nPos)) {
                needToResetPostions.addAll(nPos);
                outDevInstances.add(devInst);
            }
        }

        if (CollectionUtils.isNotEmpty(needToResetPostions)) {
            needToResetPostions.forEach(position -> {
                position.setStatus(position.getInitStatus());
            });
            positionRepository.saveAll(needToResetPostions);
            log.info("resetAllPositions updatePositionStatus, updated position count: {}",needToResetPostions.size());
        }

        return outDevInstances;
    }

    @Override
    public List<DeviceInstance> findAllDevicesInLatestLayout() {
        DeviceLayout layout = deviceLayoutRepository.findLatest().orElseGet(DeviceLayout::new);

        return layout.getDeviceInstanceList();
    }


}