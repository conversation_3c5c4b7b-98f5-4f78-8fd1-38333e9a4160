/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.VariableHelper;
import org.cjc.les.core.service.TaskStepService;
import org.cjc.les.core.task.schedule.ActionScheduler;
import org.cjc.les.core.util.TaskBreakPointUtil;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Log4j2
public class TaskStepExecutor implements TaskExecutorInterface<TaskStep> {

    private static final int POOL_SIZE = 16;

    private static ExecutorService taskActionExecutorPool = Executors.newFixedThreadPool(POOL_SIZE);

    private ExecutorConfig config;

    private ActionScheduler actionScheduler;

    private TaskStepService stepService;

    public TaskStepExecutor(ExecutorConfig config) {
        this.config = config;
        this.actionScheduler = SpringContextHolder.getBean(ActionScheduler.class);
        this.stepService = SpringContextHolder.getBean(TaskStepService.class);
    }

    @Override
    public CommandReturn<TaskStep> execute(TaskStep cmd) {

        cmd.setThread(Thread.currentThread());

        TaskExecutorContext.setTaskStep(cmd);
        long stepBeginTime = System.currentTimeMillis();
        Task task = TaskExecutorContext.getTask();
        if (task != null) {
            cmd.setExecutedStart(stepBeginTime - (task.getScheduleEntryTime() == null ? 0L : task.getScheduleEntryTime().longValue()));
        } else {
            log.warn("Task in TaskExecutorContext is null, taskStep.id={}", cmd.getId());
        }

        cmd.setStatus(CommandStatusEnum.RUNNING.name());

        if (this.config.isRecordLog()) {
            stepService.updateExecutedTime(cmd);
            TaskExecuteLogUtil.logStep(cmd);
        }

        // 解析环境变量
        parseVariables(cmd);

        for (TaskAction action : cmd.getActions()) {
            // 已完成状态的，不再执行
            if (StringUtils.equalsAny(action.getStatus()
                    , CommandStatusEnum.SUCCESS.name(), CommandStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())) {
                continue;
            }

            TaskBreakPointUtil.checkBreakPoint(action);

            if (StringUtils.equals(action.getAsyncMode(), "Y")) {
                final TaskExecutorContext context = TaskExecutorContext.getContext();
                action.setStatus(RunStatusEnum.FORKED.name());
                taskActionExecutorPool.submit(() -> {
                    TaskExecutorContext.copy(context, action);
                    //new TaskActionExecutor(TaskStepExecutor.this.config).execute(action);
                    actionScheduler.execute(action, this.config);
                    TaskExecutorContext.clear();

                    // 重新计算任务步骤的运行状态
                    calcuStatus(cmd);
                    if (this.config.isRecordLog()) {
                        TaskExecuteLogUtil.logStep(cmd);
                    }
                });

            } else {
                if (this.config.getExecuteMode() == ExecutorConfig.ExecuteModeEnum.IN_SCHEDULE) {
                    actionScheduler.execute(action, this.config);
                } else {
                    new TaskActionExecutor(this.config).execute(action);
                }

            }
        }

        if (task != null) {
            cmd.setExecutedDuration(System.currentTimeMillis() - stepBeginTime);
            stepService.updateExecutedTime(cmd);
        }

        calcuStatus(cmd);

        if (this.config.isRecordLog()) {
            TaskExecuteLogUtil.logStep(cmd);
        }

        CommandReturn<TaskStep> ret = new CommandReturn<>();
        if (StringUtils.equals(cmd.getStatus(), CommandStatusEnum.SUCCESS.name())) {
            ret.setErrorCode(ErrorCodeEnum.SUCCESS);
        } else {
            ret.setErrorCode(ErrorCodeEnum.ERROR);
        }

        cmd.setThread(null);
        return ret;
    }


    private void calcuStatus(TaskStep taskStep) {
        if (taskStep.getActions().stream().allMatch(command -> {
            return StringUtils.equalsAny(command.getStatus(), RunStatusEnum.SUCCESS.name(),RunStatusEnum.SKIPPED.name());
        })) {
            taskStep.setStatus(RunStatusEnum.SUCCESS.name());
        } else if (taskStep.getActions().stream().anyMatch(command -> {
            return StringUtils.equals(command.getStatus(), RunStatusEnum.CANCELLED.name());
        })) {
            taskStep.setStatus(RunStatusEnum.CANCELLED.name());
        } else if (taskStep.getActions().stream().anyMatch(command -> {
            return StringUtils.equals(command.getStatus(), RunStatusEnum.FAILED.name());
        })) {
            taskStep.setStatus(CommandStatusEnum.FAILED.name());
        } else if (taskStep.getActions().stream().anyMatch(act -> {
            return (StringUtils.equalsAny(act.getAsyncMode(), "Y"));
        })) {
            taskStep.setStatus(RunStatusEnum.SUCCESS_WITH_FORKED.name());
        }
    }


    private void parseVariables(TaskStep taskStep) {
        if (StringUtils.isEmpty(taskStep.getVariables())){
            return;
        }
        VariableHelper.setNodeVariables(taskStep.getVariables(), taskStep);

    }
}
