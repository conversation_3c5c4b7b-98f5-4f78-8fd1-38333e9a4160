/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.helper.VariableHelper;
import org.cjc.les.core.service.TaskActionService;
import org.cjc.les.core.service.TaskStepService;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;

@Log4j2
public class TaskActionExecutor implements TaskExecutorInterface<TaskAction> {

    private ExecutorConfig config;

    private TaskAction taskAction;

    private TaskActionService taskActionService;

    public TaskActionExecutor(ExecutorConfig config){
        this.config = config;
        this.taskActionService = SpringContextHolder.getBean(TaskActionService.class);
    }

    @Override
    public CommandReturn<TaskAction> execute(TaskAction taskAction) {
        taskAction.setThread(Thread.currentThread());

        CommandReturn<TaskAction> ret = new CommandReturn<>();
        if (StringUtils.equalsAny(taskAction.getStatus(), RunStatusEnum.CANCELLED.name())){
            log.warn("The action: {} has been cancelled yet, DO NOT run it now.", taskAction.getName());
            ret.setErrorCode(ErrorCodeEnum.ERROR);
            taskAction.setThread(null);
            return ret;
        }
        this.taskAction = taskAction;

        taskAction.setStatus(CommandStatusEnum.RUNNING.name());

        long actionBeginTime = System.currentTimeMillis();
        Task task = TaskExecutorContext.getTask();
        if (task!= null) {
            taskAction.setExecutedStart(actionBeginTime - (task.getScheduleEntryTime()==null?0L: task.getScheduleEntryTime().longValue()) ) ;
        }else{
            log.warn("Task in TaskExecutorContext is null, taskAction.id={}", taskAction.getId());
        }

        if (this.config.isRecordLog()){
            TaskExecuteLogUtil.logAction(taskAction);
        }

        parseActionVariables(taskAction);


        for (TaskCommand command : taskAction.getCommands()) {
            // 已完成状态的，不再执行
            if (StringUtils.equalsAny(command.getStatus(),
                    RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())){
                log.warn("The command: {} has been executed yet, DO NOT run it now.", command.getCommand().getName());
                continue;
            }
            // Option可选的指令，不在主流程中强制执行
            if (StringUtils.equalsAny("Y",command.getIsOption())){
                command.setStatus(RunStatusEnum.SKIPPED.name());
                continue;
            }
            log.info("Before execute command: {}", command);
            new TaskCommandExecutor(this.config).execute(command);
        }
        if (taskAction.getCommands().stream().allMatch(command -> {
            return StringUtils.equalsAny(command.getStatus(), RunStatusEnum.SKIPPED.name());
        })) {
            taskAction.setStatus(RunStatusEnum.SKIPPED.name());
        } else if (taskAction.getCommands().stream().allMatch(command -> {
            return StringUtils.equalsAny(command.getStatus(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.SKIPPED.name());
        })) {
            taskAction.setStatus(RunStatusEnum.SUCCESS.name());
        } else if (taskAction.getCommands().stream().anyMatch(command -> {
            return StringUtils.equals(command.getStatus(), RunStatusEnum.CANCELLED.name());
        })) {
            taskAction.setStatus(RunStatusEnum.CANCELLED.name());
        } else {
            taskAction.setStatus(RunStatusEnum.FAILED.name());
        }

        if (task!= null) {
            taskAction.setExecutedDuration(System.currentTimeMillis() - actionBeginTime);
            taskActionService.updateExecutedTime(taskAction);
        }

        if (this.config.isRecordLog()){
            TaskExecuteLogUtil.logAction(taskAction);
        }

        if (StringUtils.equals(taskAction.getStatus(),CommandStatusEnum.SUCCESS.name())){
            ret.setErrorCode(ErrorCodeEnum.SUCCESS);
        }else{
            ret.setErrorCode(ErrorCodeEnum.ERROR);
        }

        taskAction.setThread(null);
        return ret;
    }

    private void parseActionVariables(TaskAction taskAction) {
        if (StringUtils.isEmpty(taskAction.getVariables())){
            return;
        }
        VariableHelper.setNodeVariables(taskAction.getVariables(), taskAction);

    }
}
