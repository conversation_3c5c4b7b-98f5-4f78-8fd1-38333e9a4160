/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesConfigBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-10
**/
@Entity
@Data
@Table(name="les_task_prepare")
public class TaskPrepare extends LesConfigBaseEntity {

    /**
     * 引用流程对象
     */
    @OneToOne
    @JoinColumn(name = "procedure_id")
    private Procedure procedure;

    /**
     * 引用样品对象
     */
    @ManyToOne(targetEntity = Sample.class)
    @JoinColumn(name = "sample_id",referencedColumnName = "id", nullable = false)
    private Sample sample;

    @Column(name = "task_number")
    @Schema(description = "任务编号")
    private String taskNumber;

    /**
     * DRAFT: 初始化状态
     * WAIT_TAG_INPUT: 等待TAG输入
     * READY: 待推送到任务队列
     * WAIT_CONVEYOR_INPUT: 等待样品进入传送带
     * PUSHED: 已推送到任务队列
     * CANCELLED: 已撤销
     */
    @Column(name = "status",nullable = false)
    @NotBlank
    @Schema(description = "状态, DRAFT,READY,PUSHED,CANCELLED")
    private String status;

    @Column(name = "message")
    @Schema(description = "状态的消息详情")
    private String message;

    @Column(name = "mode")
    @Schema(description = "进样模式: TRIAL, FACTORY")
    private String mode;

    @Column(name = "rf_tag")
    private String rfTag;

    private String parallel;

    /**
     * 当rfTag代表板卡编号时，该字段表示样品所处的点位
     */
    @Column(name = "pos_index")
    private Integer posIndex;

    /**
     * 同样品中存在多预处理任务时，选择哪一个
     */
    @Transient
    boolean selected;

    public void copy(TaskPrepare source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append(
                        "id", this.getId()
                )
                .append("taskNumber", taskNumber)
                .append("status", status)
                .append("mode", mode)
                .append("rfTag", rfTag)
                .toString();
    }
}