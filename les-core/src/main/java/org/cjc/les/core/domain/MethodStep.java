/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import org.cjc.base.LesConfigBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import java.sql.Timestamp;

/**
 * 进程
 */
@Entity
@Data
@Table(name="les_method_step")
@SQLDelete(sql = "update les_method_step set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class MethodStep extends LesConfigBaseEntity {

    /**
     * 当调度模式设置为BLOCK_SELF,UNBLOCK时，不会阻塞其他任务的步骤执行，因此，若要到资源争用问题，可通过如下途径解决:
     *    1. ACTION层级的条件校验：点位状态的判断是否可用(推荐)
     *    2. STEP层级的锁校验, 控制资源独占
     */
    public static enum ScheduleModeEnum {
        BLOCK_ALL,  // 阻塞所有任务步骤
        BLOCK_SELF, // 只阻塞自己任务中的步骤, 其他任务中的该步骤不阻塞
        BLOCK_OTHERS, // 不阻塞自己，只阻塞其他任务
        UNBLOCK     // 不阻塞任务步骤
    }

    private String dagNodeId;

    @JSONField(serialize = false)
    @ManyToOne
    @JoinColumn(name = "method_id", referencedColumnName = "id")
    private Method method;

    @ManyToOne(cascade = {CascadeType.PERSIST,CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH})
    @JoinColumn(name = "step_id", referencedColumnName = "id")
    private Step step;

    private Long evaluateExecutingStart;
    private Long evaluateExecutingDuration;
    private Timestamp evaluatedTime;

    /**
     * BLOCK_ALL,BLOCK_SELF,UNBLOCK
     */
    private String scheduleMode = ScheduleModeEnum.BLOCK_ALL.name();

    /**
     * 环境变量
     */
    private String variables;

    /**
     * 条件谓词表达式
     */
    private String predicates;
    /**
     * 当预置条件不满足时处理方式：WAIT,SKIP
     */
    private String unmatchedThen;

    private String status = CommandStatusEnum.VALID.name();

    public void bind(Method method) {
        this.setMethod(method);
        if (this.step == null) {
            return;
        }
        step.bind();
    }

    public void copy(MethodStep source) {
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("method","step"));

        this.variables = source.variables;
        this.predicates = source.predicates;

        if (this.getStep()==null){
            this.setStep(source.getStep());
        }else{
            this.step.copy(source.getStep());
        }

    }
}
