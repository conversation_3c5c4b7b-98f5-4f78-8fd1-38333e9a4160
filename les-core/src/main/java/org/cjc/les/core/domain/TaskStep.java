/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_task_step")
public class TaskStep extends TaskBaseEntity {

    @Column(name = "name",unique = true)
    @Schema(description = "任务运行的步骤名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "任务中的步骤描述")
    private String description;

    private String message;

    @Column(name = "task_id")
    @Schema(description = "任务ID")
    private Long taskId;

    @Column(name = "step_id")
    @Schema(description = "引用配置的步骤ID")
    private Long stepId;

    private Long evaluateExecutingStart = 0L;
    private Long evaluateExecutingDuration = 0L;
    private Timestamp evaluatedTime;

    transient private Long scheduleEntryTime;
    private Long executedStart;
    private Long executedDuration;

    /**
     * BLOCK_ALL,BLOCK_SELF,UNBLOCK
     */
    private String scheduleMode;

    /**
     * 环境变量
     */
    private String variables;

    /**
     * 条件谓词表达式
     */
    private String predicates;
    /**
     * 当预置条件不满足时处理方式：WAIT,SKIP
     */
    private String unmatchedThen;

    @Column(name = "delete_flag")
    @Schema(description = "是否已被删除,Y/N")
    private String deleteFlag;

    @JSONField(serialize = false)
    @JoinColumn(name = "task_method_id", referencedColumnName = "id")
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = TaskMethod.class)
    @Schema(description = "任务的方法", hidden = true)
    private TaskMethod taskMethod;

    @OneToMany(mappedBy = "taskStep", targetEntity = TaskAction.class, cascade = {CascadeType.PERSIST, CascadeType.ALL}, orphanRemoval = true)
    @OrderColumn(name="order_index")
    private List<TaskAction> actions = new ArrayList<>();


    /**
     * 步骤执行器配置
     */
    private transient ExecutorConfig executorConfig;

    transient private Thread thread;

    public void copy(TaskStep source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
    /**
     * 从方法定义中拷贝
     * @param step
     * @param isDeep 是否深拷贝，默认false
     */
    public void copy(MethodStep step, boolean isDeep){
        BeanUtil.copyProperties(step,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","actions"));
        if (ObjectUtils.isNotEmpty(step.getStep())){
            BeanUtil.copyProperties(step.getStep(),this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status","actions"));
        }
        this.setStepId(step.getStep().getId());

        if (isDeep) {
            actions.clear();
            for (StepAction action : step.getStep().getActions()) {
                TaskAction taskAction = new TaskAction();
                taskAction.copy(action, true);
                taskAction.setTaskStep(this);
                actions.add(taskAction);
            }
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", this.getId())
                .append("name", name)
                .append("description", description)
                .append("status", this.getStatus())
                .append("taskId", taskId)
                .append("stepId", stepId)
                .append("deleteFlag", deleteFlag)
                .toString();
    }
}