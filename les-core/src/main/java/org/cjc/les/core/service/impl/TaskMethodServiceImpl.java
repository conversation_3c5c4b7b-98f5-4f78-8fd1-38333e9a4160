/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.TaskMethod;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.TaskMethodRepository;
import org.cjc.les.core.service.TaskMethodService;
import org.cjc.les.core.service.dto.TaskMethodDto;
import org.cjc.les.core.service.dto.TaskMethodQueryCriteria;
import org.cjc.les.core.service.mapstruct.TaskMethodMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import org.cjc.exception.EntityExistException;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-11
**/
@Service
@RequiredArgsConstructor
public class TaskMethodServiceImpl implements TaskMethodService {

    private final TaskMethodRepository taskMethodRepository;
    private final TaskMethodMapper taskMethodMapper;

    @Override
    public Map<String,Object> queryAll(TaskMethodQueryCriteria criteria, Pageable pageable){
        Page<TaskMethod> page = taskMethodRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskMethodMapper::toDto));
    }

    @Override
    public List<TaskMethodDto> queryAll(TaskMethodQueryCriteria criteria){
        return taskMethodMapper.toDto(taskMethodRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskMethodDto findById(Long id) {
        TaskMethod taskMethod = taskMethodRepository.findById(id).orElseGet(TaskMethod::new);
        ValidationUtil.isNull(taskMethod.getId(),"TaskMethod","id",id);
        return taskMethodMapper.toDto(taskMethod);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskMethodDto create(TaskMethod resources) {
        if(taskMethodRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(TaskMethod.class,"name",resources.getName());
        }
        return taskMethodMapper.toDto(taskMethodRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskMethod resources) {
        TaskMethod taskMethod = taskMethodRepository.findById(resources.getId()).orElseGet(TaskMethod::new);
        ValidationUtil.isNull( taskMethod.getId(),"TaskMethod","id",resources.getId());
        TaskMethod taskMethod1 = null;
        taskMethod1 = taskMethodRepository.findByName(resources.getName());
        if(taskMethod1 != null && !taskMethod1.getId().equals(taskMethod.getId())){
            throw new EntityExistException(TaskMethod.class,"name",resources.getName());
        }
        taskMethod.copy(resources);
        taskMethodRepository.save(taskMethod);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            taskMethodRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<TaskMethodDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskMethodDto taskMethod : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("任务创建时的方法名称", taskMethod.getName());
            map.put("任务创建时的方法描述", taskMethod.getDescription());
            map.put("方法运行状态：READY,RUNNING,SUCCESS,FAILED", taskMethod.getStatus());
            map.put("任务ID", taskMethod.getTaskId());
            map.put("引用配置的方法ID", taskMethod.getMethodId());
            map.put("是否已被删除,Y/N", taskMethod.getDeleteFlag());
            map.put("创建人", taskMethod.getCreateBy());
            map.put("创建时间", taskMethod.getCreateTime());
            map.put("更新人", taskMethod.getUpdateBy());
            map.put("更新时间", taskMethod.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}