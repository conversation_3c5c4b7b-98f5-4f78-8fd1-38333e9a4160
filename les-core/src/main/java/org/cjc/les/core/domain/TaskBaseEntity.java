/*
 *  Copyright (C) 2024-2025  Wuhan Annis Robot Co. Ltd, All Rights Reserved.
 */
package org.cjc.les.core.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.cjc.base.LesBaseEntity;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.task.execute.TaskExecutorContext;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;

/**
* 任务对象公用基类
* @description /
* <AUTHOR>
* @date 2025-03-18
**/
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@MappedSuperclass
public class TaskBaseEntity extends LesBaseEntity {

    // 运行时上下文公共对象，一个Task对象对应唯一context
    transient private TaskExecutorContext context;

    @Column(name = "status")
    @Schema(description = "动作运行状态:READY,RUNNING,SUCCESS,FAILED")
    private String status  = RunStatusEnum.READY.name();

    public void copy(TaskBaseEntity source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

}