/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.service.dto.TaskStepDto;
import org.cjc.les.core.service.dto.TaskStepQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskStepService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TaskStepQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskStepDto>
    */
    List<TaskStepDto> queryAll(TaskStepQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskStepDto
     */
    TaskStepDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return TaskStepDto
    */
    TaskStepDto create(TaskStep resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskStep resources);

    void updateExecutedTime(TaskStep resources);
    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskStepDto> all, HttpServletResponse response) throws IOException;
}