/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.ProcedureVariable;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ProcedureRepository;
import org.cjc.les.core.repository.ProcedureVariableRepository;
import org.cjc.les.core.service.ProcedureVariableService;
import org.cjc.les.core.service.dto.ProcedureVariableDto;
import org.cjc.les.core.service.dto.ProcedureVariableQueryCriteria;
import org.cjc.les.core.service.dto.ProcedureWithVariablesDto;
import org.cjc.les.core.service.mapstruct.ProcedureVariableMapper;
import org.cjc.les.core.service.mapstruct.ProcedureWithVariablesMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-07-27
**/
@Service
@RequiredArgsConstructor
public class ProcedureVariableServiceImpl implements ProcedureVariableService {

    private final ProcedureVariableRepository procedureVariableRepository;
    private final ProcedureVariableMapper procedureVariableMapper;

    private final ProcedureRepository procedureRepository;
    private final ProcedureWithVariablesMapper procedureWithVariablesMapper;

    @Override
    public Map<String,Object> queryAll(ProcedureVariableQueryCriteria criteria, Pageable pageable){
        Page<ProcedureVariable> page = procedureVariableRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(procedureVariableMapper::toDto));
    }

    @Override
    public List<ProcedureVariableDto> queryAll(ProcedureVariableQueryCriteria criteria){
        return procedureVariableMapper.toDto(procedureVariableRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<ProcedureVariableDto> queryAllUserSettingsBYProcId(Long procId) {
        Procedure procedure = procedureRepository.findById(procId).get();

        List<ProcedureVariable> dbList = procedure.getVariables();
        List<ProcedureVariable> userSettings = dbList.stream().filter(db->StringUtils.equalsAny(db.getVariable().getValueSetByUser(),"Y")).collect(Collectors.toList());

        return procedureVariableMapper.toDto(userSettings);
    }

    @Override
    @Transactional
    public ProcedureVariableDto findById(Long id) {
        ProcedureVariable procedureVariable = procedureVariableRepository.findById(id).orElseGet(ProcedureVariable::new);
        ValidationUtil.isNull(procedureVariable.getId(),"ProcedureVariable","id",id);
        return procedureVariableMapper.toDto(procedureVariable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcedureVariableDto create(ProcedureVariable resources) {
        return procedureVariableMapper.toDto(procedureVariableRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcedureVariable resources) {
        ProcedureVariable procedureVariable = procedureVariableRepository.findById(resources.getId()).orElseGet(ProcedureVariable::new);
        ValidationUtil.isNull( procedureVariable.getId(),"ProcedureVariable","id",resources.getId());
        procedureVariable.copy(resources);
        procedureVariableRepository.save(procedureVariable);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            procedureVariableRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ProcedureVariableDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProcedureVariableDto procedureVariable : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联流程ID", procedureVariable.getProcedureId());
            map.put("关联变量ID", procedureVariable.getVariableId());
            map.put("默认值", procedureVariable.getDefaultValue());
            map.put("当前值", procedureVariable.getValue());
            map.put("列表顺序下标", procedureVariable.getOrderIndex());
            map.put("是否已被删除,Y/N", procedureVariable.getDeleteFlag());
            map.put("创建人", procedureVariable.getCreateBy());
            map.put("创建时间", procedureVariable.getCreateTime());
            map.put("更新人", procedureVariable.getUpdateBy());
            map.put("更新时间", procedureVariable.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
    @Override
    public void updateDefaultValueByProcedureId(Long procedureId, List<ProcedureVariableDto> variableDtos) {
        Procedure procedure = procedureRepository.findById(procedureId).get();

        List<ProcedureVariable> dbList = procedure.getVariables();
        List<ProcedureVariable> deleteList = new ArrayList<>();
        List<ProcedureVariable> toAddList = new ArrayList<>();
        for (ProcedureVariable db : dbList) {
            Optional<ProcedureVariableDto> dtoOpt = variableDtos.stream().filter(dto->{
                return dto.getProcedureId().equals(db.getProcedure().getId()) && dto.getVariableId().equals(db.getVariable().getId());
            }).findFirst();
            if (!dtoOpt.isPresent()) {
                deleteList.add(db);
            }else{
                ProcedureVariableDto dto = dtoOpt.get();
                db.setDefaultValue(dto.getDefaultValue());
            }
        }

        for (ProcedureVariableDto dto : variableDtos) {
            Optional<ProcedureVariable> dbOpt = dbList.stream().filter(db->{
                return db.getProcedure().getId().equals(dto.getProcedureId()) && db.getVariable().getId().equals(dto.getVariableId());
            }).findFirst();
            if (!dbOpt.isPresent()) {
                ProcedureVariable entity = procedureVariableMapper.toEntity(dto);
                entity.setValue(dto.getDefaultValue());
                toAddList.add(entity);
            }
        }

        dbList.removeIf(var->deleteList.contains(var));
        dbList.addAll(toAddList);

        procedureRepository.save(procedure);
    }

    @Override
    public void updateProcedureVariablesValue(List<ProcedureWithVariablesDto> procedureDtos) {
        for (ProcedureWithVariablesDto dto : procedureDtos) {
            Procedure procedure = procedureRepository.findById(dto.getId()).get();

            List<ProcedureVariable> dbList = procedure.getVariables();
            for (ProcedureVariable db : dbList) {
                Optional<ProcedureVariableDto> dtoOpt = dto.getVariables().stream().filter(varDto->{
                    return varDto.getId().equals(db.getId());
                }).findFirst();
                if (dtoOpt.isPresent()){
                    db.setValue(dtoOpt.get().getValue());
                }
            }
            procedureRepository.save(procedure);
        }
    }

    @Override
    public List<ProcedureWithVariablesDto> queryAllRawProceduresWithVariables() {
        List<Procedure> procedureList = procedureRepository.findProceduresByType("RAW");
        for (Procedure procedure : procedureList){
            List<ProcedureVariable> toUpdateList = new ArrayList<>();
            for (ProcedureVariable procedureVariable : procedure.getVariables() ) {
                if (StringUtils.equalsAny(procedureVariable.getVariable().getValueSetByUser(),"Y")){
                    toUpdateList.add(procedureVariable);
                }
            }
            procedure.setVariables(toUpdateList);
        }

        List<ProcedureWithVariablesDto> outList = procedureWithVariablesMapper.toDto(procedureList);

        return outList;
    }
}