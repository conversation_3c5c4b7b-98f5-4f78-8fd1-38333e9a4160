/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import org.cjc.annotation.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-08-23
**/
@Data
public class TaskExecuteLogQueryCriteria{
    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> updateTime;

    /**
     * 节点类型TASK/METHOD/STEP/ACTION/COMMAND
     */
    private String nodeType;
    /**
     * 节点类型对应的主键ID
     */
    private Long id;
}