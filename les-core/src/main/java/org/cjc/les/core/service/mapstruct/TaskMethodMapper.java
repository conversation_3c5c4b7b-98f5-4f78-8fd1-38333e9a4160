/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.service.dto.TaskMethodDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskMethodMapper extends BaseMapper<TaskMethodDto, TaskMethod> {

    @Mapping(source = "task.id", target = "taskId")
    @Override
    TaskMethodDto toDto(TaskMethod entity);
}