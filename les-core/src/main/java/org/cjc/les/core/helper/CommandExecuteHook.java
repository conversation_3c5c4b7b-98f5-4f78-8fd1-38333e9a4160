/*
 *  Copyright 2024-2025 Wuhan Annis Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.helper;

import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.vo.CommandReturn;

/**
 * 指令方法运行钩子，用于在方法前后增加附加功能
 */
public interface CommandExecuteHook {

    /**
     * 方法执行前被调用
     * @param instanceCmd
     * @param cmdRet
     * @return
     */
    CommandReturn beforeInvokeMethod(DeviceInstanceCmd instanceCmd, CommandReturn cmdRet);

    /**
     * 方法执行后被调用
     * @param instanceCmd
     * @param cmdRet
     * @return
     */
    CommandReturn afterInvokeMethod(DeviceInstanceCmd instanceCmd, CommandReturn cmdRet);

}
