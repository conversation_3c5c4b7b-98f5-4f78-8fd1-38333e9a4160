/*
 *  Copyright 2024-2024 <PERSON><PERSON> Annis Robot Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.ProcedureVariable;
import org.cjc.les.core.service.dto.ProcedureDto;
import org.cjc.les.core.service.dto.ProcedureVariableDto;
import org.cjc.les.core.service.dto.ProcedureWithVariablesDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.List;

import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProcedureWithVariablesMapper extends BaseMapper<ProcedureWithVariablesDto, Procedure> {

    @Mapping(source = "variables", target = "variables", qualifiedByName = "toProcedureVariableDtos")
    @Override
    ProcedureWithVariablesDto toDto(Procedure entity);

    @Named("toProcedureVariableDtos")
    default List<ProcedureVariableDto> toProcedureVariableDtos(List<ProcedureVariable> list) {
        if ( list == null ) {
            return null;
        }

        List<ProcedureVariableDto> list1 = new ArrayList<ProcedureVariableDto>( list.size() );
        for ( ProcedureVariable procedureVariable : list ) {
            list1.add( procedureVariableToProcedureVariableDto( procedureVariable ) );
        }

        return list1;
    }

    default ProcedureVariableDto procedureVariableToProcedureVariableDto(ProcedureVariable procedureVariable) {
        if ( procedureVariable == null ) {
            return null;
        }

        ProcedureVariableDto procedureVariableDto = new ProcedureVariableDto();

        procedureVariableDto.setId( procedureVariable.getId() );
        procedureVariableDto.setDefaultValue( procedureVariable.getDefaultValue() );
        procedureVariableDto.setValue( procedureVariable.getValue() );
        procedureVariableDto.setOrderIndex( procedureVariable.getOrderIndex() );
        procedureVariableDto.setDeleteFlag( procedureVariable.getDeleteFlag() );
        procedureVariableDto.setCreateBy( procedureVariable.getCreateBy() );
        procedureVariableDto.setCreateTime( procedureVariable.getCreateTime() );
        procedureVariableDto.setUpdateBy( procedureVariable.getUpdateBy() );
        procedureVariableDto.setUpdateTime( procedureVariable.getUpdateTime() );

        procedureVariableDto.setProcedureId(procedureVariable.getProcedure().getId());
        procedureVariableDto.setVariableId(procedureVariable.getVariable().getId());
        procedureVariableDto.setName(procedureVariable.getVariable().getName());
        procedureVariableDto.setViewName(procedureVariable.getVariable().getViewName());
        procedureVariableDto.setDescription(procedureVariable.getVariable().getDescription());
        procedureVariableDto.setType(procedureVariable.getVariable().getType());

        return procedureVariableDto;
    }


    @Named("toProcedureVariables")
    default List<ProcedureVariable> toProcedureVariables(List<ProcedureVariableDto> list) {
        if ( list == null ) {
            return null;
        }

        List<ProcedureVariable> list1 = new ArrayList<ProcedureVariable>( list.size() );
        for ( ProcedureVariableDto procedureVariableDto : list ) {
            list1.add( procedureVariableDtoToProcedureVariable( procedureVariableDto ) );
        }

        return list1;
    }

    default ProcedureVariable procedureVariableDtoToProcedureVariable(ProcedureVariableDto procedureVariableDto) {
        if ( procedureVariableDto == null ) {
            return null;
        }

        ProcedureVariable procedureVariable = new ProcedureVariable();

        procedureVariable.setId( procedureVariableDto.getId() );
        procedureVariable.setDefaultValue( procedureVariableDto.getDefaultValue() );
        procedureVariable.setValue( procedureVariableDto.getValue() );
        procedureVariable.setOrderIndex( procedureVariableDto.getOrderIndex() );
        procedureVariable.setDeleteFlag( procedureVariableDto.getDeleteFlag() );
        procedureVariable.setCreateBy( procedureVariableDto.getCreateBy() );
        procedureVariable.setCreateTime( procedureVariableDto.getCreateTime() );
        procedureVariable.setUpdateBy( procedureVariableDto.getUpdateBy() );
        procedureVariable.setUpdateTime( procedureVariableDto.getUpdateTime() );

        return procedureVariable;
    }

    @Mapping(source = "variables", target = "variables", qualifiedByName = "toProcedureVariables")
    @Override
    Procedure toEntity(ProcedureWithVariablesDto dto);

}