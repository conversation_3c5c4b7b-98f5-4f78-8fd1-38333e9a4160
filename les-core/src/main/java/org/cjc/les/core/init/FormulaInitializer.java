/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.init;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.*;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.helper.FormulaFunctionsImpl;
import org.cjc.les.core.service.DeviceService;
import org.cjc.les.core.service.FunctionService;
import org.cjc.les.core.service.VariableService;
import org.cjc.les.core.vo.ConfigItemVo;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Optional;
import java.util.Set;

/**
 * 公式定义，初始化变量及函数
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class FormulaInitializer {

    private static final String BASE_PACKAGE = "org.cjc.les";

    private final VariableService variableService;
    private final FunctionService functionService;

    private final ControlDeviceMonitor controlDeviceMonitor;

    @Autowired
    private FormulaInitializer self;

    /**
     * 初始化设备
     */
    public void init() {
        // 1. 注册变量定义
        self.registerVariables();

        // 2. 注册函数定义
        self.registerFunctions();
    }

    @Transactional()
    public void registerVariables() {
        // 1. 驱动指令实现中，自定义的输出变量
        Reflections reflections = new Reflections(BASE_PACKAGE);
        // 扫描标注了@Device注解的类
        Set<Class<?>> annotatedClasses = reflections.getTypesAnnotatedWith(Device.class);

        // 处理扫描到的类，
        for (Class<?> clazz : annotatedClasses) {
            String className = clazz.getName();
            org.cjc.les.core.domain.Device device = new org.cjc.les.core.domain.Device();
            Device annoDev = clazz.getAnnotation(Device.class);
            if (annoDev == null) {
                continue;
            }
            Method[] methods = clazz.getMethods();
            for (Method mth : methods) {
                Variable varAnno = mth.getAnnotation(Variable.class);
                if (varAnno != null) {
                    saveVariable(varAnno);
                }

                Variables varAnnos = mth.getAnnotation(Variables.class);
                if (varAnnos != null) {
                    for (Variable anno : varAnnos.value()){
                        saveVariable(anno);
                    }
                }

            }

         //   deviceService.save(deviceInDb);

        }

        // 2. 工作站配置指令参数中指定的入参变量
        // TODO
    }

    private void saveVariable(Variable varAnno) {

        org.cjc.les.core.domain.Variable variable = new org.cjc.les.core.domain.Variable();
        variable.setName(varAnno.name());
        variable.setViewName(varAnno.tip());
        variable.setDescription(varAnno.tip());
        variable.setType(varAnno.type());
        variable.setScope(varAnno.scope());
        variable.setSource("SYS");
        variableService.save(variable);
    }

    public void registerFunctions() {
        Class<?> commonClz = FormulaFunctionsImpl.class;
        Method[] methods = commonClz.getMethods();
        for (Method mth : methods) {
            Function funcAnno = mth.getAnnotation(Function.class);
            if (funcAnno == null) {
                continue;
            }

            org.cjc.les.core.domain.Function function = new org.cjc.les.core.domain.Function();
            function.setName(funcAnno.name());
            function.setTip(funcAnno.tip());
            function.setDescription(funcAnno.usage());
            functionService.save(function);

        }

    }


}
