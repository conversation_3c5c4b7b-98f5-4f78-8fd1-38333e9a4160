/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.exception.EntityExistException;
import org.cjc.les.core.domain.Step;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.StepRepository;
import org.cjc.les.core.service.StepService;
import org.cjc.les.core.service.dto.StepDto;
import org.cjc.les.core.service.dto.StepQueryCriteria;
import org.cjc.les.core.service.mapstruct.StepMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-08
**/
@Service
@RequiredArgsConstructor
public class StepServiceImpl implements StepService {

    private final StepRepository stepRepository;
    private final StepMapper stepMapper;

    @Override
    public Map<String,Object> queryAll(StepQueryCriteria criteria, Pageable pageable){
        Page<Step> page = stepRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(stepMapper::toDto));
    }

    @Override
    public List<StepDto> queryAll(StepQueryCriteria criteria){
        return stepMapper.toDto(stepRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public StepDto findById(Long id) {
        Step step = stepRepository.findById(id).orElseGet(Step::new);
        ValidationUtil.isNull(step.getId(),"Step","id",id);
        return stepMapper.toDto(step);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StepDto create(Step resources) {
        if(stepRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(Step.class,"name",resources.getName());
        }
        return stepMapper.toDto(stepRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Step resources) {
        Step step = stepRepository.findById(resources.getId()).orElseGet(Step::new);
        ValidationUtil.isNull( step.getId(),"Step","id",resources.getId());
        Step step1 = null;
        step1 = stepRepository.findByName(resources.getName());
        if(step1 != null && !step1.getId().equals(step.getId())){
            throw new EntityExistException(Step.class,"name",resources.getName());
        }
        step.copy(resources);
        stepRepository.save(step);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            stepRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<StepDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (StepDto step : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("工作站名称", step.getName());
            map.put("工作站描述", step.getDescription());
            map.put("工作站状态", step.getStatus());
            map.put("是否已被删除,Y/N", step.getDeleteFlag());
            map.put("创建人", step.getCreateBy());
            map.put("创建时间", step.getCreateTime());
            map.put("更新人", step.getUpdateBy());
            map.put("更新时间", step.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}