/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.DeviceProxyHelper;
import org.cjc.les.core.helper.DeviceProxyWrapper;
import org.cjc.les.core.repository.*;
import org.cjc.les.core.service.MotionService;
import org.cjc.les.core.service.SampleService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.*;
import org.cjc.les.core.service.mapstruct.TaskMapper;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.SerialNumberGenerator;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.vo.CommandReturn;
import org.cjc.les.vo.MotionInfo;
import org.cjc.utils.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-10-11
**/
@Service
@RequiredArgsConstructor
@Log4j2
public class MotionServiceImpl implements MotionService {

    private final DeviceInstanceRepository deviceInstanceRepository;

    @Override
    public List<MotionInfoDto> getMotionInfos() {
        List<MotionInfoDto> dtoList = new ArrayList<>();
        List<DeviceProxyWrapper> robotProxies = DeviceProxyHelper.getInstance().getRobotDeviceProxies();
        for (DeviceProxyWrapper proxy : robotProxies) {
            DeviceInstance deviceInstance = proxy.getDeviceInstance();
            MotionInfoDto motionInfoDto = new MotionInfoDto();

            Optional<DeviceInstanceCmd> instanceCmdOptional = deviceInstance.getCommands().stream().filter(
                    cmd -> StringUtils.equalsAny("STATUS", cmd.getType())).findFirst();
            if (instanceCmdOptional.isPresent()) {
                CommandReturn cmdRet = deviceInstance.getProxy().executeCommand(instanceCmdOptional.get());
                if (ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode())) {
                    Object rs = cmdRet.getResult();
                    if (rs instanceof String) {
                        try {
                            MotionInfoDto objDto = JSON.parseObject((String) rs, MotionInfoDto.class);
                            BeanUtil.copyProperties(objDto, motionInfoDto);
                        } catch (JSONException ex) {
                            log.error("Could not parse rs: {}", rs, ex);
                        }
                    } else if (rs instanceof Map) {
                        Map rsMap = (Map) rs;
                        try {
                            MotionInfoDto objDto = JSON.parseObject(JSON.toJSONString(rsMap), MotionInfoDto.class);
                            BeanUtil.copyProperties(objDto, motionInfoDto);
                        } catch (JSONException ex) {
                            log.error("Could not parse rs: {}", rs, ex);
                        }
                    }

                } else {
                    motionInfoDto.setStatus(cmdRet.getStatus().name());
                    motionInfoDto.setErrorCode(cmdRet.getErrorCode().name());
                    motionInfoDto.setErrorMsg(cmdRet.getErrorMsg());
                }
                motionInfoDto.setName(deviceInstance.getName());
                dtoList.add(motionInfoDto);
            }

        }
        return dtoList;
    }

    @Override
    public MotionInfoDto getCurrentMotionInfo(MotionInfoDto dto) {
        List<DeviceProxyWrapper> robotProxies = DeviceProxyHelper.getInstance().getRobotDeviceProxies();
        if (robotProxies.size()>0){
            // 当前只支持一个主机器人
            DeviceProxyWrapper proxyWrapper = robotProxies.get(0);
            DeviceInstance deviceInstance = proxyWrapper.getDeviceInstance();
            if (!deviceInstance.isInitialized()){
                dto.setStatus(MotionInfo.StatusEnum.NO_CONNECT.name());
            }else {
                Object ret = proxyWrapper.invokeMethod("getCurrentMotionInfo");
                BeanUtil.copyProperties(ret, dto, CopyOptions.create().setIgnoreNullValue(true));
            }

            Object configParameter = proxyWrapper.getInitParameterObject();
            BeanUtil.copyProperties(configParameter, dto, CopyOptions.create().setIgnoreNullValue(true));
            //BeanUtil.getFieldValue(configParameter,"speedFactor");
        }else{
            // 无机器人配置，默认开锁
            dto.setDoorStatus(0);
        }
        return dto;
    }

    @Override
    @Transactional
    public MotionInfoDto changeSpeed(MotionInfoDto motionInfoDto) {
        List<DeviceProxyWrapper> robotProxies = DeviceProxyHelper.getInstance().getRobotDeviceProxies();
        if (robotProxies.size() > 0) {
            // 当前只支持一个主机器人
            DeviceProxyWrapper proxyWrapper = robotProxies.get(0);
            DeviceInstance deviceInstance = proxyWrapper.getDeviceInstance();


            Object configParameter = proxyWrapper.getInitParameterObject();

            Object maxSpeed = BeanUtil.getFieldValue(configParameter, "maxSpeed");
            double dMaxSpeed = 0.00;
            if (maxSpeed instanceof Double) {
                dMaxSpeed = (Double) maxSpeed;
            }
            double speedFactor = motionInfoDto.getSpeedFactor();
            if (speedFactor < 2.0) {
                speedFactor = 2.0;
                motionInfoDto.setSpeedFactor(speedFactor);
            }

            double speed = dMaxSpeed * speedFactor / 100;

            BeanUtil.setFieldValue(configParameter, "speed", speed);
            BeanUtil.setFieldValue(configParameter, "speedFactor", speedFactor);

            String configStr = JSON.toJSONString(configParameter);
            if (deviceInstance.isEnableMock()) {
                deviceInstance.setMockConfig(configStr);
            } else {
                deviceInstance.setConfig(configStr);
            }

            deviceInstanceRepository.updateConfig(deviceInstance.getId(), deviceInstance.getConfig(), deviceInstance.getMockConfig());

        }
        return motionInfoDto;
    }

    @Override
    public MotionInfoDto openDoor(MotionInfoDto motionInfoDto) {
        List<DeviceProxyWrapper> robotProxies = DeviceProxyHelper.getInstance().getRobotDeviceProxies();
        if (robotProxies.size() > 0) {
            // 当前只支持一个主机器人
            DeviceProxyWrapper proxyWrapper = robotProxies.get(0);
            DeviceInstance deviceInstance = proxyWrapper.getDeviceInstance();

            Object ret = proxyWrapper.invokeMethod("openDoor", motionInfoDto.isToOpenDoor());
            if (ret instanceof  Integer){
                motionInfoDto.setDoorStatus((Integer)ret);
            }

        }
        return motionInfoDto;
    }
}