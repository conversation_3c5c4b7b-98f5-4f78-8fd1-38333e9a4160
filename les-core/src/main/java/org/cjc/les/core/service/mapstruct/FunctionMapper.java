/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.core.domain.Function;
import org.cjc.les.core.service.dto.FunctionDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-17
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FunctionMapper extends BaseMapper<FunctionDto, Function> {

}