package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.service.dto.ResultFeatureDto;
import org.cjc.les.core.vo.ConditionItemVo;
import org.cjc.les.exception.TaskRunningException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Log4j2
public class ConclusionValidator {

    /**
     * 校验结论
     * @param feature
     * @param featureDto
     * @param stage
     * @return
     */
    public static boolean validate(ResultFeature feature, ResultFeatureDto featureDto, int stage) {
        String rules = feature.getCheckRules();
        if (StringUtils.isEmpty(rules)) {
            return true;
        }
        List<ConditionItemVo> predicatesList = JSON.parseArray(rules, ConditionItemVo.class);
        if (CollectionUtils.isEmpty(predicatesList)) {
            return true;
        }
        boolean ret = true;
        for (ConditionItemVo item : predicatesList) {
            boolean tmpRet = validateItem(item, feature, featureDto, stage);
            if ( "and".equalsIgnoreCase(item.getJoinOperator())) {
                ret = ret && tmpRet;
                if (!ret) {
                    return false;
                }
            } else if ( "or".equalsIgnoreCase(item.getJoinOperator())) {
                ret = ret || tmpRet;
                if (ret) {
                    return true;
                }
            }
        }
        return ret;
    }
    private static boolean validateItem(ConditionItemVo item, ResultFeature feature, ResultFeatureDto featureDto, int stage) {
        if (StringUtils.equalsAnyIgnoreCase(item.getName(),"RSD")){
            return validateRsd(item, feature, featureDto, stage);
        }else if (StringUtils.equalsAnyIgnoreCase(item.getName(),"DRSD")){
            return validateDRsd(item, feature, featureDto, stage);
        }
        return true;
    }
    private static boolean validateRsd(ConditionItemVo item, ResultFeature feature, ResultFeatureDto featureDto, int stage) {
        BigDecimal decimal = null;
        try {
            decimal = new BigDecimal(featureDto.getRsd());
        }catch(NumberFormatException ex){
            //throw new TaskRunningException("RSD NumberFormat error: RSD="+featureDto.getRsd());
            log.warn("RSD NumberFormat error: RSD={}", featureDto.getRsd());
            feature.setReason("Invalid RSD value:" + featureDto.getRsd());
            return false;
        }
        boolean ret = compare(decimal, item);
        if (!ret){
            String reason = (stage==0)? "检测" :  "复检";
            reason += "结果不满足条件:RSD应该小于"+item.getTargetValue();
            feature.setReason(reason);
        }
        return ret;
    }

    /**
     * 并行检测RSD校验
     * @param value1
     * @param value2
     * @param ruleStr
     * @return
     */
    public static boolean validateParallelRsd(String value1, String value2, String ruleStr){
        if (StringUtils.isEmpty(ruleStr)) {
            return true;
        }
        List<ConditionItemVo> predicatesList = JSON.parseArray(ruleStr, ConditionItemVo.class);
        if (CollectionUtils.isEmpty(predicatesList)) {
            return true;
        }
        Optional<ConditionItemVo> itemOpt  = predicatesList.stream().filter(i->StringUtils.equalsAnyIgnoreCase(i.getName(),"DRSD")).findFirst();
        if (!itemOpt.isPresent()){
            return true;
        };

        BigDecimal value1Decimal = new BigDecimal(value1);
        BigDecimal value2Decimal = new BigDecimal(value2);
        BigDecimal diff = value1Decimal.subtract(value2Decimal).abs();
        return compare(diff, itemOpt.get());
    }

    private static boolean validateDRsd(ConditionItemVo item, ResultFeature feature, ResultFeatureDto featureDto, int stage) {
        if (stage==0){
            return true;
        }

        // boolean ret = compare(diff, item);
        boolean ret = ResultHelper.checkParallelResult();
        if (!ret){
            feature.setReason("平行样检测结果偏差超过限值%"+item.getTargetValue());
        }
        return ret;
    }

    private static boolean compare(BigDecimal value, ConditionItemVo item) {
        if (StringUtils.equalsAnyIgnoreCase(item.getCompareOperator(), ConditionItemVo.CompareOperatorEnum.lt.name())) {
            return value.compareTo(new BigDecimal(item.getTargetValue())) < 0;
        } else if ( StringUtils.equalsAnyIgnoreCase(item.getCompareOperator(), ConditionItemVo.CompareOperatorEnum.gt.name())) {
            return value.compareTo(new BigDecimal(item.getTargetValue())) > 0;
        } else if ( StringUtils.equalsAnyIgnoreCase(item.getCompareOperator(), ConditionItemVo.CompareOperatorEnum.eq.name())) {
            return value.compareTo(new BigDecimal(item.getTargetValue())) == 0;
        }else if ( StringUtils.equalsAnyIgnoreCase(item.getCompareOperator(), ConditionItemVo.CompareOperatorEnum.ne.name())) {
            return value.compareTo(new BigDecimal(item.getTargetValue())) != 0;
        }else if ( StringUtils.equalsAnyIgnoreCase(item.getCompareOperator(), ConditionItemVo.CompareOperatorEnum.gte.name())) {
            return value.compareTo(new BigDecimal(item.getTargetValue())) >= 0;
        }else if ( StringUtils.equalsAnyIgnoreCase(item.getCompareOperator(), ConditionItemVo.CompareOperatorEnum.lte.name())) {
            return value.compareTo(new BigDecimal(item.getTargetValue())) <= 0;
        }
        return true;
    }
}
