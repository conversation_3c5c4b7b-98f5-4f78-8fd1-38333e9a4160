/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.DeviceLayout;
import org.cjc.les.core.domain.TaskExecuteLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-08-23
**/
public interface TaskExecuteLogRepository extends JpaRepository<TaskExecuteLog, Long>, JpaSpecificationExecutor<TaskExecuteLog> {

    @Query(value = "select log.* from les_task_execute_log log where log.task_id =?1 order by log.id desc limit 1", nativeQuery = true)
    Optional<TaskExecuteLog> findLatestLogByTaskId(Long taskId);

    List<TaskExecuteLog> findByTaskIdOrderByIdAsc(Long taskId);

    List<TaskExecuteLog> findByTaskMethodIdOrderByIdAsc(Long taskMethodId);

    List<TaskExecuteLog> findByTaskStepIdOrderByIdAsc(Long taskStepId);

    List<TaskExecuteLog> findByTaskActionIdOrderByIdAsc(Long taskActionId);

    List<TaskExecuteLog> findByTaskCommandIdOrderByIdAsc(Long taskCommandId);
}