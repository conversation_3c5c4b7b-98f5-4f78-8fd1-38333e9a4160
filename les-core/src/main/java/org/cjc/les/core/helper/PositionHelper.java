package org.cjc.les.core.helper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.constants.SysVariableNameEnum;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.PositionRobot;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.VariableValueWrapper;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;

import java.util.List;
import java.util.Optional;

public class PositionHelper {
    /**
     * 获取点位编码
     * @param deviceInstanceId
     * @param deviceInstanceName
     * @param positionName
     * @param positionStatus
     * @param options
     * @return
     */
    public static String getPositionCode(long deviceInstanceId,
                                           String deviceInstanceName,
                                           String positionName,
                                           String positionStatus, String options) {

        TaskCommand taskCommand = TaskExecutorContext.getTaskCommand();
        if (taskCommand == null) {
            return "";
        }

        String returnCode = "ROBOT_CODE"; // SERVICE_CODE // REDIRECT_CODE

        if (StringUtils.isNotEmpty(options)){
            options = options.replaceAll("\\\\\"","\"");
            JSONObject optObj = JSON.parseObject(options);
            String retCode = optObj.getString("returnCode");
            if (StringUtils.isNotEmpty(retCode)) {
                returnCode = retCode;
            }
            Boolean useRedirectedPos = optObj.getBoolean("useRedirectedPos");
            if (useRedirectedPos!=null && useRedirectedPos.booleanValue()){
                Object firstObj = TaskExecutorContext.getContext().getLocalVarObjFirstItemToTail(SysVariableNameEnum.SELECTED_POSITIONS.name());
                if (firstObj != null) {
                    VariableValueWrapper varWrapper = (VariableValueWrapper)firstObj;
                    Position pos= (Position)varWrapper.getValue();

                    if (StringUtils.equalsAny(returnCode, "REDIRECT_CODE")){
                        RepoManagementHelper repoManagementHelper = SpringContextHolder.getBean(RepoManagementHelper.class);
                        return repoManagementHelper.getRedirectCmdParameter(pos);

                    }else if (StringUtils.equalsAny(returnCode, "SERVICE_CODE")) {
                        return pos.getServiceCode();
                    } else {// ROBOT_CODE
                        Optional<PositionRobot> pRobot = pos.getRobots().stream().filter(rb -> {
                            return taskCommand.getDeviceInstance().getId().equals(
                                    rb.getRobotDevInstanceId());
                        }).findFirst();
                        if (pRobot.isPresent()) {
                            return pRobot.get().getRobotPosCode();
                        }
                    }
                }
            }
        }

        if (deviceInstanceId == 0L){
            // 默认无指定设备电位的情况，通过PRESET_POSITIONS传递参数
            Object varPresetPos = TaskExecutorContext.getVar(SysVariableNameEnum.PRESET_POSITIONS.name());
            if (varPresetPos instanceof List){
                List<VariableValueWrapper> vaList = (List<VariableValueWrapper>) varPresetPos;
                if (CollectionUtils.isNotEmpty(vaList)) {
                    VariableValueWrapper va = vaList.remove(0);
                    Object value = va.getValue();
                    if (value instanceof Position) {
                        Position pos= (Position)value;
                        TaskExecutorContext.getContext().getTaskPositionBuffer().add(pos);
                        TaskExecutorContext.getContext().addLocalVarObj(SysVariableNameEnum.SELECTED_POSITIONS.name(), pos);

                        if (StringUtils.equalsAny(returnCode, "REDIRECT_CODE")){
                            RepoManagementHelper repoManagementHelper = SpringContextHolder.getBean(RepoManagementHelper.class);
                            return repoManagementHelper.getRedirectCmdParameter(pos);

                        }else if (StringUtils.equalsAny(returnCode, "SERVICE_CODE")) {
                            return pos.getServiceCode();
                        } else {// ROBOT_CODE
                            Optional<PositionRobot> pRobot = pos.getRobots().stream().filter(rb ->
                            {
                                return taskCommand.getDeviceInstance().getId().equals(
                                        rb.getRobotDevInstanceId());
                            }).findFirst();
                            if (pRobot.isPresent()) {
                                return pRobot.get().getRobotPosCode();
                            }
                        }
                    }
                }
            }
            throw new TaskRunningException("Could not found matched PRESET_POSITIONS.");
        }

        PositionRequestVo posVo = new PositionRequestVo();
        posVo.setDeviceInstanceId(deviceInstanceId);
        posVo.setName(positionName);
        posVo.setStatus(positionStatus);
        posVo.setTaskId(taskCommand.getTaskId());
        posVo.setTaskMethodId(taskCommand.getTaskMethodId());
        if (StringUtils.equalsAny(returnCode, "REDIRECT_CODE")){
            posVo.setOrderBy("serviceCode,asc");
        }

        PositionService positionService = SpringContextHolder.getBean(PositionService.class);
        Optional<Position> posOpt = positionService.findMatchedPosition(posVo);

        if (!posOpt.isPresent()) {
            throw new TaskRunningException("Could not found matched status positions, param: " + posVo);
        }

        Position matchedPos = posOpt.get();
        // 针对多点位的场景下，不设置单个点位值，后续处理依据SELECTED_POSITIONS环境变量来实现
        // taskCommand.setSelectedDevicePos(matchedPos);
        TaskExecutorContext.getContext().getTaskPositionBuffer().add(matchedPos);
        TaskExecutorContext.getContext().addLocalVarObj(SysVariableNameEnum.SELECTED_POSITIONS.name(), matchedPos);

        if (StringUtils.equalsAny(returnCode, "REDIRECT_CODE")){
            RepoManagementHelper repoManagementHelper = SpringContextHolder.getBean(RepoManagementHelper.class);
            return repoManagementHelper.getRedirectCmdParameter(matchedPos);
        }else if (StringUtils.equalsAny(returnCode, "SERVICE_CODE")) {
            return matchedPos.getServiceCode();
        } else {// ROBOT_CODE
            Optional<PositionRobot> pRobot = matchedPos.getRobots().stream().filter(rb ->
            {
                return taskCommand.getDeviceInstance().getId().equals(
                        rb.getRobotDevInstanceId());
            }).findFirst();
            if (pRobot.isPresent()) {
                return pRobot.get().getRobotPosCode();
            } else if (CollectionUtils.isNotEmpty(matchedPos.getRobots())) {
                return matchedPos.getRobots().stream().findFirst().get().getRobotPosCode();
            }
        }
        return matchedPos.getRobotPosCode();
    }
}
