/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-16
**/
@Entity
@Data
@Table(name="les_feature_config")
@SQLDelete(sql = "UPDATE les_formula SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class FeatureConfig extends LesConfigBaseEntity {

    @Column(name = "method_id")
    @Schema(description = "关联les_method，检测方法标识")
    private Long methodId;

    @Column(name = "code",nullable = false)
    @NotBlank
    @Schema(description = "特性编码")
    private String code;

    @Column(name = "name")
    @Schema(description = "特性名称")
    private String name;

    @Column(name = "instrument_column_name")
    @Schema(description = "仪器对应字段名")
    private String instrumentColumnName;

    @Column(name = "description")
    @Schema(description = "结果特性描述")
    private String description;

    @Column(name = "unit")
    @Schema(description = "数值单位")
    private String unit;

    @Column(name = "check_rules")
    @Schema(description = "结论判定规则集")
    private String checkRules;


    public void copy(FeatureConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}