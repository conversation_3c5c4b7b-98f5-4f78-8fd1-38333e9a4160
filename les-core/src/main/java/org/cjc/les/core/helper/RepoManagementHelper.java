package org.cjc.les.core.helper;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.exception.TaskRunningException;
import org.springframework.stereotype.Component;

/**
 * 前端对应展示：1.物料管理 > 存储设备点位详情   2.货架设备监控图示状态显示
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class RepoManagementHelper {

    private final PositionService positionService;

    /**
     * 重置点位: 监听货架硬件复位标识, 设置所有点位状态为READY, ServiceCode复位,
     * 监听的调用位置ControlDeviceMonitor
     */
    public void resetAllPositionsByDeviceInstanceId(Long deviceInstanceId) {
        positionService.resetAllPositionsByDeviceInstanceId(deviceInstanceId);
    }

    /**
     * 通过position.serviceCode, 形如A03-A03,计算重定向指令参数
     * 在重定向指令参数计算时调用
     * @param position
     * @return  格式"D/R:0-12" // 从原点旋转的份数(360°分成12等份),D顺时针,R逆时针
     *         设置为6等分时，每次移动两个单位,即0 2 4 6 8 10
     */
    public String getRedirectCmdParameter(Position position) {

        String outStr = "D:";

        int redirectNum =getMoveCount(position);

        return outStr + redirectNum;
    }


    /**
     * 重定向完成后，重新计算货架所有点位的ServiceCode,顺序等
     * 在重定向指令执行成功后调用
     * @param position
     */
    public void computePositionAfterRedirect(Position position) {
        int redirectNum = getMoveCount(position);
        if (redirectNum == 0){
            return;
        }

        positionService.redirectAllPositionsOnDeviceInstance(position, redirectNum, "D");

    }

    private int getMoveCount(Position position) {

        String serviceCode = position.getServiceCode();
        if (StringUtils.isEmpty(serviceCode)){
            throw new TaskRunningException("serviceCode of position["+position.getId()+"] is empty");
        }

        String[] arr = serviceCode.split("-");
        String curPosStr = arr[1];
        int num = Integer.parseInt(curPosStr.substring(1));

        int redirectNum = (num %2 ==0) ? num-2 : num - 1;

        return redirectNum;
    }
}
