/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.PositionRobot;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.PositionRobotRepository;
import org.cjc.les.core.service.PositionRobotService;
import org.cjc.les.core.service.dto.PositionRobotDto;
import org.cjc.les.core.service.dto.PositionRobotQueryCriteria;
import org.cjc.les.core.service.mapstruct.PositionRobotMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-04-16
**/
@Service
@RequiredArgsConstructor
public class PositionRobotServiceImpl implements PositionRobotService {

    private final PositionRobotRepository positionRobotRepository;
    private final PositionRobotMapper positionRobotMapper;

    @Override
    public Map<String,Object> queryAll(PositionRobotQueryCriteria criteria, Pageable pageable){
        Page<PositionRobot> page = positionRobotRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(positionRobotMapper::toDto));
    }

    @Override
    public List<PositionRobotDto> queryAll(PositionRobotQueryCriteria criteria){
        return positionRobotMapper.toDto(positionRobotRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public PositionRobotDto findById(Long id) {
        PositionRobot positionRobot = positionRobotRepository.findById(id).orElseGet(PositionRobot::new);
        ValidationUtil.isNull(positionRobot.getId(),"PositionRobot","id",id);
        return positionRobotMapper.toDto(positionRobot);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PositionRobotDto create(PositionRobot resources) {
        return positionRobotMapper.toDto(positionRobotRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(PositionRobot resources) {
        PositionRobot positionRobot = positionRobotRepository.findById(resources.getId()).orElseGet(PositionRobot::new);
        ValidationUtil.isNull( positionRobot.getId(),"PositionRobot","id",resources.getId());
        positionRobot.copy(resources);
        positionRobotRepository.save(positionRobot);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            positionRobotRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<PositionRobotDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (PositionRobotDto positionRobot : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("点位ID", positionRobot.getPositionId());
            map.put("关联ROBOT设备实例ID", positionRobot.getRobotDevInstanceId());
            map.put("机器人定义的位置编号，用于机器人点位唯一识别", positionRobot.getRobotPosCode());
            map.put(" xpos",  positionRobot.getXpos());
            map.put(" ypos",  positionRobot.getYpos());
            map.put(" zpos",  positionRobot.getZpos());
            map.put("保留", positionRobot.getStatus());
            map.put(" upos",  positionRobot.getUpos());
            map.put(" vpos",  positionRobot.getVpos());
            map.put(" wpos",  positionRobot.getWpos());
            map.put("是否已被删除,Y/N", positionRobot.getDeleteFlag());
            map.put("创建人", positionRobot.getCreateBy());
            map.put("创建时间", positionRobot.getCreateTime());
            map.put("更新人", positionRobot.getUpdateBy());
            map.put("更新时间", positionRobot.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}