/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstance;

import java.util.Collection;
import java.util.List;

/**
 * 设备执行服务类
 */
public interface DeviceExecuteService {

    /**
     * 添加仪初始化完成的设备
     * @param device
     */
    void addDevice(Device device);

    /**
     * 通过设备ID查找到对应的设备实例
     * @param deviceId 在数据库中保存的ID
     * @return
     */
    Device getDevice(long deviceId);

    /**
     * 获取或者创建设备实例
     * @param deviceInstance 设备实例
     * @return
     */
    Device getOrCreateDevice(DeviceInstance deviceInstance);

    /**
     * 获取当前管理的所有设备
     * @return
     */
    Collection<Device> getAllDevices();

}
