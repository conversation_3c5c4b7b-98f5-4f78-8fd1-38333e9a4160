/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.MethodStep;
import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.helper.VariableHelper;
import org.cjc.les.core.service.TaskMethodService;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.core.util.TaskBreakPointUtil;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 方法执行器
 */
public class TaskMethodExecutor implements TaskExecutorInterface<TaskMethod> {

    private static final int POOL_SIZE = 16;

    private static ExecutorService taskStepExecutorPool = Executors.newFixedThreadPool(POOL_SIZE);

    private ExecutorConfig config;

    public TaskMethodExecutor(ExecutorConfig config) {
        this.config = config;
    }

    @Override
    public CommandReturn<TaskMethod> execute(TaskMethod taskMethod) {
        TaskExecutorContext.setTaskMethod(taskMethod);
        taskMethod.setStatus(CommandStatusEnum.RUNNING.name());

        if (this.config.isRecordLog()) {
            TaskExecuteLogUtil.logMethod(taskMethod);
        }

        parseVariables(taskMethod);

        RunStatusEnum lastStepRunStatus = RunStatusEnum.SUCCESS;

        for (TaskStep step : taskMethod.getSteps()) {
            // 已完成状态的，不再执行
            if (StringUtils.equalsAny(step.getStatus(),
                    RunStatusEnum.SUCCESS.name(),
                    RunStatusEnum.FAILED.name(),
                    RunStatusEnum.CANCELLED.name())) {
                continue;
            }

            TaskBreakPointUtil.checkBreakPoint(step);

            // 执行前钩子
            TaskStepHook stepHook = TaskExecutorContext.getTaskStepHook(step);
            if (stepHook != null) {
                CommandReturn<TaskStep> ret = stepHook.executeBefore(step);
                if (ret != null && (ret.getErrorCode().equals(ErrorCodeEnum.ERROR) || ret.getErrorCode().equals(ErrorCodeEnum.FATAL))) {
                    if (FailedThenEnum.THROW.equals(ret.getFailedThen())) {
                        break;
                    } else if (FailedThenEnum.CONTINUE.equals(ret.getFailedThen())) {
                        continue;
                    }
                }
            }

            // 执行步骤
            if (StringUtils.equalsAny(step.getScheduleMode(),
                    MethodStep.ScheduleModeEnum.UNBLOCK.name(),
                    MethodStep.ScheduleModeEnum.BLOCK_OTHERS.name())) {
                final TaskExecutorContext context = TaskExecutorContext.getContext();
                step.setStatus(RunStatusEnum.FORKED.name());
                taskStepExecutorPool.submit(() -> {
                    TaskExecutorContext.copy(context, step);
                    SpringContextHolder.getBean(StepScheduler.class).execute(step, this.config);
                    TaskExecutorContext.clear();
                    // 重新计算任务方法的运行状态
                    if ( !StringUtils.equalsAny( taskMethod.getStatus(), RunStatusEnum.SKIPPED.name(), RunStatusEnum.SUSPEND.name())) {
                        calcuStatus(taskMethod);
                        if (this.config.isRecordLog()) {
                            TaskExecuteLogUtil.logMethod(taskMethod);
                        }
                    }

                });

            } else {
                SpringContextHolder.getBean(StepScheduler.class).execute(step, this.config);
            }
            // 执行后钩子
            if (stepHook != null) {
                CommandReturn<TaskStep> ret = stepHook.executeAfter(step);
                if (ret != null && (RunStatusEnum.STOPPED.equals(ret.getStatus()))) {
                    lastStepRunStatus = ret.getStatus();
                    break;
                }
            }
        }

        if ( !StringUtils.equalsAny( taskMethod.getStatus(), RunStatusEnum.SKIPPED.name(), RunStatusEnum.SUSPEND.name())){
            if (RunStatusEnum.STOPPED.equals(lastStepRunStatus)) {
                taskMethod.setStatus(RunStatusEnum.STOPPED.name());
            } else {
                calcuStatus(taskMethod);
            }

            if (this.config.isRecordLog()) {
                TaskExecuteLogUtil.logMethod(taskMethod);
            }
        }


        CommandReturn<TaskMethod> ret = new CommandReturn<>();
        if (StringUtils.equalsAny(taskMethod.getStatus(),
                CommandStatusEnum.SUCCESS.name(),  RunStatusEnum.SKIPPED.name(), RunStatusEnum.SUSPEND.name())) {
            ret.setErrorCode(ErrorCodeEnum.SUCCESS);
        } else {
            ret.setErrorCode(ErrorCodeEnum.ERROR);
        }

        return ret;
    }

    private void calcuStatus(TaskMethod taskMethod) {
        if (taskMethod.getSteps().stream().allMatch(command -> {
            return StringUtils.equalsAny(command.getStatus(), RunStatusEnum.SUCCESS.name(),RunStatusEnum.SKIPPED.name());
        })) {
            taskMethod.setStatus(RunStatusEnum.SUCCESS.name());
        } else if (taskMethod.getSteps().stream().anyMatch(command -> {
            return StringUtils.equals(command.getStatus(), RunStatusEnum.CANCELLED.name());
        })) {
            taskMethod.setStatus(RunStatusEnum.CANCELLED.name());
        } else if (taskMethod.getSteps().stream().anyMatch(command -> {
            return StringUtils.equals(command.getStatus(), RunStatusEnum.FAILED.name());
        })) {
            taskMethod.setStatus(CommandStatusEnum.FAILED.name());
        } else if (taskMethod.getSteps().stream().anyMatch(step -> {
            return (StringUtils.equalsAny(step.getScheduleMode(), MethodStep.ScheduleModeEnum.UNBLOCK.name()));
        })) {
            taskMethod.setStatus(RunStatusEnum.SUCCESS_WITH_FORKED.name());
        }
    }


    private void parseVariables(TaskMethod taskMethod) {
        if (StringUtils.isEmpty(taskMethod.getVariables())){
            return;
        }
        VariableHelper.setNodeVariables(taskMethod.getVariables(), taskMethod);
    }
}
