/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.init;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.annotation.Command;
import org.cjc.les.annotation.Device;
import org.cjc.les.annotation.Parameter;
import org.cjc.les.constants.CommandTypeEnum;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.helper.DeviceProxyWrapper;
import org.cjc.les.core.service.DeviceLayoutService;
import org.cjc.les.core.service.DeviceService;
import org.cjc.les.core.vo.ConfigItemVo;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 设备定义初始化
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class DeviceInitializer {

    private static final String BASE_PACKAGE = "org.cjc.les";

    private final DeviceService deviceService;

    @Autowired
    private DeviceInitializer self;

    /**
     * 初始化设备
     */
    public void init() {
        // 1. 注册设备定义
        self.registerDevices();

    }

    @Transactional()
    public void registerDevices() {
        Reflections reflections = new Reflections(BASE_PACKAGE);

        // 扫描标注了@Device注解的类
        Set<Class<?>> annotatedClasses = reflections.getTypesAnnotatedWith(Device.class);

        // 处理扫描到的类，
        for (Class<?> clazz : annotatedClasses) {
            String className = clazz.getName();
            org.cjc.les.core.domain.Device device = new org.cjc.les.core.domain.Device();
            Device annoDev = clazz.getAnnotation(Device.class);
            String name = StringUtils.isEmpty(annoDev.name()) ? annoDev.value() : annoDev.name();
            device.setName(name);
            device.setJavaClassName(className);
            device.setDescription(annoDev.description());
            device.setType(annoDev.type().name());
            parseDoInitMethod(device, clazz);

            org.cjc.les.core.domain.Device deviceInDb = deviceService.findDeviceByJavaClassName(className).orElseGet(org.cjc.les.core.domain.Device::new);
            BeanUtil.copyProperties(device, deviceInDb, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("commands"));

            //deviceService.update(device);
            deviceInDb.getCommands().forEach(cmd -> {
                cmd.setStatus(CommandStatusEnum.INVALID);
            });

            Method[] methods = clazz.getMethods();
            for (Method mth : methods) {
                Command cmdAnno = mth.getAnnotation(Command.class);
                if (cmdAnno == null) {
                    continue;
                }
                org.cjc.les.core.domain.Command command = new org.cjc.les.core.domain.Command();
                command.setDeviceId(device.getId());
                command.setName(StringUtils.isEmpty(cmdAnno.name())?cmdAnno.value():cmdAnno.name());
                command.setDescription(cmdAnno.description());
                command.setType(cmdAnno.type());
                command.setJavaMethodName(mth.getName());
                command.setParameterType(getParameterTypeName(mth));
                if (StringUtils.isNotEmpty(command.getParameterType())){
                    command.setParameterTemplate(getParameterTemplate(mth));
                }
                command.setStatus(CommandStatusEnum.VALID);
                command.setDevice(deviceInDb);
                // 检查命令是否已经存在
                Optional<org.cjc.les.core.domain.Command> existingCommand = deviceInDb.getCommands().stream()
                        .filter(cmd -> StringUtils.equals(command.getJavaMethodName(), cmd.getJavaMethodName()) &&
                                StringUtils.equals(command.getParameterType(), cmd.getParameterType()))
                        .findFirst();

                if (existingCommand.isPresent()) {
                    // 如果命令已经存在，更新命令
                    existingCommand.get().copy(command);
                } else {
                    // 如果命令不存在，添加新命令
                    deviceInDb.getCommands().add(command);
                }
            }
            deviceService.save(deviceInDb);

        }
    }

    private void parseDoInitMethod(org.cjc.les.core.domain.Device device, Class<?> clazz) {

        Method doInitMethod = null;
        Method[] methods = clazz.getMethods();
        for (Method mth : methods) {
            Command cmdAnno = mth.getAnnotation(Command.class);
            if (StringUtils.equals(mth.getName(), "doInit")
            || (cmdAnno!=null && CommandTypeEnum.INIT.equals(cmdAnno.type()))
            ) {
                doInitMethod = mth;
                break;
            }
        }
        if (doInitMethod == null) {
            log.error("Could not found doInit method of class: {}", clazz.getName());
            return;
        }

        int paramCount = doInitMethod.getParameterCount();

        if (paramCount > 1) {
            log.error("Could not support more then 1 parameters.");
            return;
        } else if (paramCount == 0) {
            log.debug("Do not need to config parameters.");
            return;
        }

        Class<?>[] paramTypes = doInitMethod.getParameterTypes();

        Class<?> paramType = paramTypes[0];
        device.setConfigJavaClassName(paramType.getName());

        device.setConfig(getParameterTemplate(doInitMethod));
    }

    private String getParameterTemplate(Method method){

        Class<?>[] paramTypes = method.getParameterTypes();

        Class<?> paramType = paramTypes[0];
        // 解析配置模板
        ConfigItemVo rootItem = new ConfigItemVo();
        rootItem.setKey( method.getParameters()[0].getName());
        rootItem.setType(paramType.getName());
        Parameter annoParam = method.getParameters()[0].getAnnotation(Parameter.class);
        if (annoParam !=null){
            String paramName = StringUtils.isEmpty(annoParam.value())?annoParam.name():annoParam.value();
            String paramDescription = annoParam.description();
            String paramDefaultValue = annoParam.defaultValue();

            rootItem.setName(paramName);
            rootItem.setDefaultValue(paramDefaultValue);
            rootItem.setDescription(paramDescription);
            rootItem.setCandidateValues(annoParam.candidateValues());
            rootItem.setInputComponent(annoParam.inputComponent());
            rootItem.setValidateRules(annoParam.validateRules());
        }

        setConfigItemChildren(rootItem);

        return JSON.toJSONString(rootItem);
    }

    private void setConfigItemChildren(ConfigItemVo configItem) {
        String type = configItem.getType();
        if (StringUtils.equalsAny(type, "java.lang.String","java.lang.Boolean")){
            return;
        }
        try {
            Class<?> childClz = Class.forName(type);
            for (Field field : childClz.getDeclaredFields()) {

                Parameter param = field.getAnnotation(Parameter.class);
                if (param != null){
                    ConfigItemVo item = new ConfigItemVo();
                    item.setKey( field.getName());
                    item.setType(field.getType().getName());
                    item.setName(StringUtils.isEmpty(param.value())?param.name():param.value());
                    item.setDescription(param.description());
                    item.setDefaultValue(param.defaultValue());

                    if (configItem.getChildren()==null){
                        configItem.setChildren(new ArrayList<>());
                    }
                    configItem.getChildren().add(item);
                }

            }
        } catch (ClassNotFoundException e) {
            log.error("Could not found class: "+e.getMessage());
        }
    }

    private String getParameterTypeName(Method method) {

        int paramCount = method.getParameterCount();

        if (paramCount > 1) {
            log.error("Could not support more then 1 parameters.");
            return null;
        } else if (paramCount == 0) {
            log.warn("Do not need to config parameters.");
            return null;
        }

        Class<?>[] paramTypes = method.getParameterTypes();

        Class<?> paramType = paramTypes[0];
        return paramType.getName();
    }


}
