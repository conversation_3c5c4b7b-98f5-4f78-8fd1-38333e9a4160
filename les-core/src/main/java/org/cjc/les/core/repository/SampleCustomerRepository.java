/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.SampleCustomer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-22
**/
public interface SampleCustomerRepository extends JpaRepository<SampleCustomer, Long>, JpaSpecificationExecutor<SampleCustomer> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    Optional<SampleCustomer> findByName(String name);
}