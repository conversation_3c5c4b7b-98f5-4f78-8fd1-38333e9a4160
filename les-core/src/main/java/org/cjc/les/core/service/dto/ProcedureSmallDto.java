/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.ProcedureMethod;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 *
 * 用于流程的编排（新增，编辑，查询详情）
 *
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Data
public class ProcedureSmallDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工作站名称 */
    private String name;

    /** 工作站描述 */
    private String description;

    /** 流程类型:RAW, DERIVE */
    private String type;

    /**
     * 该流程绑定的进样方法, 前端在选取多检测流程时，同一个样品瓶，不允许同时选取不同进样方法
     */
    private Long entryMethodId;
    private String entryMethodName;

    /**
     * 当type=DERIVE时，该字段存储对应被派生的流程ID列表，存在多个被派生流程ID时，以英文逗号分割
     */
    private String deriveProcIds;

    /**
     * 当type=DERIVE时，该字段存储对应被派生的流程名称列表，存在多个被派生流程时，以英文逗号分割
     */
    private List<String> deriveProcNames;

    /** 工作站状态 */
    private String status;

    /**
     * 是否平行检测
     */
    private String parallel;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}