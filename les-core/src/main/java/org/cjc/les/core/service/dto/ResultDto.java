/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-25
**/
@Data
public class ResultDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 检测编号 */
    private String number;

    /** 检测名称 */
    private String name;

    /** 检测描述 */
    private String description;

    /** 样品编号 */
    private String sampleNumber;

    /**
     * 样品标签
     */
    private String sampleTag;

    /**
     * 样品名称
     */
    private String sampleName;

    /**
     * 样品分类
     */
    private String sampleCategory;

    /**
     * 送样客户
     */
    private String sampleCustomer;

    /** 生成该产品的进程实例ID */
    private Long taskId;

    /**
     * 任务编码
     */
    private String taskNumber;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;

    private SampleDto sample;

    /**
     * 检测项列表
     **/
    private List<ResultItemDto> items = new ArrayList<>();

}

