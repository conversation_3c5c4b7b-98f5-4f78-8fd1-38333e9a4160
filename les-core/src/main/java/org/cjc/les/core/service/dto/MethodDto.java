/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-08
**/
@Data
public class MethodDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 工作站名称 */
    private String name;

    /** 工作站描述 */
    private String description;

    /** 方法类型: ENTRY/MAIN/EXIT/EXCEPTION */
    private String type;

    /** 工作站状态 */
    private String status;

    /**
     * 创建该方法的流程ID
     */
    private Long createByProcedureId;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}