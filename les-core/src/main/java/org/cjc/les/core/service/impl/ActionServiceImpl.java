/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import org.cjc.les.core.domain.Action;
import lombok.RequiredArgsConstructor;
import org.cjc.les.core.repository.ActionRepository;
import org.cjc.les.core.service.ActionService;
import org.cjc.les.core.service.dto.ActionDto;
import org.cjc.les.core.service.dto.ActionQueryCriteria;
import org.cjc.les.core.service.mapstruct.ActionMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-06-08
**/
@Service
@RequiredArgsConstructor
public class ActionServiceImpl implements ActionService {

    private final ActionRepository actionRepository;
    private final ActionMapper actionMapper;

    @Override
    public Map<String,Object> queryAll(ActionQueryCriteria criteria, Pageable pageable){
        Page<Action> page = actionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(actionMapper::toDto));
    }

    @Override
    public List<ActionDto> queryAll(ActionQueryCriteria criteria){
        return actionMapper.toDto(actionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<ActionDto> queryAllForSection(ActionQueryCriteria criteria) {
        return actionMapper.toDto(actionRepository.findActionsOrderByStationAndCreateTime());
    }

    @Override
    @Transactional
    public ActionDto findById(Long id) {
        Action action = actionRepository.findById(id).orElseGet(Action::new);
        ValidationUtil.isNull(action.getId(),"Action","id",id);
        return actionMapper.toDto(action);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActionDto create(Action resources) {
        return actionMapper.toDto(actionRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Action resources) {
        Action action = actionRepository.findById(resources.getId()).orElseGet(Action::new);
        ValidationUtil.isNull( action.getId(),"Action","id",resources.getId());
        action.copy(resources);
        actionRepository.save(action);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            actionRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ActionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ActionDto action : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("命令名称", action.getName());
            map.put("命令描述", action.getDescription());
            map.put("关联设备ID", action.getStationId());
            map.put("是否已被删除,Y/N", action.getDeleteFlag());
            map.put("创建人", action.getCreateBy());
            map.put("创建时间", action.getCreateTime());
            map.put("更新人", action.getUpdateBy());
            map.put("更新时间", action.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}