/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.service.dto.MotionInfoDto;

import java.util.List;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-10-11
**/
public interface MotionService {

    /**
     * 查询当前运动信息
     * @return
     */
    MotionInfoDto getCurrentMotionInfo(MotionInfoDto dto);

    /**
     * 获取岁哦有运动对象列表
     * @return
     */
    List<MotionInfoDto> getMotionInfos();

    /**
     * 设置运行速度
     * @param motionInfoDto
     * @return
     */
    MotionInfoDto changeSpeed(MotionInfoDto motionInfoDto);

    /**
     * 设备关门
     * @param motionInfoDto
     * @return
     */
    MotionInfoDto openDoor(MotionInfoDto motionInfoDto);

}