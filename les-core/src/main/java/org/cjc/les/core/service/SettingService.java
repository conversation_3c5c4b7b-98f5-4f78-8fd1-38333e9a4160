/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.service;

import org.cjc.les.core.domain.Setting;
import org.cjc.les.core.service.dto.SettingDto;
import org.cjc.les.core.service.dto.SettingQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-05-19
**/
public interface SettingService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SettingQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SettingDto>
    */
    List<SettingDto> queryAll(SettingQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return SettingDto
     */
    SettingDto findById(Long id);

    /**
     * 获取最近设置详情
     *
     * @return
     */
    SettingDto queryLatestOne();

    /**
    * 创建
    * @param resources /
    * @return SettingDto
    */
    SettingDto create(Setting resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(Setting resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SettingDto> all, HttpServletResponse response) throws IOException;
}