/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.vo;

import lombok.Data;

@Data
public class LockRequestVo {
    private String lockObjectType;
    private String name;
    private Long deviceInstanceId;
    private Long taskId;
    private String status;
    /**
     * 加锁模式:BLOCK   阻塞模式,未抢占到锁时，一直循环等待;
     *        UNBLOCK 非阻塞模式,未抢占到锁时，直接退出
     */
    private String lockMode = "BLOCK";
}
