/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.cjc.base.LesBaseEntity;
import org.cjc.les.constants.FailedThenEnum;
import org.cjc.les.core.constants.CommandStatusEnum;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Entity
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_task_command")
public class TaskCommand extends TaskBaseEntity {

    @Column(name = "name")
    @Schema(description = "自定义命令名称")
    private String name;

    @Column(name = "task_id")
    @Schema(description = "任务ID")
    private Long taskId;

    @Column(name = "action_command_id")
    @Schema(description = "关联命令")
    private Long actionCommandId;

    @Column(name = "task_method_id")
    @Schema(description = "taskMethodId")
    private Long taskMethodId;

    @Column(name = "task_step_id")
    @Schema(description = "taskStepId")
    private Long taskStepId;

    @JSONField(serialize = false)
    @OneToOne
    @JoinColumn(name = "device_instance_id")
    @Schema(description = "设备实例ID")
    private DeviceInstance deviceInstance;

    /**
     * 引用命令对象
     */
    @Schema(description = "关联命令")
    @OneToOne
    @JoinColumn(name = "command_id")
    private DeviceInstanceCmd command;

    @Column(name = "parameter")
    @Schema(description = "parameter")
    private String parameter;

    @JSONField(serialize = false)
    @OneToOne(cascade = CascadeType.MERGE)
    @JoinColumn(name = "selected_device_pos_id", referencedColumnName = "id")
    @Schema(description = "selectedDevicePosId")
    private Position selectedDevicePos;

    @JSONField(serialize = false)
    @Transient
    private List<Position> selectedDevicePositions = new ArrayList<>();

    @Column(name = "comment")
    @Schema(description = "命令备注描述")
    private String comment;

    @Enumerated(EnumType.STRING)
    @Column(name = "failed_then")
    @Schema(description = "运行失败后的处理方式:CONTINUE/RETURN_ACTION/RETURN_STEP/RETURN_METHOD/RETURN_PROCEDURE")
    private FailedThenEnum failedThen = FailedThenEnum.PAUSE;

    /**
     * 失败后的重试次数
     */
    @Column(name = "failed_retries")
    private Integer failedRetries;
    /**
     * 指令执行前的点位检测状态
     */
    private String positionCheckingStatus;

    public List<String> getPositionCheckingStatusAsArray() {
        if (StringUtils.isEmpty(positionCheckingStatus)){
            return new ArrayList<>();
        }
        return JSON.parseArray(positionCheckingStatus, String.class);
    }

    /**
     * 指令执行前的点位状态设置
     */
    private String positionPreExecutionStatus;

    public List<String> getPositionPreExecutionStatusAsArray() {
        if (StringUtils.isEmpty(positionPreExecutionStatus)){
            return new ArrayList<>();
        }
        return JSON.parseArray(positionPreExecutionStatus, String.class);
    }

    /**
     * 指令执行后的脚本名称
     */
    private String postExecution;

    /**
     * 指令执行成功后的点位状态
     */
    private String positionExecutedStatus;

    public List<String> getPositionExecutedStatusAsArray() {
        if (StringUtils.isEmpty(positionExecutedStatus)){
            return new ArrayList<>();
        }
        return JSON.parseArray(positionExecutedStatus, String.class);
    }

    /**
     * 执行完成后的延迟时长(ms)
     */
    @Column(name = "executed_delay")
    private Integer executedDelay;

    /**
     * 物料消耗量
     */
    private String materialConsumption;

    /**
     * 是否可选: Y/N
     */
    private String isOption;
    /**
     * 可选项编码
     */
    private String optionCode;

    @Column(name = "failed_message")
    @Schema(description = "执行FAILED状态时的详情")
    private String message;

    @Column(name = "failure_fix_as")
    private String failureFixAs;
    @Column(name = "failure_fix_as_temp")
    private String failureFixAsTemp;
    @Column(name = "failure_fixed_by")
    private String failureFixedBy;

    private Long evaluateExecutingDuration;
    private Timestamp evaluatedTime;
    private Long executedDuration;

    @JSONField(serialize = true)
    @Transient
    private String layoutImage;

    @JSONField(serialize = false)
    @JoinColumn(name = "task_action_id", referencedColumnName = "id")
    @ManyToOne(fetch=FetchType.EAGER, targetEntity = TaskAction.class)
    @Schema(description = "关联ACTION", hidden = true)
    private TaskAction taskAction;

    /**
     * For DEBUG
     * @param source
     */
    @JSONField(serialize = true)
    @Transient
    private boolean breakpoint = false;

    public void copy(TaskCommand source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
    /**
     * 从ActionCommand定义中拷贝
     * @param command
     */
    public void copy(ActionCommand command){
        BeanUtil.copyProperties(command,this, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id","status"));
        this.setActionCommandId(command.getId());
    }

    @Override
    public String toString() {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("id=").append(this.getId())
                .append(",name=").append(this.getName())
                .append(",taskId=").append(this.taskId)
                .append(",status=").append(this.getStatus())
                .append(",message").append(message)
        ;
        return stringBuffer.toString();
    }
}