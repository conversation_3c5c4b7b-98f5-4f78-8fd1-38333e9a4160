/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.dto.DeviceInstanceQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-07-30
**/
public interface DeviceInstanceService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(DeviceInstanceQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<DeviceInstanceDto>
    */
    List<DeviceInstanceDto> queryAll(DeviceInstanceQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return DeviceInstanceDto
     */
    DeviceInstanceDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return DeviceInstanceDto
    */
    DeviceInstanceDto create(DeviceInstance resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(DeviceInstance resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<DeviceInstanceDto> all, HttpServletResponse response) throws IOException;

    /**
     * 重新从数据库读取最新数据
     * @param deviceInstance
     */
    void reload(DeviceInstance deviceInstance);

    /**
     * 更新点位详情配置
     * @param resources
     */
    void updatePositionDetailConfig(DeviceInstanceDto resources);
}