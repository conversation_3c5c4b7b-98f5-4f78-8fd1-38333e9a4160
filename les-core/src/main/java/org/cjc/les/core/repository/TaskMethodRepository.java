/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Position;
import org.cjc.les.core.domain.TaskMethod;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
public interface TaskMethodRepository extends JpaRepository<TaskMethod, Long>, JpaSpecificationExecutor<TaskMethod> {
    /**
    * 根据 Name 查询
    * @param name /
    * @return /
    */
    TaskMethod findByName(String name);

    @Modifying
    @Query(value="update TaskMethod set status=?2, message=?3, updateTime=CURRENT_TIMESTAMP where id=?1")
    void updateStatus(Long id, String status, String message);

}