/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.execute;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.VariableHelper;
import org.cjc.les.core.service.ResultService;
import org.cjc.les.core.util.TaskBeanUtil;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 命令执行上下文对象，存储当前命令执行所需的环境信息
 */
@Data
@Log4j2
public class TaskExecutorContext {
    // 当前执行处理中的任务上下文
    private final static ThreadLocal<TaskExecutorContext> current = new ThreadLocal<>();

    /**
     * 任务执行配置信息
     */
    private ExecutorConfig executorConfig = new ExecutorConfig();

    /**
     * 任务执行步骤钩子存储
     */
    private ConcurrentHashMap<TaskStep, TaskStepHook> taskStepHookMap = new ConcurrentHashMap<>();

    /**
     * 保存步骤中最后两次选中的点位对象，用来传递该步骤中不同ACTION,不同COMMAND之间的点位参数
     */
    private TaskPositionBuffer taskPositionBuffer = new TaskPositionBuffer();

    /**
     * 保存当前执行上下文用到的变量
     */
    private ConcurrentHashMap<String, VariableValueWrapper> variableMap = new ConcurrentHashMap<>();

    // 当前执行处理中的任务, Task与TaskExecutorContext是一对一的关系
    private Task task;
    // 最后一次执行的任务方法
    private TaskMethod lastTaskMethod;
    // 最后一次执行的任务步骤
    private TaskStep lastTaskStep;
    // 最后一次执行的任务动作
    private TaskAction lastTaskAction;
    // 最后一次执行的任务命令
    private TaskCommand lastTaskCommand;

    // 当前线程中正在执行的任务方法
    private final static ThreadLocal<TaskMethod> curTaskMethodPool = new ThreadLocal<>();
    // 当前线程中正在执行的任务步骤
    private final static ThreadLocal<TaskStep> curTaskStepPool = new ThreadLocal<>();
    // 当前线程中正在执行的任务动作
    private final static ThreadLocal<TaskAction> curTaskActionPool = new ThreadLocal<>();
    // 当前线程中正在执行的任务命令
    private final static ThreadLocal<TaskCommand> curTaskCommandPool = new ThreadLocal<>();

    /**
     * 保存当前执行线程用到的变量
     */
    private final ThreadLocal<ConcurrentHashMap<String, VariableValueWrapper>> localVariableMap = new ThreadLocal<>();


    public static Task getCurTask() {
        TaskExecutorContext context = current.get();
        if (context == null) {
            log.error("Context is null, please create it at first.");
            return null;
        }
        return context.task;
    }

    public static Task getTask() {
        return getCurTask();
    }

    public static void setTaskBaseEntity(TaskBaseEntity taskBaseEntity) {
        if (taskBaseEntity instanceof Task) {
            setTask((Task) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskMethod) {
            setTaskMethod((TaskMethod) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskStep) {
            setTaskStep((TaskStep) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskAction) {
            setTaskAction((TaskAction) taskBaseEntity);
        } else if (taskBaseEntity instanceof TaskCommand) {
            setTaskCommand((TaskCommand) taskBaseEntity);
        }
    }

    public static void setTask(Task task) {
        TaskExecutorContext context = getContext();
        context.task = task;
        task.setContext(context);
        setVar("TASK_ID", task.getId());
    }

    public static void setTaskMethod(TaskMethod taskMethod) {
        TaskExecutorContext context = getContextWithTaskValid();
        context.setLastTaskMethod(taskMethod);
        curTaskMethodPool.set(taskMethod);
    }

    public static void setTaskStep(TaskStep taskStep) {
        TaskExecutorContext context = getContextWithTaskValid();
        context.setLastTaskStep(taskStep);
        curTaskStepPool.set(taskStep);
    }

    public static void setTaskAction(TaskAction taskAction) {
        TaskExecutorContext context = getContextWithTaskValid();
        context.setLastTaskAction(taskAction);
        curTaskActionPool.set(taskAction);
    }

    public static void setTaskCommand(TaskCommand taskCommand) {
        TaskExecutorContext context = getContextWithTaskValid();
        context.setLastTaskCommand(taskCommand);
        curTaskCommandPool.set(taskCommand);
    }

    public static TaskMethod getCurTaskMethod() {
        TaskExecutorContext context = getContextWithTaskValid();
        // 优先通过taskCommand反查其所在的TaskMethod
        TaskStep taskStep = getCurTaskStep();
        if (taskStep != null) {
            TaskMethod out = TaskBeanUtil.getTaskMethodByTaskStepId(context.task, taskStep.getId());
            if (out != null) {
                return out;
            }
        }
        return curTaskMethodPool.get();
    }

    public static TaskMethod getTaskMethod() {
        return getCurTaskMethod();
    }

    public static Long getMethodIdByTaskMethodId(Long taskMethodId) {
        TaskExecutorContext context = getContextWithTaskValid();
        TaskMethod taskMethod = TaskBeanUtil.getTaskMethodById(context.task, taskMethodId);
        if (taskMethod != null) {
            return taskMethod.getMethodId();
        }
        return null;
    }

    public static List<TaskMethod> getTaskMethodsByMethodId(Long methodId) {
        TaskExecutorContext context = getContextWithTaskValid();
        return context.task.getMethodList().stream().filter(mth -> {
            return mth.getMethodId().equals(methodId);
        }).collect(Collectors.toList());
    }

    public static TaskStep getCurTaskStep() {
        TaskExecutorContext context = getContextWithTaskValid();
        // 优先通过taskCommand反查其所在的TaskStep
        TaskAction taskAction = getCurTaskAction();
        if (taskAction != null) {
            TaskStep out = TaskBeanUtil.getTaskStepByTaskActionId(context.task, taskAction.getId());
            if (out != null) {
                return out;
            }
        }
        return curTaskStepPool.get();
    }

    public static TaskStep getTaskStep() {
        return getCurTaskStep();
    }


    public static TaskAction getCurTaskAction() {
        TaskExecutorContext context = getContextWithTaskValid();
        TaskCommand curTaskCommand = getCurTaskCommand();
        if (curTaskCommand != null) {
            TaskAction taskAction = TaskBeanUtil.getTaskActionByTaskCommandId(context.task, curTaskCommand.getId());
            if (taskAction != null) {
                return taskAction;
            }
        }
        return curTaskActionPool.get();
    }

    public static TaskAction getTaskAction() {
        return getCurTaskAction();
    }

    public static TaskCommand getCurTaskCommand() {
        return curTaskCommandPool.get();
    }

    public static TaskCommand getTaskCommand() {
        return getCurTaskCommand();
    }


    public static DeviceInstance getDeviceInstance() {
        TaskCommand cmd = getCurTaskCommand();
        if (cmd == null) {
            return null;
        }
        DeviceInstance ins = cmd.getDeviceInstance();
        if (ins == null) {
            ins = cmd.getCommand().getDeviceInstance();
        }
        return ins;
    }

    public static DeviceInstance getRealDeviceInstance() {
        TaskCommand cmd = getCurTaskCommand();
        if (cmd == null) {
            return null;
        }
        return cmd.getCommand().getRealDeviceInstance();
    }

    public static void setVar(String varName, Object value) {
        VariableHelper.setVar(varName, value);
    }

    public static void addVar(String varName, Object value) {
        VariableHelper.addVar(varName, value);
    }

    public void addLocalVarObj(String varName, Object value) {
        ConcurrentHashMap<String, VariableValueWrapper> varMap = this.localVariableMap.get();
        if (varMap == null) {
            varMap = new ConcurrentHashMap<String, VariableValueWrapper>();
            this.localVariableMap.set(varMap);
        }
        VariableHelper.addVar(varName, value, varMap);
    }


    public static Object getVar(String varName) {
        return VariableHelper.getVarAsObject(varName);
    }

    public Object getVarObj(String varName) {

        VariableValueWrapper v = this.variableMap.get(varName);
        if (v != null) {
            return v.getValue();
        }
        return null;
    }

    public Object getLocalVarObj(String varName) {
        ConcurrentHashMap<String, VariableValueWrapper> varMap = this.localVariableMap.get();
        if (varMap == null) {
            return null;
        }
        VariableValueWrapper v = varMap.get(varName);
        if (v != null) {
            return v.getValue();
        }
        return null;
    }

    public void removeAllLocals() {
        ConcurrentHashMap<String, VariableValueWrapper> varMap = this.localVariableMap.get();
        if (varMap != null) {
            this.localVariableMap.remove();
        }

        TaskMethod taskMethod = this.curTaskMethodPool.get();
        if (taskMethod != null) {
            this.curTaskMethodPool.remove();
        }
        TaskStep taskStep = this.curTaskStepPool.get();
        if (taskStep != null) {
            this.curTaskStepPool.remove();
        }
        TaskAction taskAction = this.curTaskActionPool.get();
        if (taskAction != null) {
            this.curTaskActionPool.remove();
        }
        TaskCommand taskCommand = this.curTaskCommandPool.get();
        if (taskCommand != null) {
            this.curTaskCommandPool.remove();
        }
    }

    public Object getLocalVarObjFirstItemToTail(String varName) {
        Object listObj = getLocalVarObj(varName);
        if (listObj != null && listObj instanceof List) {
            List varListValues = (List) listObj;
            Object obj = varListValues.remove(0);
            varListValues.add(obj);
            return obj;
        }
        return null;
    }

    /**
     * 获取变量值并转换为String
     *
     * @param varName
     * @return
     */
    public static String getVarAsString(String varName) {
        return VariableHelper.getVarAsString(varName);
    }

    public static void setTaskStepHook(TaskStep step, TaskStepHook hook) {
        TaskExecutorContext context = getContext();
        context.taskStepHookMap.put(step, hook);
    }

    public static TaskStepHook getTaskStepHook(TaskStep step) {
        TaskExecutorContext context = getContext();
        return context.taskStepHookMap.get(step);
    }

    public static TaskExecutorContext getContext() {
        TaskExecutorContext context = current.get();
        if (context == null) {
            context = new TaskExecutorContext();
            current.set(context);
        }
        return context;
    }

    /**
     * 获取上下文并校验是否在TASK运行环境下的调用
     *
     * @return
     */
    public static TaskExecutorContext getContextWithTaskValid() {
        TaskExecutorContext context = current.get();
        if (context == null || context.task == null) {
            throw new TaskRunningException("Context or context.task is null, please create it at first.");
        }
        return context;
    }

    /**
     * 从其他线程上下文COPY对象
     *
     * @param otherContext   其他线程上下文
     * @param taskBaseEntity
     * @return
     */
    public static TaskExecutorContext copy(TaskExecutorContext otherContext, TaskBaseEntity taskBaseEntity) {
        if (otherContext == null) {
            current.set(new TaskExecutorContext());
        } else {
            current.set(otherContext);
        }
        if (taskBaseEntity != null) {
            setTaskBaseEntity(taskBaseEntity);
        }
        return current.get();
    }

    public static TaskExecutorContext copy(TaskExecutorContext otherContext) {
        return copy(otherContext, null);
    }

    public static void clear() {
        TaskExecutorContext context = current.get();
        if (context == null) {
            return;
        }

        context.removeAllLocals();
        current.remove();
    }


}
