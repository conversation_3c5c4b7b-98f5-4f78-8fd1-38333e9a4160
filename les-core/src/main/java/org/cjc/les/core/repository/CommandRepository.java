package org.cjc.les.core.repository;

import org.cjc.les.core.domain.Command;
import org.cjc.les.core.domain.Device;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
 * 设备ORM接口层
 */
public interface CommandRepository extends JpaRepository<Command, Long>, JpaSpecificationExecutor<Command> {

    // Optional<Command> findCommandByDeviceIdAndJavaMethodNameAndParameterType(Long deviceId, String javaMethodName, String parameterType);
}