/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.init;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
// import com.sun.org.apache.xpath.internal.operations.Bool; // Removed - internal package not accessible in Java 21
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.core.helper.DeviceProxyWrapper;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.DeviceLayoutService;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.task.schedule.TaskThreadFactory;
import org.cjc.les.core.util.AlertUtil;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Log4j2
@Component
@RequiredArgsConstructor
public class ControlDeviceMonitor {

    private final DeviceLayoutService deviceLayoutService;

    private List<DeviceProxyWrapper> deviceProxyWrapperList = new ArrayList<>();

    private static ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1, new TaskThreadFactory("ControlDevice-Daemon"));


    @Transactional(readOnly =true)
    public void initDevices() {


        if (CollectionUtils.isEmpty(deviceProxyWrapperList)){
            loadDeviceInstances();
        }

        // 启动监控
        scheduler.scheduleAtFixedRate(new Runnable() {
            public void run() {
                ControlDeviceMonitor.this.startMonitor();
            }
        }, 1000,5000, TimeUnit.MILLISECONDS);
    }

    private void loadDeviceInstances() {
        DeviceLayoutService deviceLayoutService = SpringContextHolder.getBean(DeviceLayoutService.class);
        List<DeviceInstance> deviceInstanceList = deviceLayoutService.findAllDevicesInLatestLayout();
        for (DeviceInstance deviceInstance : deviceInstanceList) {

            if (StringUtils.equalsAny(deviceInstance.getType(),
                    DeviceTypeEnum.CONTROL.name(), DeviceTypeEnum.ROBOT.name(),
                    DeviceTypeEnum.SYS.name(), DeviceTypeEnum.PERIPHERAL.name(), DeviceTypeEnum.FRAME.name())) {
                DeviceProxyWrapper proxy = deviceInstance.getProxy();
                if (proxy==null){
                    continue;
                }
                boolean ret = proxy.init();
                if (!ret){
                    log.error("Initialize device:{} is failed.", deviceInstance.getName());
                }
                deviceInstance.getCommands().size();// 加载所有命令
                Optional<DeviceInstanceCmd> cmdOpt = Optional.empty();
                for (DeviceInstanceCmd cmd : deviceInstance.getCommands()){
                    if (StringUtils.equals(cmd.getType(),"STATUS")){
                        cmdOpt = Optional.of(cmd);
                        break;
                    }
                }
               // if (cmdOpt.isPresent()) {
                    deviceProxyWrapperList.add(proxy);
               // }
            }

        }
    }

    private void startMonitor(){


        for (DeviceProxyWrapper proxy : deviceProxyWrapperList) {
            if (!proxy.checkInitialized()){
                boolean ret = proxy.init();
                if (!ret){
                    log.error("Initialize device:{} is failed.", proxy.getDeviceInstance().getName());
                    continue;
                }
            }
            checkStatus(proxy.getDeviceInstance());
            /*
            if (proxy.getDeviceInstance().isInitialized()){
                try {

                    Object connRet = proxy.invokeMethod("checkConnection");
                    if (connRet instanceof Boolean){
                        Boolean bConnRet = (Boolean)connRet;
                        if (!bConnRet.booleanValue()){
                            proxy.getDeviceInstance().setInitialized(false);
                            proxy.sendDeviceInstanceInitChangeMsg();
                        }
                    }
                }catch(Exception e ){
                    // Do nothing.
                }
                continue;
            }
            boolean ret = proxy.init();
            if (!ret){
                log.error("Initialize device:{} is failed.", proxy.getDeviceInstance().getName());
            }
            */
        }
    }

    private void checkStatus(DeviceInstance deviceInstance) {

        Optional<DeviceInstanceCmd> cmdOpt = Optional.empty();
        for (DeviceInstanceCmd cmd : deviceInstance.getCommands()){
            if (StringUtils.equals(cmd.getType(),"STATUS")){
                cmdOpt = Optional.of(cmd);
                break;
            }
        }
        if (!cmdOpt.isPresent()){
            return;
        }
        try {
            String status = "SUCCESS";
            CommandReturn cmdRet = deviceInstance.getProxy().executeCommand(cmdOpt.get());
            if (ErrorCodeEnum.SUCCESS.equals(cmdRet.getErrorCode())) {
                Object rs = cmdRet.getResult();
                if (rs instanceof String){
                    try {
                        JSONObject obj = JSON.parseObject((String) rs);
                        status = obj.getString("status");
                    } catch(JSONException ex){
                        log.error("Could not parse rs: {}", rs, ex);
                    }
                }else if (rs instanceof Map){
                    Map rsMap = (Map)rs;
                    if (rsMap.get("status")!=null) {
                        status = rsMap.get("status").toString();
                        cmdRet.setErrorMsg(JSON.toJSONString(rsMap));
                    }
                }
                // 清除异常告警
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("DEVICE_INST_STATUS");
                alertLog.setAlertSourceId(deviceInstance.getId());
                alertLog.setAlertCode("DEVICE_INST_STATUS_ERROR");
                alertLog.setFixedBy("System");
                alertLog.setFixType(AlertLog.FixTypeEnum.AUTO);
                AlertUtil.clear(alertLog);

            } else {
                status = "FAILED";
                log.error("{} Execute STATUS checking return Failed: {}",deviceInstance.getName(), cmdRet.getErrorMsg());
                // 记录异常告警
                AlertLog alertLog = new AlertLog();
                alertLog.setAlertCategory("DEVICE_INST_STATUS");
                alertLog.setAlertSourceId(deviceInstance.getId());
                alertLog.setAlertLevel(AlertLog.AlertLevelEnum.FATAL.name());
                alertLog.setMessage(deviceInstance.getName()+":"+cmdRet.getErrorMsg());
                alertLog.setAlertCode("DEVICE_INST_STATUS_ERROR");
                alertLog.setAlertName(cmdRet.getErrorMsg());
                AlertUtil.createOrUpdate(alertLog);

            }

            deviceInstance.getProxy().sendDeviceInstanceInitChangeMsg(status, cmdRet.getErrorMsg());

        } catch (Throwable e) {
            log.error("{} Execute STATUS checking error:{}",deviceInstance.getName(), e.getMessage(), e);
        }
    }

    public void stopMonitor(){
        log.info("ControlDeviceMonitor stopMonitor BEGIN, control devices.size: {}", deviceProxyWrapperList.size());
        for (DeviceProxyWrapper proxy : deviceProxyWrapperList) {
            log.info("ControlDeviceMonitor stopMonitor proxy.getDeviceInstance: {}",
                    ( proxy.getDeviceInstance()!=null?proxy.getDeviceInstance().getName():""));
            if (!proxy.getDeviceInstance().isInitialized()){
                continue;
            }
             proxy.finalize();
            log.info("Finalize device:{}.", proxy.getDeviceInstance().getName());
        }
        log.info("ControlDeviceMonitor stopMonitor DONE, control devices.size: {}", deviceProxyWrapperList.size());
    }

}
