/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service;

import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.service.dto.*;
import org.cjc.les.core.vo.PositionRequestVo;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface PositionService {
    /**
     * 查找匹配的点位列表
     * @param deviceInstance
     * @param holdTaskId
     * @param status
     * @return
     */
    List<Position> findMatchedPositions(DeviceInstance deviceInstance, Long holdTaskId, Position.StatusEnum status);

    Optional<Position> findMatchedPosition(PositionRequestVo positionRequestVo);

    Optional<Position> findMatchedPositionByIDsAnAndHoldByTaskId(Set<Long> posIds, Long holdTaskId);

    void updatePositionStatus(Long positionId, Position.StatusEnum status, Long taskId, Long taskMethodId, Long taskStepId, Long taskActionId);

    default void updatePositionStatus(Long positionId, Position.StatusEnum status, Long taskId, Long taskMethodId) {
        updatePositionStatus(positionId, status, taskId, taskMethodId, null, null);
    }
    /**
     * 变更点位状态
     * @param positionStatusDto
     * @return
     */
    PositionStatusDto changePositionStatus(PositionStatusDto positionStatusDto);

    /**
     * 移动点位
     * @param dto
     * @return
     */
    PositionMoveDto move(PositionMoveDto dto);

    /**
     * 绑定进样板到给定的任务清单
     *
     * @param dto          点位与任务ID对应关系列表
     * @param boardPosName 进样板名称
     */
    void bindBoardToTasks(TaskToCheckSamplesReqDto dto, String boardPosName);

    /**
     * 查看设备点位状态，多个点位只取第一个点位状态返回
     * @param deviceInstanceId
     * @param posName
     * @return
     */
    String findPositionStatus(Long deviceInstanceId, String posName);

    /**
     * 检测齐套计算状态
     * @param position
     */
    void checkAllEntryDone(Position position);

    /**
     * 齐套解除
     * @param position
     */
    void checkAllExitReset(Position position);

    /**
     * 查询点位名称选项
     * @param criteria
     * @return
     */
    List<PositionSelectDto> queryPositionNamesForSelection(PositionSelectDto criteria );

    /**
     * 变更重定向后的设备实例下的所有点位设置
     * @param position
     * @param num
     * @param direction
     */
    void redirectAllPositionsOnDeviceInstance(Position position, int num, String direction);

    /**
     * 重置设备所有点位状态及ServiceCode
     * @param deviceInstanceId
     */
    void resetAllPositionsByDeviceInstanceId(Long deviceInstanceId);

    /**
     * 设置所有指示器点位状态为待确认
     */
    void resetAllIndicatorsToConfirm();

    boolean checkAllIndicatorConfirmed();

    /**
     * 设置所有指示器点位状态为初始状态
     */
    void resetAllIndicatorsToInit();
}
