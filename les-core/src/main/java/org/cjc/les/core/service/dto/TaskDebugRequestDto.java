/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class TaskDebugRequestDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 任务需运行的流程ID */
    private Long procedureId;

    private ProcedureDto procedure;


    /** 任务需选择的样本ID */
    private Long sampleId;

    private SampleDto sample;

    /**
     * 当前请求的任务ID
     */
    private Long taskId;

    /**
     * 当前请求任务方法ID
     */
    private Long taskMethodId;

    /**
     * 当前请求任务步骤ID
     */
    private Long taskStepId;

    /**
     * 当前请求任务动作ID
     */
    private Long taskActionId;

    /**
     * 当前请求任务指令ID
     */
    private Long taskCommandId;

    /** 准备阶段完成后（PUSHED）运行的任务ID */
    private String taskNumber;


    /** 状态, DRAFT,READY,PUSHED,CANCELLED */
    private String status;

    /** 状态的消息详情 */
    private String message;

    /**
     * CONTINUE, MONO, NONE
     */
    private String debugMode = "NONE";

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}