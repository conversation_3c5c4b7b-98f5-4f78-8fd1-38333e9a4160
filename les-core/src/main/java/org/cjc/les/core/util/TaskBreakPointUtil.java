/*
 *  Copyright 2024-2025 Wuhan Annis Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.util;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskBaseEntity;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskGlobalConfig;
import org.cjc.les.vo.CommandReturn;

@Log4j2
public class TaskBreakPointUtil {

    /**
     * 检查断点设置
     *
     * @param taskBaseEntity 任务指令对象
     * @return
     */
    public static CommandReturn checkBreakPoint(TaskBaseEntity taskBaseEntity) {
        CommandReturn ret = new CommandReturn();

        ExecutorConfig executorConfig = TaskExecutorContext.getContext().getExecutorConfig();
        // 是否启用任务, 只在非调试模式下有效
        if (StringUtils.equalsAny(executorConfig.getDebugMode().name(), ExecutorConfig.DebugModeEnum.NONE.name())) {
            while (!TaskGlobalConfig.getInstance().getInfoDto().isStarted()) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    log.warn("checkBreakPoint waiting InterruptedException occurs. error={}", e.getMessage());

                    ret.setStatus(RunStatusEnum.CANCELLED);
                    ret.setErrorCode(ErrorCodeEnum.ERROR);
                    return ret;
                }
            }
        }

        if (StringUtils.equalsAnyIgnoreCase(executorConfig.getDebugMode().name(), "NONE")) {
            return ret;
        } else {
            while (StringUtils.equalsAnyIgnoreCase(executorConfig.getDebugMode().name(), "CONTINUE","MONO")) {
                synchronized (executorConfig.breakPointMuteLock) {
                    if (ExecutorConfig.DebugModeEnum.MONO.equals(executorConfig.getDebugMode())
                        && !StringUtils.equalsAny(taskBaseEntity.getStatus(), RunStatusEnum.PAUSED.name(), RunStatusEnum.BREAK.name())){
                        break;
                    }
                    if (ExecutorConfig.DebugModeEnum.CONTINUE.equals(executorConfig.getDebugMode())
                            && !StringUtils.equalsAny(taskBaseEntity.getStatus(), RunStatusEnum.BREAK.name())){
                        break;
                    }

                }
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    break;
                }
            }
        }

        return ret;
    }
}
