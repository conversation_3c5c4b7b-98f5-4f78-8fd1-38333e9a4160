/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-17
**/
@Entity
@Data
@ToString(callSuper = true, onlyExplicitlyIncluded = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Table(name="les_variable")
@SQLDelete(sql = "update les_variable set delete_flag = 'Y', update_time=now() where id = ?")
@Where(clause = "delete_flag = 'N'")
public class Variable  extends LesConfigBaseEntity {

    @Column(name = "name",unique = true)
    @Schema(description = "变量名称")
    private String name;

    @Column(name = "view_name")
    @Schema(description = "变量显示提示信息")
    private String viewName;

    @Column(name = "description")
    @Schema(description = "变量描述")
    private String description;

    @Column(name = "type")
    @Schema(description = "变量类型: NUMBER,STRING,ARRAY")
    private String type;

    private String defaultValue;

    @Column(name = "scope")
    @Schema(description = "变量访问范围: USER,SYS")
    private String scope;

    /**
     * MANUAL, STATION_CONFIG, SCRIPT, SYS
     */
    private String source;

    /**
     *  变量值可被用户设置,Y/N
     */
    private String valueSetByUser;

    public void copy(Variable source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}