/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.SysVariableNameEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.helper.CommandExecuteHook;
import org.cjc.les.core.repository.*;
import org.cjc.les.core.service.DeviceInstanceCmdService;
import org.cjc.les.core.service.MaterialService;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.ProcedureService;
import org.cjc.les.core.service.dto.*;
import org.cjc.les.core.service.mapstruct.DeviceInstanceCmdMapper;
import org.cjc.les.core.service.mapstruct.PositionSelectionMapper;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.ActionScheduler;
import org.cjc.les.core.vo.ConfigItemVo;
import org.cjc.les.core.vo.PositionRequestVo;
import org.cjc.les.vo.CommandReturn;
import org.cjc.utils.SpringContextHolder;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
@RequiredArgsConstructor
public class PositionServiceImpl implements PositionService {

    private final ActionScheduler actionScheduler;

    private final ProcedureService procedureService;

    private final DeviceInstanceRepository deviceInstanceRepository;
    private final PositionRepository positionRepository;
    private final ActionRepository actionRepository;
    private final TaskRepository taskRepository;

    private final PositionSelectionMapper positionSelectionMapper;

    @Override
    public List<Position> findMatchedPositions(DeviceInstance deviceInstance, Long holdTaskId, Position.StatusEnum status) {
        return positionRepository.findMatchedPositions(deviceInstance, holdTaskId,status);
    }

    @Override
    public Optional<Position> findMatchedPositionByIDsAnAndHoldByTaskId(Set<Long> posIds, Long holdTaskId) {
        return positionRepository.findMatchedPositionByIDsAnAndHoldByTaskId(posIds, holdTaskId);
    }

    @Override
    public Optional<Position> findMatchedPosition(PositionRequestVo positionRequestVo) {
        String posStatus = positionRequestVo.getStatus();
        if (StringUtils.isEmpty(posStatus)) {
            return findMatchedPositionWithoutStatus(positionRequestVo);
        }
        String[] posStatusArr = posStatus.split("@");
        if (posStatusArr.length == 2) {
            posStatus = posStatusArr[0];
            String[] bindPosArr = posStatusArr[1].split("\\$");
            PositionRequestVo bindPosReq = new PositionRequestVo();
            bindPosReq.setTaskId(positionRequestVo.getTaskId());
            bindPosReq.setDeviceInstanceId(Long.parseLong(bindPosArr[0]));
            bindPosReq.setName(bindPosArr[2]);
            bindPosReq.setStatus(bindPosArr[3]);
            Optional<Position> bindPosOpt = findMatchedPositionInner(bindPosReq);
            if (!bindPosOpt.isPresent()) {
                return Optional.empty();
            }
            positionRequestVo.setStatus(posStatus);
            positionRequestVo.setBoardNodeId(bindPosOpt.get().getBoardNodeId());
        }
        return findMatchedPositionInner(positionRequestVo);
    }

    private Optional<Position> findMatchedPositionInner(PositionRequestVo positionRequestVo) {
        List<Position> retList = null;
        DeviceInstance instance = new DeviceInstance();
        instance.setId(positionRequestVo.getDeviceInstanceId());
        if (StringUtils.equalsIgnoreCase(Position.StatusEnum.IDLE.name(), positionRequestVo.getStatus())) {
            if (StringUtils.isNotEmpty(positionRequestVo.getBoardNodeId())) {
                retList = positionRepository.findMatchedPositionsIdle(instance, positionRequestVo.getName(), positionRequestVo.getBoardNodeId());
            } else {
                retList = positionRepository.findMatchedPositionsIdle(instance, positionRequestVo.getName());
            }
        } else if (StringUtils.equalsIgnoreCase(Position.StatusEnum.READY.name(), positionRequestVo.getStatus())) {
            if (StringUtils.isNotEmpty(positionRequestVo.getBoardNodeId())) {
                retList = positionRepository.findMatchedPositionsReady(instance, positionRequestVo.getName(), positionRequestVo.getBoardNodeId());
            } else {
                retList = positionRepository.findMatchedPositionsReady(instance, positionRequestVo.getName());
            }
        } else {
            // 优先通过METHOD查询点位
            if (StringUtils.isNotEmpty(positionRequestVo.getBoardNodeId())) {
                retList = positionRepository.findMatchedPositionsInMethod(instance, positionRequestVo.getName(),
                        positionRequestVo.getTaskMethodId(), Position.StatusEnum.valueOf(positionRequestVo.getStatus()), positionRequestVo.getBoardNodeId());
            } else {
                retList = positionRepository.findMatchedPositionsInMethod(instance, positionRequestVo.getName(),
                        positionRequestVo.getTaskMethodId(), Position.StatusEnum.valueOf(positionRequestVo.getStatus()));
            }
            // 若找不到再次通过TASK查找
            if (CollectionUtils.isEmpty(retList)) {
                if (StringUtils.isNotEmpty(positionRequestVo.getBoardNodeId())) {
                    retList = positionRepository.findMatchedPositionsInTask(instance, positionRequestVo.getName(),
                            positionRequestVo.getTaskId(), Position.StatusEnum.valueOf(positionRequestVo.getStatus()), positionRequestVo.getBoardNodeId());
                } else {
                    retList = positionRepository.findMatchedPositionsInTask(instance, positionRequestVo.getName(),
                            positionRequestVo.getTaskId(), Position.StatusEnum.valueOf(positionRequestVo.getStatus()));
                }
            }
        }

        if (StringUtils.isNotEmpty(positionRequestVo.getOrderBy())){
            sortPositionList(retList, positionRequestVo.getOrderBy());
        }
        if (CollectionUtils.isEmpty(retList)) {
            return Optional.empty();
        } else {
            return Optional.of(retList.get(0));
        }
    }

    private void sortPositionList(List<Position> positionList, String orderBy) {
        if (CollectionUtils.isEmpty(positionList)){
            return;
        }
        String[] orderByArr = orderBy.split(",");
        if (StringUtils.equalsAny(orderByArr[0], "serviceCode")) {
            positionList.sort((a, b) -> {
                String serviceCodeA = a.getServiceCode();
                String serviceCodeB = b.getServiceCode();
                String[] serviceCodeAArr = serviceCodeA.split("-");
                String[] serviceCodeBArr = serviceCodeB.split("-");
                String cmpStrA = serviceCodeAArr[1].substring(1);
                String cmpStrB = serviceCodeBArr[1].substring(1);
                return cmpStrA.compareTo(cmpStrB);
            });
        }
    }


    private Optional<Position> findMatchedPositionWithoutStatus(PositionRequestVo positionRequestVo) {
        return  positionRepository.findFirstPosition(positionRequestVo.getDeviceInstanceId(),positionRequestVo.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePositionStatus(Long positionId, Position.StatusEnum status, Long taskId, Long taskMethodId, Long taskStepId, Long taskActionId) {
        positionRepository.updatePositionStatus(positionId, status, taskId, taskMethodId, taskStepId, taskActionId);
        log.info("updatePositionStatus:id={}, status={}, taskId={}, taskMethodId={}, taskStepId={},  taskActionId={}",positionId, status, taskId, taskMethodId,taskStepId,  taskActionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PositionStatusDto changePositionStatus(PositionStatusDto positionStatusDto) {

        if (StringUtils.equalsAny(positionStatusDto.getChangeCmd(), "reset")) {
            if (positionStatusDto.isResetAssociations()) {
                positionRepository.resetPositionStatusWithAssociations(positionStatusDto.getName());
                log.info("positionRepository.resetPositionStatusWithAssociations done, updatePositionStatus:name={}",positionStatusDto.getName());
                MaterialService materialService = SpringContextHolder.getBean(MaterialService.class);
                materialService.resetRemainValueByPosId(positionStatusDto.getId());
                List<Position> allPos = positionRepository.findAllByName(positionStatusDto.getName());
                for (Position pos : allPos) {
                    sendPositionStateChangeMsg(pos);
                }
            } else {
                positionRepository.resetPositionStatus(positionStatusDto.getId());
                log.info("positionRepository.resetPositionStatus done, updatePositionStatus:id={}",positionStatusDto.getId());
                Optional<Position> positionOpt = positionRepository.findById(positionStatusDto.getId());
                if (positionOpt.isPresent()) {
                    sendPositionStateChangeMsg(positionOpt.get());
                }
            }
            return positionStatusDto;
        }

        positionRepository.updatePositionStatus(positionStatusDto.getId(), positionStatusDto.getStatus(), null, null, null, null);
        log.info("changePositionStatus, updatePositionStatus:id={}, status={}",positionStatusDto.getId(), positionStatusDto.getStatus());
        Optional<Position> positionOpt = positionRepository.findById(positionStatusDto.getId());
        if (positionOpt.isPresent()) {
            sendPositionStateChangeMsg(positionOpt.get());
        }
        return positionStatusDto;
    }

    @Override
    public PositionMoveDto move(PositionMoveDto dto) {
        Optional<Position> srcPosOpt = positionRepository.findById(dto.getSourcePositionId());
        Position srcPos = srcPosOpt.get();
        Optional<Position> destPosOpt = positionRepository.findById(dto.getTargetPositionId());
        Position destPos = destPosOpt.get();

        Optional<DeviceInstance> deviceInstanceOpt = deviceInstanceRepository.findById(dto.getRobotDevInstanceId());
        DeviceInstance deviceInstance = deviceInstanceOpt.get();

        TaskAction taskAction = new TaskAction();
        // 构造临时任务动作对象, 通过设备实例所在工作站的动作(动作名称: 手动取放动作)
        Optional<Action> actOpt = actionRepository.findPickPlaceActionByDevInstId(dto.getRobotDevInstanceId(), "手动取放动作");
        if (!actOpt.isPresent()) {
            dto.setStatus(ErrorCodeEnum.ERROR.name());
            dto.setMessage("Could not found ACTION by this deviceInstanceId.");
            return dto;
        }

        Task task = new Task();
        task.setId(IdUtil.getSnowflake(0,0).nextId());
        task.setName("手动取放任务");
        task.setScheduleEntryTime(System.currentTimeMillis());
        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);

        Action action = actOpt.get();
        StepAction stpAct = new StepAction();
        stpAct.setAction(action);
        taskAction.copy(stpAct, true);
        fillTaskActionTempFields(taskAction, stpAct, srcPos, destPos);

        ExecutorConfig config = new ExecutorConfig();
        config.setDebugMode(ExecutorConfig.DebugModeEnum.CONTINUE);
        TaskExecutorContext context = TaskExecutorContext.getContext();
        context.setExecutorConfig(config);


        actionScheduler.submit(taskAction, config);

        return dto;
    }

    private void fillTaskActionTempFields(TaskAction taskAction, StepAction stepAction, Position srcPos, Position destPos) {
        // 设置默认ID及状态
        long taskId = TaskExecutorContext.getTask().getId();
        long taskActId = IdUtil.getSnowflake(0,0).nextId();
        taskAction.setId(taskActId);
        taskAction.setStatus(RunStatusEnum.READY.name());
        for (TaskCommand cmd : taskAction.getCommands()) {
            cmd.setId(IdUtil.getSnowflake(0,0).nextId());
            cmd.setTaskId(taskId);
            cmd.setTaskMethodId(taskId);
            cmd.setTaskStepId(taskId);
            cmd.setStatus(RunStatusEnum.READY.name());
        }
        // 设置环境变量
        TaskExecutorContext.addVar(SysVariableNameEnum.PRESET_POSITIONS.name(), srcPos);
        TaskExecutorContext.addVar(SysVariableNameEnum.PRESET_POSITIONS.name(), destPos);

    }

    private void sendPositionStateChangeMsg(Position position) {

        SocketMsg msg = new SocketMsg(JSON.toJSONString(position), MsgType.INFO);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"positionStateChange"}, true);
        } catch (IOException e) {
            log.error("sendPositionStateChangeMsg error, message={}", e.getMessage(), e);
        }

    }

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    @Override
    public void bindBoardToTasks(TaskToCheckSamplesReqDto dto, String boardPosName) {
        Optional<Position> boardPosOpt = positionRepository.findPositionByName(boardPosName);
        if (!boardPosOpt.isPresent()) {
            return;
        }
        Position boardPos = boardPosOpt.get();

        List<Position> dotPosList = positionRepository.findPositionsByBoardNodeId(boardPos.getBoardNodeId());

        updatePositionStatus(boardPos.getId(), Position.StatusEnum.READY, dto.getTaskId(), null, null, null);
        sendPositionStateChangeMsg(boardPos);
        for (TaskToCheckSamplesReqDto.SampleItem item : dto.getSampleList()) {
            Integer posIndex = item.getPosIndex();
            if (posIndex.intValue() < dotPosList.size()) {
                Position pos = dotPosList.get(posIndex);
                pos.setStatus(Position.StatusEnum.HOLD);
                updatePositionStatus(pos.getId(), Position.StatusEnum.HOLD, item.getTaskId(), null, null, null);
                Optional<Task> taskOpt = taskRepository.findById(item.getTaskId());
                if (taskOpt.isPresent()) {
                    Task task = taskOpt.get();
                    pos.setHoldByTaskId(task.getId());
                    pos.setHoldByTaskName(task.getTaskName());
                    pos.setHoldByTaskNumber(task.getTaskNumber());
                }
                sendPositionStateChangeMsg(pos);
            }
        }

    }

    @Override
    public String findPositionStatus(Long deviceInstanceId, String posName) {
        return positionRepository.findPositionStatus(deviceInstanceId, posName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void checkAllEntryDone(Position position) {
        List<Position> positions = positionRepository.findAllPositionsByBoardNodeId(position.getBoardNodeId());

        Optional<Position> boardPosOpt = positions.stream().filter(pos->{
            return "BOARD".equalsIgnoreCase(pos.getType());
        }).findFirst();
        List<Position> defHoldPositions = positions.stream().filter(pos->{
            return "DEFAULT".equalsIgnoreCase(pos.getType())
                    && Position.StatusEnum.HOLD.equals( pos.getStatus() );
        }).collect(Collectors.toList());
        Task task = TaskExecutorContext.getTask();
        List<Task> batchTaskList = taskRepository.findByBatchId(task.getBatchId());
        if (batchTaskList.size()-1 == defHoldPositions.size()){
            if (batchTaskList.stream().allMatch(task1 -> {
                if (task1.getId().equals(task.getId())){
                    return true;
                }
                return defHoldPositions.stream().anyMatch(position1 -> {
                    return position1.getHoldByTaskId()!=null && position1.getHoldByTaskId().equals(task1.getId());
                });
            })) {
                Position pos = boardPosOpt.get();
                pos.setStatus(Position.StatusEnum.HOLD);
                updatePositionStatus(pos.getId(), Position.StatusEnum.HOLD, task.getId(), null);

                pos.setHoldByTaskId(task.getId());
                pos.setHoldByTaskName(task.getTaskName());
                pos.setHoldByTaskNumber(task.getTaskNumber());

                sendPositionStateChangeMsg(pos);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void checkAllExitReset(Position position) {
        List<Position> positions = positionRepository.findAllPositionsByBoardNodeId(position.getBoardNodeId());

        Optional<Position> boardPosOpt = positions.stream().filter(pos->{
            return "BOARD".equalsIgnoreCase(pos.getType());
        }).findFirst();
        List<Position> defHoldPositions = positions.stream().filter(pos->{
            return "DEFAULT".equalsIgnoreCase(pos.getType())
                    && Position.StatusEnum.IDLE.equals( pos.getStatus() );
        }).collect(Collectors.toList());
        Task task = TaskExecutorContext.getTask();
        List<Task> batchTaskList = taskRepository.findByBatchId(task.getBatchId());
        if (batchTaskList.size() <= defHoldPositions.size()){
            if (batchTaskList.stream().allMatch(task1 -> {
                if (task1.getId().equals(task.getId())){
                    return true;
                }
                return defHoldPositions.stream().anyMatch(position1 -> {
                    return position1.getHoldByTaskId()!=null && position1.getHoldByTaskId().equals(task1.getId());
                });
            })) {
                Position pos = boardPosOpt.get();
                pos.setStatus(Position.StatusEnum.IDLE);
                updatePositionStatus(pos.getId(), Position.StatusEnum.IDLE, task.getId(), null);

                pos.setHoldByTaskId(task.getId());
                pos.setHoldByTaskName(task.getTaskName());
                pos.setHoldByTaskNumber(task.getTaskNumber());

                sendPositionStateChangeMsg(pos);
            }
        }
    }

    @Override
    public List<PositionSelectDto> queryPositionNamesForSelection(PositionSelectDto criteria) {
        List<Map> list = positionRepository.findPositionNamesForSelection();
        List<PositionSelectDto> dtoList = new ArrayList<>();
        for (Map item : list){
            PositionSelectDto dto = new PositionSelectDto();
            dto.setName((String)item.get("name"));
            dto.setType((String)item.get("type"));
            dtoList.add(dto);
        }
        return dtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void redirectAllPositionsOnDeviceInstance(Position position, int num, String direction) {
        List<Position> list = positionRepository.findAllPositionsByDeviceInstanceId(position.getDeviceInstance().getId());
        for (Position pos : list) {
            String serviceCode = pos.getServiceCode();
            if (StringUtils.isEmpty(serviceCode)){
                continue;
            }
            String part1 = serviceCode.substring(0, serviceCode.length()-2);
            String part2 = serviceCode.substring(serviceCode.length()-2);
            int curNum = Integer.parseInt(part2);
            int adjNum = direction.equalsIgnoreCase("D")?curNum-num:curNum+num;
            if (adjNum<=0){
                adjNum = 12 + adjNum;
            }else if (adjNum>12){
                adjNum = adjNum - 12;
            }
            String adjStr = String.format("%02d", adjNum);
            String adjServiceCode = part1 + adjStr;
            pos.setServiceCode(adjServiceCode);
            positionRepository.save(pos);
            if (position.getId().equals(pos.getId())){
                position.setServiceCode(adjServiceCode);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void resetAllPositionsByDeviceInstanceId(Long deviceInstanceId) {
        List<Position> list = positionRepository.findAllPositionsByDeviceInstanceId(deviceInstanceId);
        for (Position pos : list) {
            String serviceCode = pos.getServiceCode();
            if (StringUtils.isEmpty(serviceCode)) {
                continue;
            }
            String[] serviceCodeArr = serviceCode.split("-");
            if (serviceCodeArr.length != 2) {
                continue;
            }

            String part1 = serviceCode.substring(0, serviceCode.length() - 2);
            String part2 = serviceCode.substring(1, 3);

            String adjServiceCode = part1 + part2;
            pos.setServiceCode(adjServiceCode);
            if (!Position.StatusEnum.NONE.equals(pos.getStatus())) {
                pos.setStatus(Position.StatusEnum.READY);
            }
            positionRepository.save(pos);
            log.info("resetAllPositionsByDeviceInstanceId updatePositionStatus, posId:{}, posStatus:{}", pos.getId(), pos.getStatus());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetAllIndicatorsToConfirm() {
        positionRepository.resetAllIndicatorsToConfirm();
        log.info("resetAllIndicatorsToConfirm updatePositionStatus");
        List<Position> resetPoss = positionRepository.findAllByStatus(Position.StatusEnum.TO_CONFIRM);
        for (Position pos : resetPoss) {
            sendPositionStateChangeMsg(pos);
        }
    }

    @Override
    public boolean checkAllIndicatorConfirmed() {
        List<Position> resetPoss = positionRepository.findAllByStatus(Position.StatusEnum.TO_CONFIRM);
        return CollectionUtils.isEmpty(resetPoss);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetAllIndicatorsToInit() {
        positionRepository.resetAllIndicatorsToInit();
        log.info("resetAllIndicatorsToInit updatePositionStatus");
    }

    public static void main(String[] args) {
        String serviceCode = "D03-D12";
        String direction = "R";
        int num = 2;
        String part1 = serviceCode.substring(0, serviceCode.length()-2);
        String part2 = serviceCode.substring(serviceCode.length()-2);
        int curNum = Integer.parseInt(part2);
        int adjNum = direction.equalsIgnoreCase("D")?curNum-num:curNum+num;
        if (adjNum<=0){
            adjNum = 12 + adjNum;
        }else if (adjNum>12){
            adjNum = adjNum - 12;
        }
        String adjStr = String.format("%02d", adjNum);
        String adjServiceCode = part1 + adjStr;
        System.out.println("adjServiceCode=" + adjServiceCode);

        part1 = serviceCode.substring(0, serviceCode.length()-2);
        part2 = serviceCode.substring(1,3);

        adjServiceCode = part1 + part2;
        System.out.println("Reset adjServiceCode=" + adjServiceCode);

    }
}
