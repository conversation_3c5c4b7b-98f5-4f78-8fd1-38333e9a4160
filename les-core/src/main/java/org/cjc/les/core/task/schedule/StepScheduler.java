/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.task.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.DeviceTypeEnum;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.MethodStep;
import org.cjc.les.core.domain.Station;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.service.TaskStepService;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.execute.TaskStepExecutor;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.utils.SpringContextHolder;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 步骤调度器
 */
@Log4j2
@RequiredArgsConstructor
@Component
public class StepScheduler {

    private final int POOL_SIZE = 64;

    private static volatile Object lockObj = new Object();

    private static ExecutorService daemonService =  new ThreadPoolExecutor(1, 1,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(),
            new TaskThreadFactory("StepDaemon"));

    private LinkedBlockingQueue<TaskStep> runningQueue = new LinkedBlockingQueue<>();

    private LinkedBlockingQueue<TaskStep> suspendQueue = new LinkedBlockingQueue<>();

    public LinkedBlockingQueue<TaskStep> getRunningQueue() {
        return runningQueue;
    }

    public LinkedBlockingQueue<TaskStep> getSuspendQueue() {
        return suspendQueue;
    }

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        executorService = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new TaskThreadFactory("step"));
    }

    public long calculateTaskStepDurationInQueue(TaskStep taskStep) {
        if (!runningQueue.contains(taskStep)) {
            return 0L;
        }
        Optional<TaskStep> stepInQueOpt = runningQueue.stream().filter(s -> {
            return s.getId().equals(taskStep.getId());
        }).findFirst();
        if (stepInQueOpt.isPresent()) {
            TaskStep stepInQue = stepInQueOpt.get();
            if (stepInQue.getExecutedStart() == null) {
                return stepInQue.getEvaluateExecutingDuration();
            }
            return System.currentTimeMillis() - (stepInQue.getScheduleEntryTime() + stepInQue.getExecutedStart());
        } else {
            return 0L;
        }
    }

    public long calculateTaskStepStartTimeInQueue(TaskStep taskStep) {
        if (taskStep.getExecutedStart() != null) {
            return taskStep.getExecutedStart().longValue();
        }

        if (runningQueue.contains(taskStep)) {
            Optional<TaskStep> stepInQueOpt = runningQueue.stream().filter(s -> {
                return s.getId().equals(taskStep.getId());
            }).findFirst();
            if (stepInQueOpt.isPresent()) {
                Long executedStart = stepInQueOpt.get().getExecutedStart();
                if (executedStart == null) {
                    return 0L;
                } else {
                    return stepInQueOpt.get().getExecutedStart();
                }
            }
            return 0L;
        }
        // 至计算在等待队列中的步骤起始时间偏移
        if (!suspendQueue.contains(taskStep)) {
            return 0L;
        }

        Optional<TaskStep> matchedInRunningQueue = getBlockerTaskStepInRunningQueue(taskStep);

        long startTime = System.currentTimeMillis();
        // 计算在执行队列中剩余时时长
        if (matchedInRunningQueue.isPresent()) {
            TaskStep matchedStep = matchedInRunningQueue.get();
            if (matchedStep.getExecutedStart() != null) {
                startTime += (matchedStep.getScheduleEntryTime() + matchedStep.getExecutedStart().longValue()
                        + getStepExecutingDuringTime(matchedStep) - startTime);

            }
        }

        Optional<TaskStep> stepInSuspendQueOpt = Optional.empty();
        long duration = 0L;
        for (TaskStep suspendStep : suspendQueue) {
            duration += getStepExecutingDuringTime(suspendStep);
            if (suspendStep.equals(taskStep)) {
                stepInSuspendQueOpt = Optional.of(suspendStep);
                break;
            }
        }

        return startTime + duration - ((stepInSuspendQueOpt.isPresent()) ? stepInSuspendQueOpt.get().getScheduleEntryTime() : 0L);
    }

    private long getStepExecutingDuringTime(TaskStep taskStep) {
        return (taskStep.getExecutedDuration() != null ? taskStep.getExecutedDuration() : taskStep.getEvaluateExecutingDuration());
    }

    public List<TaskStep> getExistedSameSteps(TaskStep theTaskStep) {
        List<TaskStep> stepList = new ArrayList<>();
        for (TaskStep runningStep : runningQueue) {
            if (runningStep.getStepId().equals(theTaskStep.getStepId())) {
                stepList.add(runningStep);
            }
        }
        for (TaskStep suspendStep : suspendQueue) {
            if (suspendStep.getStepId().equals(theTaskStep.getStepId())) {
                stepList.add(suspendStep);
            }
        }

        return stepList;
    }

    public boolean executeImmediately(TaskStep taskStep, ExecutorConfig config, boolean replaceExistedSameStep) {

        List<TaskStep> existedSteps = getExistedSameSteps(taskStep);
        if (CollectionUtils.isNotEmpty(existedSteps)) {
            if (replaceExistedSameStep) {
                for (TaskStep step : existedSteps) {
                    cancel(step);
                }
            } else {
                StringBuffer strBuf = new StringBuffer();
                existedSteps.stream().forEach(s -> {
                    strBuf.append(",").append(s.getId());
                });
                throw new TaskRunningException("Existed another steps:" + strBuf);
            }
        }

        execute(taskStep, config);
        return true;
    }

    /**
     * 提交任务步骤到执行队列，并等待完成
     *
     * @param taskStep
     */
    public void execute(TaskStep taskStep, ExecutorConfig config) {

        if (taskStep.getContext() == null) {
            taskStep.setContext(TaskExecutorContext.getContext());
        }

        if (!StringUtils.equalsAny(taskStep.getStatus(), RunStatusEnum.READY.name())) {
            // 第一次执行该步骤，应该为READY状态
        }
        taskStep.setExecutorConfig(config);
        checkRunningConditionInAddNew(taskStep, config);

        do {
            if (StringUtils.equalsAny(taskStep.getStatus(),
                    RunStatusEnum.SUCCESS.name(),
                    RunStatusEnum.FAILED.name(),
                    RunStatusEnum.CANCELLED.name(),
                    RunStatusEnum.SKIPPED.name())) {
                // 只有等待完成才继续该方法的下一个步骤
                runningQueue.remove(taskStep);
                break;
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
                break;
            }
        } while (true);
    }

    public void start() {
        daemonService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    checkRunningConditionInSuspendQue();
                    log.info("STEP daemonService exit normally.");
                }catch (Throwable e){
                    log.error(" daemonService error: {}", e.getMessage(), e);
                }
            }
        });
    }

    public void cancel(TaskStep taskStep) {
        synchronized (lockObj) {

            TaskStep stepInQue = taskStep;
            for (TaskStep step : runningQueue){
                if (step.getId().equals(taskStep.getId())){
                    stepInQue = step;
                }
            }
            if (!StringUtils.equalsAny(stepInQue.getStatus(),
                    RunStatusEnum.SUCCESS.name(),
                    RunStatusEnum.FAILED.name(),
                    RunStatusEnum.CANCELLED.name())) {
                stepInQue.setStatus(RunStatusEnum.CANCELLED.name());

                ActionScheduler actionScheduler = SpringContextHolder.getBean(ActionScheduler.class);
                for (TaskAction taskAction : stepInQue.getActions()) {
                    actionScheduler.cancel(taskAction);
                }
            }

            suspendQueue.remove(stepInQue);

            runningQueue.remove(stepInQue);

            if (stepInQue.getThread() != null) {
                stepInQue.getThread().interrupt();
            }
        }
    }

    private Optional<TaskStep> getBlockerTaskStepInRunningQueue(TaskStep suspendStep) {
        for (TaskStep runningStep : runningQueue) {
            if (hasSameStationRunning(suspendStep, runningStep, false)) {
                return Optional.of(runningStep);
            }
        }
        return Optional.empty();
    }

    private boolean checkRunningConditionInAddNew(TaskStep taskStep, ExecutorConfig config) {
        synchronized (lockObj) {
            for (TaskStep runningStep : runningQueue) {
                if (hasSameStationRunning(taskStep, runningStep, false)) {
                    suspendQueue.offer(taskStep);
                    taskStep.setStatus(RunStatusEnum.SUSPEND.name());
                    if (config.isRecordLog()) {
                        TaskExecuteLogUtil.logStep(taskStep);
                    }
                    return false;
                }
            }
            for (TaskStep suspendStep : suspendQueue) {
                if (hasSameStationRunning(taskStep, suspendStep, false)) {
                    suspendQueue.offer(taskStep);
                    taskStep.setStatus(RunStatusEnum.SUSPEND.name());
                    if (config.isRecordLog()) {
                        TaskExecuteLogUtil.logStep(taskStep);
                    }
                    return false;
                }
            }
            runningQueue.offer(taskStep);
            traceRunningStepQue("Submit InAddNew", taskStep);
            submit(taskStep, config);
            return true;
        }
    }

    private void checkRunningConditionInSuspendQue() {
        PredicateValidator validator = SpringContextHolder.getBean(PredicateValidator.class);
        while (true) {
            synchronized (lockObj) {
                Iterator<TaskStep> itrSuspendSteps = suspendQueue.iterator();
                while (itrSuspendSteps.hasNext()) {
                    TaskStep taskStep = itrSuspendSteps.next();
                    // 匹配在运行队列中的继续等待
                    boolean matchedInRunningQue = false;
                    for (TaskStep runningStep : runningQueue) {
                        if (hasSameStationRunning(taskStep, runningStep, false)) {
                            matchedInRunningQue = true;
                            break;
                        }
                    }
                    if (matchedInRunningQue) {
                        continue;
                    }

                    // 匹配在等待队列中该步骤之前的继续等待
                    boolean matchedInSuspendQue = false;
                    for (TaskStep suspendStep : suspendQueue) {
                        if (taskStep.equals(suspendStep)) {
                            break;
                        }
                        if (hasSameStationRunning(taskStep, suspendStep, false)) {
                            matchedInSuspendQue = true;
                            break;
                        }
                    }
                    if (matchedInSuspendQue) {
                        continue;
                    }

                    // 校验规则在执行队列中运行，因其可能存在锁等待
                    //if (!validator.validate(taskStep)) {
                    //    continue;
                    //}

                    itrSuspendSteps.remove();

                    TaskExecutorContext.copy(taskStep.getContext(), taskStep);

                    runningQueue.offer(taskStep);
                    traceRunningStepQue("Submit InSuspend", taskStep);
                    submit(taskStep, taskStep.getExecutorConfig());
                }
            }

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * 校验是否有未完成的工作站被占用
     *
     * @param theStep
     * @param runningStep
     * @return
     */
    private boolean hasSameStationRunning(TaskStep theStep, TaskStep runningStep, boolean checkStatus) {

        // 非阻塞模式，直接返回
        if (StringUtils.equalsAny(runningStep.getScheduleMode(), MethodStep.ScheduleModeEnum.UNBLOCK.name(),
                MethodStep.ScheduleModeEnum.BLOCK_SELF.name())) {
            return false;
        }

        // 全阻塞模式下，需要检测相同步骤是否处于运行队列中
        if (StringUtils.equalsAny(runningStep.getScheduleMode(),
                MethodStep.ScheduleModeEnum.BLOCK_ALL.name(),
                MethodStep.ScheduleModeEnum.BLOCK_OTHERS.name())
                && theStep.getStepId().equals(runningStep.getStepId())) {
            return true;
        }

        // 默认其他场景，当有外设工作站处于运行中状态时，返回阻塞
        List<TaskAction> theActions = theStep.getActions();
        List<TaskAction> runningActions = runningStep.getActions();
        Set<Station> theStations = new HashSet<>();
        for (TaskAction theAct : theActions) {
            theStations.add(theAct.getStation());
        }

        for (Station theStation : theStations) {
            for (TaskAction runningAct : runningActions) {
                // 只限于外设工作站，存储类型的工作站依赖点位状态检查，系统控制类型无依赖限制
                Long theStationId = theStation.getId();
                if (StringUtils.equalsAny(theStation.getType(), DeviceTypeEnum.PERIPHERAL.name())
                        && theStationId.equals(runningAct.getStation().getId())
                        && (!checkStatus || !StringUtils.equalsAny(runningAct.getStatus(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())
                        || !StringUtils.equalsAny(runningStep.getStatus(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())
                )) {
                    String msg = MessageFormat.format("Exist unfinished actions[runningTaskid:{0}, runningActId:{1}, runningActName:{2}, runningActStatus:{3}] on station[stationId:{4}, stationName:{5}, stationType:{6}]",
                            runningAct.getTaskId(), runningAct.getId(), runningAct.getName(), runningAct.getStatus(),  theStation.getId(), theStation.getName(), theStation.getType());
                    theStep.setMessage(msg);

                    log.info("StationRunning: theTaskId[{}] theStep[{},{}] has same station[stationId:{}, stationName:{}, stationType:{}] action:[runningTaskid:{}, runningActId:{}, runningActName:{}, runningActStatus:{}]",
                            theStep.getTaskId(), theStep.getId(), theStep.getName(),  theStation.getId(), theStation.getName(), theStation.getType(),    runningAct.getTaskId(), runningAct.getId(), runningAct.getName(), runningAct.getStatus());
                    return true;
                }
            }
        }
        return false;
    }

    private void submit(Runnable runnable) {
        executorService.submit(runnable);
    }

    private void submit(TaskStep taskStep, ExecutorConfig config) {
        final TaskExecutorContext context = TaskExecutorContext.getContext();
        taskStep.setStatus(RunStatusEnum.IN_SCHEDULE_QUE.name());
        if (config.isRecordLog()) {
            TaskExecuteLogUtil.logStep(taskStep);
        }

        submit(new Runnable() {
            @Override
            public void run() {
                if (context.getTask() == null) {
                    TaskExecutorContext.copy(taskStep.getContext(), taskStep);
                } else {
                    TaskExecutorContext.copy(context, taskStep);
                }
                PredicateValidator validator = SpringContextHolder.getBean(PredicateValidator.class);
                try {
                    traceRunningStepQue("Entry", taskStep);
                    PredicateValidator.ValidateResult rs = validator.validate(taskStep);
                    if (rs.isValid()) {
                        new TaskStepExecutor(config).execute(taskStep);
                    } else {
                        // 不满足预设条件，默认或者WAIT策略，挂起等待重试，SKIP退出当前动作执行
                        if (StringUtils.equalsAny("WAIT",  rs.getUnmatchedThen())){
                            taskStep.setStatus(RunStatusEnum.SUSPEND.name());
                            if (config.isRecordLog()) {
                                TaskExecuteLogUtil.logStep(taskStep);
                            }
                            runningQueue.remove(taskStep);
                            suspendQueue.offer(taskStep);
                        }else if (StringUtils.equalsAny("SKIP",  rs.getUnmatchedThen())){
                            taskStep.setStatus(RunStatusEnum.SKIPPED.name());
                            if (config.isRecordLog()) {
                                TaskExecuteLogUtil.logStep(taskStep);
                            }
                        }else {
                            if (StringUtils.equalsAny("SKIP", taskStep.getUnmatchedThen())) {
                                taskStep.setStatus(RunStatusEnum.SKIPPED.name());
                                if (config.isRecordLog()) {
                                    TaskExecuteLogUtil.logStep(taskStep);
                                }
                            } else {
                                taskStep.setStatus(RunStatusEnum.SUSPEND.name());
                                if (config.isRecordLog()) {
                                    TaskExecuteLogUtil.logStep(taskStep);
                                }
                                runningQueue.remove(taskStep);
                                suspendQueue.offer(taskStep);
                            }
                        }
                    }
                } catch(Throwable e){
                    log.error("StepScheduler run exception, error:{}", e.getMessage(), e);
                } finally {
                    validator.unlock(taskStep.getPredicates());
                    runningQueue.remove(taskStep);
                    TaskExecutorContext.getContext().getTaskPositionBuffer().clearBuffer(taskStep.getId());
                    TaskExecutorContext.clear();
                }

            }
        });
    }

    private void traceRunningStepQue(String prefix, TaskStep step) {
        log.info("{} Step: {}", prefix, step);
        int i = 0;
        for (TaskStep runningStep : runningQueue) {
            log.info("Current RunningStep Queue[{}]: {}", ++i, runningStep);
        }
    }
}
