/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.core.service.dto;

import lombok.Data;
import org.cjc.les.core.domain.SampleCategory;

import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-06-11
**/
@Data
public class SampleDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 样本编号 */
    private String number;

    /** 样本名称 */
    private String name;

    /** 样本描述 */
    private String description;

    private SampleCategoryDto category;

    /** 分类名称 */
    private String categoryName;

    private SampleCustomerDto customer;
    /** 客户名称 */
    private String customerName;

    private List<TaskPrepareNestedDto> taskPrepares;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}