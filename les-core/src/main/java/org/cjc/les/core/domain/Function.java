/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.core.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-17
**/
@Entity
@Data
@Table(name="les_function")
public class Function extends LesConfigBaseEntity {

    @Column(name = "name",unique = true)
    @Schema(description = "函数名称")
    private String name;

    @Column(name = "tip")
    @Schema(description = "函数显示提示信息")
    private String tip;

    @Column(name = "description")
    @Schema(description = "函数描述")
    private String description;

    @Column(name = "parameters")
    @Schema(description = "参数定义")
    private String parameters;

    @Column(name = "return_type")
    @Schema(description = "返回类型")
    private String returnType;

    public void copy(Function source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}