# ProGuard混淆使用说明

## 项目概述

本项目是一个多模块的Spring Boot应用，包含以下模块：

### 模块依赖关系
```
system (主模块)
├── generator (代码生成模块)
├── tools (工具模块)
│   └── logging (日志模块)
│       └── common (公共模块)
├── les-core (核心模块)
│   └── common (公共模块)
├── driver (驱动模块)
│   └── les-core (核心模块)
├── les-mgt (实验管理模块)
│   ├── logging (日志模块)
│   ├── system-api (API模块)
│   └── les-core (核心模块)
└── system-api (API模块)
    └── common (公共模块)
```

## 混淆方案

### 方案一：统一在主模块混淆（推荐）

**优点：**
- 配置简单，不容易出错
- 保证模块间依赖关系正确
- 生成单一的混淆后可执行jar
- Spring Boot Loader兼容性好

**工作原理：**
1. 正常编译和打包所有子模块
2. system模块使用Spring Boot插件打包成可执行jar
3. 在system模块对整个可执行jar进行ProGuard混淆
4. 生成最终的混淆后可执行jar

**使用方法：**
```bash
# Windows
build-obfuscated.bat
# 选择选项 1

# Linux/Mac
chmod +x build-obfuscated.sh
./build-obfuscated.sh
# 选择选项 1
```

### 方案二：分模块混淆后统一打包

**优点：**
- 每个模块可以独立混淆
- 可以针对不同模块设置不同的混淆策略
- 更细粒度的控制

**缺点：**
- 配置复杂，容易出现依赖问题
- 需要仔细处理模块间的接口保留

**工作原理：**
1. 分别对每个子模块进行ProGuard混淆
2. 在system模块中引用混淆后的子模块jar
3. 最终打包成可执行jar

**使用方法：**
```bash
# Windows
build-obfuscated.bat
# 选择选项 2

# Linux/Mac
./build-obfuscated.sh
# 选择选项 2
```

## 配置文件说明

### 主模块配置 (system/proguard.cfg)
- 保留Spring Boot Loader
- 保留主类AppRun
- 保留Spring框架核心类
- 保留所有子模块的关键类
- 保留第三方库关键类

### 子模块配置
每个子模块都有对应的proguard.cfg文件，配置了该模块特有的保留规则。

## 重要注意事项

### 1. Java版本兼容性
- 项目使用Java 8
- ProGuard配置中指定了`-target 1.8`

### 2. Spring Boot兼容性
- 保留了Spring Boot Loader相关类
- 关闭了压缩和优化以保持兼容性
- 保留了所有注解和反射相关配置

### 3. 第三方库处理
已配置保留以下第三方库：
- Hutool工具库
- FastJSON
- Apache POI
- MySQL驱动
- Druid连接池
- 邮件相关库
- 七牛云SDK
- 支付宝SDK
- SSH相关库
- JasperReports
- Quartz调度器

### 4. 混淆输出
- 混淆详细信息：`obfuscate/print_seeds.txt`
- 混淆映射文件：`obfuscate/print_mapping.txt`
- 最终可执行jar：`system/target/les-admin-obfuscated.jar`

## 运行混淆后的应用

```bash
java -jar system/target/les-admin-obfuscated.jar
```

## 故障排除

### 1. 混淆后应用无法启动
- 检查proguard.cfg中是否正确保留了所有必要的类
- 查看混淆日志中的警告信息
- 确保Spring Boot相关类都被正确保留

### 2. 反射调用失败
- 在proguard.cfg中添加相应的-keep规则
- 保留被反射调用的类和方法

### 3. 第三方库问题
- 在proguard.cfg中添加对应的-keep规则
- 使用-dontwarn忽略不重要的警告

### 4. 模块依赖问题（方案二）
- 检查各模块的ProGuard配置是否正确保留了接口
- 确保依赖模块的混淆jar被正确引用

## 自定义配置

如需修改混淆策略，可以编辑对应的proguard.cfg文件：

1. **增加保留规则**：使用`-keep`指令
2. **忽略警告**：使用`-dontwarn`指令
3. **保留属性**：使用`-keepattributes`指令

## 性能建议

1. 使用方案一进行日常开发和测试
2. 只在生产环境发布时使用混淆
3. 保留映射文件以便调试生产问题
4. 定期测试混淆后的应用功能完整性
