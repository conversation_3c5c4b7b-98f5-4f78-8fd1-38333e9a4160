C:\jdks\jre1.8-32\bin\java.exe "-javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2020.1.3\lib\idea_rt.jar=50258:C:\Program Files\JetBrains\IntelliJ IDEA 2020.1.3\bin" -Dfile.encoding=UTF-8 -classpath C:\jdks\jdk1.8.0_281\jre\lib\charsets.jar;C:\jdks\jdk1.8.0_281\jre\lib\deploy.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\access-bridge-64.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\cldrdata.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\dnsns.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\jaccess.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\jfxrt.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\localedata.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\nashorn.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\sunec.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\sunjce_provider.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\sunmscapi.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\sunpkcs11.jar;C:\jdks\jdk1.8.0_281\jre\lib\ext\zipfs.jar;C:\jdks\jdk1.8.0_281\jre\lib\javaws.jar;C:\jdks\jdk1.8.0_281\jre\lib\jce.jar;C:\jdks\jdk1.8.0_281\jre\lib\jfr.jar;C:\jdks\jdk1.8.0_281\jre\lib\jfxswt.jar;C:\jdks\jdk1.8.0_281\jre\lib\jsse.jar;C:\jdks\jdk1.8.0_281\jre\lib\management-agent.jar;C:\jdks\jdk1.8.0_281\jre\lib\plugin.jar;C:\jdks\jdk1.8.0_281\jre\lib\resources.jar;C:\jdks\jdk1.8.0_281\jre\lib\rt.jar;D:\robot\les\projects\les_admin\driver\target\classes;D:\robot\les\projects\les_admin\les-core\target\classes;D:\robot\les\projects\les_admin\common\target\classes;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-all\5.3.4\hutool-all-5.3.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\2.2.10.RELEASE\spring-boot-starter-data-jpa-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.2.10.RELEASE\spring-boot-starter-aop-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.2.10.RELEASE\spring-boot-starter-jdbc-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.2.9.RELEASE\spring-jdbc-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\hibernate-core\5.4.21.Final\hibernate-core-5.4.21.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.24.0-GA\javassist-3.24.0-GA.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.10.14\byte-buddy-1.10.14.jar;C:\Users\<USER>\.m2\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\jandex\2.1.3.Final\jandex-2.1.3.Final.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\5.1.0.Final\hibernate-commons-annotations-5.1.0.Final.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.3\jaxb-runtime-2.3.3.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.3\txw2-2.3.3.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.11\istack-commons-runtime-3.0.11.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\2.2.10.RELEASE\spring-data-jpa-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.2.10.RELEASE\spring-data-commons-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\5.2.9.RELEASE\spring-orm-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.2.9.RELEASE\spring-context-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.2.9.RELEASE\spring-tx-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.2.9.RELEASE\spring-beans-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.2.9.RELEASE\spring-aspects-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.2.10.RELEASE\spring-boot-starter-web-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.2.10.RELEASE\spring-boot-starter-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.2.10.RELEASE\spring-boot-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.2.10.RELEASE\spring-boot-starter-logging-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.9\logback-classic-1.2.9.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.9\logback-core-1.2.9.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.0\log4j-to-slf4j-2.17.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.0\log4j-api-2.17.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.25\snakeyaml-1.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.2.10.RELEASE\spring-boot-starter-json-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.10.5\jackson-datatype-jdk8-2.10.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.10.5\jackson-datatype-jsr310-2.10.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.10.5\jackson-module-parameter-names-2.10.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.2.10.RELEASE\spring-boot-starter-tomcat-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.38\tomcat-embed-core-9.0.38.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.38\tomcat-embed-el-9.0.38.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.38\tomcat-embed-websocket-9.0.38.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.2.10.RELEASE\spring-boot-starter-validation-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.0.20.Final\hibernate-validator-6.0.20.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.2.9.RELEASE\spring-web-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.2.9.RELEASE\spring-webmvc-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.2.9.RELEASE\spring-expression-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.2.9.RELEASE\spring-core-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.2.9.RELEASE\spring-jcl-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.2.10.RELEASE\spring-boot-starter-security-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.2.9.RELEASE\spring-aop-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.2.6.RELEASE\spring-security-config-5.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.2.6.RELEASE\spring-security-core-5.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.2.6.RELEASE\spring-security-web-5.2.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\2.2.10.RELEASE\spring-boot-starter-cache-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.2.9.RELEASE\spring-context-support-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.2.10.RELEASE\spring-boot-starter-data-redis-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.2.10.RELEASE\spring-data-redis-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.2.10.RELEASE\spring-data-keyvalue-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.2.9.RELEASE\spring-oxm-5.2.9.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\5.2.2.RELEASE\lettuce-core-5.2.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.52.Final\netty-common-4.1.52.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.52.Final\netty-handler-4.1.52.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.52.Final\netty-resolver-4.1.52.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.52.Final\netty-buffer-4.1.52.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.52.Final\netty-codec-4.1.52.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.52.Final\netty-transport-4.1.52.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.3.10.RELEASE\reactor-core-3.3.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.5.0\commons-pool2-2.5.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.9\commons-lang3-3.9.jar;C:\Users\<USER>\.m2\repository\org\bgee\log4jdbc-log4j2\log4jdbc-log4j2-jdbc4.1\1.16\log4jdbc-log4j2-jdbc4.1-1.16.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\20.0\guava-20.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.21\swagger-annotations-1.5.21.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.21\swagger-models-1.5.21.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.10.5\jackson-annotations-2.10.5.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.21\mysql-connector-java-8.0.21.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid-spring-boot-starter\1.1.24\druid-spring-boot-starter-1.1.24.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.1.24\druid-1.1.24.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.2.10.RELEASE\spring-boot-autoconfigure-2.2.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\dreamlu\mica-ip2region\2.5.6\mica-ip2region-2.5.6.jar;C:\Users\<USER>\.m2\repository\net\dreamlu\mica-core\2.5.6\mica-core-2.5.6.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.12\lombok-1.18.12.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\3.17\poi-3.17.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.13\commons-codec-1.13.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.1\commons-collections4-4.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\3.17\poi-ooxml-3.17.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\3.17\poi-ooxml-schemas-3.17.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\2.6.0\xmlbeans-2.6.0.jar;C:\Users\<USER>\.m2\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.04\curvesapi-1.04.jar;C:\Users\<USER>\.m2\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.70\fastjson-1.2.70.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct-processor\1.3.1.Final\mapstruct-processor-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\com\github\whvcse\easy-captcha\1.6.2\easy-captcha-1.6.2.jar;C:\Users\<USER>\.m2\repository\nl\basjes\parse\useragent\yauaa\5.23\yauaa-5.23.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.0.3\httpclient5-5.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.0.2\httpcore5-5.0.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.0.2\httpcore5-h2-5.0.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.9\commons-text-1.9.jar;C:\Users\<USER>\.m2\repository\nl\basjes\collections\prefixmap\2.0\prefixmap-2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.13\httpcore-4.4.13.jar;C:\Users\<USER>\.m2\repository\me\ele\openapi\eleme-openapi-sdk\1.30.24\eleme-openapi-sdk-1.30.24.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.10.5\jackson-databind-2.10.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.10.5\jackson-core-2.10.5.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-s3\1.11.289\aws-java-sdk-s3-1.11.289.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-kms\1.11.289\aws-java-sdk-kms-1.11.289.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-core\1.11.289\aws-java-sdk-core-1.11.289.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;C:\Users\<USER>\.m2\repository\software\amazon\ion\ion-java\1.0.2\ion-java-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-cbor\2.10.5\jackson-dataformat-cbor-2.10.5.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.10.6\joda-time-2.10.6.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\jmespath-java\1.11.289\jmespath-java-1.11.289.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-sts\1.11.289\aws-java-sdk-sts-1.11.289.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.12\httpmime-4.5.12.jar org.cjc.les.driver.rfid.RfidReaderDriverMock port=4703