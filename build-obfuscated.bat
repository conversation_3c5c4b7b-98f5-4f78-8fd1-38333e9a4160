@echo off
echo ========================================
echo LES Admin ProGuard混淆构建脚本
echo ========================================
echo.

:: 创建输出目录
if not exist "obfuscate" mkdir obfuscate
if not exist "obfuscate\logs" mkdir obfuscate\logs

:: 设置变量
set PROJECT_DIR=%CD%
set OUTPUT_DIR=%PROJECT_DIR%\obfuscate
set LOGS_DIR=%OUTPUT_DIR%\logs
set START_TIME=%time%

:menu
echo 请选择混淆方案:
echo 1. 方案一: 统一在主模块混淆 (推荐)
echo 2. 方案二: 分模块混淆后统一打包
echo 3. 清理所有构建产物
echo 4. 验证混淆结果
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto plan1
if "%choice%"=="2" goto plan2
if "%choice%"=="3" goto clean
if "%choice%"=="4" goto verify
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
goto menu

:plan1
echo.
echo ========================================
echo 执行方案一: 统一在主模块混淆
echo ========================================
echo.

echo [1/5] 清理项目...
call mvn clean -q > "%LOGS_DIR%\clean.log" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: 清理失败
    echo 详细日志请查看: %LOGS_DIR%\clean.log
    pause
    goto menu
)
echo ✓ 清理完成

echo [2/5] 编译所有模块...
call mvn compile -q > "%LOGS_DIR%\compile.log" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: 编译失败
    echo 详细日志请查看: %LOGS_DIR%\compile.log
    pause
    goto menu
)
echo ✓ 编译完成

echo [3/5] 打包所有模块...
call mvn package -DskipTests -q > "%LOGS_DIR%\package.log" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: 打包失败
    echo 详细日志请查看: %LOGS_DIR%\package.log
    pause
    goto menu
)
echo ✓ 打包完成

echo [4/5] 检查生成的jar文件...
if not exist "%PROJECT_DIR%\system\target\system-1.0.jar" (
    echo ERROR: 系统主jar文件不存在
    pause
    goto menu
)
echo ✓ 主jar文件存在

echo [5/5] 在system模块执行ProGuard混淆...
cd system
call mvn package -DskipTests -q > "%LOGS_DIR%\proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: 混淆失败
    echo 详细日志请查看: %LOGS_DIR%\proguard.log
    pause
    goto menu
)
echo ✓ 混淆完成

echo.
echo ========================================
echo 方案一构建完成！
echo 构建时间: %START_TIME% - %time%
echo 原始jar: %PROJECT_DIR%\system\target\system-1.0.jar
echo 混淆后jar: %PROJECT_DIR%\system\target\system-1.0-obfuscated.jar
echo 日志目录: %LOGS_DIR%
echo ========================================
echo.
pause
goto menu

:plan2
echo.
echo ========================================
echo 执行方案二: 分模块混淆后统一打包
echo ========================================
echo.

echo [1/10] 清理项目...
call mvn clean -q > "%LOGS_DIR%\clean.log" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: 清理失败
    pause
    goto menu
)
echo ✓ 清理完成

echo [2/10] 编译所有模块...
call mvn compile -q > "%LOGS_DIR%\compile.log" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: 编译失败
    pause
    goto menu
)
echo ✓ 编译完成

echo [3/10] 混淆common模块...
cd common
call mvn package -DskipTests -q > "%LOGS_DIR%\common-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: common模块混淆失败
    pause
    goto menu
)
echo ✓ common模块混淆完成

echo [4/10] 混淆logging模块...
cd logging
call mvn package -DskipTests -q > "%LOGS_DIR%\logging-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: logging模块混淆失败
    pause
    goto menu
)
echo ✓ logging模块混淆完成

echo [5/10] 混淆tools模块...
cd tools
call mvn package -DskipTests -q > "%LOGS_DIR%\tools-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: tools模块混淆失败
    pause
    goto menu
)
echo ✓ tools模块混淆完成

echo [6/10] 混淆system-api模块...
cd system-api
call mvn package -DskipTests -q > "%LOGS_DIR%\system-api-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: system-api模块混淆失败
    pause
    goto menu
)
echo ✓ system-api模块混淆完成

echo [7/10] 混淆les-core模块...
cd les-core
call mvn package -DskipTests -q > "%LOGS_DIR%\les-core-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: les-core模块混淆失败
    pause
    goto menu
)
echo ✓ les-core模块混淆完成

echo [8/10] 混淆driver模块...
cd driver
call mvn package -DskipTests -q > "%LOGS_DIR%\driver-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: driver模块混淆失败
    pause
    goto menu
)
echo ✓ driver模块混淆完成

echo [9/10] 混淆generator模块...
cd generator
call mvn package -DskipTests -q > "%LOGS_DIR%\generator-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: generator模块混淆失败
    pause
    goto menu
)
echo ✓ generator模块混淆完成

echo [10/10] 混淆les-mgt模块...
cd les-mgt
call mvn package -DskipTests -q > "%LOGS_DIR%\les-mgt-proguard.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: les-mgt模块混淆失败
    pause
    goto menu
)
echo ✓ les-mgt模块混淆完成

echo [11/11] 在system模块统一打包...
cd system
call mvn package -DskipTests -q > "%LOGS_DIR%\system-package.log" 2>&1
cd ..
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: system模块打包失败
    pause
    goto menu
)
echo ✓ 统一打包完成

echo.
echo ========================================
echo 方案二构建完成！
echo 构建时间: %START_TIME% - %time%
echo 混淆后的可执行jar: %PROJECT_DIR%\system\target\system-1.0-obfuscated.jar
echo 各子模块混淆jar位于各自的target目录
echo 日志目录: %LOGS_DIR%
echo ========================================
echo.
pause
goto menu

:clean
echo.
echo ========================================
echo 清理所有构建产物...
echo ========================================
call mvn clean -q
if exist "obfuscate" rmdir /s /q "obfuscate"
echo 清理完成！
pause
goto menu

:verify
echo.
echo ========================================
echo 验证混淆结果...
echo ========================================
call verify-obfuscation.bat
pause
goto menu

:exit
echo 退出构建脚本
exit /b 0
