import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/boardPos',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/boardPos/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/boardPos',
    method: 'put',
    data
  })
}

export default { add, edit, del }
