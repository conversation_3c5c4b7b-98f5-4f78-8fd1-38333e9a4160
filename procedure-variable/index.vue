<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" :crud="crud" />
      <!--表单组件-->
      <el-dialog
        v-model="crud.dialogVisible"
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :title="crud.status.title"
        width="500px"
      >
        <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="120px">
          <el-form-item label="主键ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="关联流程ID">
            <el-input v-model="form.procedureId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="关联变量ID">
            <el-input v-model="form.variableId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="默认值">
            <el-input v-model="form.defaultValue" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="当前值">
            <el-input v-model="form.value" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="列表顺序下标">
            <el-input v-model="form.orderIndex" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否已被删除,Y/N">
            <el-input v-model="form.deleteFlag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updateBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="主键ID" />
        <el-table-column prop="procedureId" label="关联流程ID" />
        <el-table-column prop="variableId" label="关联变量ID" />
        <el-table-column prop="defaultValue" label="默认值" />
        <el-table-column prop="value" label="当前值" />
        <el-table-column prop="orderIndex" label="列表顺序下标" />
        <el-table-column prop="deleteFlag" label="是否已被删除,Y/N" />
        <el-table-column prop="createBy" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateBy" label="更新人" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','procedureVariable:edit','procedureVariable:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :crud="crud"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud"/>

  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import crudProcedureVariable from '@/api/procedureVariable'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
import pagination from '@/components/crud/Pagination'

// 定义组件名称
defineOptions({
  name: 'ProcedureVariable'
})

const defaultForm = { id: null, procedureId: null, variableId: null, defaultValue: null, value: null, orderIndex: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }

// 表单验证规则
const rules = reactive({
      })

// 权限配置
const permission = {
  add: ['admin', 'procedureVariable:add'],
  edit: ['admin', 'procedureVariable:edit'],
  del: ['admin', 'procedureVariable:del']
}

// refs
const formRef = ref(null)
const tableRef = ref(null)

// 权限校验
const checkPer = inject('checkPer')



// 使用CRUD钩子
const { crud, form } = useCrud({
  title: '编辑',
  url: 'api/procedureVariable',
  crudMethod: { ...crudProcedureVariable },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 生命周期
onMounted(() => {
  crud.refresh()
})

</script>

<style scoped>

</style>
