<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.cjc</groupId>
        <artifactId>les-admin</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.cjc</groupId>
    <artifactId>les-mgt</artifactId>
    <version>1.0</version>
    <name>实验管理模块</name>

    <dependencies>
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>logging</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>system-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>les-core</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>6.20.0</version> <!-- 使用最新版本 -->
        </dependency>
        <dependency>
            <groupId>org.olap4j</groupId>
            <artifactId>olap4j</artifactId>
            <version>1.2.0</version> <!-- 如果需要支持 iText -->
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version> <!-- 如果需要支持 iText -->
        </dependency>
    </dependencies>

</project>