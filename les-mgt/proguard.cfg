# les-mgt模块ProGuard配置
-verbose
-target 1.8

# 关闭压缩和优化
-dontshrink
-dontoptimize
-dontpreverify

# 保留属性
-keepattributes SourceFile,LineNumberTable
-keepattributes *Annotation*,InnerClasses
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
-keepattributes Signature

# 混淆时不使用大小写混合
-dontusemixedcaseclassnames

# 保留枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留接口
-keepnames interface ** { *; }
-keep interface * extends * { *; }

# 保留项目代码
-keep class org.cjc.** { *; }

# 保留les-mgt相关类
-keep class org.cjc.les.mgt.** { *; }

# 保留JasperReports相关类
-keep class net.sf.jasperreports.** { *; }

# 保留OLAP4J相关类
-keep class org.olap4j.** { *; }

# 保留iText相关类
-keep class com.lowagie.** { *; }

# 保留Spring相关注解的类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * { *; }

# 保留实体类和DTO
-keep class **.*Entity { *; }
-keep class **.*DTO { *; }
-keep class **.*Dto { *; }
-keep class **.*VO { *; }
-keep class **.*Vo { *; }

# 保留Service和Controller
-keep class **.*Service { *; }
-keep class **.*ServiceImpl { *; }
-keep class **.*Controller { *; }

# 保留JPA相关
-keep @jakarta.persistence.Entity class * { *; }
-keep @jakarta.persistence.Table class * { *; }

# 保留反射相关
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @jakarta.annotation.Resource *;
}

# 保留序列化相关
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 忽略警告
-dontwarn **
