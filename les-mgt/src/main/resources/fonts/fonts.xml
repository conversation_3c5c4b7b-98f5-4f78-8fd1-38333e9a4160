<?xml version="1.0" encoding="UTF-8"?>
<fontFamilies>
    <!--
        可以配置多个字体
        name 属性：指定字体名称，这里的字体名称在 JasperReport 模板文件中使用的要一致，才能够匹配上
    -->oooo
    <fontFamily name="华文宋体">
        <!-- 正常字体路径 -->
        <normal><![CDATA[fonts/STSONG.TTF]]></normal>
        <!-- 加粗字体路径 -->
        <bold><![CDATA[fonts/STSONG.TTF]]></bold>
        <!-- 斜体字体路径 -->
        <italic><![CDATA[fonts/STSONG.TTF]]></italic>
        <!-- 加粗斜体字体路径 -->
        <bolditalic><![CDATA[fonts/STSONG.TTF]]></bolditalic>
        <pdfEmbedded><![CDATA[true]]></pdfEmbedded>
        <pdfEncoding>Identity-H</pdfEncoding>
        <exportFonts>
            <export key="net.sf.jasperreports.html">'华文宋体', Arial, Helvetica, sans-serif</export>
            <export key="net.sf.jasperreports.xhtml">'华文宋体', Arial, Helvetica, sans-serif</export>
        </exportFonts>
    </fontFamily>
</fontFamilies>