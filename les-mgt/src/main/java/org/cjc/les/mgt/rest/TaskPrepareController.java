/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.apache.catalina.connector.ClientAbortException;
import org.cjc.annotation.Log;
import org.cjc.les.constants.TaskPrepareStatusEnum;
import org.cjc.les.core.domain.Method;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.service.ProcedureService;
import org.cjc.les.core.service.TaskPrepareService;
import org.cjc.les.core.service.dto.ProcedureDto;
import org.cjc.les.core.service.dto.SampleEntryStepStatusDto;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.dto.TaskPrepareQueryCriteria;
import org.cjc.les.mgt.service.TaskPrepareBatchService;
import org.cjc.les.mgt.service.TaskPrepareViewService;
import org.cjc.les.mgt.service.dto.TaskPrepareViewDto;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.AsyncEvent;
import jakarta.servlet.AsyncListener;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-10
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务启动准备管理")
@RequestMapping("/api/taskPrepare")
@EnableAsync
public class TaskPrepareController {

    private final TaskPrepareViewService taskPrepareViewService;

    private final TaskPrepareBatchService taskPrepareBatchService;

    private final TaskPrepareService taskPrepareService;

    private final ProcedureService procedureService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public void exportTaskPrepare(HttpServletResponse response, TaskPrepareQueryCriteria criteria) throws IOException {
        taskPrepareService.download(taskPrepareService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务启动准备")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> queryTaskPrepare(TaskPrepareQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskPrepareService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value="/queryTagsToEdit")
    @Log("查询任务启动准备")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> queryTagsToEdit(TaskPrepareQueryCriteria criteria){
        return new ResponseEntity<>(taskPrepareService.queryTagsToEdit(criteria),HttpStatus.OK);
    }

    /**
     * 查询待投递的预处理任务列表
     *
     * @param criteria
     * @return
     */
    @GetMapping(value = "/queryTaskPrepareByTag")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> queryTaskPrepareByTag(TaskPrepareQueryCriteria criteria, Pageable pageable){
        Set<String> statusSet = new HashSet<>();
        statusSet.add(TaskPrepareStatusEnum.DRAFT.name());
        statusSet.add(TaskPrepareStatusEnum.READY.name());
        criteria.setStatus(statusSet);
        return new ResponseEntity<>(taskPrepareService.queryAll(criteria, pageable),HttpStatus.OK);
    }

    /**
     * 查询最近一次创建的预处理任务
     *
     * @return
     */
    @GetMapping(value = "/findLatestTaskPrepare")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> findLatestTaskPrepare(){
        return new ResponseEntity<>(taskPrepareService.findLatestTaskPrepare(),HttpStatus.OK);
    }



    /**
     * 查询待投递的预处理任务列表
     *
     * @param criteria
     * @return
     */
    @GetMapping(value = "/queryTaskPrepareForConveyor")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> queryTaskPrepareForConveyor(TaskPrepareQueryCriteria criteria, Pageable pageable){
        Set<String> statusSet = new HashSet<>();
        statusSet.add(TaskPrepareStatusEnum.READY.name());
        criteria.setStatus(statusSet);
        return new ResponseEntity<>(taskPrepareService.queryAll(criteria,pageable),HttpStatus.OK);
    }


    @PostMapping("/readRfTag")
    @ApiOperation("监听RF Reader的返回")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> readRfTag(@RequestBody TaskPrepareDto resource){


        //SecurityContext context = SecurityContextHolder.getContext();

        // 在异步线程中设置 SecurityContext
      //  SecurityContextHolder.setContext(context);

        taskPrepareService.readRfTag(resource);

        return new ResponseEntity<>(resource,HttpStatus.OK);
    }

    @PostMapping("/submitTagReadingStep")
    @ApiOperation("监听RF Reader的返回")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> submitTagReadingStep(@RequestBody TaskPrepareViewDto resource) {

        taskPrepareViewService.submitTagReadingStep(resource);

        return new ResponseEntity<>(resource, HttpStatus.OK);
    }

    @PostMapping("/submitEntryConveyorAcceptingStep")
    @ApiOperation("监听RF Reader的返回")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> submitEntryConveyorAcceptingStep(@RequestBody TaskPrepareViewDto resource) {

        taskPrepareViewService.submitEntryConveyorAcceptingStep(resource);

        return new ResponseEntity<>(resource, HttpStatus.OK);
    }

    @PostMapping("/submitBatchTasks")
    @ApiOperation("提交批量任务")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> submitBatchTasks(@RequestBody TaskPrepareViewDto resource) {

        taskPrepareBatchService.submitBatchTasks(resource);

        return new ResponseEntity<>(resource, HttpStatus.OK);
    }

    @GetMapping("/getCurrentTaskPrepareView")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> getCurrentTaskPrepareView(TaskPrepareViewDto criteria) {
        return new ResponseEntity<>(taskPrepareViewService.getCurrentTaskPrepareView(criteria.getMode()), HttpStatus.OK);
    }

    @GetMapping("/cancelCurrentTaskPrepareView")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> cancelCurrentTaskPrepareView(TaskPrepareViewDto criteria) {
        taskPrepareViewService.cancelCurrentTaskPrepareView(criteria);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/querySampleEntryStepStatus")
    @ApiOperation("查询任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:list')")
    public ResponseEntity<Object> querySampleEntryStepStatus(SampleEntryStepStatusDto criteria) {
        return new ResponseEntity<>(taskPrepareService.querySampleEntryStepStatus(criteria), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务启动准备")
    @ApiOperation("新增任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:add')")
    public ResponseEntity<Object> createTaskPrepare(@Validated @RequestBody TaskPrepare resources){
        return new ResponseEntity<>(taskPrepareService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务启动准备")
    @ApiOperation("修改任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:edit')")
    public ResponseEntity<Object> updateTaskPrepare(@Validated @RequestBody TaskPrepare resources){
        taskPrepareService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务启动准备")
    @ApiOperation("删除任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:del')")
    public ResponseEntity<Object> deleteTaskPrepare(@RequestBody Long[] ids) {
        taskPrepareService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/batchSaveTaskPrepares")
    @Log("新增任务启动准备")
    @ApiOperation("新增任务启动准备")
    @PreAuthorize("@el.check('taskPrepare:add')")
    public ResponseEntity<Object> batchSaveTaskPrepares(@Validated @RequestBody List<TaskPrepareDto> resources) {
        List<TaskPrepareDto> outList = taskPrepareService.batchSaveTaskPrepares(resources);
        return new ResponseEntity<>(outList, HttpStatus.OK);
    }

}