/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import org.cjc.annotation.Query;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2025-07-31
 **/
@Data
public class StatsResultFeatureQueryCriteria {

    /**
     * 检测方法
     */
    @Query(type = Query.Type.EQUAL)
    private String methodName;

    /**
     * 样品分类
     */
    @Query(type = Query.Type.EQUAL)
    private String sampleCategory;
    /**
     * 检测结果创建时间
     */
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> featureCreateTime = new ArrayList<>();
}