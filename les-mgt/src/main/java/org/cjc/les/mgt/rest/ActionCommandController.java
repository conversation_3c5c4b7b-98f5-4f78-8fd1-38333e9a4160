/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.ActionCommand;
import org.cjc.les.core.service.ActionCommandService;
import org.cjc.les.core.service.dto.ActionCommandQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "动作指令管理")
@RequestMapping("/api/actionCommand")
public class ActionCommandController {

    private final ActionCommandService actionCommandService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('actionCommand:list')")
    public void exportActionCommand(HttpServletResponse response, ActionCommandQueryCriteria criteria) throws IOException {
        actionCommandService.download(actionCommandService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询动作指令")
    @ApiOperation("查询动作指令")
    @PreAuthorize("@el.check('actionCommand:list')")
    public ResponseEntity<Object> queryActionCommand(ActionCommandQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(actionCommandService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增动作指令")
    @ApiOperation("新增动作指令")
    @PreAuthorize("@el.check('actionCommand:add')")
    public ResponseEntity<Object> createActionCommand(@Validated @RequestBody ActionCommand resources){
        return new ResponseEntity<>(actionCommandService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改动作指令")
    @ApiOperation("修改动作指令")
    @PreAuthorize("@el.check('actionCommand:edit')")
    public ResponseEntity<Object> updateActionCommand(@Validated @RequestBody ActionCommand resources){
        actionCommandService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除动作指令")
    @ApiOperation("删除动作指令")
    @PreAuthorize("@el.check('actionCommand:del')")
    public ResponseEntity<Object> deleteActionCommand(@RequestBody Long[] ids) {
        actionCommandService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}