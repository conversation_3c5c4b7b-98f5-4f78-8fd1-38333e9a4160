/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-07-10
**/
@Data
public class ShopApproveLogDto implements Serializable {

    /** 店铺ID */
    private Long shopId;

    /** 主键自增ID */
    private Long id;

    /** 审核详情描述 */
    private String auditDetail;

    /** 审核状态: 1:待审核,2:审核通过,3:审核失败 */
    private String auditStatus;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}