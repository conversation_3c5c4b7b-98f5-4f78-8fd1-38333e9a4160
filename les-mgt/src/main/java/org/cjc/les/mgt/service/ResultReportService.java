/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.core.service.dto.ResultDto;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-10-30
**/
public interface ResultReportService {

    /**
     * 导出PDF格式报告文档
     * @param dto 待导出的数据
     * @param response
     * @throws IOException
     */
    void downloadReport(ResultDto dto, HttpServletRequest request, HttpServletResponse response) throws IOException;

}