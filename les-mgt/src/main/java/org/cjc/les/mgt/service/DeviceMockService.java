/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.domain.DeviceMock;
import org.cjc.les.mgt.service.dto.DeviceMockDto;
import org.cjc.les.mgt.service.dto.DeviceMockQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-08-04
**/
public interface DeviceMockService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(DeviceMockQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<DeviceMockDto>
    */
    List<DeviceMockDto> queryAll(DeviceMockQueryCriteria criteria);

    /**
     * 开启模拟器
     * @param dto
     */
    DeviceMockDto start(DeviceMockDto dto);

    /**
     * 关闭模拟器
     * @param dto
     */
    DeviceMockDto stop(DeviceMockDto dto);

    /**
     * 用户输入扫描
     * @param dto
     * @return
     */
    DeviceMockDto inputScanner(DeviceMockDto dto);

    /**
     * 根据ID查询
     * @param id ID
     * @return DeviceMockDto
     */
    DeviceMockDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return DeviceMockDto
    */
    DeviceMockDto create(DeviceMock resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(DeviceMock resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<DeviceMockDto> all, HttpServletResponse response) throws IOException;
}