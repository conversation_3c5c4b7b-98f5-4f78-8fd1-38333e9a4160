/*
 *  Copyright 2024-2024 <PERSON>han Annis Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Method;
import org.cjc.les.core.domain.Procedure;
import org.cjc.les.core.service.ActionService;
import org.cjc.les.core.service.MethodService;
import org.cjc.les.core.service.ProcedureService;
import org.cjc.les.core.service.StepService;
import org.cjc.les.core.service.dto.*;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "流程管理管理")
@RequestMapping("/api/procedure")
public class ProcedureController {

    private final ProcedureService procedureService;

    private final MethodService methodService;

    private final StepService stepService;

    private final ActionService actionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('procedure:list')")
    public void exportProcedure(HttpServletResponse response, ProcedureQueryCriteria criteria) throws IOException {
        procedureService.download(procedureService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryProcedure(ProcedureQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(procedureService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/queryProceduresForSelection")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryProceduresForSelection(){
        ProcedureQueryCriteria criteria = new ProcedureQueryCriteria();
        return new ResponseEntity<>(procedureService.queryProceduresForSelection(criteria),HttpStatus.OK);
    }
    @GetMapping("/queryProcedureById")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryProcedureById(@RequestParam(defaultValue = "0")Long id){
        return new ResponseEntity<>(procedureService.findById(id),HttpStatus.OK);
    }
    @GetMapping("/queryAllMethods")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryAllMethods(){
        MethodQueryCriteria criteria = new MethodQueryCriteria();
        return new ResponseEntity<>(methodService.queryAll(criteria),HttpStatus.OK);
    }
    @GetMapping("/queryAllSteps")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryAllSteps(){
        StepQueryCriteria criteria = new StepQueryCriteria();
        return new ResponseEntity<>(stepService.queryAll(criteria),HttpStatus.OK);
    }
    @GetMapping("/queryAllActions")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryAllActions(){
        ActionQueryCriteria criteria = new ActionQueryCriteria();
        return new ResponseEntity<>(actionService.queryAllForSection(criteria),HttpStatus.OK);
    }
    @GetMapping("/queryActionVarDefinesByActionId")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryActionVarDefinesByActionId(@RequestParam(defaultValue = "0")Long actionId){
        return new ResponseEntity<>(procedureService.queryActionVarDefinesByActionId(actionId),HttpStatus.OK);
    }
    @GetMapping("/queryStepVarDefinesByStepId")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryStepVarDefinesByStepId(@RequestParam(defaultValue = "0")Long stepId){
        return new ResponseEntity<>(procedureService.queryStepVarDefinesByStepId(stepId),HttpStatus.OK);
    }
    @GetMapping("/queryMethodVarDefinesByMethodId")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryMethodVarDefinesByMethodId(@RequestParam(defaultValue = "0")Long methodId){
        return new ResponseEntity<>(procedureService.queryMethodVarDefinesByMethodId(methodId),HttpStatus.OK);
    }
    @PostMapping
    @Log("新增流程管理")
    @ApiOperation("新增流程管理")
    @PreAuthorize("@el.check('procedure:add')")
    public ResponseEntity<Object> createProcedure(@Validated @RequestBody Procedure resources){
        return new ResponseEntity<>(procedureService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改流程管理")
    @ApiOperation("修改流程管理")
    @PreAuthorize("@el.check('procedure:edit')")
    public ResponseEntity<Object> updateProcedure(@Validated @RequestBody Procedure resources){
        procedureService.update(resources);
        return new ResponseEntity<>(procedureService.findById(resources.getId()),HttpStatus.OK);
    }

    @DeleteMapping
    @Log("删除流程管理")
    @ApiOperation("删除流程管理")
    @PreAuthorize("@el.check('procedure:del')")
    public ResponseEntity<Object> deleteProcedure(@RequestBody Long[] ids) {
        procedureService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/queryAllUnRefProcNodes")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryAllUnRefProcNodes(){
        return new ResponseEntity<>(procedureService.queryAllUnRefProcNodes(),HttpStatus.OK);
    }

    @PutMapping("/cleanUnRefProcNodes")
    @Log("修改流程管理")
    @ApiOperation("修改流程管理")
    @PreAuthorize("@el.check('procedure:edit')")
    public ResponseEntity<Object> cleanUnRefProcNodes(@Validated @RequestBody ProcedureDto resources){
        procedureService.cleanUnRefProcNodes(resources);
        return new ResponseEntity<>(procedureService.queryAllUnRefProcNodes(),HttpStatus.OK);
    }

    @GetMapping("/queryMainMethodNamesForSelection")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryMainMethodNamesForSelection(){
        return new ResponseEntity<>(procedureService.queryMainMethodNamesForSelection(),HttpStatus.OK);
    }


}