/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.annotation.Log;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.TaskRepository;
import org.cjc.les.core.service.TaskCommandService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskGlobalInfoDto;
import org.cjc.les.core.service.dto.TaskQueryCriteria;
import org.cjc.les.core.service.dto.TaskScheduleChartDto;
import org.cjc.les.core.service.mapstruct.*;
import org.cjc.les.core.task.execute.TaskGlobalConfig;
import org.cjc.les.core.task.schedule.*;
import org.cjc.les.mgt.service.dto.TaskOnlineDataViewDto;
import org.cjc.modules.system.service.DictDetailService;
import org.cjc.modules.system.service.dto.DictDetailDto;
import org.cjc.utils.FileUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.SpringContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务管理管理")
@RequestMapping("/api/task")
public class TaskController {

    private final TaskRepository taskRepository;

    private final TaskService taskService;

    private final TaskCommandService taskCommandService;

    private final TaskScheduler taskScheduler;

    private final EntryScheduler entryScheduler;
    private final MainScheduler mainScheduler;
    private final ExitScheduler exitScheduler;

    private final StepScheduler stepScheduler;

    private final ActionScheduler actionScheduler;

    private final TaskMapper taskMapper;
    private final TaskMethodMapper taskMethodMapper;
    private final TaskStepMapper taskStepMapper;
    private final TaskActionMapper taskActionMapper;
    private final TaskCommandMapper taskCommandMapper;

    private final DictDetailService dictDetailService;


    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('task:list')")
    public void exportTask(HttpServletResponse response, TaskQueryCriteria criteria) throws IOException {
        // 用户不选择查询状态时，默认过滤掉DRAFT
        if (criteria.getStatus().isEmpty()){
            List<DictDetailDto> dictDetailDtos = dictDetailService.getDictByName("task_status");
            for (DictDetailDto dictDetailDto : dictDetailDtos){
                criteria.getStatus().add(dictDetailDto.getValue());
            }
        }
        taskService.download(taskService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务管理")
    @ApiOperation("查询任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> queryTask(TaskQueryCriteria criteria, Pageable pageable){
        // 用户不选择查询状态时，默认过滤掉DRAFT
        if (criteria.getStatus().isEmpty()){
            List<DictDetailDto> dictDetailDtos = dictDetailService.getDictByName("task_status");
            for (DictDetailDto dictDetailDto : dictDetailDtos){
                criteria.getStatus().add(dictDetailDto.getValue());
            }
        }
        return new ResponseEntity<>(taskService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/queryLatestTasks")
    @Log("查询任务管理")
    @ApiOperation("查询任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> queryLatestTasks(TaskQueryCriteria criteria, Pageable pageable){
        Timestamp beginTime = Timestamp.valueOf(LocalDateTime.now().minus(Duration.ofHours(24)));
        Timestamp endTime = Timestamp.valueOf(LocalDateTime.now());
        criteria.setUpdateTime(new ArrayList<>());
        criteria.getUpdateTime().add(beginTime);
        criteria.getUpdateTime().add(endTime);
        return new ResponseEntity<>(taskService.queryLatestTasks(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> createTask(@Validated @RequestBody Task resources){
        return new ResponseEntity<>(taskService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务管理")
    @ApiOperation("修改任务管理")
    @PreAuthorize("@el.check('task:edit')")
    public ResponseEntity<Object> updateTask(@Validated @RequestBody Task resources){
        taskService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务管理")
    @ApiOperation("删除任务管理")
    @PreAuthorize("@el.check('task:del')")
    public ResponseEntity<Object> deleteTask(@RequestBody Long[] ids) {

        for (Long id : ids) {
            Task task = new Task();
            task.setId(id);
            taskScheduler.cancel(task);
        }
        taskService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/querySchedulerGanttData")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object>  querySchedulerGanttData(HttpServletResponse response, TaskQueryCriteria criteria) throws IOException {
        PageRequest pageable = PageRequest.of(0, 10, Sort.by(new Sort.Order(Sort.Direction.DESC,"id")));
        criteria.setUpdateTime(new ArrayList<>());
       // criteria.getUpdateTime().add(Timestamp.valueOf(LocalDateTime.now().minus(Duration.ofDays(1))));
        criteria.getUpdateTime().add(Timestamp.valueOf(LocalDateTime.now().minus(Duration.ofHours(2))));
        criteria.getUpdateTime().add(Timestamp.valueOf(LocalDateTime.now()));

        Page<Task> page = taskRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);

        List<Task> tasks = page.getContent();
        if (CollectionUtils.isEmpty(tasks)){
            // Empty list found.
        }

        TaskScheduleChartDto dto = new TaskScheduleChartDto();
        String[] taskDefDimensions = {"index", "id", "name","type","progress","taskId"};
        dto.getTaskdefs().put("dimensions", taskDefDimensions);
        List<Object[]> taskDefDataList = new ArrayList<>();
        dto.getTaskdefs().put("data",taskDefDataList);

        String[] taskItemsDimensions = {"index","parentId","id","type","name","status","progress","startTime","endTime","taskId", "taskNumber","taskName"};
        dto.getTaskItems().put("dimensions",taskItemsDimensions);
        List<Object[]> taskItemDataList = new ArrayList<>();
        dto.getTaskItems().put("data",taskItemDataList);

        //LinkedBlockingQueue<Task> tasks = taskScheduler.getTaskQueue();

        int yIndex = -1;
        AtomicInteger yIndexBuf = new AtomicInteger(yIndex);

        for (Task task : tasks){
            yIndex = yIndexBuf.incrementAndGet();
            int taskIndex = yIndex;
            Object[] taskDefData = new Object[]{yIndex, task.getId(),task.getTaskName(),"TASK",0, task.getId() };
            taskDefDataList.add(taskDefData);

            List<Object[]> taskStepsItemDataList = new ArrayList<>();

            //taskItemataList.add(new Object[]{taskDefDataList.size()-1,task.getId(), task.getId(),"STEP",task.getTaskName(), task.getStatus(),10,new Date().getTime(),new Date().getTime() + 3600000, task.getTaskNumber(),task.getTaskName()});
            Long scheduleEntryTime = task.getScheduleEntryTime() == null? task.getCreateTime().getTime():task.getScheduleEntryTime();
            for (TaskMethod method : task.getMethodList()) {
                yIndex = yIndexBuf.incrementAndGet();
                Object[] methodDefData = new Object[]{yIndex, method.getId(),method.getName(),"METHOD",0, task.getId() };
                taskDefDataList.add(methodDefData);

                List<Object[]> stepItemDataList = new ArrayList<>();

                TaskStep lastStep = null;
                for (TaskStep step: method.getSteps()) {

                    // 开始时间= 任务入列时间戳+预估开始偏移，    结束时间= 开始时间 +
                    long offsetStartTime =  getStepExecutingStartTime(step);
                    long offsetStartTimeInQueue = stepScheduler.calculateTaskStepStartTimeInQueue(step);
                    if (offsetStartTimeInQueue !=0L){
                        offsetStartTime = offsetStartTimeInQueue;
                    }else if (lastStep == null){
                        // 每个方法的第一个步骤如果不在步骤队列中，则预设评估开始时间点为当前时间戳

                    }else if (lastStep != null){
                        offsetStartTime = getStepExecutingStartTime(lastStep) + getStepExecutingDuringTime(lastStep);
                    }

                    step.setEvaluateExecutingStart(offsetStartTime);

                    long startTime = scheduleEntryTime + offsetStartTime;

                    // 计算动作执行时间
                    progressTaskActionsInStep(step, yIndexBuf, task, taskDefDataList, taskItemDataList, taskStepsItemDataList);

                    long offsetDurationInQue = stepScheduler.calculateTaskStepDurationInQueue(step);

                    long actualDuration = Math.max(getStepExecutingDuringTime(step), offsetDurationInQue);

                    step.setEvaluateExecutingDuration(actualDuration);
                    long endTime = startTime + actualDuration;
                    Object[] stepData = new Object[]{yIndex, method.getId(), step.getId(),"STEP",step.getName(), step.getStatus(),10,startTime, endTime, task.getId(), task.getTaskNumber(),task.getTaskName()};
                    taskItemDataList.add(stepData);
                    stepItemDataList.add(stepData);
                    taskStepsItemDataList.add(stepData);
                    lastStep = step;
                }
                methodDefData[4] = caculateProgress(stepItemDataList);
            }

            Long taskStartTime = task.getScheduleEntryTime()==null?task.getCreateTime().getTime():task.getScheduleEntryTime();
            Long taskEndTime = taskStartTime;
            taskItemDataList.add(new Object[]{taskIndex, task.getId(), task.getId(),"TASK",task.getTaskName(), task.getStatus(),10, taskStartTime, taskEndTime, task.getId(), task.getTaskNumber(),task.getTaskName()});
            taskDefData[4] = caculateProgress(taskStepsItemDataList);
        }

        return new ResponseEntity<>(dto,HttpStatus.OK);
      //  String dataStr = FileUtil.readString("D:/robot/references/airports.txt","utf8");
      //  return new ResponseEntity<>(JSON.parseObject(dataStr),HttpStatus.OK);

    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/querySchedulerGanttDataOnline")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object>  querySchedulerGanttDataOnline(HttpServletResponse response, TaskQueryCriteria criteria) throws IOException {

        LinkedBlockingQueue<Task> tasks = taskScheduler.getTaskQueue();

                TaskScheduleChartDto dto = new TaskScheduleChartDto();
        String[] taskDefDimensions = {"index", "id", "name","type","progress","taskId"};
        dto.getTaskdefs().put("dimensions", taskDefDimensions);
        List<Object[]> taskDefDataList = new ArrayList<>();
        dto.getTaskdefs().put("data",taskDefDataList);

        String[] taskItemsDimensions = {"index","parentId","id","type","name","status","progress","startTime","endTime","taskId", "taskNumber","taskName"};
        dto.getTaskItems().put("dimensions",taskItemsDimensions);
        List<Object[]> taskItemDataList = new ArrayList<>();
        dto.getTaskItems().put("data",taskItemDataList);

        //LinkedBlockingQueue<Task> tasks = taskScheduler.getTaskQueue();

        int yIndex = -1;
        AtomicInteger yIndexBuf = new AtomicInteger(yIndex);

        for (Task task : tasks){
            yIndex = yIndexBuf.incrementAndGet();
            int taskIndex = yIndex;
            Object[] taskDefData = new Object[]{yIndex, task.getId(),task.getTaskName(),"TASK",0, task.getId() };
            taskDefDataList.add(taskDefData);

            List<Object[]> taskStepsItemDataList = new ArrayList<>();

            //taskItemataList.add(new Object[]{taskDefDataList.size()-1,task.getId(), task.getId(),"STEP",task.getTaskName(), task.getStatus(),10,new Date().getTime(),new Date().getTime() + 3600000, task.getTaskNumber(),task.getTaskName()});
            Long scheduleEntryTime = task.getScheduleEntryTime() == null? task.getCreateTime().getTime():task.getScheduleEntryTime();
            for (TaskMethod method : task.getMethodList()) {
                yIndex = yIndexBuf.incrementAndGet();
                Object[] methodDefData = new Object[]{yIndex, method.getId(),method.getName(),"METHOD",0, task.getId() };
                taskDefDataList.add(methodDefData);

                List<Object[]> stepItemDataList = new ArrayList<>();

                TaskStep lastStep = null;
                for (TaskStep step: method.getSteps()) {

                    // 开始时间= 任务入列时间戳+预估开始偏移，    结束时间= 开始时间 +
                    long offsetStartTime =  getStepExecutingStartTime(step);
                    long offsetStartTimeInQueue = stepScheduler.calculateTaskStepStartTimeInQueue(step);
                    if (offsetStartTimeInQueue !=0L){
                        offsetStartTime = offsetStartTimeInQueue;
                    }else if (lastStep == null){
                        // 每个方法的第一个步骤如果不在步骤队列中，则预设评估开始时间点为当前时间戳

                    }else if (lastStep != null){
                        offsetStartTime = getStepExecutingStartTime(lastStep) + getStepExecutingDuringTime(lastStep);
                    }

                    step.setEvaluateExecutingStart(offsetStartTime);

                    long startTime = scheduleEntryTime + offsetStartTime;

                    // 计算动作执行时间
                    progressTaskActionsInStep(step, yIndexBuf, task, taskDefDataList, taskItemDataList, taskStepsItemDataList);

                    long offsetDurationInQue = stepScheduler.calculateTaskStepDurationInQueue(step);

                    long actualDuration = Math.max(getStepExecutingDuringTime(step), offsetDurationInQue);

                    step.setEvaluateExecutingDuration(actualDuration);
                    long endTime = startTime + actualDuration;
                    Object[] stepData = new Object[]{yIndex, method.getId(), step.getId(),"STEP",step.getName(), step.getStatus(),10,startTime, endTime, task.getId(), task.getTaskNumber(),task.getTaskName()};
                    taskItemDataList.add(stepData);
                    stepItemDataList.add(stepData);
                    taskStepsItemDataList.add(stepData);
                    lastStep = step;
                }
                methodDefData[4] = caculateProgress(stepItemDataList);
            }

            Long taskStartTime = task.getScheduleEntryTime()==null?task.getCreateTime().getTime():task.getScheduleEntryTime();
            Long taskEndTime = taskStartTime;
            taskItemDataList.add(new Object[]{taskIndex, task.getId(), task.getId(),"TASK",task.getTaskName(), task.getStatus(),10, taskStartTime, taskEndTime, task.getId(), task.getTaskNumber(),task.getTaskName()});
            taskDefData[4] = caculateProgress(taskStepsItemDataList);
        }

        return new ResponseEntity<>(dto,HttpStatus.OK);
        //  String dataStr = FileUtil.readString("D:/robot/references/airports.txt","utf8");
        //  return new ResponseEntity<>(JSON.parseObject(dataStr),HttpStatus.OK);

    }


    /**
     * 查询正在运行的任务及其状态
     * 用于任务监控
     * @return
     */
    @ApiOperation("导出数据")
    @GetMapping(value = "/querySchedulerOnlineData")
    @PreAuthorize("@el.check('task:monitor')")
    public ResponseEntity<Object> querySchedulerOnlineData() {
        TaskOnlineDataViewDto onlineDataViewDto = new TaskOnlineDataViewDto();
        // 查询在线任务
        LinkedBlockingQueue<Task> taskQueue = taskScheduler.getTaskQueue();
        for (Task task : taskQueue) {
            onlineDataViewDto.getTasks().add(taskMapper.toDto(task));
        }

        // 查询在线任务方法
        LinkedBlockingQueue<TaskMethod> entryMethodQueue = entryScheduler.getMethodsQueue();
        for (TaskMethod method : entryMethodQueue) {
            onlineDataViewDto.getTaskMethods().add(taskMethodMapper.toDto(method));
        }
        LinkedBlockingQueue<TaskMethod> mainMethodQueue = mainScheduler.getMethodsQueue();
        for (TaskMethod method : mainMethodQueue) {
            onlineDataViewDto.getTaskMethods().add(taskMethodMapper.toDto(method));
        }
        LinkedBlockingQueue<TaskMethod> exitMethodQueue = exitScheduler.getMethodsQueue();
        for (TaskMethod method : exitMethodQueue) {
            onlineDataViewDto.getTaskMethods().add(taskMethodMapper.toDto(method));
        }

        // 查询在线任务步骤
        LinkedBlockingQueue<TaskStep> stepQueue = stepScheduler.getRunningQueue();
        for (TaskStep step : stepQueue) {
            onlineDataViewDto.getTaskSteps().add(taskStepMapper.toDto(step));
        }
        LinkedBlockingQueue<TaskStep> stepPendingQueue = stepScheduler.getSuspendQueue();
        for (TaskStep step : stepPendingQueue) {
            onlineDataViewDto.getTaskSteps().add(taskStepMapper.toDto(step));
        }

        // 查询在线动作
        ConcurrentHashMap<Long, StationQueue> stationMap = actionScheduler.getStationMap();
        for (StationQueue stationQueue : stationMap.values()) {
            LinkedBlockingQueue<TaskAction> actionQueue = stationQueue.getTaskActionsQueue();
            for (TaskAction action : actionQueue) {
                onlineDataViewDto.getTaskActions().add(taskActionMapper.toDto(action));
                // 查询在线动作的指令完成情况, READY,RUNNING,SUCCESS,FAILED
                for (TaskCommand cmd : action.getCommands()) {
                    onlineDataViewDto.getTaskCommands().add(taskCommandMapper.toDto(cmd));
                }
            }
            LinkedBlockingQueue<TaskAction> actionPendingQueue = stationQueue.getPendingTaskActionsQueue();
            for (TaskAction action : actionPendingQueue) {
                onlineDataViewDto.getTaskActions().add(taskActionMapper.toDto(action));
            }
        }

        return new ResponseEntity<>(onlineDataViewDto, HttpStatus.OK);
    }

    private void progressTaskActionsInStep(TaskStep step, AtomicInteger yIndexBuf, Task task, List<Object[]> taskDefDataList, List<Object[]> taskItemDataList, List<Object[]> taskStepsItemDataList) {
        for (TaskAction action : step.getActions()) {
            if (!StringUtils.equalsAny(action.getAsyncMode(),"Y")) {
                continue;
            }
            int yIndex  = yIndexBuf.incrementAndGet();
            Object[] actionDefData = new Object[]{yIndex, action.getId(),action.getName(),"ACTION",0, task.getId() };
            taskDefDataList.add(actionDefData);

            long startTime = task.getScheduleEntryTime() + step.getEvaluateExecutingStart();
            if (action.getExecutedStart() != null){
                startTime = task.getScheduleEntryTime() + action.getExecutedStart();
            }

            Long duration =  (action.getExecutedDuration()!=null? action.getExecutedDuration(): action.getEvaluateExecutingDuration());
            if (duration == null){
                duration = System.currentTimeMillis()-startTime;
            }

            Optional<Task> taskOpt =  taskScheduler.getTaskById(task.getId());
            if (taskOpt.isPresent() && ! StringUtils.equalsAny(task.getStatus(), RunStatusEnum.CANCELLED.name(), RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name() ) ){
                duration = Math.max(duration.longValue(), System.currentTimeMillis()-startTime);
            }

            long endTime = startTime + duration.longValue();
            Object[] actionData = new Object[]{yIndex, action.getId(), step.getId(),"ACTION",action.getName(), action.getStatus(),10,startTime, endTime, task.getId(), task.getTaskNumber(),task.getTaskName()};
            taskItemDataList.add(actionData);
            taskStepsItemDataList.add(actionData);

            List<Object[]> actionDataList = new ArrayList<>();
            actionDataList.add(actionData);
            actionDefData[4] = caculateProgress(actionDataList);
        }
    }

    private int caculateProgress(List<Object[]> itemDataList) {
        int out = 0;
        long curTime = System.currentTimeMillis();
        long minStartTime = curTime;
        long maxEndTime = curTime;
        for (Object[] field : itemDataList) {
            long startTime = (Long)field[7];
            long endTime = (Long)field[8];
            minStartTime = Math.min(minStartTime, startTime);
            maxEndTime = Math.max(maxEndTime, endTime);
        }
        long range = maxEndTime - minStartTime;
        if (range == 0L){
            return 0;
        }
        double ratio = ((double)(curTime - minStartTime))/((double) range) ;

        int progress =  (int) Math.floor(ratio * (double)100);
        if (progress>100){
            progress = 100;
        }

        return progress;
    }

    private long getStepExecutingStartTime(TaskStep taskStep){
        return (taskStep.getExecutedStart() !=null? taskStep.getExecutedStart(): taskStep.getEvaluateExecutingStart());
    }

    private long getStepExecutingDuringTime(TaskStep taskStep){
        return (taskStep.getExecutedDuration()!=null? taskStep.getExecutedDuration(): taskStep.getEvaluateExecutingDuration());
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/querySchedulerGanttDataDeamon")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object>  querySchedulerGanttDataDeamon(HttpServletResponse response, TaskQueryCriteria criteria) throws IOException {

          String dataStr = FileUtil.readString("D:/robot/references/airports.txt","utf8");
         return new ResponseEntity<>(JSON.parseObject(dataStr),HttpStatus.OK);
    }

    @PostMapping("/setFavoriteScheduler")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> setFavoriteScheduler(@Validated @RequestBody Task resources){
        return new ResponseEntity<>(taskService.setFavoriteScheduler(resources),HttpStatus.OK);
    }

    @GetMapping("/queryForSelection")
    @ApiOperation("查询任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> queryForSelection(TaskQueryCriteria criteria){
        return new ResponseEntity<>(taskService.queryForSelection(criteria),HttpStatus.OK);
    }

    @GetMapping("/getStatistics")
    @ApiOperation("查询任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> getStatistics(){
        return new ResponseEntity<>(taskService.getStatistics(),HttpStatus.OK);
    }

    @GetMapping("/getRunningStatistics")
    @ApiOperation("查询任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> getRunningStatistics(){
        return new ResponseEntity<>(taskService.getRunningStatistics(),HttpStatus.OK);
    }

    @GetMapping("/getCurrentTaskGlobalInfo")
    @ApiOperation("查询任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> getCurrentTaskGlobalInfo(){
        return new ResponseEntity<>(TaskGlobalConfig.getInstance().getInfoDto(),HttpStatus.OK);
    }

    @PutMapping("/setStarted")
    @Log("修改任务管理")
    @ApiOperation("修改任务管理")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> setStarted(@Validated @RequestBody TaskGlobalInfoDto resources){
        TaskGlobalConfig.getInstance().setStarted(resources.isStarted());
        return new ResponseEntity<>(TaskGlobalConfig.getInstance().getInfoDto(),HttpStatus.OK);
    }



}