/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.modules.system.utils.RequestHolder;
import org.cjc.les.mgt.domain.ShopInfo;
import org.cjc.les.mgt.service.ShopInfoService;
import org.cjc.les.mgt.service.dto.ShopInfoQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-05-29
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "店铺管理")
@RequestMapping("/api/shopInfo")
public class ShopInfoController {

    private final ShopInfoService shopInfoService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('shopInfo:list')")
    public void exportShopInfo(HttpServletResponse response, ShopInfoQueryCriteria criteria) throws IOException {
        shopInfoService.download(shopInfoService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询店铺管理")
    @ApiOperation("查询店铺管理")
    @PreAuthorize("@el.check('shopInfo:list')")
    public ResponseEntity<Object> queryShopInfo(ShopInfoQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(shopInfoService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增店铺管理")
    @ApiOperation("新增店铺管理")
    @PreAuthorize("@el.check('shopInfo:add')")
    public ResponseEntity<Object> createShopInfo(@Validated @RequestBody ShopInfo resources){
        return new ResponseEntity<>(shopInfoService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改店铺管理")
    @ApiOperation("修改店铺管理")
    @PreAuthorize("@el.check('shopInfo:edit')")
    public ResponseEntity<Object> updateShopInfo(@Validated @RequestBody ShopInfo resources){
        shopInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除店铺管理")
    @ApiOperation("删除店铺管理")
    @PreAuthorize("@el.check('shopInfo:del')")
    public ResponseEntity<Object> deleteShopInfo(@RequestBody Long[] ids) {
        shopInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/queryShopShortListByUserId")
    @Log("查询当前登录用户所有店铺列表")
    @ApiOperation("查询当前登录用户所有店铺列表")
    @PreAuthorize("@el.check('shopInfo:list')")
    public ResponseEntity<Object> queryShopShortListByUserId(ShopInfoQueryCriteria criteria){
        return new ResponseEntity<>(shopInfoService.queryShopShortListByUserId(criteria),HttpStatus.OK);
    }


    @ApiOperation("查询当前在线店铺详情")
    @GetMapping(value = "/getCurShopDetail")
    @PreAuthorize("@el.check('shopInfo:list')")
    public ResponseEntity<Object> getCurShopDetail(){
        Long shopId = RequestHolder.getOnlineUser().getShopId();
        return new ResponseEntity<>(shopInfoService.findById(shopId), HttpStatus.OK);
    }

    @PostMapping(value = "/batchAuditShopInfo")
    @Log("批量审核店铺信息")
    @ApiOperation("批量审核店铺信息")
    @PreAuthorize("@el.check('shopInfo:batchAudit')")
    public ResponseEntity<Object> batchAuditShopInfo(@Validated @RequestBody List<ShopInfo> resources) {
        shopInfoService.batchAuditShopInfo(resources);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("获取当前店铺设置")
    @GetMapping(value = "/getCurShopSetting")
    public ResponseEntity<Object> getCurShopSetting(){
        Long shopId = RequestHolder.getOnlineUser().getShopId();
        return new ResponseEntity<>(shopInfoService.findById(shopId), HttpStatus.OK);
    }
}