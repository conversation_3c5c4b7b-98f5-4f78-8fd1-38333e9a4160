/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-31
**/
@Data
public class StatsResultFeatureLineChartItemDto implements Serializable {
    /**
     * 序列名称
     */
    private String featureName;
    /**
     * 序列时间
     */
    private Timestamp fCreateTime;
    /**
     * 统计数
     */
    private Long cnt;
    /**
     * 平均值
     */
    private BigDecimal avgValue;
    /**
     * 最小值
     */
    private BigDecimal minValue;
    /**
     * 最大值
     */
    private BigDecimal maxValue;
}