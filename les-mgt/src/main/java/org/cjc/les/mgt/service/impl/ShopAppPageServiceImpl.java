/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import org.cjc.les.mgt.domain.ShopAppPage;
import org.cjc.les.mgt.service.ShopAppPageService;
import org.cjc.les.mgt.service.mapstruct.ShopAppPageMapper;
import lombok.RequiredArgsConstructor;
import org.cjc.les.mgt.repository.ShopAppPageRepository;
import org.cjc.les.mgt.service.dto.ShopAppPageDto;
import org.cjc.les.mgt.service.dto.ShopAppPageQueryCriteria;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-03-21
**/
@Service
@RequiredArgsConstructor
public class ShopAppPageServiceImpl implements ShopAppPageService {

    private final ShopAppPageRepository pageRepository;
    private final ShopAppPageMapper pageMapper;

    @Override
    public Map<String,Object> queryAll(ShopAppPageQueryCriteria criteria, Pageable pageable){
        Page<ShopAppPage> page = pageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(pageMapper::toDto));
    }

    @Override
    public List<ShopAppPageDto> queryAll(ShopAppPageQueryCriteria criteria){
        return pageMapper.toDto(pageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public ShopAppPageDto findById(Long pageId) {
        ShopAppPage page = pageRepository.findById(pageId).orElseGet(ShopAppPage::new);
        ValidationUtil.isNull(page.getPageId(),"Page","pageId",pageId);
        return pageMapper.toDto(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopAppPageDto create(ShopAppPage resources) {
        return pageMapper.toDto(pageRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ShopAppPage resources) {
        ShopAppPage page = pageRepository.findById(resources.getPageId()).orElseGet(ShopAppPage::new);
        ValidationUtil.isNull( page.getPageId(),"Page","id",resources.getPageId());
        page.copy(resources);
        pageRepository.save(page);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long pageId : ids) {
            pageRepository.deleteById(pageId);
        }
    }

    @Override
    public void download(List<ShopAppPageDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ShopAppPageDto page : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("页面类型(10首页 20自定义页)", page.getPageType());
            map.put("页面名称", page.getPageName());
            map.put("页面数据", page.getPageData());
            map.put("店铺ID", page.getShopId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}