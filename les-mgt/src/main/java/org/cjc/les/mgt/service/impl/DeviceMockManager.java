/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.mgt.domain.DeviceMock;
import org.cjc.les.mgt.service.dto.DeviceMockDto;
import org.cjc.websocket.MsgType;
import org.cjc.websocket.SocketMsg;
import org.cjc.websocket.WebSocketServer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
@Component
public class DeviceMockManager implements DisposableBean {

    private HashMap<DeviceMock, DeviceMockWrapper> deviceMockMap = new HashMap<>();

    public void start(DeviceMock deviceMock) {
        if (deviceMockMap.containsKey(deviceMock)){
            log.warn("Mock has been started. mock="+deviceMock);
            return;
        }
        try {
            // 创建ProcessBuilder对象并设置要执行的命令
            ProcessBuilder processBuilder = null;
            if (StringUtils.isNotEmpty(deviceMock.getJavaClassName())){
                // 获取当前进程的 classpath
                String classpath = System.getProperty("java.class.path");
                if (StringUtils.isNotEmpty(deviceMock.getJavaClasspath())){
                    classpath = deviceMock.getJavaClasspath();
                }
                // 构建子进程的命令行参数
                List<String> command = new ArrayList<>();
                command.add(System.getProperty("java.home") + "/bin/java"); // Java 执行程序的路径

                if (StringUtils.isNotEmpty(deviceMock.getConfig())){

                    List<String> args = splitCommandLine(deviceMock.getConfig());
                    for (String arg : args ) {
                        if (StringUtils.isEmpty(arg)){
                            continue;
                        }
                        command.add(arg);
                    }
                }

                command.add("-cp");
                command.add(classpath);

                command.add(deviceMock.getJavaClassName()); // 子进程的主类

                processBuilder = new ProcessBuilder(command);
            }else {
                List<String> args = splitCommandLine(deviceMock.getConfig());
                List<String> cmdLineArr = new ArrayList<>();
                cmdLineArr.add(deviceMock.getCommandLine());
                cmdLineArr.addAll(args);
                processBuilder = new ProcessBuilder(cmdLineArr);
                // processBuilder = new ProcessBuilder("cmd.exe", "/c", deviceMock.getCommandLine());
            }
            DeviceMockWrapper mockWrapper = new DeviceMockWrapper();
            // 启动进程
            Process process = processBuilder.start();
            mockWrapper.setProcess(process);

            // 获取进程的输入流（标准输出）
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), Charset.forName("GBK")));

            // 获取进程的错误流（错误输出）
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), Charset.forName("GBK")));

            // 获取进程的输出流（用于输入命令）
            OutputStream outputStream = process.getOutputStream();

            // 创建一个新线程来读取标准输出
            Thread outputThread = new Thread(() -> {
                String line;
                try {
                    while ((line = reader.readLine()) != null) {
                        log.info("[{}]OUTPUT: {}",deviceMock.getName(), line);
                        mockWrapper.getLogs().add(line);
                        if (StringUtils.startsWith(line, "[PROMPT]")){
                            String prompt = line.substring("[PROMPT]".length());
                            mockWrapper.getPromptMap().put(prompt, prompt);
                        }else if (StringUtils.startsWith(line, "[PROMPT_RESP]")){
                            String prompt = line.substring("[PROMPT_RESP]".length(), line.indexOf("@END") );
                            mockWrapper.getPromptMap().remove(prompt);
                        }
                        sendLogMsg(deviceMock, MsgType.INFO);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            mockWrapper.setOutputThread(outputThread);

            // 创建一个新线程来读取错误输出
            Thread errorThread = new Thread(() -> {
                String line;
                try {
                    while ((line = errorReader.readLine()) != null) {
                        log.error("[{}]ERROR: {}",deviceMock.getName(), line);
                        mockWrapper.getLogs().add("ERROR: "+ line);
                        sendLogMsg(deviceMock, MsgType.ERROR);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            mockWrapper.setErrorThread(errorThread);

            // 启动线程
            outputThread.start();
            errorThread.start();

            deviceMockMap.put(deviceMock, mockWrapper);
            fillStatus(deviceMock);

            // 处理控制台输入
            //  Scanner scanner = new Scanner(System.in);
            //while (scanner.hasNextLine()) {
            //     String input = scanner.nextLine();
            //     outputStream.write((input + "\n").getBytes());
            //     outputStream.flush();
            // }

            // 等待进程完成
           // process.waitFor();

        } catch (IOException e) {
            log.error("Mock start error: {}", e.getMessage(), e);
        }
    }

    public void stop(DeviceMock deviceMock) {
        DeviceMockWrapper mockWrapper = deviceMockMap.get(deviceMock);
        if (mockWrapper==null){
            log.error("Could not found deviceMock:"+deviceMock);
            return;
        }
        long pid = getPid(mockWrapper.getProcess());
        mockWrapper.getProcess().destroyForcibly();
        if (pid>0){
            try {
                String osName = System.getProperty("os.name").toLowerCase();
                if (osName.contains("windows")) {
                    Runtime.getRuntime().exec("taskkill /T /F /PID " + pid);
                } else if (osName.contains("linux")) {
                    Runtime.getRuntime().exec("kill -9 " + pid);
                } else {
                    Runtime.getRuntime().exec("kill -9 " + pid);
                }

            } catch (IOException e) {
                e.printStackTrace();
                log.error("taskkill error:{}", e.getMessage(), e);
            }
        }
        deviceMockMap.remove(deviceMock);
        fillStatus(deviceMock);
    }

/*
    private int getPid(Process process) {
        try {
            // 通过反射获取 Process 对象的私有字段 "pid"
            Field field = process.getClass().getDeclaredField("pid");
            field.setAccessible(true);

            int pid = (int) field.get(process);
            return pid;
        } catch (IllegalAccessException | NoSuchFieldException e) {
            log.error("getPid error: {}",e.getMessage());
        }
        return -1;
    }
*/
    public static long getPid(Process process) {
        try {
            // 针对 UNIX/Linux
            if (process.getClass().getName().equals("java.lang.UNIXProcess")) {
                Field pidField = process.getClass().getDeclaredField("pid");
                pidField.setAccessible(true);
                return pidField.getInt(process);
            }

            // 针对 Windows
            if (process.getClass().getName().equals("java.lang.ProcessImpl")) {
                Field handleField = process.getClass().getDeclaredField("handle");
                handleField.setAccessible(true);
                long handle = handleField.getLong(process);

                // 你可以在这里进一步使用 JNA 或 JNI 获取 Windows PID（复杂且不推荐）
                // 示例代码省略
                return handle; // 实际并非 PID，需借助额外库处理
            }
        } catch (Exception e) {
            throw new RuntimeException("无法获取进程 PID", e);
        }
        throw new UnsupportedOperationException("未知的 Process 实现类型: " + process.getClass().getName());
    }
    public void fillStatus(DeviceMock deviceMock) {
        DeviceMockWrapper mockWrapper = deviceMockMap.get(deviceMock);
        if (mockWrapper==null){
            deviceMock.setStatus(RunStatusEnum.STOPPED.name());
        }else{
            boolean isAlive = mockWrapper.getProcess().isAlive();
            if (isAlive) {
                deviceMock.setStatus(RunStatusEnum.RUNNING.name());
            }else{
                deviceMock.setStatus(RunStatusEnum.STOPPED.name());
                deviceMockMap.remove(deviceMock);
            }
        }
    }

    public void fillLog(DeviceMock deviceMock) {
        DeviceMockWrapper mockWrapper = deviceMockMap.get(deviceMock);
        if (mockWrapper == null) {
            deviceMock.setLog("");
        } else {
            deviceMock.setLog(mockWrapper.getLogs().toString());
            if ( !mockWrapper.getPromptMap().isEmpty() ) {
                deviceMock.setPrompts(mockWrapper.getPromptMap().keySet().stream().collect(Collectors.toList()));
            }
        }

    }

    public void inputScanner(DeviceMock deviceMock, String inputs) {
        DeviceMockWrapper mockWrapper = deviceMockMap.get(deviceMock);
        if (mockWrapper == null) {
            log.error("Could not started device: {}", deviceMock);
            return;
        }
        String inStr = inputs + "\r\n";
        OutputStream os = mockWrapper.getProcess().getOutputStream();
        try {
            os.write(inStr.getBytes());
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("IOException in inputScanner, error: {}", e.getMessage());
        }

    }

    public static List<String> splitCommandLine(String commandLine) {
        List<String> tokens = new ArrayList<>();
        if (StringUtils.isEmpty(commandLine)){
            return tokens;
        }
        Matcher matcher = Pattern.compile("([^\"]\\S*|\"[^\"]*\")\\s*").matcher(commandLine);

        while (matcher.find()) {
            tokens.add(matcher.group(1).replace("\"", "")); // 移除引号
        }

        return tokens;
    }

    private void sendLogMsg(DeviceMock deviceMock, MsgType msgType) {
        DeviceMockWrapper mockWrapper = deviceMockMap.get(deviceMock);
        if (mockWrapper == null) {
            log.error("Could not started device: {}", deviceMock);
            return;
        }

        DeviceMockDto dto = new DeviceMockDto();
        dto.setId(deviceMock.getId());
        dto.setLog(mockWrapper.getLogs().toString());
        mockWrapper.getPromptMap().forEach((k, v)->{
            dto.getPrompts().add(k);
        });
        SocketMsg msg = new SocketMsg(JSON.toJSONString(dto), msgType);
        try {
            WebSocketServer.sendInfo(msg, new String[]{"deviceMockLog"}, true);
        } catch (IOException e) {
            log.error("sendPrompt error, message={}", e.getMessage(), e);
        }

    }

    @Override
    public void destroy() throws Exception {
        log.info("DeviceMockManager destroy devices.size:{} ...",deviceMockMap.size());
        List<DeviceMock> deviceMocks = new ArrayList<>();
        Iterator<DeviceMock> itrKeys = deviceMockMap.keySet().iterator();
        while(itrKeys.hasNext()){
            DeviceMock deviceMock = itrKeys.next();
            deviceMocks.add(deviceMock);
        }
        for (DeviceMock deviceMock : deviceMocks){
            stop(deviceMock);
        }
        log.info("DeviceMockManager destroyed DONE devices.size:{} ...",deviceMockMap.size());
    }
}
