/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import org.cjc.les.mgt.service.ShopUniappSettingService;
import org.cjc.les.mgt.constants.UniAppTypeEnum;
import org.cjc.les.mgt.domain.ShopUniappSetting;
import lombok.RequiredArgsConstructor;
import org.cjc.les.mgt.repository.ShopUniappSettingRepository;
import org.cjc.les.mgt.service.dto.ShopUniappSettingDto;
import org.cjc.les.mgt.service.dto.ShopUniappSettingQueryCriteria;
import org.cjc.les.mgt.service.mapstruct.ShopUniappSettingMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-03-11
**/
@Service
@RequiredArgsConstructor
public class ShopUniappSettingServiceImpl implements ShopUniappSettingService {

    private final ShopUniappSettingRepository shopUniappSettingRepository;
    private final ShopUniappSettingMapper shopUniappSettingMapper;

    @Override
    public Map<String,Object> queryAll(ShopUniappSettingQueryCriteria criteria, Pageable pageable){
        Page<ShopUniappSetting> page = shopUniappSettingRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(shopUniappSettingMapper::toDto));
    }

    @Override
    public List<ShopUniappSettingDto> queryAll(ShopUniappSettingQueryCriteria criteria){
        return shopUniappSettingMapper.toDto(shopUniappSettingRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public ShopUniappSettingDto findById(Long uniappSettingId) {
        ShopUniappSetting shopUniappSetting = shopUniappSettingRepository.findById(uniappSettingId).orElseGet(ShopUniappSetting::new);
        ValidationUtil.isNull(shopUniappSetting.getUniappSettingId(),"ShopUniappSetting","uniappSettingId",uniappSettingId);
        return shopUniappSettingMapper.toDto(shopUniappSetting);
    }

    @Override
    public ShopUniappSettingDto findByShopId(Long shopId, UniAppTypeEnum appType) {
        ShopUniappSetting shopUniappSetting = shopUniappSettingRepository.getShopUniappSettingByShopId(shopId, appType.name());
        ValidationUtil.isNull(shopUniappSetting.getUniappSettingId(),"ShopUniappSetting","uniappSettingId",shopUniappSetting.getUniappSettingId());
        return shopUniappSettingMapper.toDto(shopUniappSetting);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopUniappSettingDto create(ShopUniappSetting resources) {
        return shopUniappSettingMapper.toDto(shopUniappSettingRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ShopUniappSetting resources) {
        ShopUniappSetting shopUniappSetting = shopUniappSettingRepository.findById(resources.getUniappSettingId()).orElseGet(ShopUniappSetting::new);
        ValidationUtil.isNull( shopUniappSetting.getUniappSettingId(),"ShopUniappSetting","id",resources.getUniappSettingId());
        shopUniappSetting.copy(resources);
        shopUniappSettingRepository.save(shopUniappSetting);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long uniappSettingId : ids) {
            shopUniappSettingRepository.deleteById(uniappSettingId);
        }
    }

    @Override
    public void download(List<ShopUniappSettingDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ShopUniappSettingDto shopUniappSetting : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("店铺ID", shopUniappSetting.getShopId());
            map.put("租户ID", shopUniappSetting.getTenantId());
            map.put("客户端APP类型: WX, ALIPAY,BAIDU...", shopUniappSetting.getAppType());
            map.put("详细设置，JSON格式", shopUniappSetting.getSetting());
            map.put("创建人", shopUniappSetting.getCreateBy());
            map.put("创建时间", shopUniappSetting.getCreateTime());
            map.put("更新人", shopUniappSetting.getUpdateBy());
            map.put("更新时间", shopUniappSetting.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}