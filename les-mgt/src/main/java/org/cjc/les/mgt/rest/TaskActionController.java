/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.TaskAction;
import org.cjc.les.core.service.TaskActionService;
import org.cjc.les.core.service.dto.TaskActionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务运行的步骤管理")
@RequestMapping("/api/taskAction")
public class TaskActionController {

    private final TaskActionService taskActionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskAction:list')")
    public void exportTaskAction(HttpServletResponse response, TaskActionQueryCriteria criteria) throws IOException {
        taskActionService.download(taskActionService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务运行的步骤")
    @ApiOperation("查询任务运行的步骤")
    @PreAuthorize("@el.check('taskAction:list')")
    public ResponseEntity<Object> queryTaskAction(TaskActionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskActionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务运行的步骤")
    @ApiOperation("新增任务运行的步骤")
    @PreAuthorize("@el.check('taskAction:add')")
    public ResponseEntity<Object> createTaskAction(@Validated @RequestBody TaskAction resources){
        return new ResponseEntity<>(taskActionService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务运行的步骤")
    @ApiOperation("修改任务运行的步骤")
    @PreAuthorize("@el.check('taskAction:edit')")
    public ResponseEntity<Object> updateTaskAction(@Validated @RequestBody TaskAction resources){
        taskActionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务运行的步骤")
    @ApiOperation("删除任务运行的步骤")
    @PreAuthorize("@el.check('taskAction:del')")
    public ResponseEntity<Object> deleteTaskAction(@RequestBody Long[] ids) {
        taskActionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}