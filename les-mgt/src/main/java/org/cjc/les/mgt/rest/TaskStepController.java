/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.TaskStep;
import org.cjc.les.core.service.TaskStepService;
import org.cjc.les.core.service.dto.TaskStepQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务运行的步骤管理")
@RequestMapping("/api/taskStep")
public class TaskStepController {

    private final TaskStepService taskStepService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskStep:list')")
    public void exportTaskStep(HttpServletResponse response, TaskStepQueryCriteria criteria) throws IOException {
        taskStepService.download(taskStepService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务运行的步骤")
    @ApiOperation("查询任务运行的步骤")
    @PreAuthorize("@el.check('taskStep:list')")
    public ResponseEntity<Object> queryTaskStep(TaskStepQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskStepService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务运行的步骤")
    @ApiOperation("新增任务运行的步骤")
    @PreAuthorize("@el.check('taskStep:add')")
    public ResponseEntity<Object> createTaskStep(@Validated @RequestBody TaskStep resources){
        return new ResponseEntity<>(taskStepService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务运行的步骤")
    @ApiOperation("修改任务运行的步骤")
    @PreAuthorize("@el.check('taskStep:edit')")
    public ResponseEntity<Object> updateTaskStep(@Validated @RequestBody TaskStep resources){
        taskStepService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务运行的步骤")
    @ApiOperation("删除任务运行的步骤")
    @PreAuthorize("@el.check('taskStep:del')")
    public ResponseEntity<Object> deleteTaskStep(@RequestBody Long[] ids) {
        taskStepService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}