/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Step;
import org.cjc.les.core.service.StepService;
import org.cjc.les.core.service.dto.StepQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "步骤管理管理")
@RequestMapping("/api/step")
public class StepController {

    private final StepService stepService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('step:list')")
    public void exportStep(HttpServletResponse response, StepQueryCriteria criteria) throws IOException {
        stepService.download(stepService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询步骤管理")
    @ApiOperation("查询步骤管理")
    @PreAuthorize("@el.check('step:list')")
    public ResponseEntity<Object> queryStep(StepQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(stepService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增步骤管理")
    @ApiOperation("新增步骤管理")
    @PreAuthorize("@el.check('step:add')")
    public ResponseEntity<Object> createStep(@Validated @RequestBody Step resources){
        return new ResponseEntity<>(stepService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改步骤管理")
    @ApiOperation("修改步骤管理")
    @PreAuthorize("@el.check('step:edit')")
    public ResponseEntity<Object> updateStep(@Validated @RequestBody Step resources){
        stepService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除步骤管理")
    @ApiOperation("删除步骤管理")
    @PreAuthorize("@el.check('step:del')")
    public ResponseEntity<Object> deleteStep(@RequestBody Long[] ids) {
        stepService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}