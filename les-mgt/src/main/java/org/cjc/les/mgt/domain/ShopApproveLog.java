/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-07-10
**/
@Entity
@Data
@Table(name="shop_approve_log")
public class ShopApproveLog implements Serializable {

    @Column(name = "shop_id")
    @Schema(description = "店铺ID")
    private Long shopId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Schema(description = "主键自增ID")
    private Long id;

    @Column(name = "audit_detail")
    @Schema(description = "审核详情描述")
    private String auditDetail;

    @Column(name = "audit_status")
    @Schema(description = "审核状态: 1:待审核,2:审核通过,3:审核失败")
    private String auditStatus;

    @Column(name = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    @Column(name = "create_time")
    @CreationTimestamp
    @Schema(description = "创建时间")
    private Timestamp createTime;

    @Column(name = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    @Column(name = "update_time")
    @UpdateTimestamp
    @Schema(description = "更新时间")
    private Timestamp updateTime;

    public void copy(ShopApproveLog source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}