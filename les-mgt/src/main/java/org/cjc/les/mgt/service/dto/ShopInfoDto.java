/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-05-29
**/
@Data
public class ShopInfoDto implements Serializable {

    /** 店铺ID */
    private Long shopId;

    /** 店铺所属区域ID */
    private Long areaId;

    /** 租户ID */
    private Long tenantId;

    /** 店铺名称 */
    private String shopName;

    /** 地址 */
    private String address;

    /** 店铺所在纬度 */
    private String latitude;

    /** 店铺所在经度 */
    private String longitude;

    /** 描述 */
    private String description;

    /** 审核状态: 1:待审核,2:审核通过,3:审核失败 */
    private String auditStatus;

    /** 在线状态: 1:在线(营业中),2:下线(休息中), 3:停业 */
    private String onlineStatus;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}