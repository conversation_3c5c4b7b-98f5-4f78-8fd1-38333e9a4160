/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Command;
import org.cjc.les.core.service.CommandService;
import org.cjc.les.core.service.dto.CommandQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "命令管理管理")
@RequestMapping("/api/command")
public class CommandController {

    private final CommandService commandService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('command:list')")
    public void exportCommand(HttpServletResponse response, CommandQueryCriteria criteria) throws IOException {
        commandService.download(commandService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询命令管理")
    @ApiOperation("查询命令管理")
    @PreAuthorize("@el.check('command:list')")
    public ResponseEntity<Object> queryCommand(CommandQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(commandService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增命令管理")
    @ApiOperation("新增命令管理")
    @PreAuthorize("@el.check('command:add')")
    public ResponseEntity<Object> createCommand(@Validated @RequestBody Command resources){
        return new ResponseEntity<>(commandService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改命令管理")
    @ApiOperation("修改命令管理")
    @PreAuthorize("@el.check('command:edit')")
    public ResponseEntity<Object> updateCommand(@Validated @RequestBody Command resources){
        commandService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除命令管理")
    @ApiOperation("删除命令管理")
    @PreAuthorize("@el.check('command:del')")
    public ResponseEntity<Object> deleteCommand(@RequestBody Long[] ids) {
        commandService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}