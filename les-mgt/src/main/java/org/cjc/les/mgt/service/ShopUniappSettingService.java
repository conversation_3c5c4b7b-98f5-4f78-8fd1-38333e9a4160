/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.constants.UniAppTypeEnum;
import org.cjc.les.mgt.domain.ShopUniappSetting;
import org.cjc.les.mgt.service.dto.ShopUniappSettingDto;
import org.cjc.les.mgt.service.dto.ShopUniappSettingQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-03-11
**/
public interface ShopUniappSettingService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ShopUniappSettingQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ShopUniappSettingDto>
    */
    List<ShopUniappSettingDto> queryAll(ShopUniappSettingQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param uniappSettingId ID
     * @return ShopUniappSettingDto
     */
    ShopUniappSettingDto findById(Long uniappSettingId);

    /**
     * 根据shopID查询
     * @param shopId ID
     * @param appType APP类型
     * @return ShopUniappSettingDto
     */
    ShopUniappSettingDto findByShopId(Long shopId, UniAppTypeEnum appType);

    /**
    * 创建
    * @param resources /
    * @return ShopUniappSettingDto
    */
    ShopUniappSettingDto create(ShopUniappSetting resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ShopUniappSetting resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ShopUniappSettingDto> all, HttpServletResponse response) throws IOException;
}