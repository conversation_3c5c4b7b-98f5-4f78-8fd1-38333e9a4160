/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.cjc.annotation.Log;
import org.cjc.les.core.service.MotionService;
import org.cjc.les.core.service.dto.MotionInfoDto;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
* @website https:/www.ximei.com
* <AUTHOR>
* @date 2024-10-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "机器人管理")
@RequestMapping("/api/motion")
public class MotionController {

    private final MotionService motionService;

    @GetMapping(value = "/getCurrentMotionInfo")
    @ApiOperation("获取当前运动信息")
   // @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> getCurrentMotionInfo(MotionInfoDto dto){

        return new ResponseEntity<>(motionService.getCurrentMotionInfo(dto),HttpStatus.OK);
    }

    @GetMapping(value = "/getMotionInfos")
    @ApiOperation("获取当前运动信息")
    // @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> getMotionInfos(){

        return new ResponseEntity<>(motionService.getMotionInfos(),HttpStatus.OK);
    }
    @PostMapping(value = "/changeSpeed")
    @Log("改变运动速度")
    @ApiOperation("改变运动速度")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> changeSpeed(@RequestBody MotionInfoDto dto){

        return new ResponseEntity<>(motionService.changeSpeed(dto),HttpStatus.CREATED);
    }

    @PostMapping(value = "/openDoor")
    @Log("设备关门")
    @ApiOperation("设备关门")
    @PreAuthorize("@el.check('task:list')")
    public ResponseEntity<Object> openDoor(@RequestBody MotionInfoDto dto){

        return new ResponseEntity<>(motionService.openDoor(dto),HttpStatus.CREATED);
    }

}