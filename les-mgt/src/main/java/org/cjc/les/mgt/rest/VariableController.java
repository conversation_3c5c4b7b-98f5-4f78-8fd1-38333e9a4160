/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Variable;
import org.cjc.les.core.service.VariableService;
import org.cjc.les.core.service.dto.VariableQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-17
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "变量管理管理")
@RequestMapping("/api/variable")
public class VariableController {

    private final VariableService variableService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('variable:list')")
    public void exportVariable(HttpServletResponse response, VariableQueryCriteria criteria) throws IOException {
        variableService.download(variableService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询变量管理")
    @ApiOperation("查询变量管理")
    @PreAuthorize("@el.check('variable:list')")
    public ResponseEntity<Object> queryVariable(VariableQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(variableService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增变量管理")
    @ApiOperation("新增变量管理")
    @PreAuthorize("@el.check('variable:add')")
    public ResponseEntity<Object> createVariable(@Validated @RequestBody Variable resources){
        return new ResponseEntity<>(variableService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改变量管理")
    @ApiOperation("修改变量管理")
    @PreAuthorize("@el.check('variable:edit')")
    public ResponseEntity<Object> updateVariable(@Validated @RequestBody Variable resources){
        variableService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除变量管理")
    @ApiOperation("删除变量管理")
    @PreAuthorize("@el.check('variable:del')")
    public ResponseEntity<Object> deleteVariable(@RequestBody Long[] ids) {
        variableService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @GetMapping("/loadFromProcedureConfig")
    @Log("查询变量管理")
    @ApiOperation("查询变量管理")
    @PreAuthorize("@el.check('variable:list')")
    public ResponseEntity<Object> loadFromProcedureConfig(VariableQueryCriteria criteria){
        return new ResponseEntity<>(variableService.loadFromProcedureConfig(),HttpStatus.OK);
    }


}