/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Position;
import org.cjc.les.core.service.PositionService;
import org.cjc.les.core.service.dto.*;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-04-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "点位管理管理")
@RequestMapping("/api/position")
public class PositionController {

    private final PositionService positionService;

    @GetMapping("/queryPositionNamesForSelection")
    @Log("查询机器人点位配置")
    @ApiOperation("查询机器人点位配置")
    @PreAuthorize("@el.check('positionRobot:list')")
    public ResponseEntity<Object> queryPositionNamesForSelection(PositionSelectDto criteria ){
        return new ResponseEntity<>(positionService.queryPositionNamesForSelection(criteria),HttpStatus.OK);
    }

    @PutMapping("/changePositionStatus")
    @Log("修改设备布局")
    @ApiOperation("修改设备布局")
    @PreAuthorize("@el.check('deviceLayout:edit')")
    public ResponseEntity<Object> changePositionStatus(@Validated @RequestBody PositionStatusDto resources){
        return new ResponseEntity<>(positionService.changePositionStatus(resources), HttpStatus.OK);
    }

    @PutMapping("/move")
    @Log("修改设备布局")
    @ApiOperation("修改设备布局")
    @PreAuthorize("@el.check('deviceLayout:edit')")
    public ResponseEntity<Object> move(@Validated @RequestBody PositionMoveDto resources){
        return new ResponseEntity<>(positionService.move(resources), HttpStatus.OK);
    }



}