/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-10-30
**/
@Data
public class ReportDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 报告标题 */
    private ReportTitleDto title;

    /**
     * 报告样品信息
     */
    private ReportSampleDto sample;

    /**
     * 检测方法列表
     */
    private List<ReportMethodDto> methodList;

    /**
     * 检测结果列表
     */
    private List<ReportResultDto> resultList;

    /**
     * 报告结论
     */
    private ReportConclusionDto conclusion;

    /** 运行状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}