/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-05-29
**/
@Data
public class ShopSmallDto implements Serializable {

    /** 店铺ID */
    private Long shopId;

    /** 店铺所属区域ID */
    private Long areaId;

    /** 上级区域ID */
    private Long parentAreaId;

    /** 区域名称 */
    private String areaName;

    /** 店铺名称 */
    private String shopName;

    private List<ShopSmallDto> children;

    private int subCount = 0;

    public Boolean getHasChildren() {
        return subCount > 0;
    }

    public Boolean getLeaf() {
        return subCount <= 0;
    }

    public Long getId(){
        return shopId;
    }

    public String getLabel(){
        if (shopId==null ||shopId==0L){
            return areaName;
        }else{
            return shopName;
        }
    }

}