/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.service.dto.TaskPrepareViewDto;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-12-04
**/
public interface TaskPrepareViewService {

    /**
     * 获取当前任务预处理试图
     * @param mode 进样模式: TRIAL, FACTORY, SAMPLE
     */
    TaskPrepareViewDto getCurrentTaskPrepareView(String mode);

    /**
     * 提交标签阅读步骤
     *
     * @param dto
     */
    void submitTagReadingStep(TaskPrepareViewDto dto);

    /**
     * 提交进样接收步骤
     *
     * @param dto
     */
    void submitEntryConveyorAcceptingStep(TaskPrepareViewDto dto);

    /**
     * 撤销当前预处理任务
     * @param dto
     */
    void cancelCurrentTaskPrepareView(TaskPrepareViewDto dto);
}