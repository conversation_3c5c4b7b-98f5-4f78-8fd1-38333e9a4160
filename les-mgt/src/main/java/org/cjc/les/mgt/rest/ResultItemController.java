/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.ResultItem;
import org.cjc.les.core.service.ResultItemService;
import org.cjc.les.core.service.dto.ResultItemQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "结果项管理")
@RequestMapping("/api/resultItem")
public class ResultItemController {

    private final ResultItemService resultItemService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('resultItem:list')")
    public void exportResultItem(HttpServletResponse response, ResultItemQueryCriteria criteria) throws IOException {
        resultItemService.download(resultItemService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询结果项")
    @ApiOperation("查询结果项")
    @PreAuthorize("@el.check('resultItem:list')")
    public ResponseEntity<Object> queryResultItem(ResultItemQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(resultItemService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增结果项")
    @ApiOperation("新增结果项")
    @PreAuthorize("@el.check('resultItem:add')")
    public ResponseEntity<Object> createResultItem(@Validated @RequestBody ResultItem resources){
        return new ResponseEntity<>(resultItemService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改结果项")
    @ApiOperation("修改结果项")
    @PreAuthorize("@el.check('resultItem:edit')")
    public ResponseEntity<Object> updateResultItem(@Validated @RequestBody ResultItem resources){
        resultItemService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除结果项")
    @ApiOperation("删除结果项")
    @PreAuthorize("@el.check('resultItem:del')")
    public ResponseEntity<Object> deleteResultItem(@RequestBody Long[] ids) {
        resultItemService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}