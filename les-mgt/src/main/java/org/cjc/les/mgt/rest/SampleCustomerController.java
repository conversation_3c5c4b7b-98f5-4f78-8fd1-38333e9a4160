/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.cjc.annotation.Log;
import org.cjc.les.core.domain.SampleCustomer;
import org.cjc.les.core.service.SampleCustomerService;
import org.cjc.les.core.service.dto.SampleCustomerQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "样品客户管理管理")
@RequestMapping("/api/sampleCustomer")
public class SampleCustomerController {

    private final SampleCustomerService sampleCustomerService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sampleCustomer:list')")
    public void exportSampleCustomer(HttpServletResponse response, SampleCustomerQueryCriteria criteria) throws IOException {
        sampleCustomerService.download(sampleCustomerService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询样品客户管理")
    @ApiOperation("查询样品客户管理")
    @PreAuthorize("@el.check('sampleCustomer:list')")
    public ResponseEntity<Object> querySampleCustomer(SampleCustomerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sampleCustomerService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/selectSampleCustomers")
    @Log("查询样品客户管理")
    @ApiOperation("查询样品客户管理")
    @PreAuthorize("@el.check('sample:list')")
    public ResponseEntity<Object> selectSampleCustomers(SampleCustomerQueryCriteria criteria){
        return new ResponseEntity<>(sampleCustomerService.queryAll(criteria),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增样品客户管理")
    @ApiOperation("新增样品客户管理")
    @PreAuthorize("@el.check('sampleCustomer:add')")
    public ResponseEntity<Object> createSampleCustomer(@Validated @RequestBody SampleCustomer resources){
        return new ResponseEntity<>(sampleCustomerService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改样品客户管理")
    @ApiOperation("修改样品客户管理")
    @PreAuthorize("@el.check('sampleCustomer:edit')")
    public ResponseEntity<Object> updateSampleCustomer(@Validated @RequestBody SampleCustomer resources){
        sampleCustomerService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除样品客户管理")
    @ApiOperation("删除样品客户管理")
    @PreAuthorize("@el.check('sampleCustomer:del')")
    public ResponseEntity<Object> deleteSampleCustomer(@RequestBody Long[] ids) {
        sampleCustomerService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}