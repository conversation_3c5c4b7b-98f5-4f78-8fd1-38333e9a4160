/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.domain.StatsResultFeature;
import org.cjc.les.mgt.service.dto.*;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-07-31
**/
public interface StatsResultFeatureService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(StatsResultFeatureQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<StatsResultFeatureDto>
    */
    List<StatsResultFeatureDto> queryAll(StatsResultFeatureQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return StatsResultFeatureDto
     */
    StatsResultFeatureDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return StatsResultFeatureDto
    */
    StatsResultFeatureDto create(StatsResultFeature resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(StatsResultFeature resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<StatsResultFeatureDto> all, HttpServletResponse response) throws IOException;

    /**
     * 同步检测数据
     */
    void syncData();

    /**
     * 查询检测值趋势图(折线图), 同时显示多个检测物featureName数据,
     * x轴显示创建时间featureCreateTime, y轴显示检测值featureValue,
     * @param criteria 查询条件
     * @return echarts折线图显示所需数据
     */
    StatsResultFeatureLineChartDto queryStatsResultFeatureValues(StatsResultFeatureQueryCriteria criteria);

    /**
     * 按小时查询检测结果特征值统计数据
     * 返回按小时分组的统计信息，包括平均值、最小值、最大值和计数
     * @param criteria 查询条件，支持时间范围、检测方法、样品分类等过滤条件
     * @return 按小时统计的检测结果特征值列表，用于生成统计图表
     */
    List<StatsResultFeatureLineChartItemDto> queryStatsResultFeatureValuesByHour(StatsResultFeatureQueryCriteria criteria);

    /**
     * 统计方法失败成功数
     * @param criteria 查询条件，支持时间范围、检测方法、样品分类等过滤条件
     * @return
     */
    List<StatsMethodChartItemDto> queryStatsMethods(StatsResultFeatureQueryCriteria criteria);

    /**
     * 统计结论数
     * @param criteria
     * @return
     */
    List<StatsConclusionChartItemDto> queryStatsConclusions(StatsResultFeatureQueryCriteria criteria);

    /**
     * 统计热力图
     * @param criteria
     * @return
     */
    List<StatsDateTimeChartItemDto> queryStatsDateTime(StatsResultFeatureQueryCriteria criteria);
}