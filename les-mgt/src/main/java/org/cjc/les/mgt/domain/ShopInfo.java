/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.cjc.modules.system.domain.User;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Set;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2022-05-29
 **/
@Entity
@Data
@Table(name = "shop_info")
public class ShopInfo implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "shop_id")
    @Schema(description = "店铺ID")
    private Long shopId;

    @Column(name = "area_id", nullable = false)
    @NotNull
    @Schema(description = "店铺所属区域ID")
    private Long areaId;

    @Column(name = "tenant_id", nullable = false)
    @NotNull
    @Schema(description = "租户ID")
    private Long tenantId = 0L;

    @Column(name = "shop_name")
    @Schema(description = "店铺名称")
    private String shopName;

    @Column(name = "address")
    @Schema(description = "地址")
    private String address;

    @Column(name = "latitude")
    @Schema(description = "店铺所在纬度")
    private String latitude;

    @Column(name = "longitude")
    @Schema(description = "店铺所在经度")
    private String longitude;

    @Column(name = "description")
    @Schema(description = "描述")
    private String description;

    @Column(name = "audit_status")
    @Schema(description = "审核状态: 1:待审核,2:审核通过,3:审核失败")
    private String auditStatus;

    @Column(name = "online_status")
    @Schema(description = "在线状态: 1:在线(营业中),2:下线(休息中), 3:停业")
    private String onlineStatus;

    @Column(name = "shop_category")
    @Schema(description = "店铺类别")
    private String shopCategory;

    @Column(name = "industry")
    @Schema(description = "业态")
    private String industry;

    @Column(name = "logo_image")
    @Schema(description = "店铺LOGO")
    private String logoImage;

    @Column(name = "shop_images")
    @Schema(description = "店铺照片")
    private String shopImages;

    @ManyToMany(fetch = FetchType.EAGER)
    @Schema(description = "店铺的账号用户")
    @JoinTable(name = "sys_user_shops",
            joinColumns = {@JoinColumn(name = "shop_id", referencedColumnName = "shop_id")},
            inverseJoinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "user_id")})
    private Set<User> users;

    @Column(name = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    @Column(name = "create_time")
    @CreationTimestamp
    @Schema(description = "创建时间")
    private Timestamp createTime;

    @Column(name = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    @Column(name = "update_time")
    @UpdateTimestamp
    @Schema(description = "更新时间")
    private Timestamp updateTime;

    public void copy(ShopInfo source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }
}