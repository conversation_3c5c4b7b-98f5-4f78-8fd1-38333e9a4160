/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-11
**/
@Entity
@Data
@Table(name="shop_uniapp_setting")
public class ShopUniappSetting implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "uniapp_setting_id")
    @Schema(description = "主键ID: shop_id+tenant_id")
    private Long uniappSettingId;

    @Column(name = "shop_id",nullable = false)
    @NotNull
    @Schema(description = "店铺ID")
    private Long shopId;

    @Column(name = "tenant_id",nullable = false)
    @NotNull
    @Schema(description = "租户ID")
    private Long tenantId;

    @Column(name = "app_type")
    @Schema(description = "客户端APP类型: WX, ALIPAY,BAIDU...")
    private String appType;

    @Column(name = "setting")
    @Schema(description = "详细设置，JSON格式")
    private String setting;

    @Column(name = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    @Column(name = "create_time")
    @Schema(description = "创建时间")
    private Timestamp createTime;

    @Column(name = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    @Column(name = "update_time")
    @Schema(description = "更新时间")
    private Timestamp updateTime;

    public void copy(ShopUniappSetting source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}