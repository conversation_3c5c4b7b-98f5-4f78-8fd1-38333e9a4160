/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import lombok.Data;

import java.util.concurrent.ConcurrentHashMap;

@Data
public class DeviceMockWrapper {

    private Process process;

    private Thread outputThread;

    private Thread errorThread;

    private FixedSizeLogQueue<String> logs = new FixedSizeLogQueue<String>(10);

    private ConcurrentHashMap<String, String> promptMap = new ConcurrentHashMap<>();

}
