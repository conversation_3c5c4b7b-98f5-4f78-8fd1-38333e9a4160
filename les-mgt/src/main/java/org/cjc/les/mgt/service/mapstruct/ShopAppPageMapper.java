/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.mgt.domain.ShopAppPage;
import org.cjc.les.mgt.service.dto.ShopAppPageDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-21
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ShopAppPageMapper extends BaseMapper<ShopAppPageDto, ShopAppPage> {

}