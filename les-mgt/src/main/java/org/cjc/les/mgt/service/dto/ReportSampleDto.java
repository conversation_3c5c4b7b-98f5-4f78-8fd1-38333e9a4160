/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-10-30
 **/
@Data
public class ReportSampleDto implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 样品类型
     */
    private String sampleType;

    /**
     * 样品数量
     */
    private String sampleCount;

    /**
     * 样品采集地
     */
    private String collectionAddress;

    /**
     * 送样日期（接收样品日期）
     */
    private String sampleAcceptationDate;

    /**
     * 委托单位地址
     */
    private String delegateCompanyAddress;

    /**
     * 委托单位名称
     */
    private String delegateCompany;

    /**
     * 委托人
     */
    private String delegator;

    /**
     * 委托人电话
     */
    private String telephone;

    /**
     * 分析日期
     */
    private String analyseDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运行状态
     */
    private String status;

    /**
     * 是否已被删除,Y/N
     */
    private String deleteFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}