/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-11
**/
@Data
public class ShopUniappSettingDto implements Serializable {

    /** 主键ID: shop_id+tenant_id */
    private Long uniappSettingId;

    /** 店铺ID */
    private Long shopId;

    /** 租户ID */
    private Long tenantId;

    /** 客户端APP类型: WX, ALIPAY,BAIDU... */
    private String appType;

    /** 详细设置，JSON格式 */
    private String setting;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}