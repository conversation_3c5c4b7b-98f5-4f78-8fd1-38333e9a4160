/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.repository;

import org.cjc.les.mgt.domain.StatsResultFeature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-31
**/
public interface StatsResultFeatureRepository extends JpaRepository<StatsResultFeature, Long>, JpaSpecificationExecutor<StatsResultFeature> {
    /**
    * 根据 ResultFeatureId 查询
    * @param result_feature_id /
    * @return /
    */
    Optional<StatsResultFeature> findByResultFeatureId(Long result_feature_id);
    /**
     * 查找最近一次更新的检测结果统计项
     * @return
     */
    @Query(value = "select * from les_stats_result_feature order by feature_create_time desc limit 1", nativeQuery = true)
    Optional<StatsResultFeature> findLatestOne();

    /**
     * 按小时统计检测结果特征值
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param methodName 检测方法名称（可选）
     * @param sampleCategory 样品分类（可选）
     * @return 统计结果
     */
    @Query(value = "select t.feature_name, CAST(DATE_FORMAT(t.feature_create_time, '%Y-%m-%d %H:00:00') AS DATETIME) as f_create_time, " +
            "count(1) as cnt, ROUND(AVG(CAST(t.feature_value AS DECIMAL(10,2))),2) as avg_value, " +
            "MIN(CAST(t.feature_value AS DECIMAL(10,2))) as min_value, MAX(CAST(t.feature_value AS DECIMAL(10,2))) as max_value " +
            "from les_stats_result_feature t " +
            "where t.delete_flag = 'N' " +
            "and (:beginTime is null or t.feature_create_time >= :beginTime) " +
            "and (:endTime is null or t.feature_create_time <= :endTime) " +
            "and (:methodName is null or t.method_name = :methodName) " +
            "and (:sampleCategory is null or t.sample_category = :sampleCategory) " +
            "group by t.feature_name, f_create_time " +
            "order by t.feature_name, f_create_time", nativeQuery = true)
    List<Map<String, Object>> findStatsByHour(@Param("beginTime") Timestamp beginTime,
                                              @Param("endTime") Timestamp endTime,
                                              @Param("methodName") String methodName,
                                              @Param("sampleCategory") String sampleCategory);

    @Query(value = "SELECT t.method_name, t.feature_conclusion, count(1) cnt " +
            "from les_stats_result_feature t " +
            "where t.delete_flag = 'N' " +
            "and (:beginTime is null or t.feature_create_time >= :beginTime) " +
            "and (:endTime is null or t.feature_create_time <= :endTime) " +
            "and (:methodName is null or t.method_name = :methodName) " +
            "and (:sampleCategory is null or t.sample_category = :sampleCategory) " +
            "group by t.method_name, t.feature_conclusion " +
            "order by t.method_name, t.feature_conclusion", nativeQuery = true)
    List<Map<String, Object>> findStatsAsMethod(@Param("beginTime") Timestamp beginTime,
                                                @Param("endTime") Timestamp endTime,
                                                @Param("methodName") String methodName,
                                                @Param("sampleCategory") String sampleCategory);

    @Query(value = "SELECT t.feature_conclusion, count(1) cnt " +
            "from les_stats_result_feature t " +
            "where t.delete_flag = 'N' " +
            "and (:beginTime is null or t.feature_create_time >= :beginTime) " +
            "and (:endTime is null or t.feature_create_time <= :endTime) " +
            "and (:methodName is null or t.method_name = :methodName) " +
            "and (:sampleCategory is null or t.sample_category = :sampleCategory) " +
            "group by t.feature_conclusion " +
            "order by t.feature_conclusion", nativeQuery = true)
    List<Map<String, Object>> findStatsAsConclusion(@Param("beginTime") Timestamp beginTime,
                                                @Param("endTime") Timestamp endTime,
                                                @Param("methodName") String methodName,
                                                @Param("sampleCategory") String sampleCategory);

    @Query(value = "SELECT CAST(DATE_FORMAT(t.feature_create_time, '%Y-%m-%d %H:00:00') AS DATETIME) as f_create_time, count(1) cnt " +
            "from les_stats_result_feature t " +
            "where t.delete_flag = 'N' " +
            "and (:beginTime is null or t.feature_create_time >= :beginTime) " +
            "and (:endTime is null or t.feature_create_time <= :endTime) " +
            "and (:methodName is null or t.method_name = :methodName) " +
            "and (:sampleCategory is null or t.sample_category = :sampleCategory) " +
            "group by f_create_time " +
            "order by f_create_time", nativeQuery = true)
    List<Map<String, Object>> findStatsAsDateTime(@Param("beginTime") Timestamp beginTime,
                                                    @Param("endTime") Timestamp endTime,
                                                    @Param("methodName") String methodName,
                                                    @Param("sampleCategory") String sampleCategory);
}