/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Method;
import org.cjc.les.core.service.MethodService;
import org.cjc.les.core.service.dto.MethodQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "方法管理管理")
@RequestMapping("/api/method")
public class MethodController {

    private final MethodService methodService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('method:list')")
    public void exportMethod(HttpServletResponse response, MethodQueryCriteria criteria) throws IOException {
        methodService.download(methodService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询方法管理")
    @ApiOperation("查询方法管理")
    @PreAuthorize("@el.check('method:list')")
    public ResponseEntity<Object> queryMethod(MethodQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(methodService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增方法管理")
    @ApiOperation("新增方法管理")
    @PreAuthorize("@el.check('method:add')")
    public ResponseEntity<Object> createMethod(@Validated @RequestBody Method resources){
        return new ResponseEntity<>(methodService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改方法管理")
    @ApiOperation("修改方法管理")
    @PreAuthorize("@el.check('method:edit')")
    public ResponseEntity<Object> updateMethod(@Validated @RequestBody Method resources){
        methodService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除方法管理")
    @ApiOperation("删除方法管理")
    @PreAuthorize("@el.check('method:del')")
    public ResponseEntity<Object> deleteMethod(@RequestBody Long[] ids) {
        methodService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}