/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.common.uniapp.UniappResponseEntity;
import org.cjc.les.constants.ErrorCodeEnum;
import org.cjc.les.constants.ErrorLevelEnum;
import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.domain.Task;
import org.cjc.les.core.domain.TaskPrepare;
import org.cjc.les.core.service.SampleService;
import org.cjc.les.core.service.dto.SampleDto;
import org.cjc.les.core.service.dto.SampleQueryCriteria;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.mapstruct.SampleMapper;
import org.cjc.les.core.service.mapstruct.TaskPrepareMapper;
import org.cjc.les.mgt.service.TaskPrepareViewService;
import org.cjc.les.mgt.service.dto.TaskPrepareViewDto;
import org.cjc.les.vo.CommandReturn;
import org.cjc.les.vo.CommonResponseEntity;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.Optional;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "样品管理管理")
@RequestMapping("/api/sample")
public class SampleController {

    private final SampleService sampleService;
    private final SampleMapper sampleMapper;

    private final TaskPrepareViewService taskPrepareViewService;
    private final TaskPrepareMapper taskPrepareMapper;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sample:list')")
    public void exportSample(HttpServletResponse response, SampleQueryCriteria criteria) throws IOException {
        sampleService.download(sampleService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询样品管理")
    @ApiOperation("查询样品管理")
    @PreAuthorize("@el.check('sample:list')")
    public ResponseEntity<Object> querySample(SampleQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sampleService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/querySamplesForSelection")
    @ApiOperation("查询样品管理")
    @PreAuthorize("@el.check('sample:list')")
    public ResponseEntity<Object> querySamplesForSelection(SampleQueryCriteria criteria){
        return new ResponseEntity<>(sampleService.queryAll(criteria),HttpStatus.OK);
    }

    @GetMapping(value = "/checkSampleName")
    @ApiOperation("查询样品管理")
    @PreAuthorize("@el.check('sample:list')")
    public ResponseEntity<Object> checkSampleName(Sample resources) {
        SampleDto sampleDto = sampleService.checkSampleName(resources);
        CommonResponseEntity resp = new CommonResponseEntity();
        if (sampleDto != null && !sampleDto.getId().equals(resources.getId())) {
            resp.setErrorLevel(ErrorLevelEnum.ERROR);
            resp.setErrorCode("error.sample.sampleExisted");
        }
        return new ResponseEntity<>(resp, HttpStatus.OK);
    }

    @GetMapping(value = "/queryLatestSample")
    @ApiOperation("查询样品管理")
    @PreAuthorize("@el.check('sample:list')")
    public ResponseEntity<Object> queryLatestSample() {
        SampleDto sampleDto = sampleService.queryLatestSample();
        if (sampleDto == null) {
            sampleDto = new SampleDto();
        }
        return new ResponseEntity<>(sampleDto, HttpStatus.OK);
    }
    /*
    @PostMapping(value = "/submitSampleTagScanning")
    @Log("新增样品管理")
    @ApiOperation("新增样品管理")
    @PreAuthorize("@el.check('sample:add')")
    public ResponseEntity<Object> submitSampleTagScanning(@Validated @RequestBody SampleDto resources) {
        if (resources.getId() == null) {
            sampleService.create(resources);
        }else{
            sampleService.update(resources);
        }
        TaskPrepareViewDto viewDto = new TaskPrepareViewDto();
        Optional<TaskPrepare> tpOpt = resources.getTaskPrepares().stream().filter(tp -> {
            return tp.isSelected();
        }).findFirst();
        if (!tpOpt.isPresent()) {
            return new ResponseEntity<>(viewDto, HttpStatus.CREATED);
        }
        viewDto.setMode(TaskPrepareViewDto.ModeEnum.SAMPLE.name());
        TaskPrepareDto tpDto = taskPrepareMapper.toDto(tpOpt.get());
        viewDto.getTaskPrepareDtos().add(tpDto);
        viewDto.setCurTaskPrepareDto(tpDto);

        taskPrepareViewService.submitTagReadingStep(viewDto);

        return new ResponseEntity<>(sampleMapper.toDto(resources), HttpStatus.CREATED);
    }
     */


    @PostMapping
    @Log("新增样品管理")
    @ApiOperation("新增样品管理")
    @PreAuthorize("@el.check('sample:add')")
    public ResponseEntity<Object> createSample(@Validated @RequestBody SampleDto resources){
        return new ResponseEntity<>(sampleService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改样品管理")
    @ApiOperation("修改样品管理")
    @PreAuthorize("@el.check('sample:edit')")
    public ResponseEntity<Object> updateSample(@Validated @RequestBody Sample resources){
        sampleService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除样品管理")
    @ApiOperation("删除样品管理")
    @PreAuthorize("@el.check('sample:del')")
    public ResponseEntity<Object> deleteSample(@RequestBody Long[] ids) {
        sampleService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}