/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.TenantBaseEntity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-21
**/
@Entity
@Data
@Table(name="shop_page")
public class ShopAppPage extends TenantBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "page_id")
    @Schema(description = "页面ID")
    private Long pageId;

    @Column(name = "page_type",nullable = false)
    @NotNull
    @Schema(description = "页面类型(10首页 20自定义页)")
    private Integer pageType;

    @Column(name = "page_name",nullable = false)
    @NotBlank
    @Schema(description = "页面名称")
    private String pageName;

    @Column(name = "page_data",nullable = false)
    @NotBlank
    @Schema(description = "页面数据")
    private String pageData;

    @Column(name = "shop_id",nullable = false)
    @NotNull
    @Schema(description = "店铺ID")
    private Long shopId;

    public void copy(ShopAppPage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}