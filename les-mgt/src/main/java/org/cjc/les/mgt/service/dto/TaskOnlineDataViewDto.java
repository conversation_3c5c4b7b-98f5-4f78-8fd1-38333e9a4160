/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import org.cjc.les.core.service.dto.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-06-10
 **/
@Data
public class TaskOnlineDataViewDto implements Serializable {
    /**
     * 任务列表
     */
    private List<TaskDto> tasks = new ArrayList<>();
    /**
     * 任务方法列表
     */
    private List<TaskMethodDto> taskMethods = new ArrayList<>();
    /**
     * 任务步骤列表
     */
    private List<TaskStepDto> taskSteps = new ArrayList<>();
    /**
     * 任务动作列表
     */
    private List<TaskActionDto> taskActions = new ArrayList<>();
    /**
     * 任务命令列表
     */
    private List<TaskCommandDto> taskCommands = new ArrayList<>();

}