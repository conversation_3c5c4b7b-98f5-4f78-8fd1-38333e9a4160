/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.mgt.domain.DeviceMock;
import org.cjc.les.mgt.service.DeviceMockService;
import org.cjc.les.mgt.service.dto.DeviceMockDto;
import org.cjc.les.mgt.service.dto.DeviceMockQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-08-04
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "设备模拟管理")
@RequestMapping("/api/deviceMock")
public class DeviceMockController {

    private final DeviceMockService deviceMockService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('deviceMock:list')")
    public void exportDeviceMock(HttpServletResponse response, DeviceMockQueryCriteria criteria) throws IOException {
        deviceMockService.download(deviceMockService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询设备模拟")
    @ApiOperation("查询设备模拟")
    @PreAuthorize("@el.check('deviceMock:list')")
    public ResponseEntity<Object> queryDeviceMock(DeviceMockQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(deviceMockService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/queryAllDeviceMocks")
    @Log("查询所有设备模拟")
    @ApiOperation("查询设备模拟")
    @PreAuthorize("@el.check('deviceMock:list')")
    public ResponseEntity<Object> queryAllDeviceMocks(DeviceMockQueryCriteria criteria){
        return new ResponseEntity<>(deviceMockService.queryAll(criteria),HttpStatus.OK);
    }

    @PutMapping("/start")
    @Log("查询所有设备模拟")
    @ApiOperation("查询设备模拟")
    @PreAuthorize("@el.check('deviceMock:list')")
    public ResponseEntity<Object> start(@RequestBody DeviceMockDto dto){
        return new ResponseEntity<>(deviceMockService.start(dto),HttpStatus.OK);
    }

    @PutMapping("/stop")
    @Log("查询所有设备模拟")
    @ApiOperation("查询设备模拟")
    @PreAuthorize("@el.check('deviceMock:list')")
    public ResponseEntity<Object> stop(@RequestBody DeviceMockDto dto){
        return new ResponseEntity<>(deviceMockService.stop(dto),HttpStatus.OK);
    }

    @PostMapping("/inputScanner")
    @Log("新增设备模拟")
    @ApiOperation("新增设备模拟")
    @PreAuthorize("@el.check('deviceMock:list')")
    public ResponseEntity<Object> inputScanner(@RequestBody DeviceMockDto dto){
        return new ResponseEntity<>(deviceMockService.inputScanner(dto),HttpStatus.CREATED);
    }

    @PostMapping
    @Log("新增设备模拟")
    @ApiOperation("新增设备模拟")
    @PreAuthorize("@el.check('deviceMock:add')")
    public ResponseEntity<Object> createDeviceMock(@Validated @RequestBody DeviceMock resources){
        return new ResponseEntity<>(deviceMockService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改设备模拟")
    @ApiOperation("修改设备模拟")
    @PreAuthorize("@el.check('deviceMock:edit')")
    public ResponseEntity<Object> updateDeviceMock(@Validated @RequestBody DeviceMock resources){
        deviceMockService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除设备模拟")
    @ApiOperation("删除设备模拟")
    @PreAuthorize("@el.check('deviceMock:del')")
    public ResponseEntity<Object> deleteDeviceMock(@RequestBody Long[] ids) {
        deviceMockService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}