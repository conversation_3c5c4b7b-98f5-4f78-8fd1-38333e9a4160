/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Setting;
import org.cjc.les.core.service.SettingService;
import org.cjc.les.core.service.dto.SettingQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-05-19
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "系统设置管理")
@RequestMapping("/api/setting")
public class SettingController {

    private final SettingService settingService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('setting:list')")
    public void exportSetting(HttpServletResponse response, SettingQueryCriteria criteria) throws IOException {
        settingService.download(settingService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询系统设置")
    @ApiOperation("查询系统设置")
    @PreAuthorize("@el.check('setting:list')")
    public ResponseEntity<Object> querySetting(SettingQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(settingService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/queryLatestOne")
    @Log("查询最近系统设置")
    @ApiOperation("查询最近系统设置")
    @PreAuthorize("@el.check('setting:list')")
    public ResponseEntity<Object> queryLatestOne(SettingQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(settingService.queryLatestOne(),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增系统设置")
    @ApiOperation("新增系统设置")
    @PreAuthorize("@el.check('setting:add')")
    public ResponseEntity<Object> createSetting(@Validated @RequestBody Setting resources){
        return new ResponseEntity<>(settingService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改系统设置")
    @ApiOperation("修改系统设置")
    @PreAuthorize("@el.check('setting:edit')")
    public ResponseEntity<Object> updateSetting(@Validated @RequestBody Setting resources){
        settingService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除系统设置")
    @ApiOperation("删除系统设置")
    @PreAuthorize("@el.check('setting:del')")
    public ResponseEntity<Object> deleteSetting(@RequestBody Long[] ids) {
        settingService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}