/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.annotation.rest.AnonymousGetMapping;
import org.cjc.les.core.domain.Material;
import org.cjc.les.core.service.MaterialService;
import org.cjc.les.core.service.dto.MaterialQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-03-26
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "物料管理管理")
@RequestMapping("/api/material")
public class MaterialController {

    private final MaterialService materialService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('material:list')")
    public void exportMaterial(HttpServletResponse response, MaterialQueryCriteria criteria) throws IOException {
        materialService.download(materialService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询物料管理")
    @ApiOperation("查询物料管理")
    @PreAuthorize("@el.check('material:list')")
    public ResponseEntity<Object> queryMaterial(MaterialQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(materialService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/queryTopByStatus")
    @Log("查询物料管理")
    @ApiOperation("查询物料管理")
    @AnonymousGetMapping()
    public ResponseEntity<Object> queryTopByStatus(MaterialQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(materialService.queryTopByStatus(criteria),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增物料管理")
    @ApiOperation("新增物料管理")
    @PreAuthorize("@el.check('material:add')")
    public ResponseEntity<Object> createMaterial(@Validated @RequestBody Material resources){
        return new ResponseEntity<>(materialService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改物料管理")
    @ApiOperation("修改物料管理")
    @PreAuthorize("@el.check('material:edit')")
    public ResponseEntity<Object> updateMaterial(@Validated @RequestBody Material resources){
        materialService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除物料管理")
    @ApiOperation("删除物料管理")
    @PreAuthorize("@el.check('material:del')")
    public ResponseEntity<Object> deleteMaterial(@RequestBody Long[] ids) {
        materialService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value="/reset")
    @Log("修改物料管理")
    @ApiOperation("修改物料管理")
    @PreAuthorize("@el.check('material:edit')")
    public ResponseEntity<Object> reset(@RequestBody Material resources){
        materialService.reset(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}