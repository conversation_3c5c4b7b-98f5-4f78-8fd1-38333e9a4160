/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.repository;

import org.cjc.les.mgt.domain.ShopUniappSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-11
**/
public interface ShopUniappSettingRepository extends JpaRepository<ShopUniappSetting, Long>, JpaSpecificationExecutor<ShopUniappSetting> {

    @Query("select ss.uniappSettingId, ss.shopId,ss.appType,ss.setting from ShopUniappSetting ss where ss.shopId=?1 and ss.appType=?2")
    ShopUniappSetting getShopUniappSettingByShopId(Long shopId, String appType);
}