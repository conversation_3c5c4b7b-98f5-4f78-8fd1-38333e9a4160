/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-10-30
**/
@Data
public class ReportMethodDto implements Serializable {

    /** 主键ID */
    private Long id;


    /** 项目类别 */
    private String itemCategory;

    /** 检测项目名称 */
    private String itemName;

    /**
     * 检测参考标准
     */
    private String referenceStd;

    /** 分析方法 */
    private String methodName;

    /** 备注 */
    private String remark;

    private String refValue = "";

    private String value = "";

    /** 运行状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}