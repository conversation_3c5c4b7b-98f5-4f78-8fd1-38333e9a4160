/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimplePdfExporterConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.cjc.exception.EntityExistException;
import org.cjc.les.core.domain.Result;
import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.domain.ResultItem;
import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.repository.ResultRepository;
import org.cjc.les.core.service.dto.ResultDto;
import org.cjc.les.core.service.mapstruct.ResultMapper;
import org.cjc.les.exception.TaskRunningException;
import org.cjc.les.mgt.domain.DeviceMock;
import org.cjc.les.mgt.repository.DeviceMockRepository;
import org.cjc.les.mgt.service.DeviceMockService;
import org.cjc.les.mgt.service.ResultReportService;
import org.cjc.les.mgt.service.dto.*;
import org.cjc.les.mgt.service.mapstruct.DeviceMockMapper;
import org.cjc.utils.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-08-04
 **/
@Log4j2
@Service
@RequiredArgsConstructor
public class ResultReportServiceImpl implements ResultReportService {

    private final ResultRepository resultRepository;
    private final ResultMapper resultMapper;


    @Override
    public void downloadReport(ResultDto dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            // 数据源
//            List<Map<String, Object>> dsAnalyzeMethodTable = new ArrayList<>();
//            Map<String, Object> data1 = new HashMap<>();
//            data1.put("sampleType", "John Doe");
//            data1.put("itemCategory", "养分检测");
//            data1.put("itemName", "N-NH4");
//            data1.put("referenceStd", "0.34223");
//            data1.put("methodName", "离子色谱法");
//            data1.put("refValue", "1");
//            data1.put("value", "2");
//            dsAnalyzeMethodTable.add(data1);
//
//            Map<String, Object> data2 = new HashMap<>();
//            data2.put("sampleType", "Jane Smith");
//            data2.put("itemCategory", "养分检测");
//            data2.put("itemName", "P4");
//            data2.put("referenceStd", "0.009887");
//            data2.put("methodName", "离子色谱法");
//            data2.put("refValue", "1");
//            data2.put("value", "3");
//            dsAnalyzeMethodTable.add(data2);

            ReportDto reportDto = getReport(dto);

            // 参数
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("reportTitle", reportDto.getTitle());
            parameters.put("reportSample", reportDto.getSample());
            parameters.put("analyzeMethodTable", new JRBeanCollectionDataSource(reportDto.getMethodList()));
            parameters.put("analyzeResultTable", new JRBeanCollectionDataSource(reportDto.getResultList()));
            parameters.put("conclusion", reportDto.getConclusion());

            // JRBeanCollectionDataSource jrDataSource = new JREmptyDataSource();

            JasperReport jasperReport = JasperCompileManager.compileReport("report.jrxml");

            // 加载模板
            //   String reportPath = "report.jasper";
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, new JREmptyDataSource());

            // response.setContentType("application/pdf;charset=utf-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=report.pdf");


            // 将 JasperPrint 导出为 PDF 并写入响应的输出流中
            //       OutputStream outputStream = response.getOutputStream();
            //       JasperExportManager.exportReportToPdfStream(jasperPrint, outputStream);

            // 关闭流
            //       response.flushBuffer();


            // 导出 PDF
            JRPdfExporter exporter = new JRPdfExporter();
            exporter.setExporterInput(new SimpleExporterInput(jasperPrint));

            SimpleOutputStreamExporterOutput outPut = new SimpleOutputStreamExporterOutput(response.getOutputStream());
            exporter.setExporterOutput(outPut);
            //   exporter.setExporterOutput(new SimpleOutputStreamExporterOutput("output_report.pdf"));
            SimplePdfExporterConfiguration configuration = new SimplePdfExporterConfiguration();
            exporter.setConfiguration(configuration);

            exporter.exportReport();

            //       FileUtil.downloadFile(request, response, new File("output_report.pdf"), true);


            System.out.println("PDF generated successfully!");


        } catch (JRException e) {
            e.printStackTrace();
            log.error("dowload Report error: {}", e.getMessage(), e);
        }
    }

    private ReportDto getReport(ResultDto dto) {

        Optional<Result> resultOpt = resultRepository.findById(dto.getId());
        if (!resultOpt.isPresent()) {
            throw new TaskRunningException("Could not found result by id="+dto.getId());
        }

        Result result = resultOpt.get();

        ReportDto reportDto = new ReportDto();
        // 报告标题头
        ReportTitleDto titleDto = new ReportTitleDto();
        reportDto.setTitle(titleDto);
        titleDto.setCheckingDate(DateUtil.localDateTimeFormatyMd(result.getCreateTime().toLocalDateTime()));
        titleDto.setDelegateCompany("Test XXXX 公司");
        titleDto.setItemCategory("委托检测");
        titleDto.setItemName("XXX检测");

        // 报告样品信息
        ReportSampleDto sampleDto = new ReportSampleDto();
        reportDto.setSample(sampleDto);
        sampleDto.setAnalyseDate(DateUtil.localDateTimeFormatyMd(result.getCreateTime().toLocalDateTime()));
        sampleDto.setCollectionAddress("XXX 测试地址");
        sampleDto.setDelegateCompany("XXX公司");
        sampleDto.setDelegateCompanyAddress("XXX 委托公司地址");
        sampleDto.setDelegator("张三XX");
        sampleDto.setSampleAcceptationDate(DateUtil.localDateTimeFormatyMd(result.getCreateTime().toLocalDateTime()));
        sampleDto.setSampleCount("1");
        sampleDto.setSampleType("磷肥检测");
        sampleDto.setTelephone("027-12340092");
        sampleDto.setRemark("无");

        // 报告检测方法
        List<ReportMethodDto> methodDtoList = new ArrayList<>();
        reportDto.setMethodList(methodDtoList);

        List<ResultItem> itemList = result.getItems();
        for (ResultItem item : itemList ) {
            String methodName = item.getTaskMethod().getName();
            for (ResultFeature feature : item.getResultFeatures()) {
                ReportMethodDto outMthDto = getOrCreateMethodDto(methodDtoList, feature);
                outMthDto.setMethodName(methodName);
                outMthDto.setItemCategory("Test项目类型");
                outMthDto.setReferenceStd("GB XXXx");
            }
        }

        // 报告检测结果
        List<ReportResultDto> reportResultDtoList = new ArrayList<>();
        reportDto.setResultList(reportResultDtoList);
        Sample sample = result.getSample();

        long seq = 0;
        for (ResultItem item : itemList ) {
            for (ResultFeature feature : item.getResultFeatures()) {
                ReportResultDto resultDto = new ReportResultDto();
                reportResultDtoList.add(resultDto);
                resultDto.setSeqNumber(++seq);
                resultDto.setSampleNumber(sample.getNumber());
                resultDto.setSampleName(sample.getName());
                resultDto.setItemName(feature.getName());
                resultDto.setUnit(feature.getUnit());
                resultDto.setResultValue(feature.getValue());
                resultDto.setRemark("无");
            }
        }

        // 报告结论
        ReportConclusionDto conclusionDto = new ReportConclusionDto();
        conclusionDto.setCheckingCompany("XXX 公司");
        reportDto.setConclusion(conclusionDto);

        return reportDto;
    }

    private ReportMethodDto getOrCreateMethodDto(List<ReportMethodDto> methodDtoList, ResultFeature feature) {
        ReportMethodDto outMthDto = null;
        for (ReportMethodDto mthDto : methodDtoList) {
            if (StringUtils.equalsAny(mthDto.getItemName(), feature.getName())){
                outMthDto = mthDto;
                break;
            }
        }
        if (outMthDto == null) {
            outMthDto = new ReportMethodDto();
            outMthDto.setItemName(feature.getName());
            methodDtoList.add(outMthDto);
        }

        return outMthDto;
    }
}