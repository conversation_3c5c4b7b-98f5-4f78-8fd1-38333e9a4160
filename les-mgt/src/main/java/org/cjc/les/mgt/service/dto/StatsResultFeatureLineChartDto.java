/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-31
**/
@Data
public class StatsResultFeatureLineChartDto implements Serializable {

    /**
     * 数据序列Map<featureName,List<featureValue>
     */
    private Map<String, List<BigDecimal>> serialsMap = new HashMap<>();
    /**
     * X轴数据集: featureCreateTime
     */
    private List<Timestamp> xAxisData = new ArrayList<>();
}