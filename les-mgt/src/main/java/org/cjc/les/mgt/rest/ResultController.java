/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Result;
import org.cjc.les.core.service.ResultService;
import org.cjc.les.core.service.dto.ResultQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;

import java.io.IOException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @date 2024-06-25
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "检测结果管理管理")
@RequestMapping("/api/result")
public class ResultController {

    private final ResultService resultService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('result:list')")
    public void exportResult(HttpServletResponse response, ResultQueryCriteria criteria) throws IOException {
        resultService.download(resultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询检测结果管理")
    @ApiOperation("查询检测结果管理")
    @PreAuthorize("@el.check('result:list')")
    public ResponseEntity<Object> queryResult(ResultQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(resultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增检测结果管理")
    @ApiOperation("新增检测结果管理")
    @PreAuthorize("@el.check('result:add')")
    public ResponseEntity<Object> createResult(@Validated @RequestBody Result resources) {
        return new ResponseEntity<>(resultService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改检测结果管理")
    @ApiOperation("修改检测结果管理")
    @PreAuthorize("@el.check('result:edit')")
    public ResponseEntity<Object> updateResult(@Validated @RequestBody Result resources) {
        resultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除检测结果管理")
    @ApiOperation("删除检测结果管理")
    @PreAuthorize("@el.check('result:del')")
    public ResponseEntity<Object> deleteResult(@RequestBody Long[] ids) {
        resultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}