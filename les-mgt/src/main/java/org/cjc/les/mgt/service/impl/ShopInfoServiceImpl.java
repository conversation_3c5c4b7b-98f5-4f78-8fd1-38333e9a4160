/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import cn.hutool.json.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.cjc.modules.system.domain.User;
import org.cjc.les.mgt.domain.ShopInfo;

import lombok.RequiredArgsConstructor;
import org.cjc.les.mgt.repository.ShopInfoRepository;
import org.cjc.les.mgt.service.ShopInfoService;
import org.cjc.les.mgt.service.dto.ShopInfoDto;
import org.cjc.les.mgt.service.dto.ShopInfoQueryCriteria;
import org.cjc.les.mgt.service.dto.ShopSmallDto;
import org.cjc.les.mgt.service.mapstruct.ShopInfoMapper;
import org.cjc.les.mgt.service.mapstruct.ShopSmallMapper;
import org.cjc.utils.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-05-29
**/
@Service
@RequiredArgsConstructor
public class ShopInfoServiceImpl implements ShopInfoService {

    private final ShopInfoRepository shopInfoRepository;
    private final ShopInfoMapper shopInfoMapper;
    private final ShopSmallMapper shopSmallMapper;

    @Override
    public Map<String,Object> queryAll(ShopInfoQueryCriteria criteria, Pageable pageable){
        Page<ShopInfo> page = shopInfoRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(shopInfoMapper::toDto));
    }

    @Override
    public List<ShopInfoDto> queryAll(ShopInfoQueryCriteria criteria){
        return shopInfoMapper.toDto(shopInfoRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<ShopSmallDto> queryShopShortListByUserId(ShopInfoQueryCriteria criteria) {
        String shopName = (criteria!=null)?criteria.getShopName():null;
        Long uid = SecurityUtils.getCurrentUserId();
        return shopSmallMapper.toDto(shopInfoRepository.queryShopShortListByUserId( uid));
    }

    @Override
    public ShopSmallDto getDefaultShop(Long areaId) {
        ShopInfo shopInfo = shopInfoRepository.findDefaultShop(areaId).orElseGet(ShopInfo::new);
        return shopSmallMapper.toDto(shopInfo);
    }

    @Override
    @Transactional
    public ShopInfoDto findById(Long shopId) {
        ShopInfo shopInfo = shopInfoRepository.findById(shopId).orElseGet(ShopInfo::new);
        ValidationUtil.isNull(shopInfo.getShopId(),"ShopInfo","shopId",shopId);
        return shopInfoMapper.toDto(shopInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopInfoDto create(ShopInfo resources) {
        Long userId = SecurityUtils.getCurrentUserId();
        User user = new User();
        user.setId(userId);
        resources.setUsers(new HashSet<>());
        resources.getUsers().add(user);
        resources.setCreateBy(SecurityUtils.getCurrentUsername());
        // 设置店铺LOGO图片
        JSONArray logoImages = new JSONArray(resources.getLogoImage());
        StringBuilder logoImageRealNames = setImageValue(logoImages);
        resources.setLogoImage(logoImageRealNames.toString());
        // 设置店铺图片
        JSONArray shopImages = new JSONArray(resources.getShopImages());
        StringBuilder shopImageRealNames = setImageValue(shopImages);
        resources.setShopImages(shopImageRealNames.toString());
        return shopInfoMapper.toDto(shopInfoRepository.save(resources));
    }

    private StringBuilder setImageValue(JSONArray images) {
        StringBuilder realNames = new StringBuilder();
        for (int i = 0; i < images.size(); i++) {
            String response = images.getJSONObject(i).getStr("response");
            JSONObject jsonResponse = JSONObject.parseObject(response);
            if (realNames.length() > 0) {
                realNames.append(",").append(jsonResponse.getString("realName"));
            } else {
                realNames.append(jsonResponse.getString("realName"));
            }
        }
        return realNames;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ShopInfo resources) {
        ShopInfo shopInfo = shopInfoRepository.findById(resources.getShopId()).orElseGet(ShopInfo::new);
        ValidationUtil.isNull( shopInfo.getShopId(),"ShopInfo","id",resources.getShopId());
        shopInfo.copy(resources);
        shopInfoRepository.save(shopInfo);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long shopId : ids) {
            shopInfoRepository.deleteById(shopId);
        }
    }

    @Override
    public void download(List<ShopInfoDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ShopInfoDto shopInfo : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("店铺所属区域ID", shopInfo.getAreaId());
            map.put("租户ID", shopInfo.getTenantId());
            map.put("店铺名称", shopInfo.getShopName());
            map.put("地址", shopInfo.getAddress());
            map.put("店铺所在纬度", shopInfo.getLatitude());
            map.put("店铺所在经度", shopInfo.getLongitude());
            map.put("描述", shopInfo.getDescription());
            map.put("审核状态: 1:待审核,2:审核通过,3:审核失败", shopInfo.getAuditStatus());
            map.put("在线状态: 1:在线(营业中),2:下线(休息中), 3:停业", shopInfo.getOnlineStatus());
            map.put("创建人", shopInfo.getCreateBy());
            map.put("创建时间", shopInfo.getCreateTime());
            map.put("更新人", shopInfo.getUpdateBy());
            map.put("更新时间", shopInfo.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void batchAuditShopInfo(List<ShopInfo> resources) {
        for (ShopInfo shopInfo : resources) {
            update(shopInfo);
        }
    }
}