/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.FeatureConfig;
import org.cjc.les.core.service.FeatureConfigService;
import org.cjc.les.core.service.dto.FeatureConfigQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-16
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "检测特性配置管理")
@RequestMapping("/api/featureConfig")
public class FeatureConfigController {

    private final FeatureConfigService featureConfigService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('featureConfig:list')")
    public void exportFeatureConfig(HttpServletResponse response, FeatureConfigQueryCriteria criteria) throws IOException {
        featureConfigService.download(featureConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询检测特性配置")
    @ApiOperation("查询检测特性配置")
    @PreAuthorize("@el.check('featureConfig:list')")
    public ResponseEntity<Object> queryFeatureConfig(FeatureConfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(featureConfigService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增检测特性配置")
    @ApiOperation("新增检测特性配置")
    @PreAuthorize("@el.check('featureConfig:add')")
    public ResponseEntity<Object> createFeatureConfig(@Validated @RequestBody FeatureConfig resources){
        return new ResponseEntity<>(featureConfigService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改检测特性配置")
    @ApiOperation("修改检测特性配置")
    @PreAuthorize("@el.check('featureConfig:edit')")
    public ResponseEntity<Object> updateFeatureConfig(@Validated @RequestBody FeatureConfig resources){
        featureConfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除检测特性配置")
    @ApiOperation("删除检测特性配置")
    @PreAuthorize("@el.check('featureConfig:del')")
    public ResponseEntity<Object> deleteFeatureConfig(@RequestBody Long[] ids) {
        featureConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}