/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.hibernate.id.GUIDGenerator;
import org.hibernate.id.UUIDGenerator;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-06-10
 **/
@Data
public class TaskPrepareViewDto implements Serializable {

    public enum ModeEnum {
        TRIAL, FACTORY, SAMPLE
    }

    private String id = UUID.randomUUID().toString();

    /**
     * DRAFT: 初始化状态
     * SUBMITTED: 已提交
     * WAIT_TAG_INPUT: 等待TAG输入
     * READY: 待推送到任务队列
     * CONVEYOR_SUBMITTED: 样品进样已提交
     * WAIT_CONVEYOR_INPUT: 等待样品进入传送带
     * PUSHED: 已推送到任务队列
     * CANCELLED: 已撤销
     */
    private String status = "DRAFT";

    /**
     * 状态的消息详情
     */
    private String message;

    /**
     * 进样模式: TRIAL, FACTORY, SAMPLE,BOARD
     */
    private String mode;

    /**
     * 待处理任务列表
     */
    private List<TaskPrepareDto> taskPrepareDtos = new ArrayList<>();

    /**
     * 当前待处理任务
     */
    private TaskPrepareDto curTaskPrepareDto;

    /**
     * 调试模式
     */
    private Boolean debugMode = false;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TaskPrepareViewDto that = (TaskPrepareViewDto) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}