/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.repository;

import org.cjc.les.mgt.domain.DeviceMock;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-08-04
**/
public interface DeviceMockRepository extends JpaRepository<DeviceMock, Long>, JpaSpecificationExecutor<DeviceMock> {
    /**
    * 根据 JavaClassName 查询
    * @param javaClassName /
    * @return /
    */
    DeviceMock findByJavaClassNameAndConfig(String javaClassName, String config);
}