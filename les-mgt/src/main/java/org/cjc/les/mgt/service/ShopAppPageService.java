/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.domain.ShopAppPage;
import org.cjc.les.mgt.service.dto.ShopAppPageDto;
import org.cjc.les.mgt.service.dto.ShopAppPageQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-03-21
**/
public interface ShopAppPageService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ShopAppPageQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<PageDto>
    */
    List<ShopAppPageDto> queryAll(ShopAppPageQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param pageId ID
     * @return PageDto
     */
    ShopAppPageDto findById(Long pageId);

    /**
    * 创建
    * @param resources /
    * @return PageDto
    */
    ShopAppPageDto create(ShopAppPage resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ShopAppPage resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ShopAppPageDto> all, HttpServletResponse response) throws IOException;
}