/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesBaseEntity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-31
**/
@Entity
@Data
@Table(name="les_stats_result_feature")
public class StatsResultFeature extends LesBaseEntity {

    @Column(name = "result_feature_id",unique = true)
    @Schema(description = "结果指标ID")
    private Long resultFeatureId;

    @Column(name = "feature_name")
    @Schema(description = "结果指标名称")
    private String featureName;

    @Column(name = "feature_value")
    @Schema(description = "结果指标值")
    private String featureValue;

    @Column(name = "feature_conclusion")
    @Schema(description = "结果结论")
    private String featureConclusion;

    @Column(name = "feature_create_time")
    @Schema(description = "结果指标创建时间")
    private Timestamp featureCreateTime;

    @Column(name = "method_name")
    @Schema(description = "检测方法")
    private String methodName;

    @Column(name = "sample_name")
    @Schema(description = "样品名称")
    private String sampleName;

    @Column(name = "sample_category")
    @Schema(description = "样品分类")
    private String sampleCategory;

    @Column(name = "sample_customer")
    @Schema(description = "样品所属客户")
    private String sampleCustomer;

    @Column(name = "delete_flag")
    @Schema(description = "是否已被删除,Y/N")
    private String deleteFlag="N";

    public void copy(StatsResultFeature source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}