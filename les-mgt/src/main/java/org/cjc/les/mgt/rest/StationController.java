/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import lombok.extern.log4j.Log4j2;
import org.cjc.annotation.Log;
import org.cjc.common.uniapp.UniappResponseEntity;
import org.cjc.les.core.domain.Station;
import org.cjc.les.core.service.StationService;
import org.cjc.les.core.service.dto.StationQueryCriteria;
import org.cjc.les.exception.ConfigApplicationException;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-07-14
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "工作站管理管理")
@RequestMapping("/api/station")
@Log4j2
public class StationController {

    private final StationService stationService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('station:list')")
    public void exportStation(HttpServletResponse response, StationQueryCriteria criteria) throws IOException {
        stationService.download(stationService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询工作站管理")
    @ApiOperation("查询工作站管理")
    @PreAuthorize("@el.check('station:list')")
    public ResponseEntity<Object> queryStation(StationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(stationService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增工作站管理")
    @ApiOperation("新增工作站管理")
    @PreAuthorize("@el.check('station:add')")
    public ResponseEntity<Object> createStation(@Validated @RequestBody Station resources){
        return new ResponseEntity<>(stationService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改工作站管理")
    @ApiOperation("修改工作站管理")
    @PreAuthorize("@el.check('station:edit')")
    public ResponseEntity<Object> updateStation(@Validated @RequestBody Station resources){
        try {
            stationService.update(resources);
        } catch (ConfigApplicationException e) {
            log.error("updateStation: {}", e.getMessage(), e);
            UniappResponseEntity<List<Map>> resp = new UniappResponseEntity();
            resp.setStatus(400);
            resp.setMessage("更新失败");
            resp.setData((List)e.getData());
            return new ResponseEntity<>(resp, HttpStatus.OK);
        }
        return new ResponseEntity<>(stationService.findById(resources.getId()), HttpStatus.OK);
    }

    @DeleteMapping
    @Log("删除工作站管理")
    @ApiOperation("删除工作站管理")
    @PreAuthorize("@el.check('station:del')")
    public ResponseEntity<Object> deleteStation(@RequestBody Long[] ids) {
        try {
            stationService.deleteAll(ids);
        } catch (ConfigApplicationException e) {
            log.error("deleteStationConfigApplicationException: {}", e.getMessage(), e);
            UniappResponseEntity<List<Map>> resp = new UniappResponseEntity();
            resp.setStatus(400);
            resp.setMessage("删除失败");
            resp.setData((List)e.getData());
            return new ResponseEntity<>(resp, HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/queryStationById")
    @Log("查询工作站管理")
    @ApiOperation("查询工作站管理")
    @PreAuthorize("@el.check('station:list')")
    public ResponseEntity<Object> queryStationById(@RequestParam(defaultValue = "0")Long id){
        return new ResponseEntity<>(stationService.findById(id),HttpStatus.OK);
    }

    @GetMapping("/queryActionQueueByStationId")
    @Log("查询工作站管理")
    @ApiOperation("查询工作站管理")
    @PreAuthorize("@el.check('station:list')")
    public ResponseEntity<Object> queryActionQueueByStationId(@RequestParam(defaultValue = "0")Long id){
        return new ResponseEntity<>(stationService.queryActionQueueByStationId(id),HttpStatus.OK);
    }

    @GetMapping("/queryAllStationsInRunning")
    @ApiOperation("查询工作站管理")
    //@PreAuthorize("@el.check('station:list')")
    public ResponseEntity<Object> queryAllStationsInRunning(StationQueryCriteria criteria){
        return new ResponseEntity<>(stationService.queryAllStationsInRunning(criteria),HttpStatus.OK);
    }
}