/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.mgt.domain.ShopApproveLog;
import org.cjc.les.mgt.service.ShopApproveLogService;
import org.cjc.les.mgt.service.dto.ShopApproveLogQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-07-10
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "ShopInfoService管理")
@RequestMapping("/api/shopApproveLog")
public class ShopApproveLogController {

    private final ShopApproveLogService shopApproveLogService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('shopApproveLog:list')")
    public void exportShopApproveLog(HttpServletResponse response, ShopApproveLogQueryCriteria criteria) throws IOException {
        shopApproveLogService.download(shopApproveLogService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询ShopInfoService")
    @ApiOperation("查询ShopInfoService")
    @PreAuthorize("@el.check('shopApproveLog:list')")
    public ResponseEntity<Object> queryShopApproveLog(ShopApproveLogQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(shopApproveLogService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增ShopInfoService")
    @ApiOperation("新增ShopInfoService")
    @PreAuthorize("@el.check('shopApproveLog:add')")
    public ResponseEntity<Object> createShopApproveLog(@Validated @RequestBody ShopApproveLog resources){
        return new ResponseEntity<>(shopApproveLogService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改ShopInfoService")
    @ApiOperation("修改ShopInfoService")
    @PreAuthorize("@el.check('shopApproveLog:edit')")
    public ResponseEntity<Object> updateShopApproveLog(@Validated @RequestBody ShopApproveLog resources){
        shopApproveLogService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除ShopInfoService")
    @ApiOperation("删除ShopInfoService")
    @PreAuthorize("@el.check('shopApproveLog:del')")
    public ResponseEntity<Object> deleteShopApproveLog(@RequestBody Long[] ids) {
        shopApproveLogService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/batchSaveShopApprovelLog")
    @Log("批量审核店铺信息")
    @ApiOperation("批量审核店铺信息")
    @PreAuthorize("@el.check('shopInfo:batchSaveShopApprovelLog')")
    public ResponseEntity<Object> batchSaveShopApprovelLog(@Validated @RequestBody List<ShopApproveLog> resources) {
        for (ShopApproveLog shopApproveLog : resources) {
            createShopApproveLog(shopApproveLog);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }
}