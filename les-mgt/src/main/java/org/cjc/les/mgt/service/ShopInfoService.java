/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.domain.ShopInfo;
import org.cjc.les.mgt.service.dto.ShopInfoDto;
import org.cjc.les.mgt.service.dto.ShopInfoQueryCriteria;
import org.cjc.les.mgt.service.dto.ShopSmallDto;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-05-29
**/
public interface ShopInfoService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ShopInfoQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ShopInfoDto>
    */
    List<ShopInfoDto> queryAll(ShopInfoQueryCriteria criteria);

    /**
     * 查询当前用户下的店铺列表
     * @param criteria
     * @return
     */
    List<ShopSmallDto> queryShopShortListByUserId(ShopInfoQueryCriteria criteria);

    /**
     * 获取指定区域下的默认店铺，
     * 用于运营管理中的店铺切换
     *
     * @param areaId 区域ID
     * @return 默认店铺, 查找不到返回空
     */
    ShopSmallDto getDefaultShop(Long areaId);

    /**
     * 根据ID查询
     * @param shopId ID
     * @return ShopInfoDto
     */
    ShopInfoDto findById(Long shopId);

    /**
    * 创建
    * @param resources /
    * @return ShopInfoDto
    */
    ShopInfoDto create(ShopInfo resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ShopInfo resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ShopInfoDto> all, HttpServletResponse response) throws IOException;

    /**
     * 批量同步第三方商品列表
     * @param resources 商品列表
     */
    void batchAuditShopInfo(List<ShopInfo> resources);
}