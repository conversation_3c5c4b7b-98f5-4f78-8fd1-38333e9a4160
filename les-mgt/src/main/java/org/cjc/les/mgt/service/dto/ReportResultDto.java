/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-10-30
 **/
@Data
public class ReportResultDto implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 序号
     */
    private Long seqNumber;

    /**
     * 样品编号
     */
    private String sampleNumber;

    /**
     * 样品名称
     */
    private String sampleName;

    /**
     * 检测项目
     */
    private String itemName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 结果
     */
    private String resultValue;

    /**
     * 备注
     */
    private String remark;

    private String refRemark;

    /**
     * 运行状态
     */
    private String status;

    /**
     * 是否已被删除,Y/N
     */
    private String deleteFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}