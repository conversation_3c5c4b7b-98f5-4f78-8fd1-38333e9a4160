/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.cjc.les.core.domain.Method;
import org.cjc.les.core.domain.Result;
import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.domain.Sample;
import org.cjc.les.core.repository.MethodRepository;
import org.cjc.les.core.repository.ResultFeatureRepository;
import org.cjc.les.core.service.dto.ResultFeatureQueryCriteria;
import org.cjc.les.core.service.dto.ResultQueryCriteria;
import org.cjc.les.mgt.domain.StatsResultFeature;
import org.cjc.exception.EntityExistException;
import lombok.RequiredArgsConstructor;
import org.cjc.les.mgt.repository.StatsResultFeatureRepository;
import org.cjc.les.mgt.service.StatsResultFeatureService;
import org.cjc.les.mgt.service.dto.*;
import org.cjc.les.mgt.service.mapstruct.StatsResultFeatureMapper;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-07-31
**/
@Service("statsResultFeatureService")
@RequiredArgsConstructor
public class StatsResultFeatureServiceImpl implements StatsResultFeatureService {

    private final StatsResultFeatureRepository statsResultFeatureRepository;
    private final StatsResultFeatureMapper statsResultFeatureMapper;

    private final ResultFeatureRepository resultFeatureRepository;
    private final MethodRepository methodRepository;

    @Override
    public Map<String,Object> queryAll(StatsResultFeatureQueryCriteria criteria, Pageable pageable){
        Page<StatsResultFeature> page = statsResultFeatureRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(statsResultFeatureMapper::toDto));
    }

    @Override
    public List<StatsResultFeatureDto> queryAll(StatsResultFeatureQueryCriteria criteria){
        return statsResultFeatureMapper.toDto(statsResultFeatureRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public StatsResultFeatureDto findById(Long id) {
        StatsResultFeature statsResultFeature = statsResultFeatureRepository.findById(id).orElseGet(StatsResultFeature::new);
        ValidationUtil.isNull(statsResultFeature.getId(),"StatsResultFeature","id",id);
        return statsResultFeatureMapper.toDto(statsResultFeature);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StatsResultFeatureDto create(StatsResultFeature resources) {
        if(statsResultFeatureRepository.findByResultFeatureId(resources.getResultFeatureId()) != null){
            throw new EntityExistException(StatsResultFeature.class,"result_feature_id",resources.getResultFeatureId());
        }
        return statsResultFeatureMapper.toDto(statsResultFeatureRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StatsResultFeature resources) {
        StatsResultFeature statsResultFeature = statsResultFeatureRepository.findById(resources.getId()).orElseGet(StatsResultFeature::new);
        ValidationUtil.isNull( statsResultFeature.getId(),"StatsResultFeature","id",resources.getId());
        Optional<StatsResultFeature> statsResultFeature1Opt = statsResultFeatureRepository.findByResultFeatureId(resources.getResultFeatureId());
        if(statsResultFeature1Opt.isPresent() && !statsResultFeature1Opt.get().getId().equals(statsResultFeature.getId())){
            throw new EntityExistException(StatsResultFeature.class,"result_feature_id",resources.getResultFeatureId());
        }
        statsResultFeature.copy(resources);
        statsResultFeatureRepository.save(statsResultFeature);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            statsResultFeatureRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<StatsResultFeatureDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (StatsResultFeatureDto statsResultFeature : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("结果指标ID", statsResultFeature.getResultFeatureId());
            map.put("结果指标名称", statsResultFeature.getFeatureName());
            map.put("结果指标值", statsResultFeature.getFeatureValue());
            map.put("结果结论", statsResultFeature.getFeatureConclusion());
            map.put("结果指标创建时间", statsResultFeature.getFeatureCreateTime());
            map.put("检测方法", statsResultFeature.getMethodName());
            map.put("样品名称", statsResultFeature.getSampleName());
            map.put("样品分类", statsResultFeature.getSampleCategory());
            map.put("样品所属客户", statsResultFeature.getSampleCustomer());
            map.put("是否已被删除,Y/N", statsResultFeature.getDeleteFlag());
            map.put("创建人", statsResultFeature.getCreateBy());
            map.put("创建时间", statsResultFeature.getCreateTime());
            map.put("更新人", statsResultFeature.getUpdateBy());
            map.put("更新时间", statsResultFeature.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void syncData() {
        ResultFeatureQueryCriteria criteria = new ResultFeatureQueryCriteria();
        Optional<StatsResultFeature> opt = statsResultFeatureRepository.findLatestOne();
        if (opt.isPresent()){
            criteria.getCreateTime().add(opt.get().getFeatureCreateTime());
        }else {
            criteria.getCreateTime().add(new Timestamp(0));
        }
        criteria.getCreateTime().add(new Timestamp(System.currentTimeMillis()));
        Pageable pageable = PageRequest.of(0, 1000, Sort.by(new Sort.Order(Sort.Direction.ASC,"id")));
        Page<ResultFeature> page = null;
        do {
            if (page != null) {
                pageable = page.getPageable().next();
            }
            page = resultFeatureRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
            for (ResultFeature feature : page.getContent()) {
                saveFeature(feature);
            }
        }while(!page.isLast());
    }

    private void saveFeature(ResultFeature feature) {
        StatsResultFeature statsResultFeature = new StatsResultFeature();
        statsResultFeature.setResultFeatureId(feature.getId());
        statsResultFeature.setFeatureName(feature.getName());
        statsResultFeature.setFeatureValue(feature.getValue());
        statsResultFeature.setFeatureConclusion(feature.getConclusion());
        statsResultFeature.setFeatureCreateTime(feature.getCreateTime());
        Method method = methodRepository.findById(feature.getResultItem().getTaskMethod().getMethodId()).orElseGet(Method::new);
        statsResultFeature.setMethodName(method.getName());
        Sample sample = feature.getResultItem().getResult().getSample();
        if ( sample!= null) {
            statsResultFeature.setSampleName(sample.getName());
            statsResultFeature.setSampleCategory(sample.getCategory().getName());
            statsResultFeature.setSampleCustomer(sample.getCustomer().getName());
        }
        Optional<StatsResultFeature> opt = statsResultFeatureRepository.findByResultFeatureId(feature.getId());
        if (!opt.isPresent()) {
            statsResultFeatureRepository.save(statsResultFeature);
        }
    }

    /**
     * 查询检测值趋势图(折线图), 同时显示多个检测物featureName数据,
     * x轴显示创建时间featureCreateTime, y轴显示检测值featureValue,
     * select t.feature_name, CAST(DATE_FORMAT(t.feature_create_time, '%Y-%m-%d %H') AS DATETIME) as f_create_time, " +
     *             "count(1) cnt, ROUND(AVG(t.feature_value),2) avg_value, MIN(t.feature_value) min_value, MAX(t.feature_value) max_value from les_stats_result_feature t  where t.feature_create_time between :beginTime and :endTime group by t.feature_name, f_create_time
     * @param criteria 查询条件
     * @return echarts折线图显示所需数据
     */
    @Override
    public StatsResultFeatureLineChartDto queryStatsResultFeatureValues(StatsResultFeatureQueryCriteria criteria) {


        List<StatsResultFeature> list = statsResultFeatureRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        StatsResultFeatureLineChartDto dto = new StatsResultFeatureLineChartDto();
        // 转换为echarts折线图所需数据格式
        for (StatsResultFeature feature : list) {
            dto.getSerialsMap().computeIfAbsent(feature.getFeatureName(), k -> new ArrayList<>()).add(new BigDecimal(feature.getFeatureValue()));
            dto.getXAxisData().add(feature.getFeatureCreateTime());
        }

        return dto;
    }

    /**
     * 按小时查询检测结果特征值统计数据
     * 查询SQL: select t.feature_name, CAST(DATE_FORMAT(t.feature_create_time, '%Y-%m-%d %H:00:00') AS DATETIME) as f_create_time,
     *          count(1) cnt, ROUND(AVG(t.feature_value),2) avg_value, MIN(t.feature_value) min_value, MAX(t.feature_value) max_value
     *          from les_stats_result_feature t where 动态拼接条件 group by t.feature_name, f_create_time
     *
     * @param criteria 查询条件
     * @return 按小时统计的检测结果特征值列表
     */
    @Override
    public List<StatsResultFeatureLineChartItemDto> queryStatsResultFeatureValuesByHour(StatsResultFeatureQueryCriteria criteria) {
        // 提取查询条件
        Timestamp beginTime = null;
        Timestamp endTime = null;

        if (criteria.getFeatureCreateTime() != null && criteria.getFeatureCreateTime().size() >= 2) {
            beginTime = criteria.getFeatureCreateTime().get(0);
            endTime = criteria.getFeatureCreateTime().get(1);
        }

        String methodName = StringUtils.isEmpty(criteria.getMethodName()) ? null : criteria.getMethodName();
        String sampleCategory = StringUtils.isEmpty(criteria.getSampleCategory()) ? null : criteria.getSampleCategory();

        // 调用Repository执行查询
        List<Map<String, Object>> rawResults = statsResultFeatureRepository.findStatsByHour(
            beginTime, endTime, methodName, sampleCategory);

        // 转换为DTO对象列表
        List<StatsResultFeatureLineChartItemDto> resultList = new ArrayList<>();

        for (Map<String, Object> row : rawResults) {
            StatsResultFeatureLineChartItemDto item = new StatsResultFeatureLineChartItemDto();

            // 映射查询结果到DTO字段
            item.setFeatureName((String) row.get("feature_name"));
            item.setFCreateTime((Timestamp) row.get("f_create_time"));

            // 处理数值类型转换
            Object cntObj = row.get("cnt");
            if (cntObj != null) {
                item.setCnt(((Number) cntObj).longValue());
            }

            Object avgObj = row.get("avg_value");
            if (avgObj != null) {
                item.setAvgValue(new BigDecimal(avgObj.toString()));
            }

            Object minObj = row.get("min_value");
            if (minObj != null) {
                item.setMinValue(new BigDecimal(minObj.toString()));
            }

            Object maxObj = row.get("max_value");
            if (maxObj != null) {
                item.setMaxValue(new BigDecimal(maxObj.toString()));
            }

            resultList.add(item);
        }

        return resultList;
    }

    @Override
    public List<StatsMethodChartItemDto> queryStatsMethods(StatsResultFeatureQueryCriteria criteria) {
        // 提取查询条件
        Timestamp beginTime = null;
        Timestamp endTime = null;

        if (criteria.getFeatureCreateTime() != null && criteria.getFeatureCreateTime().size() >= 2) {
            beginTime = criteria.getFeatureCreateTime().get(0);
            endTime = criteria.getFeatureCreateTime().get(1);
        }

        String methodName = StringUtils.isEmpty(criteria.getMethodName()) ? null : criteria.getMethodName();
        String sampleCategory = StringUtils.isEmpty(criteria.getSampleCategory()) ? null : criteria.getSampleCategory();

        // 调用Repository执行查询
        // List: {method_name:String, feature_conclusion:String, cnt:Long } feature_conclusion='NORMAL' 为成功数, 'ABNORMAL' 为失败数
        // 数据示例： [{"method_name":"ICP-OES检测","feature_conclusion":"NORMAL","cnt":100},{"method_name":"ICP-OES检测","feature_conclusion":"ABNORMAL","cnt":10}]
        List<Map<String, Object>> rawResults = statsResultFeatureRepository.findStatsAsMethod(
                beginTime, endTime, methodName, sampleCategory);
        List<StatsMethodChartItemDto> resultList = new ArrayList<>();
        for (Map<String, Object> row : rawResults) {

            Optional<StatsMethodChartItemDto> itemOpt = resultList.stream().filter(i -> i.getMethodName().equals(row.get("method_name"))).findFirst();
            StatsMethodChartItemDto item = null;
            if (!itemOpt.isPresent()) {
                item = new StatsMethodChartItemDto();
                item.setMethodName((String) row.get("method_name"));
                resultList.add(item);
            }else{
                item = itemOpt.get();
            }
            if ("NORMAL".equals(row.get("feature_conclusion"))) {
                item.setSuccessCount(((Number) row.get("cnt")).longValue());
            } else {
                item.setFailedCount(((Number) row.get("cnt")).longValue());
            }
        }

        return resultList;
    }

    @Override
    public List<StatsConclusionChartItemDto> queryStatsConclusions(StatsResultFeatureQueryCriteria criteria) {
        // 提取查询条件
        Timestamp beginTime = null;
        Timestamp endTime = null;

        if (criteria.getFeatureCreateTime() != null && criteria.getFeatureCreateTime().size() >= 2) {
            beginTime = criteria.getFeatureCreateTime().get(0);
            endTime = criteria.getFeatureCreateTime().get(1);
        }

        String methodName = StringUtils.isEmpty(criteria.getMethodName()) ? null : criteria.getMethodName();
        String sampleCategory = StringUtils.isEmpty(criteria.getSampleCategory()) ? null : criteria.getSampleCategory();

        // 调用Repository执行查询
        // List: {feature_conclusion:String, cnt:Long } feature_conclusion='NORMAL' 或 'ABNORMAL'
        // 数据示例： [{"feature_conclusion":"NORMAL","cnt":100},{"feature_conclusion":"ABNORMAL","cnt":10}]
        List<Map<String, Object>> rawResults = statsResultFeatureRepository.findStatsAsMethod(
                beginTime, endTime, methodName, sampleCategory);
        List<StatsConclusionChartItemDto> resultList = new ArrayList<>();
        for (Map<String, Object> row : rawResults) {
            StatsConclusionChartItemDto item = new StatsConclusionChartItemDto();
            item.setConclusion(row.get("feature_conclusion").toString());
            item.setCnt(((Number) row.get("cnt")).longValue());
            resultList.add(item);
        }

        return resultList;
    }

    @Override
    public List<StatsDateTimeChartItemDto> queryStatsDateTime(StatsResultFeatureQueryCriteria criteria) {
        // 提取查询条件
        Timestamp beginTime = null;
        Timestamp endTime = null;

        if (criteria.getFeatureCreateTime() != null && criteria.getFeatureCreateTime().size() >= 2) {
            beginTime = criteria.getFeatureCreateTime().get(0);
            endTime = criteria.getFeatureCreateTime().get(1);
        }

        String methodName = StringUtils.isEmpty(criteria.getMethodName()) ? null : criteria.getMethodName();
        String sampleCategory = StringUtils.isEmpty(criteria.getSampleCategory()) ? null : criteria.getSampleCategory();

        // 调用Repository执行查询
        // List: {f_create_time:Timestamp, cnt:Long } f_create_time 小时维度
        // 数据示例： [{"f_create_time":"2025-01-01 12:00:00","cnt":100},{"f_create_time":"2025-01-02 14:00:00","cnt":100}]
        List<Map<String, Object>> rawResults = statsResultFeatureRepository.findStatsAsDateTime(
                beginTime, endTime, methodName, sampleCategory);
        List<StatsDateTimeChartItemDto> resultList = new ArrayList<>();
        for (Map<String, Object> row : rawResults) {
            StatsDateTimeChartItemDto item = new StatsDateTimeChartItemDto();
            item.setFCreateTime((Timestamp) row.get("f_create_time"));
            item.setCnt(((Number) row.get("cnt")).longValue());
            resultList.add(item);
        }

        return resultList;
    }
}