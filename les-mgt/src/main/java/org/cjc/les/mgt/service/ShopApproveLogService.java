/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service;

import org.cjc.les.mgt.domain.ShopApproveLog;
import org.cjc.les.mgt.service.dto.ShopApproveLogDto;
import org.cjc.les.mgt.service.dto.ShopApproveLogQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2022-07-10
**/
public interface ShopApproveLogService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ShopApproveLogQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ShopApproveLogDto>
    */
    List<ShopApproveLogDto> queryAll(ShopApproveLogQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return ShopApproveLogDto
     */
    ShopApproveLogDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return ShopApproveLogDto
    */
    ShopApproveLogDto create(ShopApproveLog resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(ShopApproveLog resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ShopApproveLogDto> all, HttpServletResponse response) throws IOException;
}