/*
 *  Copyright 2024-2024 <PERSON>han <PERSON>mei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import lombok.extern.log4j.Log4j2;
import org.cjc.annotation.Log;
import org.cjc.annotation.rest.AnonymousGetMapping;
import org.cjc.common.uniapp.UniappResponseEntity;
import org.cjc.les.core.domain.DeviceLayout;
import org.cjc.les.core.service.DeviceLayoutService;
import org.cjc.les.core.service.dto.DeviceLayoutQueryCriteria;
import org.cjc.les.exception.ConfigApplicationException;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-04
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "设备布局管理")
@RequestMapping("/api/deviceLayout")
@Log4j2
public class DeviceLayoutController {

    private final DeviceLayoutService deviceLayoutService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('deviceLayout:list')")
    public void exportDeviceLayout(HttpServletResponse response, DeviceLayoutQueryCriteria criteria) throws IOException {
        deviceLayoutService.download(deviceLayoutService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询设备布局")
    @ApiOperation("查询设备布局")
    @PreAuthorize("@el.check('deviceLayout:list')")
    public ResponseEntity<Object> queryDeviceLayout(DeviceLayoutQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(deviceLayoutService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @AnonymousGetMapping("/getLatest")
    @ApiOperation("查询设备布局")
   // @PreAuthorize("@el.check('deviceLayout:list')")
    public ResponseEntity<Object> getLatest(DeviceLayoutQueryCriteria criteria){
        String status = (criteria==null)?null:criteria.getStatus();
        return new ResponseEntity<>(deviceLayoutService.findLatest(status),HttpStatus.OK);
    }

    @AnonymousGetMapping("/getLatestLayoutStyle")
    @ApiOperation("查询设备布局")
    public ResponseEntity<Object> getLatestLayoutStyle(DeviceLayoutQueryCriteria criteria){
        String status = (criteria==null)?null:criteria.getStatus();
        return new ResponseEntity<>(deviceLayoutService.getLatestLayoutStyle(status),HttpStatus.OK);
    }

    @AnonymousGetMapping("/findAllDevicesInLatestLayout")
    @Log("查询设备布局")
    @ApiOperation("查询设备布局")
    // @PreAuthorize("@el.check('deviceLayout:list')")
    public ResponseEntity<Object> findAllDevicesInLatestLayout(DeviceLayoutQueryCriteria criteria){
        String status = (criteria==null)?null:criteria.getStatus();
        return new ResponseEntity<>(deviceLayoutService.findAllDevicesInLatestLayout(),HttpStatus.OK);
    }
    @PostMapping
    @Log("新增设备布局")
    @ApiOperation("新增设备布局")
    @PreAuthorize("@el.check('deviceLayout:add')")
    public ResponseEntity<Object> createDeviceLayout(@Validated @RequestBody DeviceLayout resources){
        return new ResponseEntity<>(deviceLayoutService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改设备布局")
    @ApiOperation("修改设备布局")
    @PreAuthorize("@el.check('deviceLayout:edit')")
    public ResponseEntity<Object> updateDeviceLayout(@Validated @RequestBody DeviceLayout resources){
        try{
            deviceLayoutService.update(resources);
        } catch (ConfigApplicationException e) {
            log.error("updateDeviceLayout: {}", e.getMessage(), e);
            UniappResponseEntity<List<Map>> resp = new UniappResponseEntity();
            resp.setStatus(400);
            resp.setMessage("更新失败");
            resp.setData((List)e.getData());
            return new ResponseEntity<>(resp, HttpStatus.OK);
        }
        return new ResponseEntity<>(deviceLayoutService.findById(resources.getId()), HttpStatus.OK);
    }

    @DeleteMapping
    @Log("删除设备布局")
    @ApiOperation("删除设备布局")
    @PreAuthorize("@el.check('deviceLayout:del')")
    public ResponseEntity<Object> deleteDeviceLayout(@RequestBody Long[] ids) {
        deviceLayoutService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping("/resetAllPositions")
    @Log("修改设备布局")
    @ApiOperation("修改设备布局")
    @PreAuthorize("@el.check('deviceLayout:edit')")
    public ResponseEntity<Object> resetAllPositions(@Validated @RequestBody DeviceLayout resources){
        return new ResponseEntity<>(deviceLayoutService.resetAllPositions(resources), HttpStatus.OK);
    }

}