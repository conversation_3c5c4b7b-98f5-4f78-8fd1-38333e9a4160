/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.TaskCommand;
import org.cjc.les.core.service.TaskCommandService;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.core.service.dto.TaskCommandQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务运行的命令管理")
@RequestMapping("/api/taskCommand")
public class TaskCommandController {

    private final TaskCommandService taskCommandService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskCommand:list')")
    public void exportTaskCommand(HttpServletResponse response, TaskCommandQueryCriteria criteria) throws IOException {
        taskCommandService.download(taskCommandService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务运行的命令")
    @ApiOperation("查询任务运行的命令")
    @PreAuthorize("@el.check('taskCommand:list')")
    public ResponseEntity<Object> queryTaskCommand(TaskCommandQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskCommandService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务运行的命令")
    @ApiOperation("新增任务运行的命令")
    @PreAuthorize("@el.check('taskCommand:add')")
    public ResponseEntity<Object> createTaskCommand(@Validated @RequestBody TaskCommand resources){
        return new ResponseEntity<>(taskCommandService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务运行的命令")
    @ApiOperation("修改任务运行的命令")
    @PreAuthorize("@el.check('taskCommand:edit')")
    public ResponseEntity<Object> updateTaskCommand(@Validated @RequestBody TaskCommand resources){
        taskCommandService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PutMapping("/updateFailureFixInfo")
    @Log("修改任务运行的命令")
    @ApiOperation("修改任务运行的命令")
    @PreAuthorize("@el.check('taskCommand:edit')")
    public ResponseEntity<Object> updateFailureFixInfo(@Validated @RequestBody TaskCommandDto dto){
        taskCommandService.updateFailureFixInfo(dto);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务运行的命令")
    @ApiOperation("删除任务运行的命令")
    @PreAuthorize("@el.check('taskCommand:del')")
    public ResponseEntity<Object> deleteTaskCommand(@RequestBody Long[] ids) {
        taskCommandService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}