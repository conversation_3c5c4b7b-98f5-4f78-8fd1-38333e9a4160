/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-08-04
**/
@Data
public class DeviceMockDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 设备名称 */
    private String name;

    /** 设备描述 */
    private String description;

    /** 设备实例ID */
    private Long deviceInstanceId;

    /** 命令行 */
    private String commandLine;

    /** 该设备自定义的Java类名 */
    private String javaClassName;

    private String javaClasspath;

    /** 配置信息 */
    private String config;

    private String image;

    private String log;

    /** 输入提示 */
    private List<String> prompts = new ArrayList<>();

    /**
     * 用户输入的字符串
     */
    private String inputScanner;

    /** 运行状态 */
    private String status;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}