/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.AlertLog;
import org.cjc.les.core.service.AlertLogService;
import org.cjc.les.core.service.dto.AlertLogDto;
import org.cjc.les.core.service.dto.AlertLogQueryCriteria;
import org.cjc.les.core.service.dto.AlertStatDto;
import org.cjc.les.core.service.dto.AlertStatItemDto;
import org.cjc.les.core.service.mapstruct.AlertLogMapper;
import org.cjc.utils.SecurityUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-10-14
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "告警日志管理")
@RequestMapping("/api/alert")
public class AlertController {

    private final AlertLogService alertLogService;

    private final AlertLogMapper alertLogMapper;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('alertLog:list')")
    public void exportAlertLog(HttpServletResponse response, AlertLogQueryCriteria criteria) throws IOException {
        alertLogService.download(alertLogService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询告警日志")
    @ApiOperation("查询告警日志")
    @PreAuthorize("@el.check('alertLog:list')")
    public ResponseEntity<Object> queryAlertLog(AlertLogQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(alertLogService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/findAlertStats")
    @ApiOperation("查询告警日志")
    //@PreAuthorize("@el.check('alertLog:list')")
    public ResponseEntity<Object> findAlertStats() {
        List<AlertStatItemDto> statItemDtoList = alertLogService.findAlertLogStatsLevelInInitStatus();
        List<AlertLogDto> alertLogDtoList = alertLogService.findLatestAlertLogInInitStatus();
        AlertStatDto dto = new AlertStatDto();
        dto.setAlertLogList(alertLogDtoList);
        dto.setAlertStatItemList(statItemDtoList);
        return new ResponseEntity<>(dto, HttpStatus.OK);
    }

    @PostMapping
    @Log("新增告警日志")
    @ApiOperation("新增告警日志")
    @PreAuthorize("@el.check('alertLog:add')")
    public ResponseEntity<Object> createAlertLog(@Validated @RequestBody AlertLog resources){
        return new ResponseEntity<>(alertLogService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改告警日志")
    @ApiOperation("修改告警日志")
    @PreAuthorize("@el.check('alertLog:edit')")
    public ResponseEntity<Object> updateAlertLog(@Validated @RequestBody AlertLog resources){
        alertLogService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PutMapping("/clearAlertLog")
    @Log("修改告警日志")
    @ApiOperation("修改告警日志")
    @PreAuthorize("@el.check('alertLog:edit')")
    public ResponseEntity<Object> clearAlertLog(@RequestBody AlertLogDto resources){
        resources.setFixedBy(SecurityUtils.getCurrentUsername());
        AlertLog alertLog = alertLogMapper.toEntity(resources);
        alertLogService.fixLog(alertLog);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除告警日志")
    @ApiOperation("删除告警日志")
    @PreAuthorize("@el.check('alertLog:del')")
    public ResponseEntity<Object> deleteAlertLog(@RequestBody Long[] ids) {
        alertLogService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}