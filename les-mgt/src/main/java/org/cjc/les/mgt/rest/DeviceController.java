/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.cjc.annotation.Log;
import org.cjc.exception.ApplicationException;
import org.cjc.les.core.domain.Device;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.service.DeviceService;
import org.cjc.les.core.service.dto.DeviceQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-05-29
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "设备管理")
@RequestMapping("/api/device")
public class DeviceController {

    private final DeviceService deviceService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('device:list')")
    public void exportDevice(HttpServletResponse response, DeviceQueryCriteria criteria) throws IOException {
        deviceService.download(deviceService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询店铺管理")
    @ApiOperation("查询店铺管理")
    @PreAuthorize("@el.check('device:list')")
    public ResponseEntity<Object> queryDevice(DeviceQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(deviceService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/queryAllDevices")
    @Log("查询所有有效设备定义")
    @ApiOperation("查询所有有效设备定义")
    @PreAuthorize("@el.check('device:list')")
    public ResponseEntity<Object> queryAllDevices(){
        return new ResponseEntity<>(deviceService.findAllValidDevices(),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增设备")
    @ApiOperation("新增设备")
    @PreAuthorize("@el.check('device:add')")
    public ResponseEntity<Object> createDevice(@Validated @RequestBody Device resources){
        return new ResponseEntity<>(deviceService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改设备")
    @ApiOperation("修改设备")
    @PreAuthorize("@el.check('device:edit')")
    public ResponseEntity<Object> updateDevice(@Validated @RequestBody Device resources){
        deviceService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/createDevice")
    @Log("新增设备实例")
    @ApiOperation("新增设备实例")
    @PreAuthorize("@el.check('device:add')")
    public ResponseEntity<Object> createDeviceInstance(@Validated @RequestBody DeviceInstance resources){
        return new ResponseEntity<>(deviceService.createDeviceInstance(resources),HttpStatus.CREATED);
    }

    @DeleteMapping
    @Log("删除设备")
    @ApiOperation("删除设备")
    @PreAuthorize("@el.check('device:del')")
    public ResponseEntity<Object> deleteDevice(@RequestBody Long[] ids)  throws ApplicationException {
        deviceService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}