/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.SampleCategory;
import org.cjc.les.core.service.SampleCategoryService;
import org.cjc.les.core.service.dto.SampleCategoryQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "样品分类管理管理")
@RequestMapping("/api/sampleCategory")
public class SampleCategoryController {

    private final SampleCategoryService sampleCategoryService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sampleCategory:list')")
    public void exportSampleCategory(HttpServletResponse response, SampleCategoryQueryCriteria criteria) throws IOException {
        sampleCategoryService.download(sampleCategoryService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询样品分类管理")
    @ApiOperation("查询样品分类管理")
    @PreAuthorize("@el.check('sampleCategory:list')")
    public ResponseEntity<Object> querySampleCategory(SampleCategoryQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sampleCategoryService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/selectSampleCategories")
    @Log("查询样品分类管理")
    @ApiOperation("查询样品分类管理")
    @PreAuthorize("@el.check('sample:list')")
    public ResponseEntity<Object> selectSampleCategories(SampleCategoryQueryCriteria criteria){
        return new ResponseEntity<>(sampleCategoryService.queryAll(criteria),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增样品分类管理")
    @ApiOperation("新增样品分类管理")
    @PreAuthorize("@el.check('sampleCategory:add')")
    public ResponseEntity<Object> createSampleCategory(@Validated @RequestBody SampleCategory resources){
        return new ResponseEntity<>(sampleCategoryService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改样品分类管理")
    @ApiOperation("修改样品分类管理")
    @PreAuthorize("@el.check('sampleCategory:edit')")
    public ResponseEntity<Object> updateSampleCategory(@Validated @RequestBody SampleCategory resources){
        sampleCategoryService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除样品分类管理")
    @ApiOperation("删除样品分类管理")
    @PreAuthorize("@el.check('sampleCategory:del')")
    public ResponseEntity<Object> deleteSampleCategory(@RequestBody Long[] ids) {
        sampleCategoryService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}