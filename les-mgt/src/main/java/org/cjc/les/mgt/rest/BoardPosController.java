/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.BoardPos;
import org.cjc.les.core.service.BoardPosService;
import org.cjc.les.core.service.dto.BoardPosQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-04-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "板位管理管理")
@RequestMapping("/api/boardPos")
public class BoardPosController {

    private final BoardPosService boardPosService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('boardPos:list')")
    public void exportBoardPos(HttpServletResponse response, BoardPosQueryCriteria criteria) throws IOException {
        boardPosService.download(boardPosService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询板位管理")
    @ApiOperation("查询板位管理")
    @PreAuthorize("@el.check('boardPos:list')")
    public ResponseEntity<Object> queryBoardPos(BoardPosQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(boardPosService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增板位管理")
    @ApiOperation("新增板位管理")
    @PreAuthorize("@el.check('boardPos:add')")
    public ResponseEntity<Object> createBoardPos(@Validated @RequestBody BoardPos resources){
        return new ResponseEntity<>(boardPosService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改板位管理")
    @ApiOperation("修改板位管理")
    @PreAuthorize("@el.check('boardPos:edit')")
    public ResponseEntity<Object> updateBoardPos(@Validated @RequestBody BoardPos resources){
        boardPosService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除板位管理")
    @ApiOperation("删除板位管理")
    @PreAuthorize("@el.check('boardPos:del')")
    public ResponseEntity<Object> deleteBoardPos(@RequestBody Long[] ids) {
        boardPosService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}