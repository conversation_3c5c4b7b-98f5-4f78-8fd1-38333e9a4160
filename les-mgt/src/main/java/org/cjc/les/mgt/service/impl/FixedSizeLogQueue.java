/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import java.util.LinkedList;

public class FixedSizeLogQueue<T> {

    private int maxSize = 10;

    private LinkedList<T> queue = new LinkedList<>();

    public FixedSizeLogQueue(int maxSize) {
        this.maxSize = maxSize;
        this.queue = new LinkedList<>();
    }

    public void add(T element) {
        if (queue.size() >= maxSize) {
            queue.removeFirst(); // 移除最早的元素
        }
        queue.addLast(element); // 添加新元素到队列末尾
    }

    public T poll() {
        return queue.pollFirst(); // 获取并移除队列头部的元素
    }

    public T peek() {
        return queue.peekFirst(); // 获取但不移除队列头部的元素
    }

    public T last() {
        return queue.getLast(); // 获取队尾元素
    }

    public int size() {
        return queue.size(); // 返回当前队列的大小
    }

    public boolean isEmpty() {
        return queue.isEmpty(); // 检查队列是否为空
    }

    @Override
    public String toString() {
        StringBuffer strBuf = new StringBuffer();
        for (T item : queue) {
            strBuf.append(item).append("\r\n");
        }
        return strBuf.toString(); // 返回队列的字符串表示
    }
}
