/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.ProcedureRepository;
import org.cjc.les.core.repository.TaskPrepareRepository;
import org.cjc.les.core.repository.TaskRepository;
import org.cjc.les.core.service.TaskPrepareService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.impl.ConveyorExecutor;
import org.cjc.les.core.service.impl.TagReaderExecutor;
import org.cjc.les.core.service.mapstruct.TaskPrepareMapper;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.EntryScheduler;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.mgt.service.TaskPrepareBatchService;
import org.cjc.les.mgt.service.dto.TaskPrepareViewDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-08-04
 **/
@Log4j2
@Service
@RequiredArgsConstructor
public class TaskPrepareBatchServiceImpl implements TaskPrepareBatchService {

    /**
     * 任务预处理视图队列
     * mode: FACTORY与SAMPLE互不影响，TRIAL与FACTORY,SAMPLE互相排斥
     */
    private LinkedBlockingQueue<TaskPrepareViewDto> taskPrepareViewQueue = new LinkedBlockingQueue<>();

    private final TagReaderExecutor tagReaderExecutor;

    private final ConveyorExecutor conveyorExecutor;

    private final TaskPrepareRepository taskPrepareRepository;
    private final TaskPrepareMapper taskPrepareMapper;

    private final TaskPrepareService taskPrepareService;

    private final ProcedureRepository procedureRepository;

    private final TaskRepository taskRepository;

    private final StepScheduler stepScheduler;

    private final TaskService taskService;
    private final TaskScheduler taskScheduler;
    private final EntryScheduler entryScheduler;


    private String getTaskPrepareStatus(TaskPrepareDto dto) {
        String outStatus = dto.getStatus();
        Task task = taskRepository.findByTaskNumber(dto.getTaskNumber());
        if (task == null){
            log.warn("Could not found task by taskNumber={}", dto.getTaskNumber());
            return outStatus;
        }
        Optional<TaskMethod> entryMethodOpt = task.getMethodList().stream().filter(mth -> {
            return StringUtils.equalsAny(mth.getType(), "ENTRY");
        }).findFirst();
        if (!entryMethodOpt.isPresent()) {
            return outStatus;
        }

        TaskMethod entryMethod = entryMethodOpt.get();
        Optional<TaskMethod> entryMethodInQueOpt = entryScheduler.getMethodsQueue().stream().filter(mth -> {
            return mth.getId().equals(entryMethod.getId());
        }).findFirst();
        if (!entryMethodInQueOpt.isPresent()) {
            return outStatus;
        }

        TaskMethod entryMethodInQue = entryMethodInQueOpt.get();

        for (TaskStep step : entryMethodInQue.getSteps()) {
            for (TaskAction action : step.getActions()) {
                for (TaskCommand cmd : action.getCommands()) {
                    if (StringUtils.equalsAny(cmd.getStatus(), RunStatusEnum.FAILED.name())) {
                        return RunStatusEnum.FAILED.name();
                    }
                }

            }
        }

        return outStatus;
    }

    private void submitTaskSchedulerImmediately(TaskPrepare paramTaskPrepare){

        TaskPrepare taskPrepare = taskPrepareRepository.findById(paramTaskPrepare.getId()).orElseGet(TaskPrepare::new);

        Procedure procedure = taskPrepare.getProcedure();

        Task task = new Task();
        task.copy(procedure, true);
        task.setPrepareTaskId(taskPrepare.getId());
        task.setStatus(CommandStatusEnum.READY.name());
        task.setTaskNumber(taskPrepare.getTaskNumber());
        task.setTaskName(taskPrepare.getSample().getName() + ":" + procedure.getName());
        task.setTag(taskPrepare.getRfTag());
        task.setSample(taskPrepare.getSample());

        //1. 保存任务状态
        taskService.create(task);

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);

        task.setMessage("任务创建成功，等待调度执行");
        TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.INFO);

        taskScheduler.schedule(task);

        taskPrepare.setStatus("PUSHED");
        taskPrepareRepository.save(taskPrepare);

    }

    private boolean checkIfEntryMethodPresent(TaskPrepare paramTp) {
        return taskPrepareService.checkIfEntryMethodPresent(paramTp);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitBatchTasks(TaskPrepareViewDto dto) {

        List<TaskPrepareDto> tpDtoList = dto.getTaskPrepareDtos();
        if ("BOARD".equalsIgnoreCase(dto.getMode())) {
            tpDtoList = taskPrepareService.batchSaveTaskPrepares(dto.getTaskPrepareDtos());
        }

        // 创建任务
        List<Task> tasks = createTasks(tpDtoList);

        // 校验任务提交条件



        // 提交任务
        for (Task task : tasks) {

            TaskExecutorContext.clear();
            TaskExecutorContext.setTask(task);
            if (dto.getDebugMode()!=null && dto.getDebugMode().booleanValue()) {
                TaskExecutorContext.getContext().getExecutorConfig().setDebugMode(ExecutorConfig.DebugModeEnum.CONTINUE);
            }
            task.setMessage("任务创建成功，等待调度执行");
            TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.INFO);

            taskScheduler.schedule(task);

            TaskPrepare taskPrepare = taskPrepareRepository.findById(task.getPrepareTaskId()).orElseGet(TaskPrepare::new);
            taskPrepare.setStatus("PUSHED");
            taskPrepareRepository.save(taskPrepare);
        }
    }

    private List<Task> createTasks(List<TaskPrepareDto> tpDtoList) {
        List<Task> outList = new ArrayList<>();
        long curTm = System.currentTimeMillis();
        for ( TaskPrepareDto tpDto : tpDtoList) {
            TaskPrepare taskPrepare = taskPrepareRepository.findById(tpDto.getId()).orElseGet(TaskPrepare::new);

            Procedure procedure = procedureRepository.findById(taskPrepare.getProcedure().getId()).get();

            if (StringUtils.equals(taskPrepare.getParallel(),"Y")){
                List<ProcedureMethod> methods = procedure.getMainMethods();
                for (ProcedureMethod mth : methods) {
                    mth.setParallel("Y");
                }
            }

            Task task = new Task();
            task.copy(procedure, true);
            task.setPrepareTaskId(taskPrepare.getId());
            task.setStatus(CommandStatusEnum.READY.name());
            task.setTaskNumber(taskPrepare.getTaskNumber());
            task.setTaskName(taskPrepare.getSample().getName() + ":" + procedure.getName());
            task.setTag(taskPrepare.getRfTag());
            task.setSample(taskPrepare.getSample());
            task.setBatchId(Long.valueOf(taskPrepare.getRfTag().hashCode() + curTm));

            //1. 保存任务状态
            taskService.create(task);
            outList.add(task);
        }
        return outList;
    }

    @Override
    public void cancelCurrentTaskPrepareView(TaskPrepareViewDto dto) {

    }
}