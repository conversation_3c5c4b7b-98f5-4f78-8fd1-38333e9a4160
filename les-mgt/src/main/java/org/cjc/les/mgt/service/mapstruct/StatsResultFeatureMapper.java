/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.les.mgt.domain.StatsResultFeature;
import org.cjc.les.mgt.service.dto.StatsResultFeatureDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-31
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StatsResultFeatureMapper extends BaseMapper<StatsResultFeatureDto, StatsResultFeature> {

}