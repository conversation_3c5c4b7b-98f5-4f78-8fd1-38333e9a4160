/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.repository;

import org.cjc.les.mgt.domain.ShopInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-05-29
**/
public interface ShopInfoRepository extends JpaRepository<ShopInfo, Long>, JpaSpecificationExecutor<ShopInfo> {
    @Query(value = "SELECT\n" +
            "\tshop.*  \n" +
            "FROM\n" +
            "\tshop_info shop,\n" +
            "\tsys_user_shops us \n" +
            "WHERE\n" +
            "\tus.shop_id = shop.shop_id\n" +
            "\tAND us.user_id =?1", nativeQuery = true)
    List<ShopInfo> queryShopShortListByUserId(Long userId);

    @Query(value = "select shop.* from shop_info shop where shop.area_id =?1 limit 1", nativeQuery = true)
    Optional<ShopInfo> findDefaultShop(Long areaId);
}