/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import org.cjc.exception.EntityExistException;
import org.cjc.les.mgt.domain.DeviceMock;
import lombok.RequiredArgsConstructor;
import org.cjc.les.mgt.repository.DeviceMockRepository;
import org.cjc.les.mgt.service.DeviceMockService;
import org.cjc.les.mgt.service.dto.DeviceMockDto;
import org.cjc.les.mgt.service.dto.DeviceMockQueryCriteria;
import org.cjc.les.mgt.service.mapstruct.DeviceMockMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-08-04
**/
@Service
@RequiredArgsConstructor
public class DeviceMockServiceImpl implements DeviceMockService {

    private final DeviceMockRepository deviceMockRepository;
    private final DeviceMockMapper deviceMockMapper;

    private final DeviceMockManager deviceMockManager;

    @Override
    public Map<String,Object> queryAll(DeviceMockQueryCriteria criteria, Pageable pageable){
        Page<DeviceMock> page = deviceMockRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        List<DeviceMock> mockList = page.getContent();
        for (DeviceMock deviceMock : mockList) {
            deviceMockManager.fillStatus(deviceMock);
            deviceMockManager.fillLog(deviceMock);
        }
        return PageUtil.toPage(page.map(deviceMockMapper::toDto));
    }

    @Override
    public List<DeviceMockDto> queryAll(DeviceMockQueryCriteria criteria){
        List<DeviceMock> result = deviceMockRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder));
        result.stream().forEach(deviceMock -> {
            deviceMockManager.fillStatus(deviceMock);
            deviceMockManager.fillLog(deviceMock);
        });
        return deviceMockMapper.toDto(result);
    }

    @Override
    public DeviceMockDto start(DeviceMockDto dto) {
        DeviceMock deviceMock = deviceMockRepository.findById(dto.getId()).orElseGet(DeviceMock::new);
        deviceMockManager.start(deviceMock);
        return deviceMockMapper.toDto(deviceMock);
    }
    @Override
    public DeviceMockDto stop(DeviceMockDto dto) {
        DeviceMock deviceMock = deviceMockRepository.findById(dto.getId()).orElseGet(DeviceMock::new);
        deviceMockManager.stop(deviceMock);
        return deviceMockMapper.toDto(deviceMock);
    }

    @Override
    public DeviceMockDto inputScanner(DeviceMockDto dto) {
        DeviceMock deviceMock = deviceMockRepository.findById(dto.getId()).orElseGet(DeviceMock::new);
        deviceMockManager.inputScanner(deviceMock, dto.getInputScanner());
        return dto;
    }

    @Override
    @Transactional
    public DeviceMockDto findById(Long id) {
        DeviceMock deviceMock = deviceMockRepository.findById(id).orElseGet(DeviceMock::new);
        ValidationUtil.isNull(deviceMock.getId(),"DeviceMock","id",id);
        return deviceMockMapper.toDto(deviceMock);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceMockDto create(DeviceMock resources) {
        if(deviceMockRepository.findByJavaClassNameAndConfig(resources.getJavaClassName(), resources.getConfig()) != null){
            throw new EntityExistException(DeviceMock.class,"java_class_name",resources.getJavaClassName());
        }
        return deviceMockMapper.toDto(deviceMockRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceMock resources) {
        DeviceMock deviceMock = deviceMockRepository.findById(resources.getId()).orElseGet(DeviceMock::new);
        ValidationUtil.isNull( deviceMock.getId(),"DeviceMock","id",resources.getId());

        deviceMock.copy(resources);
        deviceMockRepository.save(deviceMock);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            deviceMockRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<DeviceMockDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DeviceMockDto deviceMock : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("设备名称", deviceMock.getName());
            map.put("设备描述", deviceMock.getDescription());
            map.put("设备实例ID", deviceMock.getDeviceInstanceId());
            map.put("命令行", deviceMock.getCommandLine());
            map.put("该设备自定义的Java类名", deviceMock.getJavaClassName());
            map.put("配置信息", deviceMock.getConfig());
            map.put("运行状态", deviceMock.getStatus());
            map.put("是否已被删除,Y/N", deviceMock.getDeleteFlag());
            map.put("创建人", deviceMock.getCreateBy());
            map.put("创建时间", deviceMock.getCreateTime());
            map.put("更新人", deviceMock.getUpdateBy());
            map.put("更新时间", deviceMock.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}