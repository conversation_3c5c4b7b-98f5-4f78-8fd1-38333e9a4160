/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Result;
import org.cjc.les.core.service.ResultService;
import org.cjc.les.core.service.dto.ResultDto;
import org.cjc.les.core.service.dto.ResultQueryCriteria;
import org.cjc.les.mgt.service.ResultReportService;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-10-30
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "检测结果管理管理")
@RequestMapping("/api/resultReport")
public class ResultReportController {

    private final ResultReportService resultReportService;

    private final ResultService resultService;


    @Log("导出报告PDF")
    @ApiOperation("导出报告PDF")
    @GetMapping(value = "/downloadReport/{id}")
    @PreAuthorize("@el.check('result:list')")
    public void exportReport(HttpServletRequest request, HttpServletResponse response, @PathVariable Long id) throws IOException {
        ResultDto dto = new ResultDto();
        dto.setId(id);
        resultReportService.downloadReport(dto, request, response);
    }

}