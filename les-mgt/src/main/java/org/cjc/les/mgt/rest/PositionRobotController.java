/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.PositionRobot;
import org.cjc.les.core.service.PositionRobotService;
import org.cjc.les.core.service.dto.PositionRobotQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-04-16
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "机器人点位配置管理")
@RequestMapping("/api/positionRobot")
public class PositionRobotController {

    private final PositionRobotService positionRobotService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('positionRobot:list')")
    public void exportPositionRobot(HttpServletResponse response, PositionRobotQueryCriteria criteria) throws IOException {
        positionRobotService.download(positionRobotService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询机器人点位配置")
    @ApiOperation("查询机器人点位配置")
    @PreAuthorize("@el.check('positionRobot:list')")
    public ResponseEntity<Object> queryPositionRobot(PositionRobotQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(positionRobotService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增机器人点位配置")
    @ApiOperation("新增机器人点位配置")
    @PreAuthorize("@el.check('positionRobot:add')")
    public ResponseEntity<Object> createPositionRobot(@Validated @RequestBody PositionRobot resources){
        return new ResponseEntity<>(positionRobotService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改机器人点位配置")
    @ApiOperation("修改机器人点位配置")
    @PreAuthorize("@el.check('positionRobot:edit')")
    public ResponseEntity<Object> updatePositionRobot(@Validated @RequestBody PositionRobot resources){
        positionRobotService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除机器人点位配置")
    @ApiOperation("删除机器人点位配置")
    @PreAuthorize("@el.check('positionRobot:del')")
    public ResponseEntity<Object> deletePositionRobot(@RequestBody Long[] ids) {
        positionRobotService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}