/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import net.dreamlu.mica.core.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.annotation.Log;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.domain.DeviceInstanceCmd;
import org.cjc.les.core.service.DeviceInstanceCmdService;
import org.cjc.les.core.service.DeviceLayoutService;
import org.cjc.les.core.service.dto.DeviceInstanceCmdDto;
import org.cjc.les.core.service.dto.DeviceInstanceCmdQueryCriteria;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.mapstruct.DeviceInstanceCmdMapper;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-07-19
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "设备实例命令管理")
@RequestMapping("/api/deviceInstanceCmd")
public class DeviceInstanceCmdController {

    private final DeviceInstanceCmdService deviceInstanceCmdService;

    private final DeviceLayoutService deviceLayoutService;

    private final DeviceInstanceCmdMapper deviceInstanceCmdMapper;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('deviceInstanceCmd:list')")
    public void exportDeviceInstanceCmd(HttpServletResponse response, DeviceInstanceCmdQueryCriteria criteria) throws IOException {
        deviceInstanceCmdService.download(deviceInstanceCmdService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询设备实例命令")
    @ApiOperation("查询设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:list')")
    public ResponseEntity<Object> queryDeviceInstanceCmd(DeviceInstanceCmdQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(deviceInstanceCmdService.queryAll(criteria,pageable),HttpStatus.OK);
    }
    @GetMapping("/queryNoneProxyCmdForSection")
    @Log("查询设备实例命令")
    @ApiOperation("查询设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:list')")
    public ResponseEntity<Object> queryNoneProxyCmdForSection(){
        List<DeviceInstance> deviceInstanceList = deviceLayoutService.findAllDevicesInLatestLayout();
        List<DeviceInstanceCmdDto> instanceCmdDtoList = new ArrayList<>();
        for (DeviceInstance devInst : deviceInstanceList) {
            List<DeviceInstanceCmd> filteredCmdList = devInst.getCommands().stream().filter(cmd->{
                return !StringUtils.equals("PROXY",cmd.getCommandType());
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredCmdList)) {
                for (DeviceInstanceCmd insCmd : filteredCmdList){
                    DeviceInstanceCmdDto dto = deviceInstanceCmdMapper.toDto(insCmd);
                    dto.setDeviceInstanceName(insCmd.getDeviceInstance().getName());
                    dto.setDeviceInstanceId(insCmd.getDeviceInstance().getId());
                    instanceCmdDtoList.add(dto);
                }
            }
        }
        return new ResponseEntity<>(instanceCmdDtoList,HttpStatus.OK);
    }

    @PostMapping
    @Log("新增设备实例命令")
    @ApiOperation("新增设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:add')")
    public ResponseEntity<Object> createDeviceInstanceCmd(@Validated @RequestBody DeviceInstanceCmd resources){
        return new ResponseEntity<>(deviceInstanceCmdService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改设备实例命令")
    @ApiOperation("修改设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:edit')")
    public ResponseEntity<Object> updateDeviceInstanceCmd(@Validated @RequestBody DeviceInstanceCmd resources){
        deviceInstanceCmdService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除设备实例命令")
    @ApiOperation("删除设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:del')")
    public ResponseEntity<Object> deleteDeviceInstanceCmd(@RequestBody Long[] ids) {
        deviceInstanceCmdService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PostMapping("/executeCommand")
    @Log("新增设备实例命令")
    @ApiOperation("新增设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:add')")
    public ResponseEntity<Object> executeCommand( @RequestBody DeviceInstanceCmdDto resources){
        if (StringUtils.isNotBlank(resources.getTestParameter())) {
            deviceInstanceCmdService.updateTestParameter(resources.getId(), resources.getTestParameter());
        }
        return new ResponseEntity<>(deviceInstanceCmdService.executeCommand(resources),HttpStatus.CREATED);
    }

    @PostMapping("/executeCommandByControlCode")
    @Log("新增设备实例命令")
    @ApiOperation("新增设备实例命令")
    @PreAuthorize("@el.check('deviceInstanceCmd:add')")
    public ResponseEntity<Object> executeCommandByControlCode( @RequestBody DeviceInstanceCmdDto resources){
        return new ResponseEntity<>(deviceInstanceCmdService.executeCommandByControlCode(resources),HttpStatus.CREATED);
    }


}