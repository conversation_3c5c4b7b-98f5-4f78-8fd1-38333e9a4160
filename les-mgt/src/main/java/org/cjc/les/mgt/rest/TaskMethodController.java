/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.TaskMethod;
import org.cjc.les.core.service.TaskMethodService;
import org.cjc.les.core.service.dto.TaskMethodQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务运行的方法管理")
@RequestMapping("/api/taskMethod")
public class TaskMethodController {

    private final TaskMethodService taskMethodService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskMethod:list')")
    public void exportTaskMethod(HttpServletResponse response, TaskMethodQueryCriteria criteria) throws IOException {
        taskMethodService.download(taskMethodService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务运行的方法")
    @ApiOperation("查询任务运行的方法")
    @PreAuthorize("@el.check('taskMethod:list')")
    public ResponseEntity<Object> queryTaskMethod(TaskMethodQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskMethodService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务运行的方法")
    @ApiOperation("新增任务运行的方法")
    @PreAuthorize("@el.check('taskMethod:add')")
    public ResponseEntity<Object> createTaskMethod(@Validated @RequestBody TaskMethod resources){
        return new ResponseEntity<>(taskMethodService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务运行的方法")
    @ApiOperation("修改任务运行的方法")
    @PreAuthorize("@el.check('taskMethod:edit')")
    public ResponseEntity<Object> updateTaskMethod(@Validated @RequestBody TaskMethod resources){
        taskMethodService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务运行的方法")
    @ApiOperation("删除任务运行的方法")
    @PreAuthorize("@el.check('taskMethod:del')")
    public ResponseEntity<Object> deleteTaskMethod(@RequestBody Long[] ids) {
        taskMethodService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}