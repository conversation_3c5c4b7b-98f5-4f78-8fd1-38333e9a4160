/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import org.cjc.les.mgt.service.ShopApproveLogService;
import org.cjc.les.mgt.domain.ShopApproveLog;
import lombok.RequiredArgsConstructor;
import org.cjc.les.mgt.repository.ShopApproveLogRepository;
import org.cjc.les.mgt.service.dto.ShopApproveLogDto;
import org.cjc.les.mgt.service.dto.ShopApproveLogQueryCriteria;
import org.cjc.les.mgt.service.mapstruct.ShopApproveLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2022-07-10
**/
@Service
@RequiredArgsConstructor
public class ShopApproveLogServiceImpl implements ShopApproveLogService {

    private final ShopApproveLogRepository shopApproveLogRepository;
    private final ShopApproveLogMapper shopApproveLogMapper;

    @Override
    public Map<String,Object> queryAll(ShopApproveLogQueryCriteria criteria, Pageable pageable){
        Page<ShopApproveLog> page = shopApproveLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(shopApproveLogMapper::toDto));
    }

    @Override
    public List<ShopApproveLogDto> queryAll(ShopApproveLogQueryCriteria criteria){
        return shopApproveLogMapper.toDto(shopApproveLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public ShopApproveLogDto findById(Long id) {
        ShopApproveLog shopApproveLog = shopApproveLogRepository.findById(id).orElseGet(ShopApproveLog::new);
        ValidationUtil.isNull(shopApproveLog.getId(),"ShopApproveLog","id",id);
        return shopApproveLogMapper.toDto(shopApproveLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopApproveLogDto create(ShopApproveLog resources) {
        return shopApproveLogMapper.toDto(shopApproveLogRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ShopApproveLog resources) {
        ShopApproveLog shopApproveLog = shopApproveLogRepository.findById(resources.getId()).orElseGet(ShopApproveLog::new);
        ValidationUtil.isNull( shopApproveLog.getId(),"ShopApproveLog","id",resources.getId());
        shopApproveLog.copy(resources);
        shopApproveLogRepository.save(shopApproveLog);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            shopApproveLogRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ShopApproveLogDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ShopApproveLogDto shopApproveLog : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("店铺ID", shopApproveLog.getShopId());
            map.put("审核详情描述", shopApproveLog.getAuditDetail());
            map.put("审核状态: 1:待审核,2:审核通过,3:审核失败", shopApproveLog.getAuditStatus());
            map.put("创建人", shopApproveLog.getCreateBy());
            map.put("创建时间", shopApproveLog.getCreateTime());
            map.put("更新人", shopApproveLog.getUpdateBy());
            map.put("更新时间", shopApproveLog.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}