/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.constants.TaskPrepareStatusEnum;
import org.cjc.les.core.constants.CommandStatusEnum;
import org.cjc.les.core.constants.TaskLogLevelEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.repository.TaskPrepareRepository;
import org.cjc.les.core.repository.TaskRepository;
import org.cjc.les.core.service.TaskPrepareService;
import org.cjc.les.core.service.TaskService;
import org.cjc.les.core.service.dto.TaskPrepareDto;
import org.cjc.les.core.service.impl.ConveyorExecutor;
import org.cjc.les.core.service.impl.TagReaderExecutor;
import org.cjc.les.core.service.mapstruct.TaskPrepareMapper;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.EntryScheduler;
import org.cjc.les.core.task.schedule.StepScheduler;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.cjc.les.mgt.service.TaskPrepareViewService;
import org.cjc.les.mgt.service.dto.TaskPrepareViewDto;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description 服务实现
 * @date 2024-08-04
 **/
@Log4j2
@Service
@RequiredArgsConstructor
public class TaskPrepareViewServiceImpl implements TaskPrepareViewService {

    /**
     * 任务预处理视图队列
     * mode: FACTORY与SAMPLE互不影响，TRIAL与FACTORY,SAMPLE互相排斥
     */
    private LinkedBlockingQueue<TaskPrepareViewDto> taskPrepareViewQueue = new LinkedBlockingQueue<>();

    private final TagReaderExecutor tagReaderExecutor;

    private final ConveyorExecutor conveyorExecutor;

    private final TaskPrepareRepository taskPrepareRepository;
    private final TaskPrepareMapper taskPrepareMapper;

    private final TaskPrepareService taskPrepareService;

    private final TaskRepository taskRepository;

    private final StepScheduler stepScheduler;

    private final TaskService taskService;
    private final TaskScheduler taskScheduler;
    private final EntryScheduler entryScheduler;

    @Override
    public TaskPrepareViewDto getCurrentTaskPrepareView(String mode) {
        if (taskPrepareViewQueue.isEmpty()) {
            TaskPrepareViewDto dto = new TaskPrepareViewDto();
            dto.setMode(TaskPrepareViewDto.ModeEnum.valueOf(mode).name());
            return dto;
        }
        Optional<TaskPrepareViewDto> viewInQueOpt = taskPrepareViewQueue.stream().filter(v -> {
            return v.getMode().equals(mode);
        }).findFirst();

        if (viewInQueOpt.isPresent()) {
            TaskPrepareViewDto viewInQue = viewInQueOpt.get();
            List<TaskPrepareDto> dtoList = viewInQue.getTaskPrepareDtos();
            List<Long> idList = dtoList.stream().map(TaskPrepareDto::getId).collect(Collectors.toList());
            List<TaskPrepare> entityList = taskPrepareRepository.findAllById(idList);
            List<TaskPrepareDto> outDtoList = taskPrepareMapper.toDto(entityList);
            for (TaskPrepareDto dto : outDtoList) {
                dto.setWaitingForTagInput(tagReaderExecutor.isWaitingForTagInput(dto));
                dto.setWaitingForConveyorInput(conveyorExecutor.isWaitingForConveyorInput(dto));
                dto.setStatus(getTaskPrepareStatus(dto));
            }
            viewInQue.setTaskPrepareDtos(outDtoList);
            boolean allPushed = outDtoList.stream().allMatch(dto -> {
                return StringUtils.equalsAny(dto.getStatus(),
                        TaskPrepareStatusEnum.PUSHED.name(),
                        TaskPrepareStatusEnum.SUCCESS.name(),
                        TaskPrepareStatusEnum.CANCELLED.name()
                        );
            });
            if (allPushed) {
                viewInQue.setStatus(TaskPrepareStatusEnum.PUSHED.name());
                taskPrepareViewQueue.remove(viewInQue);
            }
            boolean allCancelled = outDtoList.stream().allMatch(dto -> {
                return StringUtils.equalsAny(dto.getStatus(), TaskPrepareStatusEnum.CANCELLED.name());
            });
            if (allCancelled) {
                TaskPrepareViewDto dto = new TaskPrepareViewDto();
                dto.setMode(TaskPrepareViewDto.ModeEnum.valueOf(mode).name());
                return dto;
            }
            return viewInQue;
        }
        TaskPrepareViewDto dto = new TaskPrepareViewDto();
        dto.setMode(TaskPrepareViewDto.ModeEnum.valueOf(mode).name());
        return dto;
    }

    private String getTaskPrepareStatus(TaskPrepareDto dto) {
        String outStatus = dto.getStatus();
        Task task = taskRepository.findByTaskNumber(dto.getTaskNumber());
        if (task == null){
            log.warn("Could not found task by taskNumber={}", dto.getTaskNumber());
            return outStatus;
        }
        Optional<TaskMethod> entryMethodOpt = task.getMethodList().stream().filter(mth -> {
            return StringUtils.equalsAny(mth.getType(), "ENTRY");
        }).findFirst();
        if (!entryMethodOpt.isPresent()) {
            return outStatus;
        }

        TaskMethod entryMethod = entryMethodOpt.get();
        Optional<TaskMethod> entryMethodInQueOpt = entryScheduler.getMethodsQueue().stream().filter(mth -> {
            return mth.getId().equals(entryMethod.getId());
        }).findFirst();
        if (!entryMethodInQueOpt.isPresent()) {
            return outStatus;
        }

        TaskMethod entryMethodInQue = entryMethodInQueOpt.get();

        for (TaskStep step : entryMethodInQue.getSteps()) {
            for (TaskAction action : step.getActions()) {
                for (TaskCommand cmd : action.getCommands()) {
                    if (StringUtils.equalsAny(cmd.getStatus(), RunStatusEnum.FAILED.name())) {
                        return RunStatusEnum.FAILED.name();
                    }
                }

            }
        }

        return outStatus;
    }

    @Override
    public void submitTagReadingStep(TaskPrepareViewDto dto) {
        TaskPrepareViewDto viewInQue = makeSureDtoInQue(dto);

        TaskPrepare taskPrepare = taskPrepareMapper.toEntity(viewInQue.getCurTaskPrepareDto());

        if (checkIfEntryMethodPresent(taskPrepare)) {
            tagReaderExecutor.submit(taskPrepare);
        }else{
            submitTaskSchedulerImmediately(taskPrepare);
        }
    }

    @Override
    public void submitEntryConveyorAcceptingStep(TaskPrepareViewDto dto) {
        TaskPrepareViewDto viewInQue = makeSureDtoInQue(dto);
        viewInQue.setStatus("CONVEYOR_SUBMITTED");
        List<TaskPrepare> taskPrepareList = taskPrepareMapper.toEntity(viewInQue.getTaskPrepareDtos());
        conveyorExecutor.submit(taskPrepareList);
    }

    private void submitTaskSchedulerImmediately(TaskPrepare paramTaskPrepare){

        TaskPrepare taskPrepare = taskPrepareRepository.findById(paramTaskPrepare.getId()).orElseGet(TaskPrepare::new);

        Procedure procedure = taskPrepare.getProcedure();

        Task task = new Task();
        task.copy(procedure, true);
        task.setPrepareTaskId(taskPrepare.getId());
        task.setStatus(CommandStatusEnum.READY.name());
        task.setTaskNumber(taskPrepare.getTaskNumber());
        task.setTaskName(taskPrepare.getSample().getName() + ":" + procedure.getName());
        task.setTag(taskPrepare.getRfTag());
        task.setSample(taskPrepare.getSample());

        //1. 保存任务状态
        taskService.create(task);

        TaskExecutorContext.clear();
        TaskExecutorContext.setTask(task);

        task.setMessage("任务创建成功，等待调度执行");
        TaskExecuteLogUtil.logTask(task, TaskLogLevelEnum.INFO);

        taskScheduler.schedule(task);

        taskPrepare.setStatus("PUSHED");
        taskPrepareRepository.save(taskPrepare);

    }

    private boolean checkIfEntryMethodPresent(TaskPrepare paramTp) {
        return taskPrepareService.checkIfEntryMethodPresent(paramTp);
    }

    private List<TaskPrepareViewDto> getViewDtoInQue(TaskPrepareViewDto dto) {
        List<TaskPrepareViewDto> outList = new ArrayList<>();
        // 优先查找mode相同的对象
        for (TaskPrepareViewDto dtoInQue : taskPrepareViewQueue) {
            if (StringUtils.equalsAny(dto.getMode(), dtoInQue.getMode())) {
                outList.add(dtoInQue);
            }
        }
        if (CollectionUtils.isNotEmpty(outList)) {
            return outList;
        }

        // 工厂模式及样品录入模式，需要再次检测是否存在TRIAL模式的对象
        if (StringUtils.equalsAny(dto.getMode(), TaskPrepareViewDto.ModeEnum.FACTORY.name(), TaskPrepareViewDto.ModeEnum.SAMPLE.name())) {
            for (TaskPrepareViewDto dtoInQue : taskPrepareViewQueue) {
                if (StringUtils.equalsAny(dto.getMode(), TaskPrepareViewDto.ModeEnum.TRIAL.name())) {
                    outList.add(dtoInQue);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(outList)) {
            return outList;
        }

        // 试验模式下，清理所有对象
        if (StringUtils.equalsAny(dto.getMode(), TaskPrepareViewDto.ModeEnum.TRIAL.name())) {
            for (TaskPrepareViewDto dtoInQue : taskPrepareViewQueue) {
                outList.add(dtoInQue);
            }
        }
        if (CollectionUtils.isNotEmpty(outList)) {
            return outList;
        }

        return outList;
    }

    synchronized private TaskPrepareViewDto makeSureDtoInQue(TaskPrepareViewDto dto) {

        List<TaskPrepareViewDto> viewInQueList = getViewDtoInQue(dto);

        if (CollectionUtils.isEmpty(viewInQueList)) {
            dto.setStatus("SUBMITTED");
            taskPrepareViewQueue.offer(dto);
            return dto;
        }

        Optional<TaskPrepareViewDto> matchedInQue = viewInQueList.stream().filter(v -> {
            return v.getId().equals(dto.getId());
        }).findFirst();

        List<TaskPrepareViewDto> notMatchedInQues = viewInQueList.stream().filter(v -> {
            return !v.getId().equals(dto.getId());
        }).collect(Collectors.toList());
        // 移除不存在的对象
        for (TaskPrepareViewDto dtoInQue : notMatchedInQues) {
            removeViewInQue(dtoInQue);
            taskPrepareViewQueue.remove(dtoInQue);
        }

        // 优先选择已有对象
        if (matchedInQue.isPresent()) {
            TaskPrepareViewDto outDto = matchedInQue.get();
            updateViewInQue(outDto, dto);
            return outDto;
        } else {
            dto.setStatus("SUBMITTED");
            taskPrepareViewQueue.offer(dto);
            return dto;
        }

    }

    private void removeViewInQue(TaskPrepareViewDto viewInQue) {

        for (TaskPrepareDto taskPrepare : viewInQue.getTaskPrepareDtos()) {
            Task task = taskRepository.findByTaskNumber(taskPrepare.getTaskNumber());
            if (task != null) {
                Optional<TaskMethod> entryMethodOpt = task.getMethodList().stream().filter(mth -> {
                    return StringUtils.equalsAny(mth.getType(), "ENTRY");
                }).findFirst();
                if (!entryMethodOpt.isPresent()) {
                    continue;
                }
                // 只有在执行队列中的方法才需要撤销.
                TaskMethod taskMethod = entryMethodOpt.get();
                Optional<TaskMethod> entryMethodInQueOpt = entryScheduler.getMethodsQueue().stream().filter(mth->{
                    return mth.getId().equals(taskMethod.getId());
                }).findFirst();
                if (entryMethodInQueOpt.isPresent()) {
                    // 遍历步骤
                    for (int idxStep = taskMethod.getSteps().size() - 1; idxStep >= 0; idxStep--) {
                        TaskStep step = taskMethod.getSteps().get(idxStep);
                        if (!StringUtils.equalsAny(step.getStatus(),
                                RunStatusEnum.SUCCESS.name(), RunStatusEnum.FAILED.name(), RunStatusEnum.CANCELLED.name())) {
                            step.setStatus(RunStatusEnum.CANCELLED.name());
                        }
                        stepScheduler.cancel(step);
                    }
                    taskMethod.setStatus(RunStatusEnum.CANCELLED.name());
                }
            }
            taskPrepareService.cancel(taskPrepare);
        }
        taskPrepareViewQueue.remove(viewInQue);
    }

    private void updateViewInQue(TaskPrepareViewDto viewInQue, TaskPrepareViewDto dto) {

    }

    @Override
    public void cancelCurrentTaskPrepareView(TaskPrepareViewDto dto) {

        TaskPrepareViewDto dtoInQue = null;
        for (TaskPrepareViewDto dtoInQueItem : taskPrepareViewQueue) {
            if (StringUtils.equalsAny(dtoInQueItem.getId(), dto.getId())) {
                dtoInQue = dtoInQueItem;
                break;
            }
        }
        if (dtoInQue != null) {
            removeViewInQue(dtoInQue);
        }
    }
}