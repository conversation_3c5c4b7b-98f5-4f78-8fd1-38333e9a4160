/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-08-04
**/
@Entity
@Data
@Table(name="les_device_mock")
@SQLDelete(sql = "UPDATE les_command SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class DeviceMock extends LesConfigBaseEntity {

    @Column(name = "name")
    @Schema(description = "设备名称")
    private String name;

    @Column(name = "description")
    @Schema(description = "设备描述")
    private String description;

    @Column(name = "device_instance_id")
    @Schema(description = "设备实例ID")
    private Long deviceInstanceId;

    @Column(name = "command_line")
    @Schema(description = "命令行")
    private String commandLine;

    @Column(name = "java_class_name",unique = true)
    @Schema(description = "该设备自定义的Java类名")
    private String javaClassName;

    private String javaClasspath;

    @Column(name = "config")
    @Schema(description = "配置信息")
    private String config;

    @Column(name = "image")
    private String image;

    @Transient
    private transient String log;

    @Transient
    private transient List<String> prompts;

    @Column(name = "status")
    @Schema(description = "运行状态")
    private String status;

    @Column(name = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    @Column(name = "create_time")
    @Schema(description = "创建时间")
    private Timestamp createTime;

    @Column(name = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    @Column(name = "update_time")
    @Schema(description = "更新时间")
    private Timestamp updateTime;

    public void copy(DeviceMock source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeviceMock that = (DeviceMock) o;
        return Objects.equals(this.getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.getId());
    }
}