/*
 *  Copyright 2024-2025 <PERSON><PERSON>is Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.cjc.annotation.Log;
import org.cjc.les.constants.RunStatusEnum;
import org.cjc.les.core.domain.*;
import org.cjc.les.core.service.TaskCommandService;
import org.cjc.les.core.service.TaskDebugService;
import org.cjc.les.core.service.dto.TaskCommandDto;
import org.cjc.les.core.service.dto.TaskDebugRequestDto;
import org.cjc.les.core.task.execute.ExecutorConfig;
import org.cjc.les.core.task.execute.TaskExecutorContext;
import org.cjc.les.core.task.schedule.TaskScheduler;
import org.cjc.les.core.util.TaskExecuteLogUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
* 任务调试
 *
* <AUTHOR>
* @date 2025-03-17
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务调试")
@RequestMapping("/api/taskDebug")
public class TaskDebugController {

    private final TaskDebugService taskDebugService;

    private final TaskCommandService taskCommandService;

    private final TaskScheduler taskScheduler;

    @PostMapping("/createTaskDebug")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> createTaskDebug(@Validated @RequestBody TaskDebugRequestDto dto){
        return new ResponseEntity<>(taskDebugService.createTaskDebug(dto),HttpStatus.CREATED);
    }

    @PostMapping("/loadTaskDebug")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> loadTaskDebug(@Validated @RequestBody TaskDebugRequestDto dto){
        return new ResponseEntity<>(taskDebugService.loadTaskDebug(dto),HttpStatus.OK);
    }

    @PostMapping("/setDebugMode")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> setDebugMode(@Validated @RequestBody TaskDebugRequestDto dto) {
        Long taskId = dto.getTaskId();
        Optional<Task> taskOpt = taskScheduler.getTaskById(taskId);
        if (!taskOpt.isPresent()) {
            dto.setStatus("FAILED");
            dto.setMessage("Could not found task in executing queue.");
            return new ResponseEntity<>(dto, HttpStatus.OK);
        }
        Task task = taskOpt.get();
        TaskExecutorContext context = task.getContext();
        ExecutorConfig executorConfig = context.getExecutorConfig();
        executorConfig.setDebugMode(ExecutorConfig.DebugModeEnum.valueOf(dto.getDebugMode()));

        if (StringUtils.equalsAny(dto.getDebugMode(), ExecutorConfig.DebugModeEnum.MONO.name())) {
            taskDebugService.setMonoMode(task, dto);
        } else if (StringUtils.equalsAny(dto.getDebugMode(), ExecutorConfig.DebugModeEnum.CONTINUE.name())) {
            taskDebugService.setContinueMode(task, dto);
        }

        return new ResponseEntity<>(dto, HttpStatus.OK);
    }

    @PostMapping("/setBreakPoint")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> setBreakPoint(@Validated @RequestBody TaskDebugRequestDto dto){
        Long taskId = dto.getTaskId();
        Optional<Task> taskOpt = taskScheduler.getTaskById(taskId);
        if (!taskOpt.isPresent()){
            dto.setStatus("FAILED");
            dto.setMessage("Could not found task in executing queue.");
            return new ResponseEntity<>(dto,HttpStatus.OK);
        }
        Task task = taskOpt.get();
        setTaskEntryStatusToBreak(task, dto);

        return new ResponseEntity<>(dto,HttpStatus.OK);
    }

    private void setTaskEntryStatusToBreak(Task task, TaskDebugRequestDto dto) {
        for (TaskMethod method : task.getMethodList()) {
            if (method.getId().equals(dto.getTaskMethodId())) {
                setTaskEntryStatusToBreak(method);
            }
            for (TaskStep step : method.getSteps()) {
                if (step.getId().equals(dto.getTaskStepId())) {
                    setTaskEntryStatusToBreak(step);
                }
                for (TaskAction action : step.getActions()) {
                    if (action.getId().equals(dto.getTaskActionId())) {
                        setTaskEntryStatusToBreak(action);
                    }
                    for (TaskCommand command : action.getCommands()) {
                        if (command.getId().equals(dto.getTaskCommandId())) {
                            setTaskEntryStatusToBreak(command);
                        }
                    }
                }
            }
        }
    }

    private boolean setTaskEntryStatusToBreak(TaskBaseEntity taskBaseEntity) {
        if (StringUtils.equals(taskBaseEntity.getStatus(), RunStatusEnum.BREAK.name())) {
            taskBaseEntity.setStatus(RunStatusEnum.READY.name());
            TaskExecuteLogUtil.log(taskBaseEntity);
            return true;
        }else if (StringUtils.equals(taskBaseEntity.getStatus(), RunStatusEnum.READY.name())) {
            taskBaseEntity.setStatus(RunStatusEnum.BREAK.name());
            TaskExecuteLogUtil.log(taskBaseEntity);
            return true;
        }
        return false;
    }

    @PostMapping("/doRetryCommand")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> doRetryCommand(@Validated @RequestBody TaskCommandDto dto){
        TaskCommandDto taskCommand = taskCommandService.findById(dto.getId());
        if (StringUtils.equalsIgnoreCase(taskCommand.getStatus(),"FAILED")){
            dto.setFailureFixAs("RETRY");
            dto.setFailureFixAsTemp(dto.getFailureFixAs());
            taskCommandService.updateFailureFixInfo(dto);
        }
        return new ResponseEntity<>(dto,HttpStatus.OK);
    }

    @PostMapping("/doNextCommand")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> doNextCommand(@Validated @RequestBody TaskCommandDto dto){
        Long taskId = dto.getTaskId();
        Optional<Task> taskOpt = taskScheduler.getTaskById(taskId);
        if (!taskOpt.isPresent()){
            dto.setStatus("FAILED");
            dto.setMessage("Could not found task in executing queue.");
            return new ResponseEntity<>(dto,HttpStatus.OK);
        }
        Task task = taskOpt.get();
        TaskExecutorContext context = task.getContext();
        ExecutorConfig executorConfig = context.getExecutorConfig();
        // CONTINUE, MONO调试模式有效
        //if (!StringUtils.equalsAnyIgnoreCase(executorConfig.getDebugMode().name(), ExecutorConfig.DebugModeEnum.MONO.name())) {
        //    executorConfig.setDebugMode(ExecutorConfig.DebugModeEnum.MONO);
        //}
        executorConfig.getMonoBreakpointLock().set(true);

        return new ResponseEntity<>(dto,HttpStatus.OK);
    }


    @PostMapping("/doSkipCommand")
    @Log("新增任务管理")
    @ApiOperation("新增任务管理")
    @PreAuthorize("@el.check('task:add')")
    public ResponseEntity<Object> doSkipCommand(@Validated @RequestBody TaskCommandDto dto){
        Long taskId = dto.getTaskId();
        Optional<Task> taskOpt = taskScheduler.getTaskById(taskId);
        if (!taskOpt.isPresent()){
            dto.setStatus("FAILED");
            dto.setMessage("Could not found task in executing queue.");
            return new ResponseEntity<>(dto,HttpStatus.OK);
        }
        Task task = taskOpt.get();

        Optional<TaskCommand> taskCommandOpt = findRunningTaskCommandById(task, dto.getId());
        if (!taskCommandOpt.isPresent()) {
            dto.setStatus("FAILED");
            dto.setMessage("Could not found command in executing queue.");
            return new ResponseEntity<>(dto,HttpStatus.OK);
        }

        TaskCommand taskCommand = taskCommandOpt.get();
        Thread actThread = taskCommand.getTaskAction().getThread();
        if (actThread != null){
            taskCommand.setStatus(RunStatusEnum.CANCELLED.name());
            actThread.interrupt();
        }

        return new ResponseEntity<>(dto,HttpStatus.OK);
    }

    private Optional<TaskCommand> findRunningTaskCommandById(Task task, Long taskCommandId) {
        for (TaskMethod method : task.getMethodList()) {
            for (TaskStep step : method.getSteps()) {
                for (TaskAction action : step.getActions()) {
                    for (TaskCommand command : action.getCommands()) {
                        if (command.getId().equals(taskCommandId)
                         && RunStatusEnum.RUNNING.name().equals(command.getStatus())){
                             return Optional.of(command);
                        }
                    }
                }
            }
        }
        return Optional.empty();
    }


}