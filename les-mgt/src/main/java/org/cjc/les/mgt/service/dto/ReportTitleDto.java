/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @website https://el-admin.vip
 * @description /
 * @date 2024-10-30
 **/
@Data
public class ReportTitleDto implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 检测项目名称
     */
    private String itemName;

    /**
     * 检测项目类别
     */
    private String itemCategory;

    /**
     * 代理公司
     */
    private String delegateCompany;

    /**
     * 检测日期
     */
    private String checkingDate;

    /**
     * 运行状态
     */
    private String status;

    /**
     * 是否已被删除,Y/N
     */
    private String deleteFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}