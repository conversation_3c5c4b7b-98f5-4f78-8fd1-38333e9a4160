/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.mgt.domain.StatsResultFeature;
import org.cjc.les.mgt.service.StatsResultFeatureService;
import org.cjc.les.mgt.service.dto.StatsResultFeatureQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-31
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "检测结果统计项管理")
@RequestMapping("/api/statsResultFeature")
public class StatsResultFeatureController {

    private final StatsResultFeatureService statsResultFeatureService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public void exportStatsResultFeature(HttpServletResponse response, StatsResultFeatureQueryCriteria criteria) throws IOException {
        statsResultFeatureService.download(statsResultFeatureService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询检测结果统计项")
    @ApiOperation("查询检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public ResponseEntity<Object> queryStatsResultFeature(StatsResultFeatureQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(statsResultFeatureService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增检测结果统计项")
    @ApiOperation("新增检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:add')")
    public ResponseEntity<Object> createStatsResultFeature(@Validated @RequestBody StatsResultFeature resources){
        return new ResponseEntity<>(statsResultFeatureService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改检测结果统计项")
    @ApiOperation("修改检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:edit')")
    public ResponseEntity<Object> updateStatsResultFeature(@Validated @RequestBody StatsResultFeature resources){
        statsResultFeatureService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除检测结果统计项")
    @ApiOperation("删除检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:del')")
    public ResponseEntity<Object> deleteStatsResultFeature(@RequestBody Long[] ids) {
        statsResultFeatureService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/queryStatsResultFeatureValues")
    @Log("查询检测结果统计项")
    @ApiOperation("查询检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public ResponseEntity<Object> queryStatsResultFeatureValues(StatsResultFeatureQueryCriteria criteria){
        return new ResponseEntity<>(statsResultFeatureService.queryStatsResultFeatureValues(criteria),HttpStatus.OK);
    }

    @GetMapping("/queryStatsResultFeatureValuesByHour")
    @Log("查询检测结果统计项")
    @ApiOperation("查询检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public ResponseEntity<Object> queryStatsResultFeatureValuesByHour(StatsResultFeatureQueryCriteria criteria){
        return new ResponseEntity<>(statsResultFeatureService.queryStatsResultFeatureValuesByHour(criteria),HttpStatus.OK);
    }

    @GetMapping("/queryStatsMethods")
    @Log("查询检测结果统计项")
    @ApiOperation("查询检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public ResponseEntity<Object> queryStatsMethods(StatsResultFeatureQueryCriteria criteria){
        return new ResponseEntity<>(statsResultFeatureService.queryStatsMethods(criteria),HttpStatus.OK);
    }

    @GetMapping("/queryStatsConclusions")
    @Log("查询检测结果统计项")
    @ApiOperation("查询检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public ResponseEntity<Object> queryStatsConclusions(StatsResultFeatureQueryCriteria criteria){
        return new ResponseEntity<>(statsResultFeatureService.queryStatsConclusions(criteria),HttpStatus.OK);
    }

    @GetMapping("/queryStatsDateTime")
    @Log("查询检测结果统计项")
    @ApiOperation("查询检测结果统计项")
    @PreAuthorize("@el.check('statsResultFeature:list')")
    public ResponseEntity<Object> queryStatsDateTime(StatsResultFeatureQueryCriteria criteria){
        return new ResponseEntity<>(statsResultFeatureService.queryStatsDateTime(criteria),HttpStatus.OK);
    }


}