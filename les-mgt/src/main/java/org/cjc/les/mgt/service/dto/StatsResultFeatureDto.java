/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2025-07-31
**/
@Data
public class StatsResultFeatureDto implements Serializable {

    /** 主键ID */
    private Long id;

    /** 结果指标ID */
    private Long resultFeatureId;

    /** 结果指标名称 */
    private String featureName;

    /** 结果指标值 */
    private String featureValue;

    /** 结果结论 */
    private String featureConclusion;

    /** 结果指标创建时间 */
    private Timestamp featureCreateTime;

    /** 检测方法 */
    private String methodName;

    /** 样品名称 */
    private String sampleName;

    /** 样品分类 */
    private String sampleCategory;

    /** 样品所属客户 */
    private String sampleCustomer;

    /** 是否已被删除,Y/N */
    private String deleteFlag;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Timestamp createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Timestamp updateTime;
}