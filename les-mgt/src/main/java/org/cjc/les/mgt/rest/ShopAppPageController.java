/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import com.alibaba.fastjson2.JSON;
import org.cjc.annotation.Log;
import org.cjc.modules.system.utils.RequestHolder;
import org.cjc.les.mgt.domain.ShopAppPage;
import org.cjc.les.mgt.service.ShopAppPageService;
import org.cjc.les.mgt.service.ShopUniappSettingService;
import org.cjc.les.mgt.service.dto.ShopAppPageData;
import org.cjc.les.mgt.service.dto.ShopAppPageDto;
import org.cjc.les.mgt.service.dto.ShopAppPageQueryCriteria;
import org.cjc.les.mgt.service.dto.ShopUniappSettingDto;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-21
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "店铺页面管理接口管理")
@RequestMapping("/api/page")
public class ShopAppPageController {

    private final ShopAppPageService pageService;

    private final ShopUniappSettingService shopUniappSettingService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('page:list')")
    public void exportPage(HttpServletResponse response, ShopAppPageQueryCriteria criteria) throws IOException {
        pageService.download(pageService.queryAll(criteria), response);
    }

    @GetMapping("/list")
    @Log("查询店铺页面管理接口")
    @ApiOperation("查询店铺页面管理接口")
    @PreAuthorize("@el.check('page:list')")
    public ResponseEntity<Object> queryPage(ShopAppPageQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(pageService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping("/add")
    @Log("新增店铺页面管理接口")
    @ApiOperation("新增店铺页面管理接口")
    @PreAuthorize("@el.check('page:add')")
    public ResponseEntity<Object> createPage( @RequestBody ShopAppPage resources){

        String pageData = resources.getPageData();
        ShopAppPageData pageDataObj = JSON.parseObject(pageData, ShopAppPageData.class);
        resources.setPageName(pageDataObj.getPage().getParams().getName());
        resources.setShopId(RequestHolder.getOnlineUser().getShopId());
        resources.setPageType(20);
        return new ResponseEntity<>(pageService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping("/edit")
    @Log("修改店铺页面管理接口")
    @ApiOperation("修改店铺页面管理接口")
    @PreAuthorize("@el.check('page:edit')")
    public ResponseEntity<Object> updatePage( @RequestBody ShopAppPage resources){

        String pageData = resources.getPageData();
        ShopAppPageData pageDataObj = JSON.parseObject(pageData, ShopAppPageData.class);
        resources.setPageName(pageDataObj.getPage().getParams().getName());
        pageService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除店铺页面管理接口")
    @ApiOperation("删除店铺页面管理接口")
    @PreAuthorize("@el.check('page:del')")
    public ResponseEntity<Object> deletePage(@RequestBody Long[] ids) {
        pageService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/defaultData")
    @Log("获取页面模板默认数据接口")
    @ApiOperation("获取页面模板默认数据接口")
    @PreAuthorize("@el.check('page:list')")
    public ResponseEntity<Object> getDefaultData(){

        ShopUniappSettingDto settingDto = shopUniappSettingService.findById(1L);
        String defaultData = settingDto.getSetting();

        return new ResponseEntity<>(JSON.parseObject(defaultData),HttpStatus.OK);
    }

    @GetMapping("/detail")
    @Log("获取页面模板默认数据接口")
    @ApiOperation("获取页面模板默认数据接口")
    @PreAuthorize("@el.check('page:list')")
    public ResponseEntity<Object> getDetailData(@RequestParam(defaultValue = "0")Long pageId){

        ShopAppPageDto pageDto = pageService.findById(pageId);
        String pageData = pageDto.getPageData();

        return new ResponseEntity<>(JSON.parseObject(pageData),HttpStatus.OK);
    }

}