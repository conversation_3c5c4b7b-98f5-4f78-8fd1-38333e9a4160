/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Function;
import org.cjc.les.core.service.FunctionService;
import org.cjc.les.core.service.dto.FunctionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-17
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "函数管理管理")
@RequestMapping("/api/function")
public class FunctionController {

    private final FunctionService functionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('function:list')")
    public void exportFunction(HttpServletResponse response, FunctionQueryCriteria criteria) throws IOException {
        functionService.download(functionService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询函数管理")
    @ApiOperation("查询函数管理")
    @PreAuthorize("@el.check('function:list')")
    public ResponseEntity<Object> queryFunction(FunctionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(functionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增函数管理")
    @ApiOperation("新增函数管理")
    @PreAuthorize("@el.check('function:add')")
    public ResponseEntity<Object> createFunction(@Validated @RequestBody Function resources){
        return new ResponseEntity<>(functionService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改函数管理")
    @ApiOperation("修改函数管理")
    @PreAuthorize("@el.check('function:edit')")
    public ResponseEntity<Object> updateFunction(@Validated @RequestBody Function resources){
        functionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除函数管理")
    @ApiOperation("删除函数管理")
    @PreAuthorize("@el.check('function:del')")
    public ResponseEntity<Object> deleteFunction(@RequestBody Long[] ids) {
        functionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}