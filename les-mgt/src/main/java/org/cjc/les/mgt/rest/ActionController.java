/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Action;
import org.cjc.les.core.service.ActionService;
import org.cjc.les.core.service.dto.ActionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-08
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "动作管理管理")
@RequestMapping("/api/action")
public class ActionController {

    private final ActionService actionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('action:list')")
    public void exportAction(HttpServletResponse response, ActionQueryCriteria criteria) throws IOException {
        actionService.download(actionService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询动作管理")
    @ApiOperation("查询动作管理")
    @PreAuthorize("@el.check('action:list')")
    public ResponseEntity<Object> queryAction(ActionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(actionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增动作管理")
    @ApiOperation("新增动作管理")
    @PreAuthorize("@el.check('action:add')")
    public ResponseEntity<Object> createAction(@Validated @RequestBody Action resources){
        return new ResponseEntity<>(actionService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改动作管理")
    @ApiOperation("修改动作管理")
    @PreAuthorize("@el.check('action:edit')")
    public ResponseEntity<Object> updateAction(@Validated @RequestBody Action resources){
        actionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除动作管理")
    @ApiOperation("删除动作管理")
    @PreAuthorize("@el.check('action:del')")
    public ResponseEntity<Object> deleteAction(@RequestBody Long[] ids) {
        actionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}