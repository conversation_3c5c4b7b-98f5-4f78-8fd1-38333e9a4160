/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.TaskExecuteLog;
import org.cjc.les.core.service.TaskExecuteLogService;
import org.cjc.les.core.service.dto.TaskExecuteLogQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-08-23
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "任务运行日志管理")
@RequestMapping("/api/taskExecuteLog")
public class TaskExecuteLogController {

    private final TaskExecuteLogService taskExecuteLogService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskExecuteLog:list')")
    public void exportTaskExecuteLog(HttpServletResponse response, TaskExecuteLogQueryCriteria criteria) throws IOException {
        taskExecuteLogService.download(taskExecuteLogService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务运行日志")
    @ApiOperation("查询任务运行日志")
    @PreAuthorize("@el.check('taskExecuteLog:list')")
    public ResponseEntity<Object> queryTaskExecuteLog(TaskExecuteLogQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskExecuteLogService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/queryLatestTaskExecuteLog")
    @ApiOperation("查询任务运行日志")
    //@PreAuthorize("@el.check('taskExecuteLog:list')")
    public ResponseEntity<Object> queryLatestTaskExecuteLog(TaskExecuteLogQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskExecuteLogService.queryLatestTaskExecuteLog(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping("/queryTaskExecuteLogForNode")
    @Log("查询任务运行日志")
    @ApiOperation("查询任务运行日志")
    @PreAuthorize("@el.check('taskExecuteLog:list')")
    public ResponseEntity<Object> queryTaskExecuteLogForNode(TaskExecuteLogQueryCriteria criteria){
        return new ResponseEntity<>(taskExecuteLogService.queryTaskExecuteLogForNode(criteria),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务运行日志")
    @ApiOperation("新增任务运行日志")
    @PreAuthorize("@el.check('taskExecuteLog:add')")
    public ResponseEntity<Object> createTaskExecuteLog(@Validated @RequestBody TaskExecuteLog resources){
        return new ResponseEntity<>(taskExecuteLogService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务运行日志")
    @ApiOperation("修改任务运行日志")
    @PreAuthorize("@el.check('taskExecuteLog:edit')")
    public ResponseEntity<Object> updateTaskExecuteLog(@Validated @RequestBody TaskExecuteLog resources){
        taskExecuteLogService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务运行日志")
    @ApiOperation("删除任务运行日志")
    @PreAuthorize("@el.check('taskExecuteLog:del')")
    public ResponseEntity<Object> deleteTaskExecuteLog(@RequestBody Long[] ids) {
        taskExecuteLogService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}