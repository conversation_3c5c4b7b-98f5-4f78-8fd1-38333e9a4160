/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.apache.commons.collections4.CollectionUtils;
import org.cjc.annotation.Log;
import org.cjc.exception.BadRequestException;
import org.cjc.les.core.domain.ProcedureVariable;
import org.cjc.les.core.service.ProcedureVariableService;
import org.cjc.les.core.service.dto.ProcedureVariableQueryCriteria;
import org.cjc.les.core.service.dto.ProcedureVariableSettingViewDto;
import org.cjc.les.core.service.dto.ProcedureWithVariablesDto;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2025-07-27
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "流程变量管理")
@RequestMapping("/api/procedureVariable")
public class ProcedureVariableController {

    private final ProcedureVariableService procedureVariableService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('procedureVariable:list')")
    public void exportProcedureVariable(HttpServletResponse response, ProcedureVariableQueryCriteria criteria) throws IOException {
        procedureVariableService.download(procedureVariableService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询流程变量")
    @ApiOperation("查询流程变量")
    @PreAuthorize("@el.check('procedureVariable:list')")
    public ResponseEntity<Object> queryProcedureVariable(ProcedureVariableQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(procedureVariableService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增流程变量")
    @ApiOperation("新增流程变量")
    @PreAuthorize("@el.check('procedureVariable:add')")
    public ResponseEntity<Object> createProcedureVariable(@Validated @RequestBody ProcedureVariable resources){
        return new ResponseEntity<>(procedureVariableService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改流程变量")
    @ApiOperation("修改流程变量")
    @PreAuthorize("@el.check('procedureVariable:edit')")
    public ResponseEntity<Object> updateProcedureVariable(@Validated @RequestBody ProcedureVariable resources){
        procedureVariableService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PutMapping(value = "/updateProcedureVariablesValue")
    @Log("修改流程变量")
    @ApiOperation("修改流程变量")
    @PreAuthorize("@el.check('procedureVariable:edit')")
    public ResponseEntity<Object> updateProcedureVariablesValue(@Validated @RequestBody ProcedureVariableSettingViewDto resources){
        List<ProcedureWithVariablesDto> toUpdateList = resources.getProcedures();
        if (CollectionUtils.isEmpty(toUpdateList)){
            throw new BadRequestException("Could not found procedures.");
        }
        procedureVariableService.updateProcedureVariablesValue(toUpdateList);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除流程变量")
    @ApiOperation("删除流程变量")
    @PreAuthorize("@el.check('procedureVariable:del')")
    public ResponseEntity<Object> deleteProcedureVariable(@RequestBody Long[] ids) {
        procedureVariableService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/queryAllRawProceduresWithVariables")
    @Log("查询流程管理")
    @ApiOperation("查询流程管理")
    @PreAuthorize("@el.check('procedure:list')")
    public ResponseEntity<Object> queryAllRawProceduresWithVariables(){
        return new ResponseEntity<>(procedureVariableService.queryAllRawProceduresWithVariables(),HttpStatus.OK);
    }

}