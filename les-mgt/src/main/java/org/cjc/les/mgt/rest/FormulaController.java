/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.Formula;
import org.cjc.les.core.service.FormulaService;
import org.cjc.les.core.service.FunctionService;
import org.cjc.les.core.service.VariableService;
import org.cjc.les.core.service.dto.*;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-15
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "公式管理管理")
@RequestMapping("/api/formula")
public class FormulaController {

    private final FormulaService formulaService;

    private final VariableService variableService;

    private final FunctionService functionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('formula:list')")
    public void exportFormula(HttpServletResponse response, FormulaQueryCriteria criteria) throws IOException {
        formulaService.download(formulaService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询公式管理")
    @ApiOperation("查询公式管理")
    @PreAuthorize("@el.check('formula:list')")
    public ResponseEntity<Object> queryFormula(FormulaQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(formulaService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增公式管理")
    @ApiOperation("新增公式管理")
    @PreAuthorize("@el.check('formula:add')")
    public ResponseEntity<Object> createFormula(@Validated @RequestBody Formula resources){
        return new ResponseEntity<>(formulaService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改公式管理")
    @ApiOperation("修改公式管理")
    @PreAuthorize("@el.check('formula:edit')")
    public ResponseEntity<Object> updateFormula(@Validated @RequestBody Formula resources){
        formulaService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除公式管理")
    @ApiOperation("删除公式管理")
    @PreAuthorize("@el.check('formula:del')")
    public ResponseEntity<Object> deleteFormula(@RequestBody Long[] ids) {
        formulaService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("变量与函数选择查询")
    @GetMapping(value = "/selectVariablesAndFunctions")
    @PreAuthorize("@el.check('formula:list')")
    public ResponseEntity<Object> selectVariablesAndFunctions(FormulaQueryCriteria criteria) throws IOException {
        List<VariableDto> varList = variableService.queryAll(new VariableQueryCriteria());
        List<FunctionDto> functionDtoList = functionService.queryAll(new FunctionQueryCriteria());
        Map<String, Object> outMap = new HashMap<>();
        outMap.put("varList", varList);
        outMap.put("funcList", functionDtoList);
        return new ResponseEntity<>(outMap, HttpStatus.OK);
    }

    @GetMapping(value = "/queryForSelection")
    @Log("查询公式管理")
    @ApiOperation("查询公式管理")
    @PreAuthorize("@el.check('formula:list')")
    public ResponseEntity<Object> queryForSelection(FormulaQueryCriteria criteria) {
        return new ResponseEntity<>(formulaService.queryForSelection(criteria.getType()), HttpStatus.OK);
    }

    @GetMapping(value = "/queryByName")
    @Log("查询公式管理")
    @ApiOperation("查询公式管理")
    @PreAuthorize("@el.check('formula:list')")
    public ResponseEntity<Object> queryByName(FormulaQueryCriteria criteria) {
        return new ResponseEntity<>(formulaService.queryByName(criteria.getName()), HttpStatus.OK);
    }

}