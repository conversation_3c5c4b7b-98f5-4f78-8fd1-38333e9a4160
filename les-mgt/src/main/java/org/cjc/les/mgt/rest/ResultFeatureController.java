/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.ResultFeature;
import org.cjc.les.core.service.ResultFeatureService;
import org.cjc.les.core.service.dto.ResultFeatureQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-06-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "结果特性管理")
@RequestMapping("/api/resultFeature")
public class ResultFeatureController {

    private final ResultFeatureService resultFeatureService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('resultFeature:list')")
    public void exportResultFeature(HttpServletResponse response, ResultFeatureQueryCriteria criteria) throws IOException {
        resultFeatureService.download(resultFeatureService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询结果特性")
    @ApiOperation("查询结果特性")
    @PreAuthorize("@el.check('resultFeature:list')")
    public ResponseEntity<Object> queryResultFeature(ResultFeatureQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(resultFeatureService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增结果特性")
    @ApiOperation("新增结果特性")
    @PreAuthorize("@el.check('resultFeature:add')")
    public ResponseEntity<Object> createResultFeature(@Validated @RequestBody ResultFeature resources){
        return new ResponseEntity<>(resultFeatureService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改结果特性")
    @ApiOperation("修改结果特性")
    @PreAuthorize("@el.check('resultFeature:edit')")
    public ResponseEntity<Object> updateResultFeature(@Validated @RequestBody ResultFeature resources){
        resultFeatureService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除结果特性")
    @ApiOperation("删除结果特性")
    @PreAuthorize("@el.check('resultFeature:del')")
    public ResponseEntity<Object> deleteResultFeature(@RequestBody Long[] ids) {
        resultFeatureService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}