/*
 *  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.core.domain.DeviceInstance;
import org.cjc.les.core.service.DeviceInstanceService;
import org.cjc.les.core.service.dto.DeviceInstanceDto;
import org.cjc.les.core.service.dto.DeviceInstanceQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-07-30
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "设备实例管理管理")
@RequestMapping("/api/deviceInstance")
public class DeviceInstanceController {

    private final DeviceInstanceService deviceInstanceService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('deviceInstance:list')")
    public void exportDeviceInstance(HttpServletResponse response, DeviceInstanceQueryCriteria criteria) throws IOException {
        deviceInstanceService.download(deviceInstanceService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询设备实例管理")
    @ApiOperation("查询设备实例管理")
    @PreAuthorize("@el.check('deviceInstance:list')")
    public ResponseEntity<Object> queryDeviceInstance(DeviceInstanceQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(deviceInstanceService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增设备实例管理")
    @ApiOperation("新增设备实例管理")
    @PreAuthorize("@el.check('deviceInstance:add')")
    public ResponseEntity<Object> createDeviceInstance(@Validated @RequestBody DeviceInstance resources){
        return new ResponseEntity<>(deviceInstanceService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改设备实例管理")
    @ApiOperation("修改设备实例管理")
    @PreAuthorize("@el.check('deviceInstance:edit')")
    public ResponseEntity<Object> updateDeviceInstance(@Validated @RequestBody DeviceInstance resources){
        deviceInstanceService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除设备实例管理")
    @ApiOperation("删除设备实例管理")
    @PreAuthorize("@el.check('deviceInstance:del')")
    public ResponseEntity<Object> deleteDeviceInstance(@RequestBody Long[] ids) {
        deviceInstanceService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/queryDeviceInstanceById")
    @Log("查询设备实例管理")
    @ApiOperation("查询设备实例管理")
    @PreAuthorize("@el.check('deviceInstance:list')")
    public ResponseEntity<Object> queryDeviceInstanceById(Long deviceInstanceId){
        return new ResponseEntity<>(deviceInstanceService.findById(deviceInstanceId),HttpStatus.OK);
    }

    @PutMapping(value="updatePositionDetailConfig")
    @Log("修改设备实例管理")
    @ApiOperation("修改设备实例管理")
    @PreAuthorize("@el.check('deviceInstance:edit')")
    public ResponseEntity<Object> updatePositionDetailConfig(@Validated @RequestBody DeviceInstanceDto resources){
        deviceInstanceService.updatePositionDetailConfig(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }



}