/*
 *  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
 */
package org.cjc.les.mgt.rest;

import org.cjc.annotation.Log;
import org.cjc.les.mgt.domain.ShopUniappSetting;
import org.cjc.les.mgt.service.ShopUniappSettingService;
import org.cjc.les.mgt.service.dto.ShopUniappSettingQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "店铺APP设置接口管理")
@RequestMapping("/api/shopUniappSetting")
public class ShopUniappSettingController {

    private final ShopUniappSettingService shopUniappSettingService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('shopUniappSetting:list')")
    public void exportShopUniappSetting(HttpServletResponse response, ShopUniappSettingQueryCriteria criteria) throws IOException {
        shopUniappSettingService.download(shopUniappSettingService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询店铺APP设置接口")
    @ApiOperation("查询店铺APP设置接口")
    @PreAuthorize("@el.check('shopUniappSetting:list')")
    public ResponseEntity<Object> queryShopUniappSetting(ShopUniappSettingQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(shopUniappSettingService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增店铺APP设置接口")
    @ApiOperation("新增店铺APP设置接口")
    @PreAuthorize("@el.check('shopUniappSetting:add')")
    public ResponseEntity<Object> createShopUniappSetting(@Validated @RequestBody ShopUniappSetting resources){
        return new ResponseEntity<>(shopUniappSettingService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改店铺APP设置接口")
    @ApiOperation("修改店铺APP设置接口")
    @PreAuthorize("@el.check('shopUniappSetting:edit')")
    public ResponseEntity<Object> updateShopUniappSetting(@Validated @RequestBody ShopUniappSetting resources){
        shopUniappSettingService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除店铺APP设置接口")
    @ApiOperation("删除店铺APP设置接口")
    @PreAuthorize("@el.check('shopUniappSetting:del')")
    public ResponseEntity<Object> deleteShopUniappSetting(@RequestBody Long[] ids) {
        shopUniappSettingService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}