import org.cjc.les.mgt.service.dto.StatsResultFeatureLineChartItemDto;
import org.cjc.les.mgt.service.dto.StatsResultFeatureQueryCriteria;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class TestStatsResultFeatureService {
    
    public static void main(String[] args) {
        System.out.println("=== 测试 StatsResultFeatureService.queryStatsResultFeatureValuesByHour ===");
        
        // 创建测试查询条件
        StatsResultFeatureQueryCriteria criteria = new StatsResultFeatureQueryCriteria();
        
        // 设置时间范围（最近24小时）
        List<Timestamp> timeRange = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        timeRange.add(new Timestamp(currentTime - 24 * 60 * 60 * 1000)); // 24小时前
        timeRange.add(new Timestamp(currentTime)); // 现在
        criteria.setFeatureCreateTime(timeRange);
        
        // 设置检测方法
        criteria.setMethodName("ICP-OES检测");
        
        // 设置样品分类
        criteria.setSampleCategory("水质样品");
        
        System.out.println("查询条件:");
        System.out.println("  时间范围: " + timeRange.get(0) + " 到 " + timeRange.get(1));
        System.out.println("  检测方法: " + criteria.getMethodName());
        System.out.println("  样品分类: " + criteria.getSampleCategory());
        
        // 模拟查询结果
        List<StatsResultFeatureLineChartItemDto> mockResults = createMockResults();
        
        System.out.println("\n模拟查询结果:");
        for (StatsResultFeatureLineChartItemDto item : mockResults) {
            System.out.println("特征名称: " + item.getFeatureName());
            System.out.println("  时间: " + item.getFCreateTime());
            System.out.println("  统计数: " + item.getCnt());
            System.out.println("  平均值: " + item.getAvgValue());
            System.out.println("  最小值: " + item.getMinValue());
            System.out.println("  最大值: " + item.getMaxValue());
            System.out.println();
        }
        
        System.out.println("测试完成！");
    }
    
    private static List<StatsResultFeatureLineChartItemDto> createMockResults() {
        List<StatsResultFeatureLineChartItemDto> results = new ArrayList<>();
        
        // 模拟第一个特征的数据
        StatsResultFeatureLineChartItemDto item1 = new StatsResultFeatureLineChartItemDto();
        item1.setFeatureName("pH值");
        item1.setFCreateTime(new Timestamp(System.currentTimeMillis() - 2 * 60 * 60 * 1000)); // 2小时前
        item1.setCnt(15L);
        item1.setAvgValue(new BigDecimal("7.25"));
        item1.setMinValue(new BigDecimal("7.10"));
        item1.setMaxValue(new BigDecimal("7.40"));
        results.add(item1);
        
        // 模拟第二个特征的数据
        StatsResultFeatureLineChartItemDto item2 = new StatsResultFeatureLineChartItemDto();
        item2.setFeatureName("pH值");
        item2.setFCreateTime(new Timestamp(System.currentTimeMillis() - 1 * 60 * 60 * 1000)); // 1小时前
        item2.setCnt(12L);
        item2.setAvgValue(new BigDecimal("7.18"));
        item2.setMinValue(new BigDecimal("7.05"));
        item2.setMaxValue(new BigDecimal("7.35"));
        results.add(item2);
        
        // 模拟第三个特征的数据
        StatsResultFeatureLineChartItemDto item3 = new StatsResultFeatureLineChartItemDto();
        item3.setFeatureName("溶解氧");
        item3.setFCreateTime(new Timestamp(System.currentTimeMillis() - 2 * 60 * 60 * 1000)); // 2小时前
        item3.setCnt(18L);
        item3.setAvgValue(new BigDecimal("8.45"));
        item3.setMinValue(new BigDecimal("8.20"));
        item3.setMaxValue(new BigDecimal("8.70"));
        results.add(item3);
        
        // 模拟第四个特征的数据
        StatsResultFeatureLineChartItemDto item4 = new StatsResultFeatureLineChartItemDto();
        item4.setFeatureName("溶解氧");
        item4.setFCreateTime(new Timestamp(System.currentTimeMillis() - 1 * 60 * 60 * 1000)); // 1小时前
        item4.setCnt(20L);
        item4.setAvgValue(new BigDecimal("8.52"));
        item4.setMinValue(new BigDecimal("8.30"));
        item4.setMaxValue(new BigDecimal("8.75"));
        results.add(item4);
        
        return results;
    }
}
