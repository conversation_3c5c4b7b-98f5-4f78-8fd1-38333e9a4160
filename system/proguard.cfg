#-injars C:\Users\<USER>\Desktop\混淆试验\poc-common-pojo-1.0.0-SNAPSHOT.jar
#-outjars C:\Users\<USER>\Desktop\混淆试验\poc-common-pojo-1.0.0-SNAPSHOT-out.jar
#-libraryjars 'D:\Program\Java\jdk1.8.0_261\jre\lib\rt.jar'
#-libraryjars C:\Users\<USER>\Desktop\混淆试验\poc-common-sdk-1.0.0-SNAPSHOT-my-jar-with-dependencies\poc-common-sdk-1.0.0-SNAPSHOT\lib\lombok-1.18.22.jar
# 打印混淆过程的详细信息
-verbose

-target 1.8

################################### [1 Shrink]
# 关闭压缩 | 压缩（Shrink）:用于检测和删除没有使用的类、字段、方法和属性。
-dontshrink

################################### [2 Optimize]
# 关闭优化 | 优化（Optimize）:对于字节码进行优化，并且移除无用指令。
-dontoptimize

################################### [print]
# 列出未混淆的类和成员(打印出那些被keep住的类和成员，结果输出到指定文件里)
-printseeds D:\ANNIS\projects\les_admin\obfuscate\print_seeds.txt
# 列出混淆前后的映射，生成映射文件
-printmapping D:\ANNIS\projects\les_admin\obfuscate\print_mapping.txt
################################### [3 Obfuscate] 混淆（Obfuscate）:使用a,b,c等名称对类，字段和方法进行重命名
# 不混淆。默认启用混淆，类和类成员名会变成短小且随机的名字。可通过keep选项来保留一些。
# -dontobfuscate
# 使用模糊字典，您可以指定保留关键字的列表，或具有外来字符的标识符
# 例如： 忽略空格，标点符号，重复字和＃符号后的注释。
# 注意，模糊字典几乎不改善混淆。 有些编译器可以自动替换它们，并且通过使用更简单的名称再次混淆，可以很简单地撤消该效果。
# 最有用的是指定类文件中通常已经存在的字符串（例如'Code'），从而减少类文件的大小。 仅适用于混淆处理。
# -obfuscationdictionary C:\Users\<USER>\Desktop\混淆试验\pro_package.txt
# 指定一个文本文件，其中所有有效词都用作混淆类名。 与-obfuscationdictionary类似。 仅适用于混淆处理。
# -classobfuscationdictionary C:\Users\<USER>\Desktop\混淆试验\pro_class.txt
# 指定一个文本文件，其中所有有效词都用作混淆包名称。与-obfuscationdictionary类似。 仅适用于混淆处理。
# -packageobfuscationdictionary C:\Users\<USER>\Desktop\混淆试验\pro_func.txt
 
# 混淆时不使用大小写混合，混淆后的类名为小写(大小写混淆容易导致 class 文件相互覆盖）
-dontusemixedcaseclassnames
# 指定不去忽略非公共的库类(不跳过 library 中的非public的类)
# -dontskipnonpubliclibraryclasses
# 指定不去忽略包可见的库类的成员
# -dontskipnonpubliclibraryclassmembers
# 对于类成员的命名的混淆采取唯一策略
# -useuniqueclassmembernames
 
# 保留属性：避免混淆泛型、抛出异常时保留代码行号
-keepattributes SourceFile,LineNumberTable
# 保留属性：Annotation、内部类 不混淆
-keepattributes *Annotation*,InnerClasses
# 对异常、注解信息予以保留
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
# 保留属性：Signature ，否则你的object中含有其他对象的字段的时候会抛出ClassCastException
-keepattributes Signature
# 屏蔽所有警告 (如：找不到引用的类等情况)
# -ignorewarnings
# 屏蔽指定告警
#-dontwarn com.google.android.material.*
 
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
# 保留: 所有接口的原始名称不进行混淆
-keepnames interface ** { *; }
# 保留: 所有包中原始接口文件不进行混淆
-keep interface * extends * { *; }
######## 注：混淆语法主要用于定义不需要混淆的代码
# -keeppackagenames
# -keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,LocalVariable*Table,*Annotation*,Synthetic,EnclosingMethod
# 保留(不混淆)：指定（包/类/接口/枚举类、属性/方法）路径
# -keep class android.support.**{*;}
# -keep interface androidx.** {;}
# -keep class com.jack.bigdata.poc.pojo.**{*;}
#-keep class com.jack.bigdata.poc.pojo.BigdataMessageDto{*;}
#-keep public enum com.jack.bigdata.poc.pojo.BigdataMessageTypeEnum{*;}
 
# 保护指定的类和类的成员的名称，如果所有指定的类成员出席
#-keepclasseswithmembernames class * {
#	native <methods>;
#}
# 保护指定的类和类的成员，但条件是所有指定的类和类成员是要存在
#-keepclasseswithmembers class * {
#    public <init>(android.content.Context, android.util.AttributeSet);
#}
 
 
# Spring Boot 应用混淆配置 - 简化版本

# 完全保留Spring Boot Loader
-keep class org.springframework.boot.loader.** { *; }

# 保留主类
-keep class org.cjc.AppRun {
    public static void main(java.lang.String[]);
}

# 保留Spring Boot和Spring Framework核心类
-keep class org.springframework.** { *; }

# 保留项目代码的公共接口，但允许内部实现混淆
-keep class org.cjc.** {
    public *;
    protected *;
    private *;
}

# 保留所有子模块的关键类
# common模块
-keep class org.cjc.utils.** { *; }
-keep class org.cjc.config.** { *; }
-keep class org.cjc.base.** { *; }

# logging模块
-keep class org.cjc.logging.** { *; }

# tools模块
-keep class org.cjc.tools.** { *; }

# system-api模块
-keep class org.cjc.api.** { *; }

# les-core模块
-keep class org.cjc.les.core.** { *; }

# driver模块
-keep class org.cjc.driver.** { *; }

# generator模块
-keep class org.cjc.generator.** { *; }

# les-mgt模块
-keep class org.cjc.les.mgt.** { *; }

# 保留所有注解
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# 保留Spring相关注解的类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * { *; }

# 保留实体类和DTO
-keep class **.*Entity { *; }
-keep class **.*DTO { *; }
-keep class **.*Dto { *; }
-keep class **.*VO { *; }
-keep class **.*Vo { *; }

# 保留枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留序列化相关
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留JPA相关
-keep @jakarta.persistence.Entity class * { *; }
-keep @jakarta.persistence.Table class * { *; }

# 保留反射相关
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @jakarta.annotation.Resource *;
}

# 保留数据库驱动
-keep class com.mysql.** { *; }
-keep class org.h2.** { *; }

# 保留第三方库关键类
-keep class cn.hutool.** { *; }
-keep class com.alibaba.fastjson.** { *; }
-keep class org.apache.poi.** { *; }
-keep class com.github.oshi.** { *; }
-keep class javax.mail.** { *; }
-keep class com.qiniu.** { *; }
-keep class com.alipay.** { *; }
-keep class ch.ethz.ganymed.** { *; }
-keep class com.jcraft.jsch.** { *; }
-keep class net.sf.jasperreports.** { *; }
-keep class org.apache.commons.jexl3.** { *; }
-keep class org.reflections.** { *; }
-keep class io.jsonwebtoken.** { *; }
-keep class org.quartz.** { *; }

# 保留数据库驱动
-keep class com.mysql.** { *; }
-keep class org.h2.** { *; }
-keep class net.sf.log4jdbc.** { *; }
-keep class com.alibaba.druid.** { *; }

# 保留MANIFEST.MF中的Main-Class
-keepclasseswithmembers public class * {
    public static void main(java.lang.String[]);
}

# 忽略警告
-dontwarn **
################################### [4 Preverify] 预检（Preverify）:主要是在Java平台上对处理后的代码进行预检
# 不进行预校验 , 可加快混淆速度。
-dontpreverify