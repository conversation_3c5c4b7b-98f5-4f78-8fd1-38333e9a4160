#配置数据源
spring:
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      location: ${user.home}/.eladmin/file/tmp
  datasource:
    # 使用H2内存数据库进行测试
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driverClassName: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

#配置JPA
  jpa:
    hibernate:
      # 生产环境设置成 none，避免程序运行时自动更新数据库结构
      ddl-auto: create-drop

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 7200000
  # 在线用户key
  online-key: "online-token:"
  # 验证码
  code-key: "code-key:"
  # token 续期检查时间范围（默认30分钟，单位默认毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，这里单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为false
generator:
  enabled: true

#是否开启 swagger-ui
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# IP 本地解析
ip:
  local-parsing: true

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /home/<USER>/file/
    avatar: /home/<USER>/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5

# 登录图形验证码有效时间/分钟
loginCode:
  #  expiration: 2
  expiration: 1000
  # 验证码内容长度
  length: 2

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/分钟
code:
  expiration: 5

# SM.MS 图床的 token
smms:
  token: 1oOP0oDFRdDfaIYuamOqpWL3El6Ak7Ao

#阿里云
aliyun:
  accessKeyId: 
  accessKeySecret: 
  bucketName: 
  endpoint: 
  objectName: 
  # oss 外链域名
  domain: 
  # oss 外链域名协议
  protocol: https

# 百度云
baiduyun:
  accessKey: 
  secretKey: 
  bucketName: 
  # oss 外链域名
  domain: 
  # oss 外链域名协议
  protocol: https

# 腾讯云
tencent:
  secretId: 
  secretKey: 
  region: 
  bucketName: 
  # oss 外链域名
  domain: 
  # oss 外链域名协议
  protocol: https

# Minio
minio:
  endpoint: 
  accessKey: 
  secretKey: 
  bucketName: 
  # oss 外链域名
  domain: 
  # oss 外链域名协议
  protocol: https

# 本地存储
local:
  # oss 外链域名
  domain: 
  # oss 外链域名协议
  protocol: https

logging:
  level:
    org.cjc: debug
