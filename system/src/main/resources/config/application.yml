server:
  port: 8000

spring:
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  jackson:
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false
#  pid:
#    file: /自行指定位置/eladmin.pid

  #配置 Jpa
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

  redis:
    #数据库索引
    database: ${REDIS_DB:0}
    host: ${REDIS_HOST:127.0.0.1}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:b7be6c52}
    #连接超时时间
    timeout: 5000

logging:
  level:
    root: INFO                    # 设置全局日志级别
    org.springframework: WARN     # 设置 Spring 框架日志级别
    org.cjc.les: DEBUG    # 设置你的业务包日志级别

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 30
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/秒
code:
  expiration: 300

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

# 内存用户缓存配置
user-cache:
  # 最小回收数(当缓存数量达到此值时进行回收)
  min-evictable-size: 512
  # 最小回收间隔
  min-evictable-interval: 1800000
  # 最小存活时间 (ms)
  min-idle-time: 3600000