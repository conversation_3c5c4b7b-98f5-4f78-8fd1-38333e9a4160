#配置数据源
spring:
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      location: ${user.home}/.eladmin/file/tmp
  datasource:
    druid:
      db-type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      #-DDB_PWD=b7be6c52 -Dspring.redis.password=b7be6c52 -DDB_HOST=localhost -DDB_NAME=annis2_lesdb -DDB_USER=root -DDB_PWD=password
      url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:annis2_lesdb}?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&autoReconnect=true
      username: ${DB_USER:root}
      password: ${DB_PWD:password}
      #url: jdbc:log4jdbc:mysql://${DB_HOST:*************}:${DB_PORT:3306}/${DB_NAME:shopping_admin_dev}?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&autoReconnect=true
      #username: ${DB_USER:admin_dev}
      #password: ${DB_PWD:b7be6c52}
      # 初始连接数
      initial-size: 5
      # 最小连接数
      min-idle: 15
      # 最大连接数
      max-active: 30
      # 超时时间(以秒数为单位)
      remove-abandoned-timeout: 180
      # 获取连接超时时间
      max-wait: 3000
      # 连接有效性检测时间
      time-between-eviction-runs-millis: 30000
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 连接在池中最大生存的时间
      max-evictable-idle-time-millis: 900000
      # 指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除
      test-while-idle: true
      # 指明是否在从池中取出连接前进行检验,如果检验失败, 则从池中去除连接并尝试取出另一个
      test-on-borrow: true
      # 是否在归还到池中前进行检验
      test-on-return: false
      # 检测连接是否有效
      validation-query: select 1
      # 配置监控统计
      webStatFilter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
      filter:
        stat:
          enabled: true
          # 记录慢SQL
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# 登录相关配置
login:
  # 登录缓存
  cache-enable: true
  #  是否限制单用户登录
  single-login: false
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: arithmetic
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    height: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 14400000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为false
generator:
  enabled: true

#是否开启 swagger-ui
springdoc:
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
  api-docs:
    path: /v3/api-docs

# IP 本地解析
ip:
  local-parsing: true

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /www/wwwroot/shopping-admin-dev/file/
    avatar: /www/wwwroot/shopping-admin-dev/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5
