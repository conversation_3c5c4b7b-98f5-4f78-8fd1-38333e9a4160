<#--noinspection ALL-->
<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
    <#if hasQuery>
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <#if queryColumns??>
          <#list queryColumns as column>
            <#if column.queryType != 'BetWeen'>
        <label class="el-form-item-label"><#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if></label>
        <el-input v-model="query.${column.changeColumnName}" clearable placeholder="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
            </#if>
          </#list>
        </#if>
          <#if betweens??>
            <#list betweens as column>
              <#if column.queryType = 'BetWeen'>
                <date-range-picker
                  v-model="query.${column.changeColumnName}"
                  start-placeholder="${column.changeColumnName}Start"
                  end-placeholder="${column.changeColumnName}Start"
                  class="date-item"
                />
              </#if>
            </#list>
          </#if>
        <rrOperation :crud="crud" />
      </div>
    </#if>
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" :crud="crud" />
      <!--表单组件-->
      <el-dialog
        v-model="crud.dialogVisible"
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :title="crud.status.title"
        width="500px"
      >
        <el-form ref="formRef" :model="form" <#if isNotNullColumns??>:rules="rules"</#if> size="small" label-width="120px">
    <#if columns??>
      <#list columns as column>
        <#if column.formShow>
          <el-form-item label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>"<#if column.istNotNull> prop="${column.changeColumnName}"</#if>>
            <#if column.formType = 'Input'>
            <el-input v-model="form.${column.changeColumnName}" style="width: 370px;" />
            <#elseif column.formType = 'Textarea'>
            <el-input v-model="form.${column.changeColumnName}" :rows="3" type="textarea" style="width: 370px;" />
            <#elseif column.formType = 'Radio'>
              <#if (column.dictName)?? && (column.dictName)!="">
            <el-radio v-model="form.${column.changeColumnName}" v-for="item in dict.${column.dictName}" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
              <#else>
                未设置字典，请手动设置 Radio
              </#if>
            <#elseif column.formType = 'Select'>
              <#if (column.dictName)?? && (column.dictName)!="">
            <el-select v-model="form.${column.changeColumnName}" filterable placeholder="请选择">
              <el-option
                v-for="item in dict.${column.dictName}"
                :key="item.id"
                :label="item.label"
                :value="item.value" />
            </el-select>
              <#else>
            未设置字典，请手动设置 Select
              </#if>
            <#else>
            <el-date-picker v-model="form.${column.changeColumnName}" type="datetime" style="width: 370px;" />
            </#if>
          </el-form-item>
        </#if>
      </#list>
    </#if>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <#if columns??>
            <#list columns as column>
            <#if column.columnShow>
          <#if (column.dictName)?? && (column.dictName)!="">
        <el-table-column prop="${column.changeColumnName}" label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>">
          <template slot-scope="scope">
            {{ dict.label.${column.dictName}[scope.row.${column.changeColumnName}] }}
          </template>
        </el-table-column>
                <#else>
        <el-table-column prop="${column.changeColumnName}" label="<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>" />
                </#if>
            </#if>
            </#list>
        </#if>
        <el-table-column v-if="checkPer(['admin','${changeClassName}:edit','${changeClassName}:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :crud="crud"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud"/>

  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import crud${className} from '@/api/${changeClassName}'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
import pagination from '@/components/crud/Pagination'

// 定义组件名称
defineOptions({
  name: '${className}'
})

const defaultForm = { <#if columns??><#list columns as column>${column.changeColumnName}: null<#if column_has_next>, </#if></#list></#if> }

// 表单验证规则
const rules = reactive({
        <#if isNotNullColumns??>
        <#list isNotNullColumns as column>
        <#if column.istNotNull>
        ${column.changeColumnName}: [
          { required: true, message: '<#if column.remark != ''>${column.remark}</#if>不能为空', trigger: 'blur' }
        ]<#if column_has_next>,</#if>
        </#if>
        </#list>
        </#if>
      })

// 权限配置
const permission = {
  add: ['admin', '${changeClassName}:add'],
  edit: ['admin', '${changeClassName}:edit'],
  del: ['admin', '${changeClassName}:del']
}

// refs
const formRef = ref(null)
const tableRef = ref(null)

// 权限校验
const checkPer = inject('checkPer')

<#if hasQuery>
const queryTypeOptions = reactive( [
        <#if queryColumns??>
        <#list queryColumns as column>
        <#if column.queryType != 'BetWeen'>
        { key: '${column.changeColumnName}', display_name: '<#if column.remark != ''>${column.remark}<#else>${column.changeColumnName}</#if>' }<#if column_has_next>,</#if>
        </#if>
        </#list>
        </#if>
      ] )
</#if>


// 使用CRUD钩子
const { crud, form } = useCrud({
  title: '编辑',
  url: 'api/${changeClassName}',
  crudMethod: { ...crud${className} },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 生命周期
onMounted(() => {
  crud.refresh()
})

</script>

<style scoped>

</style>
