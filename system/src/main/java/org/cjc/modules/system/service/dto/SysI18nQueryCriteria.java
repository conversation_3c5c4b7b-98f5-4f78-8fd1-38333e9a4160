/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.util.List;
import org.cjc.annotation.Query;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-13
**/
@Data
public class SysI18nQueryCriteria{

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String key;

    /** 精确 */
    @Query
    private String locale;

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String message;
    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> updateTime;
}