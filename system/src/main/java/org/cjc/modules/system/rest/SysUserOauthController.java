/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.rest;

import org.cjc.annotation.Log;
import org.cjc.modules.system.domain.SysUserOauth;
import org.cjc.modules.system.service.SysUserOauthService;
import org.cjc.modules.system.service.dto.SysUserOauthDto;
import org.cjc.modules.system.service.dto.SysUserOauthQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-11
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "用户第三方平台鉴权管理")
@RequestMapping("/api/sysUserOauth")
public class SysUserOauthController {

    private final SysUserOauthService sysUserOauthService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sysUserOauth:list')")
    public void exportSysUserOauth(HttpServletResponse response, SysUserOauthQueryCriteria criteria) throws IOException {
        sysUserOauthService.download(sysUserOauthService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询用户第三方平台鉴权")
    @ApiOperation("查询用户第三方平台鉴权")
    @PreAuthorize("@el.check('sysUserOauth:list')")
    public ResponseEntity<Object> querySysUserOauth(SysUserOauthQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysUserOauthService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增用户第三方平台鉴权")
    @ApiOperation("新增用户第三方平台鉴权")
    @PreAuthorize("@el.check('sysUserOauth:add')")
    public ResponseEntity<Object> createSysUserOauth(@Validated @RequestBody SysUserOauthDto resources){
        return new ResponseEntity<>(sysUserOauthService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改用户第三方平台鉴权")
    @ApiOperation("修改用户第三方平台鉴权")
    @PreAuthorize("@el.check('sysUserOauth:edit')")
    public ResponseEntity<Object> updateSysUserOauth(@Validated @RequestBody SysUserOauth resources){
        sysUserOauthService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除用户第三方平台鉴权")
    @ApiOperation("删除用户第三方平台鉴权")
    @PreAuthorize("@el.check('sysUserOauth:del')")
    public ResponseEntity<Object> deleteSysUserOauth(@RequestBody Long[] ids) {
        sysUserOauthService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}