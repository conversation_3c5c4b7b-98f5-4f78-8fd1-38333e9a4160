/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-03-11
**/
@Entity
@Data
@Table(name="sys_user_oauth")
public class SysUserOauth implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Schema(description = "ID")
    private Long id;

    @Column(name = "tenant_id")
    @Schema(description = "租户ID")
    private Long tenantId;

    @Column(name = "user_id")
    @Schema(description = "用户ID")
    private Long userId;

    @Column(name = "oauth_type")
    @Schema(description = "第三方平台鉴权类型,MP-WEIXIN")
    private String oauthType;

    @Column(name = "oauth_open_id")
    @Schema(description = "第三方平台用户唯一标识open_id")
    private String oauthOpenId;

    @Column(name = "create_by")
    @Schema(description = "创建者")
    private String createBy;

    @Column(name = "update_by")
    @Schema(description = "更新者")
    private String updateBy;

    @Column(name = "create_time")
    @Schema(description = "创建日期")
    private Timestamp createTime;

    @Column(name = "update_time")
    @Schema(description = "更新时间")
    private Timestamp updateTime;

    public void copy(SysUserOauth source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}