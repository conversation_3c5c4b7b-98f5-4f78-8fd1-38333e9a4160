/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.impl;

import org.cjc.exception.EntityExistException;
import org.cjc.modules.system.domain.UserGrade;
import lombok.RequiredArgsConstructor;
import org.cjc.modules.system.repository.UserGradeRepository;
import org.cjc.modules.system.service.UserGradeService;
import org.cjc.modules.system.service.dto.UserGradeDto;
import org.cjc.modules.system.service.dto.UserGradeQueryCriteria;
import org.cjc.modules.system.service.mapstruct.UserGradeMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-03-19
**/
@Service
@RequiredArgsConstructor
public class UserGradeServiceImpl implements UserGradeService {

    private final UserGradeRepository userGradeRepository;
    private final UserGradeMapper userGradeMapper;

    @Override
    public Map<String,Object> queryAll(UserGradeQueryCriteria criteria, Pageable pageable){
        Page<UserGrade> page = userGradeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(userGradeMapper::toDto));
    }

    @Override
    public List<UserGradeDto> queryAll(UserGradeQueryCriteria criteria){
        return userGradeMapper.toDto(userGradeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public UserGradeDto findById(Long gradeId) {
        UserGrade userGrade = userGradeRepository.findById(gradeId).orElseGet(UserGrade::new);
        ValidationUtil.isNull(userGrade.getGradeId(),"UserGrade","gradeId",gradeId);
        return userGradeMapper.toDto(userGrade);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserGradeDto create(UserGrade resources) {
        if(userGradeRepository.findByName(resources.getName()) != null){
            throw new EntityExistException(UserGrade.class,"name",resources.getName());
        }
        return userGradeMapper.toDto(userGradeRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UserGrade resources) {
        UserGrade userGrade = userGradeRepository.findById(resources.getGradeId()).orElseGet(UserGrade::new);
        ValidationUtil.isNull( userGrade.getGradeId(),"UserGrade","id",resources.getGradeId());
        UserGrade userGrade1 = userGradeRepository.findByName(resources.getName());
        if(userGrade1 != null && !userGrade1.getGradeId().equals(userGrade.getGradeId())){
            throw new EntityExistException(UserGrade.class,"name",resources.getName());
        }
        userGrade.copy(resources);
        userGradeRepository.save(userGrade);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long gradeId : ids) {
            userGradeRepository.deleteById(gradeId);
        }
    }

    @Override
    public void download(List<UserGradeDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (UserGradeDto userGrade : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("租户ID", userGrade.getTenantId());
            map.put("等级名称", userGrade.getName());
            map.put("等级权重(1-9999)", userGrade.getWeight());
            map.put("升级条件", userGrade.getUpgrade());
            map.put("等级权益(折扣率0-100)", userGrade.getEquity());
            map.put("状态：1启用、0禁用", userGrade.getEnabled());
            map.put("创建日期", userGrade.getCreateTime());
            map.put("创建者", userGrade.getCreateBy());
            map.put("更新时间", userGrade.getUpdateTime());
            map.put("更新者", userGrade.getUpdateBy());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}