/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.service.mapstruct;

import org.cjc.base.BaseMapper;
import org.cjc.modules.system.domain.SysI18n;
import org.cjc.modules.system.service.dto.SysI18nDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-13
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysI18nMapper extends BaseMapper<SysI18nDto, SysI18n> {

}