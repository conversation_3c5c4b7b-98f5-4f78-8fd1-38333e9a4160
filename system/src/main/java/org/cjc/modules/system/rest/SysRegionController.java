/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.rest;

import org.cjc.annotation.Log;
import org.cjc.modules.system.domain.SysRegion;
import org.cjc.modules.system.service.SysRegionService;
import org.cjc.modules.system.service.dto.SysRegionQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-12-16
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "区域管理管理")
@RequestMapping("/api/sysRegion")
public class SysRegionController {

    private final SysRegionService sysRegionService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sysRegion:list')")
    public void exportSysRegion(HttpServletResponse response, SysRegionQueryCriteria criteria) throws IOException {
        sysRegionService.download(sysRegionService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询区域管理")
    @ApiOperation("查询区域管理")
    @PreAuthorize("@el.check('sysRegion:list')")
    public ResponseEntity<Object> querySysRegion(SysRegionQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysRegionService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增区域管理")
    @ApiOperation("新增区域管理")
    @PreAuthorize("@el.check('sysRegion:add')")
    public ResponseEntity<Object> createSysRegion(@Validated @RequestBody SysRegion resources){
        return new ResponseEntity<>(sysRegionService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改区域管理")
    @ApiOperation("修改区域管理")
    @PreAuthorize("@el.check('sysRegion:edit')")
    public ResponseEntity<Object> updateSysRegion(@Validated @RequestBody SysRegion resources){
        sysRegionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除区域管理")
    @ApiOperation("删除区域管理")
    @PreAuthorize("@el.check('sysRegion:del')")
    public ResponseEntity<Object> deleteSysRegion(@RequestBody Integer[] ids) {
        sysRegionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}