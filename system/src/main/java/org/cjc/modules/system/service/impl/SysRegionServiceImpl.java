/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.cjc.modules.system.domain.SysRegion;
import lombok.RequiredArgsConstructor;
import org.cjc.modules.system.repository.SysRegionRepository;
import org.cjc.modules.system.service.SysRegionService;
import org.cjc.modules.system.service.dto.SysRegionDto;
import org.cjc.modules.system.service.dto.SysRegionQueryCriteria;
import org.cjc.modules.system.service.mapstruct.SysRegionMapper;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-12-16
**/
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "sysRegion")
public class SysRegionServiceImpl implements SysRegionService {

    private final SysRegionRepository sysRegionRepository;
    private final SysRegionMapper sysRegionMapper;

    @Override
    public Map<String,Object> queryAll(SysRegionQueryCriteria criteria, Pageable pageable){
        Page<SysRegion> page = sysRegionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sysRegionMapper::toDto));
    }

    @Override
    public List<SysRegionDto> queryAll(SysRegionQueryCriteria criteria){
        return sysRegionMapper.toDto(sysRegionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Cacheable(key = "'SysRegionTrees'")
    @Override
    public List<SysRegionDto> findAllAsTree() {
        SysRegionQueryCriteria criteria = new SysRegionQueryCriteria();
        List<SysRegionDto> list = queryAll(criteria);
        List<SysRegionDto> rootList = new ArrayList<>();
        for (SysRegionDto level1Dto : list) {
            if (level1Dto.getLevel() == 1){
                rootList.add(level1Dto);
                List<SysRegionDto> level2List = new ArrayList<>();
                for (SysRegionDto level2Dto : list) {
                    if (level2Dto.getLevel()==2 && level2Dto.getPid() == level1Dto.getId()) {
                        level2List.add(level2Dto);
                        List<SysRegionDto> level3List = new ArrayList<>();
                        for (SysRegionDto level3Dto : list) {
                            if (level3Dto.getLevel()==3 && level3Dto.getPid() == level2Dto.getId()) {
                                level3List.add(level3Dto);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(level3List)){
                            level2Dto.setChildren(level3List);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(level2List)){
                    level1Dto.setChildren(level2List);
                }
            }
        }
        return rootList;
    }

    @Override
    @Transactional
    public SysRegionDto findById(Integer id) {
        SysRegion sysRegion = sysRegionRepository.findById(id).orElseGet(SysRegion::new);
        ValidationUtil.isNull(sysRegion.getId(),"SysRegion","id",id);
        return sysRegionMapper.toDto(sysRegion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysRegionDto create(SysRegion resources) {
        return sysRegionMapper.toDto(sysRegionRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysRegion resources) {
        SysRegion sysRegion = sysRegionRepository.findById(resources.getId()).orElseGet(SysRegion::new);
        ValidationUtil.isNull( sysRegion.getId(),"SysRegion","id",resources.getId());
        sysRegion.copy(resources);
        sysRegionRepository.save(sysRegion);
    }

    @Override
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            sysRegionRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SysRegionDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysRegionDto sysRegion : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("区划名称", sysRegion.getName());
            map.put("父级ID", sysRegion.getPid());
            map.put("区划编码", sysRegion.getCode());
            map.put("层级(1省级 2市级 3区/县级)", sysRegion.getLevel());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}