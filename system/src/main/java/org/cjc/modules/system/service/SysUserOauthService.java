/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service;

import org.cjc.modules.system.domain.SysUserOauth;
import org.cjc.modules.system.service.dto.SysUserOauthDto;
import org.cjc.modules.system.service.dto.SysUserOauthQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2023-03-11
**/
public interface SysUserOauthService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SysUserOauthQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SysUserOauthDto>
    */
    List<SysUserOauthDto> queryAll(SysUserOauthQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return SysUserOauthDto
     */
    SysUserOauthDto findById(Long id);

    /**
     * 根据openId及类型查找
     *
     * @param openId 第三方平台openId
     * @param oauthType 第三方平台类型
     * @return SysUserOauthDto
     */
    SysUserOauthDto findByOpenIdAndType(String openId, String oauthType);


    /**
    * 创建
    * @param resources /
    * @return SysUserOauthDto
    */
    SysUserOauthDto create(SysUserOauthDto resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(SysUserOauth resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SysUserOauthDto> all, HttpServletResponse response) throws IOException;
}