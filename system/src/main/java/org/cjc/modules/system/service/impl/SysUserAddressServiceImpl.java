/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.impl;

import org.cjc.modules.system.domain.SysUserAddress;
import lombok.RequiredArgsConstructor;
import org.cjc.modules.system.repository.SysUserAddressRepository;
import org.cjc.modules.system.service.SysUserAddressService;
import org.cjc.modules.system.service.dto.SysUserAddressDto;
import org.cjc.modules.system.service.dto.SysUserAddressQueryCriteria;
import org.cjc.modules.system.service.mapstruct.SysUserAddressMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-12-16
**/
@Service
@RequiredArgsConstructor
public class SysUserAddressServiceImpl implements SysUserAddressService {

    private final SysUserAddressRepository sysUserAddressRepository;
    private final SysUserAddressMapper sysUserAddressMapper;

    @Override
    public Map<String,Object> queryAll(SysUserAddressQueryCriteria criteria, Pageable pageable){
        Page<SysUserAddress> page = sysUserAddressRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sysUserAddressMapper::toDto));
    }

    @Override
    public List<SysUserAddressDto> queryAll(SysUserAddressQueryCriteria criteria){
        return sysUserAddressMapper.toDto(sysUserAddressRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public List<SysUserAddressDto> findByUserId(Long userId) {
        SysUserAddressQueryCriteria criteria = new SysUserAddressQueryCriteria();
        criteria.setUserId(userId);
        return queryAll(criteria);
    }

    @Override
    @Transactional
    public SysUserAddressDto findById(Integer addressId) {
        SysUserAddress sysUserAddress = sysUserAddressRepository.findById(addressId).orElseGet(SysUserAddress::new);
        ValidationUtil.isNull(sysUserAddress.getAddressId(),"SysUserAddress","addressId",addressId);
        return sysUserAddressMapper.toDto(sysUserAddress);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserAddressDto create(SysUserAddress resources) {
        return sysUserAddressMapper.toDto(sysUserAddressRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserAddress resources) {
        SysUserAddress sysUserAddress = sysUserAddressRepository.findById(resources.getAddressId()).orElseGet(SysUserAddress::new);
        ValidationUtil.isNull( sysUserAddress.getAddressId(),"SysUserAddress","id",resources.getAddressId());
        sysUserAddress.copy(resources);
        sysUserAddressRepository.save(sysUserAddress);
    }

    @Override
    public void deleteAll(Integer[] ids) {
        for (Integer addressId : ids) {
            sysUserAddressRepository.deleteById(addressId);
        }
    }

    @Override
    public void download(List<SysUserAddressDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysUserAddressDto sysUserAddress : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("收货人姓名", sysUserAddress.getName());
            map.put("联系电话", sysUserAddress.getPhone());
            map.put("省份ID", sysUserAddress.getProvinceId());
            map.put("城市ID", sysUserAddress.getCityId());
            map.put("区/县ID", sysUserAddress.getRegionId());
            map.put("详细地址", sysUserAddress.getDetail());
            map.put("用户ID", sysUserAddress.getUserId());
            map.put("是否删除", sysUserAddress.getIsDelete());
            map.put("租户ID", sysUserAddress.getTenantId());
            map.put("创建时间", sysUserAddress.getCreateTime());
            map.put("更新时间", sysUserAddress.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}