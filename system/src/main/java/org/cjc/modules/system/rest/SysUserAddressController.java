/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.rest;

import org.cjc.annotation.Log;
import org.cjc.modules.system.domain.SysUserAddress;
import org.cjc.modules.system.service.SysUserAddressService;
import org.cjc.modules.system.service.dto.SysUserAddressQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-12-16
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "用户收货地址管理")
@RequestMapping("/api/sysUserAddress")
public class SysUserAddressController {

    private final SysUserAddressService sysUserAddressService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sysUserAddress:list')")
    public void exportSysUserAddress(HttpServletResponse response, SysUserAddressQueryCriteria criteria) throws IOException {
        sysUserAddressService.download(sysUserAddressService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询用户收货地址")
    @ApiOperation("查询用户收货地址")
    @PreAuthorize("@el.check('sysUserAddress:list')")
    public ResponseEntity<Object> querySysUserAddress(SysUserAddressQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysUserAddressService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增用户收货地址")
    @ApiOperation("新增用户收货地址")
    @PreAuthorize("@el.check('sysUserAddress:add')")
    public ResponseEntity<Object> createSysUserAddress(@Validated @RequestBody SysUserAddress resources){
        return new ResponseEntity<>(sysUserAddressService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改用户收货地址")
    @ApiOperation("修改用户收货地址")
    @PreAuthorize("@el.check('sysUserAddress:edit')")
    public ResponseEntity<Object> updateSysUserAddress(@Validated @RequestBody SysUserAddress resources){
        sysUserAddressService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除用户收货地址")
    @ApiOperation("删除用户收货地址")
    @PreAuthorize("@el.check('sysUserAddress:del')")
    public ResponseEntity<Object> deleteSysUserAddress(@RequestBody Integer[] ids) {
        sysUserAddressService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}