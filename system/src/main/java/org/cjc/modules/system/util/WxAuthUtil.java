package org.cjc.modules.system.util;

import com.alibaba.fastjson2.TypeReference;
import org.apache.http.client.utils.HttpClientUtils;
import org.cjc.utils.HttpClientUtil;

import java.util.HashMap;
import java.util.Map;

public class WxAuthUtil {
    /**
     * code 换取 session_key
     * 这是一个 HTTPS 接口，开发者服务器使用登录凭证 code 获取 session_key 和 openid。
     * 其中 session_key 是对用户数据进行加密签名的密钥。为了自身应用安全，session_key 不应该在网络上传输。
     *  $url = 'https://api.weixin.qq.com/sns/jscode2session';
     *         $result = helper::jsonDecode($this->get($url, [
     *             'appid' => $this->appId,
     *             'secret' => $this->appSecret,
     *             'grant_type' => 'authorization_code',
     *             'js_code' => $code
     *         ]));
     *         if (isset($result['errcode'])) {
     *             $this->error = $result['errmsg'];
     *             return false;
     *         }
     *         return $result;
     */
    public static Map<String, String> jscode2Session(String code) {
        Map<String, String> rsMap = HttpClientUtil.httpGet("https://api.weixin.qq.com/sns/jscode2session?appId=wxaa52c1a2f2aa80a7&secret=539f82c7329fe4a6261dd5661bc485f1&grant_type=authorization_code&js_code="+code,
                new HashMap<String,String>(), new TypeReference<HashMap<String,String>>(){});

        return rsMap;
    }

}
