/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.service.impl;

import org.cjc.modules.system.domain.SysUserOauth;
import lombok.RequiredArgsConstructor;
import org.cjc.modules.system.domain.User;
import org.cjc.modules.system.repository.SysUserOauthRepository;
import org.cjc.modules.system.repository.UserRepository;
import org.cjc.modules.system.service.SysUserOauthService;
import org.cjc.modules.system.service.dto.SysUserOauthDto;
import org.cjc.modules.system.service.dto.SysUserOauthQueryCriteria;
import org.cjc.modules.system.service.mapstruct.SysUserOauthMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2023-03-11
**/
@Service
@RequiredArgsConstructor
public class SysUserOauthServiceImpl implements SysUserOauthService {

    private final SysUserOauthRepository sysUserOauthRepository;
    private final SysUserOauthMapper sysUserOauthMapper;
    private final UserRepository userRepository;

    @Override
    public Map<String,Object> queryAll(SysUserOauthQueryCriteria criteria, Pageable pageable){
        Page<SysUserOauth> page = sysUserOauthRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sysUserOauthMapper::toDto));
    }

    @Override
    public List<SysUserOauthDto> queryAll(SysUserOauthQueryCriteria criteria){
        return sysUserOauthMapper.toDto(sysUserOauthRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public SysUserOauthDto findById(Long id) {
        SysUserOauth sysUserOauth = sysUserOauthRepository.findById(id).orElseGet(SysUserOauth::new);
        ValidationUtil.isNull(sysUserOauth.getId(),"SysUserOauth","id",id);
        return sysUserOauthMapper.toDto(sysUserOauth);
    }

    @Override
    public SysUserOauthDto findByOpenIdAndType(String openId, String oauthType) {
        SysUserOauth sysUserOauth = sysUserOauthRepository.findByOauthOpenIdAndOauthType(openId, oauthType);
        if (sysUserOauth == null){
            return null;
        }
        return sysUserOauthMapper.toDto(sysUserOauth);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserOauthDto create(SysUserOauthDto resources) {

        User user = new User();
        user.setUsername(resources.getOauthOpenId());
        user.setNickName(resources.getNickName());
        user.setPhone(resources.getPhone());
        user.setAvatarPath(resources.getAvatarPath());
        user.setGender(resources.getGender());
        user.setEnabled(true);
        user.setIsAdmin(false);
        userRepository.save(user);
        resources.setUserId(user.getId());

        SysUserOauth userOauth = sysUserOauthMapper.toEntity(resources);
        return sysUserOauthMapper.toDto(sysUserOauthRepository.save(userOauth));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserOauth resources) {
        SysUserOauth sysUserOauth = sysUserOauthRepository.findById(resources.getId()).orElseGet(SysUserOauth::new);
        ValidationUtil.isNull( sysUserOauth.getId(),"SysUserOauth","id",resources.getId());
        sysUserOauth.copy(resources);
        sysUserOauthRepository.save(sysUserOauth);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            sysUserOauthRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SysUserOauthDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysUserOauthDto sysUserOauth : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("租户ID", sysUserOauth.getTenantId());
            map.put("用户ID", sysUserOauth.getUserId());
            map.put("第三方平台鉴权类型,MP-WEIXIN", sysUserOauth.getOauthType());
            map.put("第三方平台用户唯一标识open_id", sysUserOauth.getOauthOpenId());
            map.put("创建者", sysUserOauth.getCreateBy());
            map.put("更新者", sysUserOauth.getUpdateBy());
            map.put("创建日期", sysUserOauth.getCreateTime());
            map.put("更新时间", sysUserOauth.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}