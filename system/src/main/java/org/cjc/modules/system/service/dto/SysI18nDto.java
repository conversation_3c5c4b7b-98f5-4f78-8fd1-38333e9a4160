/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-13
**/
@Data
public class SysI18nDto implements Serializable {

    /** ID */
    private Long id;

    /** 消息访问键 */
    private String key;

    private boolean keyShowEditor = false;

    /** 区域码 */
    private String locale;

    private boolean localeShowEditor = false;

    /** 消息 */
    private String message;

    private boolean messageShowEditor = false;


    /** 描述，解释占位符等信息，补充说明 */
    private String description;

    private boolean descriptionShowEditor = false;

    /** 是否删除, Y/N */
    private String deleteFlag;

    /** 创建者 */
    private String createBy;

    /** 更新者 */
    private String updateBy;

    /** 创建日期 */
    private Timestamp createTime;

    /** 更新时间 */
    private Timestamp updateTime;
}