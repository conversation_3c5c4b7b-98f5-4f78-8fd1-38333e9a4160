/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.service.impl;

import com.alibaba.fastjson.JSON;
import org.cjc.config.FileProperties;
import org.cjc.les.core.domain.TaskExecuteLog;
import org.cjc.modules.system.domain.SysI18n;
import lombok.RequiredArgsConstructor;
import org.cjc.modules.system.repository.SysI18nRepository;
import org.cjc.modules.system.service.SysI18nService;
import org.cjc.modules.system.service.dto.SysI18nDto;
import org.cjc.modules.system.service.dto.SysI18nQueryCriteria;
import org.cjc.modules.system.service.mapstruct.SysI18nMapper;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.cjc.utils.FileUtil;
import org.cjc.utils.PageUtil;
import org.cjc.utils.QueryHelp;
import org.cjc.utils.ValidationUtil;

import java.util.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
* @website https://el-admin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-13
**/
@Service
@RequiredArgsConstructor
public class SysI18nServiceImpl implements SysI18nService {

    private final SysI18nRepository sysI18nRepository;
    private final SysI18nMapper sysI18nMapper;

    private final FileProperties fileProperties;

    @Override
    public Map<String,Object> queryAll(SysI18nQueryCriteria criteria, Pageable pageable){
        Page<SysI18n> page = sysI18nRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(sysI18nMapper::toDto));
    }

    @Override
    public List<SysI18nDto> queryAll(SysI18nQueryCriteria criteria){
        return sysI18nMapper.toDto(sysI18nRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public SysI18nDto findById(Long id) {
        SysI18n sysI18n = sysI18nRepository.findById(id).orElseGet(SysI18n::new);
        ValidationUtil.isNull(sysI18n.getId(),"SysI18n","id",id);
        return sysI18nMapper.toDto(sysI18n);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysI18nDto create(SysI18n resources) {
        return sysI18nMapper.toDto(sysI18nRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysI18n resources) {
        SysI18n sysI18n = sysI18nRepository.findById(resources.getId()).orElseGet(SysI18n::new);
        ValidationUtil.isNull( sysI18n.getId(),"SysI18n","id",resources.getId());
        sysI18n.copy(resources);
        sysI18nRepository.save(sysI18n);
    }

    @Override
    public List<SysI18nDto> batchSave(List<SysI18nDto> resources) {
        List<SysI18nDto> toUpdateList = resources.stream().filter(dto->{
            return dto.getId()>0;
        }).collect(Collectors.toList());

        List<SysI18nDto> toAddList = resources.stream().filter(dto->{
            return dto.getId()<0;
        }).collect(Collectors.toList());

        List<SysI18n> toSaveList = new ArrayList<>();
        List<Long> toUpdateIds = toUpdateList.stream().map(SysI18nDto::getId).collect(Collectors.toList());
        List<SysI18n> toUpdateListInDb = sysI18nRepository.findAllById(toUpdateIds);
        for (SysI18n db : toUpdateListInDb) {
            Optional<SysI18nDto> dtoI18n = toUpdateList.stream().filter(dto->{
                return db.getId().equals(dto.getId());
            }).findFirst();
            if (dtoI18n.isPresent()) {
                db.copy(sysI18nMapper.toEntity(dtoI18n.get()));
            }
        }
        toSaveList.addAll(toUpdateListInDb);

        for (SysI18nDto newDto : toAddList) {
            newDto.setId(null);
            toSaveList.add(sysI18nMapper.toEntity(newDto));
        }

        List<SysI18n> savedList = sysI18nRepository.saveAll(toSaveList);

        generateLanguageFile();

        return sysI18nMapper.toDto(savedList);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            sysI18nRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<SysI18nDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysI18nDto sysI18n : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("消息访问键", sysI18n.getKey());
            map.put("区域码", sysI18n.getLocale());
            map.put("消息", sysI18n.getMessage());
            map.put("描述，解释占位符等信息，补充说明", sysI18n.getDescription());
            map.put("是否删除, Y/N", sysI18n.getDeleteFlag());
            map.put("创建者", sysI18n.getCreateBy());
            map.put("更新者", sysI18n.getUpdateBy());
            map.put("创建日期", sysI18n.getCreateTime());
            map.put("更新时间", sysI18n.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    private void generateLanguageFile() {
        SysI18nQueryCriteria criteria = new SysI18nQueryCriteria();
        Pageable pageable = PageRequest.of(0, 3, Sort.by(new Sort.Order(Sort.Direction.ASC,"id")));

        Map<String, Map<String,String>> outMap = new HashMap<>();
        Page<SysI18n> page = null;
        do{
            page = sysI18nRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
            List<SysI18n> result = page.getContent();
            for (SysI18n i18n : result) {
                if (outMap.get(i18n.getLocale()) == null){
                    outMap.put(i18n.getLocale(), new HashMap<String, String>());
                }
                outMap.get(i18n.getLocale()).put(i18n.getKey(), i18n.getMessage());
            }

            if (page.isLast()) {
                break;
            }

            pageable = page.getPageable().next();
        }
        while (true);
        // Save File
        String jsonStr = JSON.toJSONString(outMap);
        String fileName = fileProperties.getPath().getPath() + "/lang.js";
        FileUtil.writeString(jsonStr, fileName, "utf8");
    }
}