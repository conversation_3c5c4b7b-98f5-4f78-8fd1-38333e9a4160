/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.rest;

import org.cjc.annotation.Log;
import org.cjc.modules.system.domain.SysI18n;
import org.cjc.modules.system.service.SysI18nService;
import org.cjc.modules.system.service.dto.SysI18nDto;
import org.cjc.modules.system.service.dto.SysI18nQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2024-11-13
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "国际化管理管理")
@RequestMapping("/api/sysI18n")
public class SysI18nController {

    private final SysI18nService sysI18nService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sysI18n:list')")
    public void exportSysI18n(HttpServletResponse response, SysI18nQueryCriteria criteria) throws IOException {
        sysI18nService.download(sysI18nService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询国际化管理")
    @ApiOperation("查询国际化管理")
    @PreAuthorize("@el.check('sysI18n:list')")
    public ResponseEntity<Object> querySysI18n(SysI18nQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysI18nService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增国际化管理")
    @ApiOperation("新增国际化管理")
    @PreAuthorize("@el.check('sysI18n:add')")
    public ResponseEntity<Object> createSysI18n(@Validated @RequestBody SysI18n resources){
        return new ResponseEntity<>(sysI18nService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改国际化管理")
    @ApiOperation("修改国际化管理")
    @PreAuthorize("@el.check('sysI18n:edit')")
    public ResponseEntity<Object> updateSysI18n(@Validated @RequestBody SysI18n resources){
        sysI18nService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除国际化管理")
    @ApiOperation("删除国际化管理")
    @PreAuthorize("@el.check('sysI18n:del')")
    public ResponseEntity<Object> deleteSysI18n(@RequestBody Long[] ids) {
        sysI18nService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/batchSave")
    @Log("批量保存国际化管理")
    @ApiOperation("批量保存国际化管理")
    @PreAuthorize("@el.check('sysI18n:add')")
    public ResponseEntity<Object> batchSave(@Validated @RequestBody List<SysI18nDto> resources){
        return new ResponseEntity<>(sysI18nService.batchSave(resources),HttpStatus.CREATED);
    }

}