/*
*  Copyright 2024-2024 Wuhan Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.service;

import org.cjc.modules.system.domain.SysI18n;
import org.cjc.modules.system.service.dto.SysI18nDto;
import org.cjc.modules.system.service.dto.SysI18nQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-11-13
**/
public interface SysI18nService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SysI18nQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SysI18nDto>
    */
    List<SysI18nDto> queryAll(SysI18nQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return SysI18nDto
     */
    SysI18nDto findById(Long id);

    /**
    * 创建
    * @param resources /
    * @return SysI18nDto
    */
    SysI18nDto create(SysI18n resources);

    /**
     * 批量保存
     * @param resources
     * @return
     */
    List<SysI18nDto> batchSave(List<SysI18nDto> resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(SysI18n resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SysI18nDto> all, HttpServletResponse response) throws IOException;
}