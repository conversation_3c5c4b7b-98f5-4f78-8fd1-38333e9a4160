/*
*  Copyright 2022-2022 cjc
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package org.cjc.modules.system.rest;

import org.cjc.annotation.Log;
import org.cjc.modules.system.domain.UserGrade;
import org.cjc.modules.system.service.UserGradeService;
import org.cjc.modules.system.service.dto.UserGradeQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-19
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "会员等级接口管理")
@RequestMapping("/api/userGrade")
public class UserGradeController {

    private final UserGradeService userGradeService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('userGrade:list')")
    public void exportUserGrade(HttpServletResponse response, UserGradeQueryCriteria criteria) throws IOException {
        userGradeService.download(userGradeService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询会员等级接口")
    @ApiOperation("查询会员等级接口")
    @PreAuthorize("@el.check('userGrade:list')")
    public ResponseEntity<Object> queryUserGrade(UserGradeQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(userGradeService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增会员等级接口")
    @ApiOperation("新增会员等级接口")
    @PreAuthorize("@el.check('userGrade:add')")
    public ResponseEntity<Object> createUserGrade(@Validated @RequestBody UserGrade resources){
        return new ResponseEntity<>(userGradeService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改会员等级接口")
    @ApiOperation("修改会员等级接口")
    @PreAuthorize("@el.check('userGrade:edit')")
    public ResponseEntity<Object> updateUserGrade(@Validated @RequestBody UserGrade resources){
        userGradeService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除会员等级接口")
    @ApiOperation("删除会员等级接口")
    @PreAuthorize("@el.check('userGrade:del')")
    public ResponseEntity<Object> deleteUserGrade(@RequestBody Long[] ids) {
        userGradeService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}