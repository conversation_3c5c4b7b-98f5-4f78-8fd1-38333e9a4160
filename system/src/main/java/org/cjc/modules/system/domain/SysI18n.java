/*
*  Copyright 2024-2024 <PERSON>han Ximei Technology Co., Ltd. All rights reserved.
*/
package org.cjc.modules.system.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.EqualsAndHashCode;
import org.cjc.base.BaseEntity;
import org.cjc.base.LesConfigBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-11-13
**/
@Entity
@Data
@Table(name="sys_i18n")
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@SQLDelete(sql = "UPDATE sys_i18n SET delete_flag = 'Y', update_time=now() WHERE id = ?")
@Where(clause = "delete_flag = 'N'")
public class SysI18n extends LesConfigBaseEntity {

    @Column(name = "`key`",nullable = false)
    @NotBlank
    @Schema(description = "消息访问键")
    private String key;

    @Column(name = "locale",nullable = false)
    @NotBlank
    @Schema(description = "区域码")
    private String locale;

    @Column(name = "message")
    @Schema(description = "消息")
    private String message;

    @Column(name = "description")
    @Schema(description = "描述，解释占位符等信息，补充说明")
    private String description;

    public void copy(SysI18n source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}