package org.cjc.modules.security.rest;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cjc.annotation.rest.AnonymousGetMapping;
import org.cjc.annotation.rest.AnonymousPostMapping;
import org.cjc.common.uniapp.UniappResponseEntity;
import org.cjc.modules.security.config.bean.LoginProperties;
import org.cjc.modules.security.config.bean.SecurityProperties;
import org.cjc.modules.security.security.TokenProvider;
import org.cjc.modules.security.service.OnlineUserService;
import org.cjc.modules.security.service.dto.JwtUserDto;
import org.cjc.modules.system.service.SysUserOauthService;
import org.cjc.modules.system.service.dto.SysUserOauthDto;
import org.cjc.modules.system.util.WxAuthUtil;
import org.cjc.utils.RedisUtils;
import org.cjc.utils.SecurityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-3-8
 * UNIAPP前端系统登录验证
 */
@Slf4j
@RestController
@RequestMapping("/uniapp/auth")
@RequiredArgsConstructor
@Api(tags = "UNIAPP系统：系统授权接口")
public class UniappAuthorizationController {
    private final SecurityProperties properties;
    private final RedisUtils redisUtils;
    private final OnlineUserService onlineUserService;
    private final TokenProvider tokenProvider;
    private final AuthenticationManagerBuilder authenticationManagerBuilder;
    private final SysUserOauthService sysUserOauthService;

    private final UserDetailsService userDetailsService;
    @Resource
    private LoginProperties loginProperties;



    @ApiOperation("获取当前登录用户设置")
    @AnonymousGetMapping(value = "/setting")
    public ResponseEntity<Object> settings() {
        // 验证码信息
        Map<String, Object> imgResult = new HashMap<String, Object>(2) {{
            put("img", "captcha.toBase64()");
            put("uuid", "uuid");
        }};
        // Testing
        String resp = "{\"status\":200,\"message\":\"success\",\"data\":{\"setting\":{\"page_category_template\":{\"style\":20,\"shareTitle\":\"商品分类\"},\"points\":{\"points_name\":\"积分\",\"describe\":\"a) 积分不可兑现、不可转让,仅可在本平台使用;\\nb) 您在本平台参加特定活动也可使用积分,详细使用规则以具体活动时的规则为准;\\nc) 积分的数值精确到个位(小数点后全部舍弃,不进行四舍五入)\\nd) 买家在完成该笔交易(订单状态为“已签收”)后才能得到此笔交易的相应积分,如购买商品参加店铺其他优惠,则优惠的金额部分不享受积分获取;\"},\"recharge\":{\"is_entrance\":1,\"is_custom\":1,\"describe\":\"1. 账户充值仅限微信在线方式支付，充值金额实时到账；\\n2. 账户充值套餐赠送的金额即时到账；\\n3. 账户余额有效期：自充值日起至用完即止；\\n4. 若有其它疑问，可拨打客服电话400-000-1234\"},\"register\":{\"registerMethod\":10,\"isOauthMpweixin\":1,\"isManualBind\":1},\"_other\":{\"h5Url\":\"https:\\/\\/51y016016y.wicp.vip\\/public\\/\"}}}}";
        return ResponseEntity.ok(resp);
    }

    // loginMpWx
    @ApiOperation("微信小程序基本用户信息登录")
    @AnonymousPostMapping(value = "/loginMpWx")
    public ResponseEntity<Object> loginMpWx(@RequestBody String authInfo, HttpServletRequest request) throws Exception {
        // {"form":{"partyData":{"userInfo":{"country":"","gender":0,"province":"","city":"","avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/Fy8Wje4gvvbz4jUj2icvDgge8yvSpyORdbaBsYgOMxKx1hZAm4ykhCcfH1vBJia36C4NfBDmqsusPjLsJTp8k58g/132","nickName":"BAMY（陈金才）","language":"zh_CN"},"code":"033sp40w3wRGg03iqP0w3B3pss0sp402","oauth":"MP-WEIXIN"}}}
        JSONObject jsonObject = JSONUtil.parseObj(authInfo);
        String jscode = (String)jsonObject.getByPath("form.partyData.code");
        Map<String, String> result = WxAuthUtil.jscode2Session(jscode);
        // session_key -> t51jks8N0+HRS9eG1smu+g==
        // openid = ovMiJ4iyaZptZspPydqCrwPWkbJg
        String openid = result.get("openid");
        String sessionKey = result.get("session_key");
        SysUserOauthDto oauthDto = sysUserOauthService.findByOpenIdAndType(openid, "MP_WEIXIN");
        if (oauthDto == null ){
            // 开户默认用户
            oauthDto = new SysUserOauthDto();
            oauthDto.setOauthOpenId(openid);
            oauthDto.setOauthType("MP_WEIXIN");
            oauthDto.setPhone("0");
            oauthDto.setNickName( (String)jsonObject.getByPath("form.partyData.userInfo.nickName"));
            oauthDto.setGender(jsonObject.getByPath("form.partyData.userInfo.gender").toString());
            oauthDto.setAvatarPath((String)jsonObject.getByPath("form.partyData.userInfo.avatarUrl"));
            oauthDto = sysUserOauthService.create(oauthDto);
        }

        // 登录并设置会话
        JwtUserDto jwtUserDto = (JwtUserDto)userDetailsService.loadUserByUsername(oauthDto.getOauthOpenId());
        Authentication authentication =  new UsernamePasswordAuthenticationToken(jwtUserDto, null);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        // 生成令牌与第三方系统获取令牌方式
        String token = tokenProvider.createToken(authentication);
        // 保存在线信息
        onlineUserService.save(jwtUserDto, token, request);

        Map<String, Object> rsMap = new HashMap<>();
        rsMap.put("userId", jwtUserDto.getUser().getId());
        rsMap.put("token", properties.getTokenStartWith() + token);
    //    String resp = "{\"status\":500,\"message\":\"登录成功\",\"data\":{\"userId\":1,\"token\":\"c6dff77280b0d69c46d55fdb51f10e5f\"}}";
        return new ResponseEntity(new UniappResponseEntity<Map<String, Object>>(rsMap), HttpStatus.OK);
    }

    @ApiOperation("获取用户信息")
    @GetMapping(value = "/info")
    public ResponseEntity<Object> getUserInfo() {
        return ResponseEntity.ok(new UniappResponseEntity<>(SecurityUtils.getCurrentUser()));
    }

}
