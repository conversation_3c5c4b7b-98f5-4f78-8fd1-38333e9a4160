package org.cjc;

import org.cjc.modules.security.service.UserDetailsServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class LoginCacheTest {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;
    ExecutorService executor = Executors.newCachedThreadPool();

    @Test
    public void testCache() throws InterruptedException {
        long start1 = System.currentTimeMillis();
        int size = 1000;
        CountDownLatch latch = new CountDownLatch(size);
        for (int i = 0; i < size; i++) {
            executor.submit(() -> userDetailsService.loadUserByUsername("admin"));
            latch.countDown();
        }
        latch.await();

        long end1 = System.currentTimeMillis();
        //关闭缓存
        userDetailsService.setEnableCache(false);
        long start2 = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            userDetailsService.loadUserByUsername("admin");
        }
        long end2 = System.currentTimeMillis();
        System.out.print("使用缓存：" + (end1 - start1) + "毫秒\n 不使用缓存：" + (end2 - start2) + "毫秒");
    }

    @Test
    public void testCacheManager() throws InterruptedException {
        int size = 1000;
        CountDownLatch latch = new CountDownLatch(size);
        for (int i = 0; i < size; i++) {
            int mod = i % 10;
            executor.submit(() -> {
                try {
                    Thread.sleep(mod * 2 + (int) (Math.random() * 10000));
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                userDetailsService.loadUserByUsername("admin" + mod);
                latch.countDown();
                System.out.println("剩余未完成数量" + latch.getCount());
            });
        }
        latch.await();
    }

}
