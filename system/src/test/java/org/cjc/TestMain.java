package org.cjc;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class TestMain {
    /**
     *
     *
     *

     0.62070  = 4.926 / 300.168

     4.899  422.882

     0.86320 = 422.882 / 4.899/100


     0.87510

     // 9-6 14:25
     ? = 425.258 / 4.965
     factor=0.01095
     9-6 14:25 dilution=0.86838
     actual dilution:

     * @param args
     */
    public static void main(String[] args){

        System.out.println("9-6 14:25 dilution=" + cacluDilution(new BigDecimal("4.965"), new BigDecimal("425.258")));

        int a =10;
        int b= 20;

        int c = a+b;

        BigDecimal waterWeight = new BigDecimal("422.881");
        BigDecimal sampleWeight = new BigDecimal("4.899");
        BigDecimal dilution =  cacluDilution(sampleWeight,waterWeight);

        System.out.println("422.882 dilution=" + dilution);

        System.out.println("dilution==" + cacluDilution(new BigDecimal("4.926"),new BigDecimal("300.168")));

        System.out.println("c="+c);

        BigDecimal dec = new BigDecimal("0.000");

        long vLong = dec.longValue();

        if (dec.equals(new BigDecimal("0.0"))){
            System.out.println("dec equals 0.");
        }else{
            System.out.println("dec not equals 0.");
        }

    }

    private static BigDecimal cacluDilution(BigDecimal sampleWeight, BigDecimal waterWeight) {
        // BigDecimal factor = sampleWeight.divide(waterWeight,5,RoundingMode.HALF_UP).multiply(sampleWeight);
        BigDecimal factor = new BigDecimal("0.01095");
        System.out.println("factor=" + factor);
        sampleWeight = sampleWeight.subtract(factor);
        waterWeight = waterWeight.subtract(factor);
        return waterWeight.add(sampleWeight).divide(sampleWeight,5, RoundingMode.HALF_UP).divide(new BigDecimal(100),5, RoundingMode.HALF_UP);
    }

}
