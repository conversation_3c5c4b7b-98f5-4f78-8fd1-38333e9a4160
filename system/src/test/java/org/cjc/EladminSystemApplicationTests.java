package org.cjc;

import lombok.extern.log4j.Log4j2;
import org.cjc.les.core.helper.FormulaExecutionHelper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Log4j2
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class EladminSystemApplicationTests {

    @Test
    public void contextLoads() {

    }

    public static void main(String[] args) {
    }
}

