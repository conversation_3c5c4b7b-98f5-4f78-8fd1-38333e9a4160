# Spring Boot 专用ProGuard配置
-verbose
-target 1.8

# 关闭压缩和优化以保持Spring Boot兼容性
-dontshrink
-dontoptimize
-dontpreverify

# 保留属性
-keepattributes SourceFile,LineNumberTable
-keepattributes *Annotation*,InnerClasses
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
-keepattributes Signature

# 混淆时不使用大小写混合
-dontusemixedcaseclassnames

# 保留目录结构
-keepdirectories

# 完全保留Spring Boot Loader - 不进行任何处理
-keep class org.springframework.boot.loader.** { *; }

# 保留主类
-keep class org.cjc.AppRun {
    public static void main(java.lang.String[]);
}

# 保留Spring Boot和Spring Framework
-keep class org.springframework.** { *; }

# 保留所有第三方库
-keep class !org.cjc.** { *; }

# 只混淆我们自己的代码，但保留公共接口
-keep class org.cjc.** {
    public *;
    protected *;
}

# 保留Spring注解的类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * { *; }

# 保留实体类和DTO
-keep class **.*Entity { *; }
-keep class **.*DTO { *; }
-keep class **.*Dto { *; }
-keep class **.*VO { *; }
-keep class **.*Vo { *; }

# 保留枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留序列化相关
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保留JPA相关
-keep @jakarta.persistence.Entity class * { *; }
-keep @jakarta.persistence.Table class * { *; }

# 保留反射相关
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @jakarta.annotation.Resource *;
}

# 保留MANIFEST.MF中的Main-Class
-keepclasseswithmembers public class * {
    public static void main(java.lang.String[]);
}

# 忽略警告
-dontwarn **

# 打印映射文件
-printmapping D:\ANNIS\projects\les_admin\obfuscate\print_mapping.txt
-printseeds D:\ANNIS\projects\les_admin\obfuscate\print_seeds.txt
