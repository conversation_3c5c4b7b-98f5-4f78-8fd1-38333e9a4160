<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.cjc</groupId>
        <artifactId>les-admin</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>system</artifactId>
    <name>核心模块</name>

    <properties>
        <jjwt.version>0.11.1</jjwt.version>
        <!-- oshi监控需要指定jna版本, 问题详见 https://github.com/oshi/oshi/issues/1040 -->
        <jna.version>5.8.0</jna.version>
    </properties>

    <dependencies>
        <!-- 代码生成模块 -->
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>generator</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.cjc</groupId>
                    <artifactId>common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- tools 模块包含了 common 和 logging 模块 -->
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>tools</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>les-core</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>driver</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>les-mgt</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.cjc</groupId>
            <artifactId>system-api</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- jwt -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
        </dependency>

        <!-- quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>

        <!-- linux的管理 -->
		<dependency>
			<groupId>ch.ethz.ganymed</groupId>
			<artifactId>ganymed-ssh2</artifactId>
			<version>build210</version>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

        <!-- 获取系统信息 -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>5.7.1</version>
        </dependency>
    </dependencies>

    <!-- 打包 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skip>false</skip>
                    <mainClass>org.cjc.AppRun</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

        </plugins>
    </build>
</project>
