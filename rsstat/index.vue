<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" :crud="crud" />
      <!--表单组件-->
      <el-dialog
        v-model="crud.dialogVisible"
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :title="crud.status.title"
        width="500px"
      >
        <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="120px">
          <el-form-item label="主键ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="结果指标ID">
            <el-input v-model="form.resultFeatureId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="结果指标名称">
            <el-input v-model="form.featureName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="结果指标值">
            <el-input v-model="form.featureValue" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="结果结论">
            <el-input v-model="form.featureConclusion" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="结果指标创建时间">
            <el-input v-model="form.featureCreateTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="检测方法">
            <el-input v-model="form.methodName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="样品名称">
            <el-input v-model="form.sampleName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="样品分类">
            <el-input v-model="form.sampleCategory" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="样品所属客户">
            <el-input v-model="form.sampleCustomer" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否已被删除,Y/N">
            <el-input v-model="form.deleteFlag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updateBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="主键ID" />
        <el-table-column prop="resultFeatureId" label="结果指标ID" />
        <el-table-column prop="featureName" label="结果指标名称" />
        <el-table-column prop="featureValue" label="结果指标值" />
        <el-table-column prop="featureConclusion" label="结果结论" />
        <el-table-column prop="featureCreateTime" label="结果指标创建时间" />
        <el-table-column prop="methodName" label="检测方法" />
        <el-table-column prop="sampleName" label="样品名称" />
        <el-table-column prop="sampleCategory" label="样品分类" />
        <el-table-column prop="sampleCustomer" label="样品所属客户" />
        <el-table-column prop="deleteFlag" label="是否已被删除,Y/N" />
        <el-table-column prop="createBy" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateBy" label="更新人" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','statsResultFeature:edit','statsResultFeature:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :crud="crud"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud"/>

  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import crudStatsResultFeature from '@/api/statsResultFeature'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
import pagination from '@/components/crud/Pagination'

// 定义组件名称
defineOptions({
  name: 'StatsResultFeature'
})

const defaultForm = { id: null, resultFeatureId: null, featureName: null, featureValue: null, featureConclusion: null, featureCreateTime: null, methodName: null, sampleName: null, sampleCategory: null, sampleCustomer: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }

// 表单验证规则
const rules = reactive({
      })

// 权限配置
const permission = {
  add: ['admin', 'statsResultFeature:add'],
  edit: ['admin', 'statsResultFeature:edit'],
  del: ['admin', 'statsResultFeature:del']
}

// refs
const formRef = ref(null)
const tableRef = ref(null)

// 权限校验
const checkPer = inject('checkPer')



// 使用CRUD钩子
const { crud, form } = useCrud({
  title: '编辑',
  url: 'api/statsResultFeature',
  crudMethod: { ...crudStatsResultFeature },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 生命周期
onMounted(() => {
  crud.refresh()
})

</script>

<style scoped>

</style>
