@echo off
echo ========================================
echo 测试ProGuard混淆配置
echo ========================================
echo.

echo 1. 检查Maven是否可用...
mvn --version
if %errorlevel% neq 0 (
    echo 错误: Maven未安装或未配置到PATH中
    pause
    exit /b 1
)

echo.
echo 2. 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: Java未安装或未配置到PATH中
    pause
    exit /b 1
)

echo.
echo 3. 清理项目...
call mvn clean

echo.
echo 4. 编译项目...
call mvn compile
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 5. 打包项目（不执行混淆）...
call mvn package -DskipTests
if %errorlevel% neq 0 (
    echo 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 测试完成！项目可以正常编译和打包
echo 现在可以运行 build-obfuscated.bat 进行混淆
echo ========================================
pause
