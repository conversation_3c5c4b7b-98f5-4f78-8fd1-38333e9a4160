import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/material',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/material/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/material',
    method: 'put',
    data
  })
}

export default { add, edit, del }
